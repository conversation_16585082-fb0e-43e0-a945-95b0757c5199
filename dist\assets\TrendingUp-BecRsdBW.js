var hv=Object.defineProperty;var gv=(a,i,o)=>i in a?hv(a,i,{enumerable:!0,configurable:!0,writable:!0,value:o}):a[i]=o;var Yi=(a,i,o)=>gv(a,typeof i!="symbol"?i+"":i,o);function yv(a,i){for(var o=0;o<i.length;o++){const u=i[o];if(typeof u!="string"&&!Array.isArray(u)){for(const s in u)if(s!=="default"&&!(s in a)){const f=Object.getOwnPropertyDescriptor(u,s);f&&Object.defineProperty(a,s,f.get?f:{enumerable:!0,get:()=>u[s]})}}}return Object.freeze(Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}))}function n0(a){return a&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,"default")?a.default:a}var hs={exports:{}},Vi={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var nh;function vv(){if(nh)return Vi;nh=1;var a=Symbol.for("react.transitional.element"),i=Symbol.for("react.fragment");function o(u,s,f){var p=null;if(f!==void 0&&(p=""+f),s.key!==void 0&&(p=""+s.key),"key"in s){f={};for(var h in s)h!=="key"&&(f[h]=s[h])}else f=s;return s=f.ref,{$$typeof:a,type:u,key:p,ref:s!==void 0?s:null,props:f}}return Vi.Fragment=i,Vi.jsx=o,Vi.jsxs=o,Vi}var ah;function bv(){return ah||(ah=1,hs.exports=vv()),hs.exports}var tt=bv(),gs={exports:{}},Xi={},ys={exports:{}},vs={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var lh;function Sv(){return lh||(lh=1,function(a){function i($,K){var at=$.length;$.push(K);t:for(;0<at;){var lt=at-1>>>1,x=$[lt];if(0<s(x,K))$[lt]=K,$[at]=x,at=lt;else break t}}function o($){return $.length===0?null:$[0]}function u($){if($.length===0)return null;var K=$[0],at=$.pop();if(at!==K){$[0]=at;t:for(var lt=0,x=$.length,H=x>>>1;lt<H;){var J=2*(lt+1)-1,I=$[J],ut=J+1,bt=$[ut];if(0>s(I,at))ut<x&&0>s(bt,I)?($[lt]=bt,$[ut]=at,lt=ut):($[lt]=I,$[J]=at,lt=J);else if(ut<x&&0>s(bt,at))$[lt]=bt,$[ut]=at,lt=ut;else break t}}return K}function s($,K){var at=$.sortIndex-K.sortIndex;return at!==0?at:$.id-K.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;a.unstable_now=function(){return f.now()}}else{var p=Date,h=p.now();a.unstable_now=function(){return p.now()-h}}var y=[],g=[],b=1,C=null,E=3,B=!1,R=!1,T=!1,L=!1,V=typeof setTimeout=="function"?setTimeout:null,Z=typeof clearTimeout=="function"?clearTimeout:null,X=typeof setImmediate<"u"?setImmediate:null;function U($){for(var K=o(g);K!==null;){if(K.callback===null)u(g);else if(K.startTime<=$)u(g),K.sortIndex=K.expirationTime,i(y,K);else break;K=o(g)}}function M($){if(T=!1,U($),!R)if(o(y)!==null)R=!0,Q||(Q=!0,P());else{var K=o(g);K!==null&&ht(M,K.startTime-$)}}var Q=!1,F=-1,W=5,nt=-1;function v(){return L?!0:!(a.unstable_now()-nt<W)}function k(){if(L=!1,Q){var $=a.unstable_now();nt=$;var K=!0;try{t:{R=!1,T&&(T=!1,Z(F),F=-1),B=!0;var at=E;try{e:{for(U($),C=o(y);C!==null&&!(C.expirationTime>$&&v());){var lt=C.callback;if(typeof lt=="function"){C.callback=null,E=C.priorityLevel;var x=lt(C.expirationTime<=$);if($=a.unstable_now(),typeof x=="function"){C.callback=x,U($),K=!0;break e}C===o(y)&&u(y),U($)}else u(y);C=o(y)}if(C!==null)K=!0;else{var H=o(g);H!==null&&ht(M,H.startTime-$),K=!1}}break t}finally{C=null,E=at,B=!1}K=void 0}}finally{K?P():Q=!1}}}var P;if(typeof X=="function")P=function(){X(k)};else if(typeof MessageChannel<"u"){var it=new MessageChannel,ft=it.port2;it.port1.onmessage=k,P=function(){ft.postMessage(null)}}else P=function(){V(k,0)};function ht($,K){F=V(function(){$(a.unstable_now())},K)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function($){$.callback=null},a.unstable_forceFrameRate=function($){0>$||125<$?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):W=0<$?Math.floor(1e3/$):5},a.unstable_getCurrentPriorityLevel=function(){return E},a.unstable_next=function($){switch(E){case 1:case 2:case 3:var K=3;break;default:K=E}var at=E;E=K;try{return $()}finally{E=at}},a.unstable_requestPaint=function(){L=!0},a.unstable_runWithPriority=function($,K){switch($){case 1:case 2:case 3:case 4:case 5:break;default:$=3}var at=E;E=$;try{return K()}finally{E=at}},a.unstable_scheduleCallback=function($,K,at){var lt=a.unstable_now();switch(typeof at=="object"&&at!==null?(at=at.delay,at=typeof at=="number"&&0<at?lt+at:lt):at=lt,$){case 1:var x=-1;break;case 2:x=250;break;case 5:x=1073741823;break;case 4:x=1e4;break;default:x=5e3}return x=at+x,$={id:b++,callback:K,priorityLevel:$,startTime:at,expirationTime:x,sortIndex:-1},at>lt?($.sortIndex=at,i(g,$),o(y)===null&&$===o(g)&&(T?(Z(F),F=-1):T=!0,ht(M,at-lt))):($.sortIndex=x,i(y,$),R||B||(R=!0,Q||(Q=!0,P()))),$},a.unstable_shouldYield=v,a.unstable_wrapCallback=function($){var K=E;return function(){var at=E;E=K;try{return $.apply(this,arguments)}finally{E=at}}}}(vs)),vs}var ih;function Cv(){return ih||(ih=1,ys.exports=Sv()),ys.exports}var bs={exports:{}},gt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var rh;function Tv(){if(rh)return gt;rh=1;var a=Symbol.for("react.transitional.element"),i=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),u=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),p=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),b=Symbol.for("react.lazy"),C=Symbol.iterator;function E(x){return x===null||typeof x!="object"?null:(x=C&&x[C]||x["@@iterator"],typeof x=="function"?x:null)}var B={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},R=Object.assign,T={};function L(x,H,J){this.props=x,this.context=H,this.refs=T,this.updater=J||B}L.prototype.isReactComponent={},L.prototype.setState=function(x,H){if(typeof x!="object"&&typeof x!="function"&&x!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,x,H,"setState")},L.prototype.forceUpdate=function(x){this.updater.enqueueForceUpdate(this,x,"forceUpdate")};function V(){}V.prototype=L.prototype;function Z(x,H,J){this.props=x,this.context=H,this.refs=T,this.updater=J||B}var X=Z.prototype=new V;X.constructor=Z,R(X,L.prototype),X.isPureReactComponent=!0;var U=Array.isArray,M={H:null,A:null,T:null,S:null,V:null},Q=Object.prototype.hasOwnProperty;function F(x,H,J,I,ut,bt){return J=bt.ref,{$$typeof:a,type:x,key:H,ref:J!==void 0?J:null,props:bt}}function W(x,H){return F(x.type,H,void 0,void 0,void 0,x.props)}function nt(x){return typeof x=="object"&&x!==null&&x.$$typeof===a}function v(x){var H={"=":"=0",":":"=2"};return"$"+x.replace(/[=:]/g,function(J){return H[J]})}var k=/\/+/g;function P(x,H){return typeof x=="object"&&x!==null&&x.key!=null?v(""+x.key):H.toString(36)}function it(){}function ft(x){switch(x.status){case"fulfilled":return x.value;case"rejected":throw x.reason;default:switch(typeof x.status=="string"?x.then(it,it):(x.status="pending",x.then(function(H){x.status==="pending"&&(x.status="fulfilled",x.value=H)},function(H){x.status==="pending"&&(x.status="rejected",x.reason=H)})),x.status){case"fulfilled":return x.value;case"rejected":throw x.reason}}throw x}function ht(x,H,J,I,ut){var bt=typeof x;(bt==="undefined"||bt==="boolean")&&(x=null);var dt=!1;if(x===null)dt=!0;else switch(bt){case"bigint":case"string":case"number":dt=!0;break;case"object":switch(x.$$typeof){case a:case i:dt=!0;break;case b:return dt=x._init,ht(dt(x._payload),H,J,I,ut)}}if(dt)return ut=ut(x),dt=I===""?"."+P(x,0):I,U(ut)?(J="",dt!=null&&(J=dt.replace(k,"$&/")+"/"),ht(ut,H,J,"",function(Ge){return Ge})):ut!=null&&(nt(ut)&&(ut=W(ut,J+(ut.key==null||x&&x.key===ut.key?"":(""+ut.key).replace(k,"$&/")+"/")+dt)),H.push(ut)),1;dt=0;var Zt=I===""?".":I+":";if(U(x))for(var Bt=0;Bt<x.length;Bt++)I=x[Bt],bt=Zt+P(I,Bt),dt+=ht(I,H,J,bt,ut);else if(Bt=E(x),typeof Bt=="function")for(x=Bt.call(x),Bt=0;!(I=x.next()).done;)I=I.value,bt=Zt+P(I,Bt++),dt+=ht(I,H,J,bt,ut);else if(bt==="object"){if(typeof x.then=="function")return ht(ft(x),H,J,I,ut);throw H=String(x),Error("Objects are not valid as a React child (found: "+(H==="[object Object]"?"object with keys {"+Object.keys(x).join(", ")+"}":H)+"). If you meant to render a collection of children, use an array instead.")}return dt}function $(x,H,J){if(x==null)return x;var I=[],ut=0;return ht(x,I,"","",function(bt){return H.call(J,bt,ut++)}),I}function K(x){if(x._status===-1){var H=x._result;H=H(),H.then(function(J){(x._status===0||x._status===-1)&&(x._status=1,x._result=J)},function(J){(x._status===0||x._status===-1)&&(x._status=2,x._result=J)}),x._status===-1&&(x._status=0,x._result=H)}if(x._status===1)return x._result.default;throw x._result}var at=typeof reportError=="function"?reportError:function(x){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var H=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof x=="object"&&x!==null&&typeof x.message=="string"?String(x.message):String(x),error:x});if(!window.dispatchEvent(H))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",x);return}console.error(x)};function lt(){}return gt.Children={map:$,forEach:function(x,H,J){$(x,function(){H.apply(this,arguments)},J)},count:function(x){var H=0;return $(x,function(){H++}),H},toArray:function(x){return $(x,function(H){return H})||[]},only:function(x){if(!nt(x))throw Error("React.Children.only expected to receive a single React element child.");return x}},gt.Component=L,gt.Fragment=o,gt.Profiler=s,gt.PureComponent=Z,gt.StrictMode=u,gt.Suspense=y,gt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=M,gt.__COMPILER_RUNTIME={__proto__:null,c:function(x){return M.H.useMemoCache(x)}},gt.cache=function(x){return function(){return x.apply(null,arguments)}},gt.cloneElement=function(x,H,J){if(x==null)throw Error("The argument must be a React element, but you passed "+x+".");var I=R({},x.props),ut=x.key,bt=void 0;if(H!=null)for(dt in H.ref!==void 0&&(bt=void 0),H.key!==void 0&&(ut=""+H.key),H)!Q.call(H,dt)||dt==="key"||dt==="__self"||dt==="__source"||dt==="ref"&&H.ref===void 0||(I[dt]=H[dt]);var dt=arguments.length-2;if(dt===1)I.children=J;else if(1<dt){for(var Zt=Array(dt),Bt=0;Bt<dt;Bt++)Zt[Bt]=arguments[Bt+2];I.children=Zt}return F(x.type,ut,void 0,void 0,bt,I)},gt.createContext=function(x){return x={$$typeof:p,_currentValue:x,_currentValue2:x,_threadCount:0,Provider:null,Consumer:null},x.Provider=x,x.Consumer={$$typeof:f,_context:x},x},gt.createElement=function(x,H,J){var I,ut={},bt=null;if(H!=null)for(I in H.key!==void 0&&(bt=""+H.key),H)Q.call(H,I)&&I!=="key"&&I!=="__self"&&I!=="__source"&&(ut[I]=H[I]);var dt=arguments.length-2;if(dt===1)ut.children=J;else if(1<dt){for(var Zt=Array(dt),Bt=0;Bt<dt;Bt++)Zt[Bt]=arguments[Bt+2];ut.children=Zt}if(x&&x.defaultProps)for(I in dt=x.defaultProps,dt)ut[I]===void 0&&(ut[I]=dt[I]);return F(x,bt,void 0,void 0,null,ut)},gt.createRef=function(){return{current:null}},gt.forwardRef=function(x){return{$$typeof:h,render:x}},gt.isValidElement=nt,gt.lazy=function(x){return{$$typeof:b,_payload:{_status:-1,_result:x},_init:K}},gt.memo=function(x,H){return{$$typeof:g,type:x,compare:H===void 0?null:H}},gt.startTransition=function(x){var H=M.T,J={};M.T=J;try{var I=x(),ut=M.S;ut!==null&&ut(J,I),typeof I=="object"&&I!==null&&typeof I.then=="function"&&I.then(lt,at)}catch(bt){at(bt)}finally{M.T=H}},gt.unstable_useCacheRefresh=function(){return M.H.useCacheRefresh()},gt.use=function(x){return M.H.use(x)},gt.useActionState=function(x,H,J){return M.H.useActionState(x,H,J)},gt.useCallback=function(x,H){return M.H.useCallback(x,H)},gt.useContext=function(x){return M.H.useContext(x)},gt.useDebugValue=function(){},gt.useDeferredValue=function(x,H){return M.H.useDeferredValue(x,H)},gt.useEffect=function(x,H,J){var I=M.H;if(typeof J=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return I.useEffect(x,H)},gt.useId=function(){return M.H.useId()},gt.useImperativeHandle=function(x,H,J){return M.H.useImperativeHandle(x,H,J)},gt.useInsertionEffect=function(x,H){return M.H.useInsertionEffect(x,H)},gt.useLayoutEffect=function(x,H){return M.H.useLayoutEffect(x,H)},gt.useMemo=function(x,H){return M.H.useMemo(x,H)},gt.useOptimistic=function(x,H){return M.H.useOptimistic(x,H)},gt.useReducer=function(x,H,J){return M.H.useReducer(x,H,J)},gt.useRef=function(x){return M.H.useRef(x)},gt.useState=function(x){return M.H.useState(x)},gt.useSyncExternalStore=function(x,H,J){return M.H.useSyncExternalStore(x,H,J)},gt.useTransition=function(){return M.H.useTransition()},gt.version="19.1.0",gt}var oh;function Vs(){return oh||(oh=1,bs.exports=Tv()),bs.exports}var Ss={exports:{}},xe={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var uh;function xv(){if(uh)return xe;uh=1;var a=Vs();function i(y){var g="https://react.dev/errors/"+y;if(1<arguments.length){g+="?args[]="+encodeURIComponent(arguments[1]);for(var b=2;b<arguments.length;b++)g+="&args[]="+encodeURIComponent(arguments[b])}return"Minified React error #"+y+"; visit "+g+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(){}var u={d:{f:o,r:function(){throw Error(i(522))},D:o,C:o,L:o,m:o,X:o,S:o,M:o},p:0,findDOMNode:null},s=Symbol.for("react.portal");function f(y,g,b){var C=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:s,key:C==null?null:""+C,children:y,containerInfo:g,implementation:b}}var p=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function h(y,g){if(y==="font")return"";if(typeof g=="string")return g==="use-credentials"?g:""}return xe.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=u,xe.createPortal=function(y,g){var b=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!g||g.nodeType!==1&&g.nodeType!==9&&g.nodeType!==11)throw Error(i(299));return f(y,g,null,b)},xe.flushSync=function(y){var g=p.T,b=u.p;try{if(p.T=null,u.p=2,y)return y()}finally{p.T=g,u.p=b,u.d.f()}},xe.preconnect=function(y,g){typeof y=="string"&&(g?(g=g.crossOrigin,g=typeof g=="string"?g==="use-credentials"?g:"":void 0):g=null,u.d.C(y,g))},xe.prefetchDNS=function(y){typeof y=="string"&&u.d.D(y)},xe.preinit=function(y,g){if(typeof y=="string"&&g&&typeof g.as=="string"){var b=g.as,C=h(b,g.crossOrigin),E=typeof g.integrity=="string"?g.integrity:void 0,B=typeof g.fetchPriority=="string"?g.fetchPriority:void 0;b==="style"?u.d.S(y,typeof g.precedence=="string"?g.precedence:void 0,{crossOrigin:C,integrity:E,fetchPriority:B}):b==="script"&&u.d.X(y,{crossOrigin:C,integrity:E,fetchPriority:B,nonce:typeof g.nonce=="string"?g.nonce:void 0})}},xe.preinitModule=function(y,g){if(typeof y=="string")if(typeof g=="object"&&g!==null){if(g.as==null||g.as==="script"){var b=h(g.as,g.crossOrigin);u.d.M(y,{crossOrigin:b,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0})}}else g==null&&u.d.M(y)},xe.preload=function(y,g){if(typeof y=="string"&&typeof g=="object"&&g!==null&&typeof g.as=="string"){var b=g.as,C=h(b,g.crossOrigin);u.d.L(y,b,{crossOrigin:C,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0,type:typeof g.type=="string"?g.type:void 0,fetchPriority:typeof g.fetchPriority=="string"?g.fetchPriority:void 0,referrerPolicy:typeof g.referrerPolicy=="string"?g.referrerPolicy:void 0,imageSrcSet:typeof g.imageSrcSet=="string"?g.imageSrcSet:void 0,imageSizes:typeof g.imageSizes=="string"?g.imageSizes:void 0,media:typeof g.media=="string"?g.media:void 0})}},xe.preloadModule=function(y,g){if(typeof y=="string")if(g){var b=h(g.as,g.crossOrigin);u.d.m(y,{as:typeof g.as=="string"&&g.as!=="script"?g.as:void 0,crossOrigin:b,integrity:typeof g.integrity=="string"?g.integrity:void 0})}else u.d.m(y)},xe.requestFormReset=function(y){u.d.r(y)},xe.unstable_batchedUpdates=function(y,g){return y(g)},xe.useFormState=function(y,g,b){return p.H.useFormState(y,g,b)},xe.useFormStatus=function(){return p.H.useHostTransitionStatus()},xe.version="19.1.0",xe}var ch;function Ev(){if(ch)return Ss.exports;ch=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(i){console.error(i)}}return a(),Ss.exports=xv(),Ss.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sh;function Av(){if(sh)return Xi;sh=1;var a=Cv(),i=Vs(),o=Ev();function u(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function f(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function p(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function h(t){if(f(t)!==t)throw Error(u(188))}function y(t){var e=t.alternate;if(!e){if(e=f(t),e===null)throw Error(u(188));return e!==t?null:t}for(var n=t,l=e;;){var r=n.return;if(r===null)break;var c=r.alternate;if(c===null){if(l=r.return,l!==null){n=l;continue}break}if(r.child===c.child){for(c=r.child;c;){if(c===n)return h(r),t;if(c===l)return h(r),e;c=c.sibling}throw Error(u(188))}if(n.return!==l.return)n=r,l=c;else{for(var d=!1,m=r.child;m;){if(m===n){d=!0,n=r,l=c;break}if(m===l){d=!0,l=r,n=c;break}m=m.sibling}if(!d){for(m=c.child;m;){if(m===n){d=!0,n=c,l=r;break}if(m===l){d=!0,l=c,n=r;break}m=m.sibling}if(!d)throw Error(u(189))}}if(n.alternate!==l)throw Error(u(190))}if(n.tag!==3)throw Error(u(188));return n.stateNode.current===n?t:e}function g(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=g(t),e!==null)return e;t=t.sibling}return null}var b=Object.assign,C=Symbol.for("react.element"),E=Symbol.for("react.transitional.element"),B=Symbol.for("react.portal"),R=Symbol.for("react.fragment"),T=Symbol.for("react.strict_mode"),L=Symbol.for("react.profiler"),V=Symbol.for("react.provider"),Z=Symbol.for("react.consumer"),X=Symbol.for("react.context"),U=Symbol.for("react.forward_ref"),M=Symbol.for("react.suspense"),Q=Symbol.for("react.suspense_list"),F=Symbol.for("react.memo"),W=Symbol.for("react.lazy"),nt=Symbol.for("react.activity"),v=Symbol.for("react.memo_cache_sentinel"),k=Symbol.iterator;function P(t){return t===null||typeof t!="object"?null:(t=k&&t[k]||t["@@iterator"],typeof t=="function"?t:null)}var it=Symbol.for("react.client.reference");function ft(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===it?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case R:return"Fragment";case L:return"Profiler";case T:return"StrictMode";case M:return"Suspense";case Q:return"SuspenseList";case nt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case B:return"Portal";case X:return(t.displayName||"Context")+".Provider";case Z:return(t._context.displayName||"Context")+".Consumer";case U:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case F:return e=t.displayName||null,e!==null?e:ft(t.type)||"Memo";case W:e=t._payload,t=t._init;try{return ft(t(e))}catch{}}return null}var ht=Array.isArray,$=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,K=o.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,at={pending:!1,data:null,method:null,action:null},lt=[],x=-1;function H(t){return{current:t}}function J(t){0>x||(t.current=lt[x],lt[x]=null,x--)}function I(t,e){x++,lt[x]=t.current,t.current=e}var ut=H(null),bt=H(null),dt=H(null),Zt=H(null);function Bt(t,e){switch(I(dt,e),I(bt,t),I(ut,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?_m(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=_m(e),t=Bm(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}J(ut),I(ut,t)}function Ge(){J(ut),J(bt),J(dt)}function Aa(t){t.memoizedState!==null&&I(Zt,t);var e=ut.current,n=Bm(e,t.type);e!==n&&(I(bt,t),I(ut,n))}function Kn(t){bt.current===t&&(J(ut),J(bt)),Zt.current===t&&(J(Zt),Hi._currentValue=at)}var Jn=Object.prototype.hasOwnProperty,Wn=a.unstable_scheduleCallback,gn=a.unstable_cancelCallback,Za=a.unstable_shouldYield,Ql=a.unstable_requestPaint,oe=a.unstable_now,cn=a.unstable_getCurrentPriorityLevel,ge=a.unstable_ImmediatePriority,yn=a.unstable_UserBlockingPriority,Je=a.unstable_NormalPriority,mt=a.unstable_LowPriority,dr=a.unstable_IdlePriority,pr=a.log,mr=a.unstable_setDisableYieldValue,Pn=null,ye=null;function sn(t){if(typeof pr=="function"&&mr(t),ye&&typeof ye.setStrictMode=="function")try{ye.setStrictMode(Pn,t)}catch{}}var ve=Math.clz32?Math.clz32:zn,be=Math.log,Xt=Math.LN2;function zn(t){return t>>>=0,t===0?32:31-(be(t)/Xt|0)|0}var Re=256,Se=4194304;function _n(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function hr(t,e,n){var l=t.pendingLanes;if(l===0)return 0;var r=0,c=t.suspendedLanes,d=t.pingedLanes;t=t.warmLanes;var m=l&134217727;return m!==0?(l=m&~c,l!==0?r=_n(l):(d&=m,d!==0?r=_n(d):n||(n=m&~t,n!==0&&(r=_n(n))))):(m=l&~c,m!==0?r=_n(m):d!==0?r=_n(d):n||(n=l&~t,n!==0&&(r=_n(n)))),r===0?0:e!==0&&e!==r&&(e&c)===0&&(c=r&-r,n=e&-e,c>=n||c===32&&(n&4194048)!==0)?e:r}function Zl(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function ng(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function pf(){var t=Re;return Re<<=1,(Re&4194048)===0&&(Re=256),t}function mf(){var t=Se;return Se<<=1,(Se&62914560)===0&&(Se=4194304),t}function lu(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function Kl(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function ag(t,e,n,l,r,c){var d=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var m=t.entanglements,S=t.expirationTimes,_=t.hiddenUpdates;for(n=d&~n;0<n;){var G=31-ve(n),Y=1<<G;m[G]=0,S[G]=-1;var D=_[G];if(D!==null)for(_[G]=null,G=0;G<D.length;G++){var N=D[G];N!==null&&(N.lane&=-536870913)}n&=~Y}l!==0&&hf(t,l,0),c!==0&&r===0&&t.tag!==0&&(t.suspendedLanes|=c&~(d&~e))}function hf(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var l=31-ve(e);t.entangledLanes|=e,t.entanglements[l]=t.entanglements[l]|1073741824|n&4194090}function gf(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var l=31-ve(n),r=1<<l;r&e|t[l]&e&&(t[l]|=e),n&=~r}}function iu(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function ru(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function yf(){var t=K.p;return t!==0?t:(t=window.event,t===void 0?32:Wm(t.type))}function lg(t,e){var n=K.p;try{return K.p=t,e()}finally{K.p=n}}var Fn=Math.random().toString(36).slice(2),Ce="__reactFiber$"+Fn,_e="__reactProps$"+Fn,Ka="__reactContainer$"+Fn,ou="__reactEvents$"+Fn,ig="__reactListeners$"+Fn,rg="__reactHandles$"+Fn,vf="__reactResources$"+Fn,Jl="__reactMarker$"+Fn;function uu(t){delete t[Ce],delete t[_e],delete t[ou],delete t[ig],delete t[rg]}function Ja(t){var e=t[Ce];if(e)return e;for(var n=t.parentNode;n;){if(e=n[Ka]||n[Ce]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=wm(t);t!==null;){if(n=t[Ce])return n;t=wm(t)}return e}t=n,n=t.parentNode}return null}function Wa(t){if(t=t[Ce]||t[Ka]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Wl(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(u(33))}function Pa(t){var e=t[vf];return e||(e=t[vf]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function ue(t){t[Jl]=!0}var bf=new Set,Sf={};function Ra(t,e){Fa(t,e),Fa(t+"Capture",e)}function Fa(t,e){for(Sf[t]=e,t=0;t<e.length;t++)bf.add(e[t])}var og=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Cf={},Tf={};function ug(t){return Jn.call(Tf,t)?!0:Jn.call(Cf,t)?!1:og.test(t)?Tf[t]=!0:(Cf[t]=!0,!1)}function gr(t,e,n){if(ug(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var l=e.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function yr(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function Bn(t,e,n,l){if(l===null)t.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+l)}}var cu,xf;function Ia(t){if(cu===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);cu=e&&e[1]||"",xf=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+cu+t+xf}var su=!1;function fu(t,e){if(!t||su)return"";su=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(e){var Y=function(){throw Error()};if(Object.defineProperty(Y.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Y,[])}catch(N){var D=N}Reflect.construct(t,[],Y)}else{try{Y.call()}catch(N){D=N}t.call(Y.prototype)}}else{try{throw Error()}catch(N){D=N}(Y=t())&&typeof Y.catch=="function"&&Y.catch(function(){})}}catch(N){if(N&&D&&typeof N.stack=="string")return[N.stack,D.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var r=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");r&&r.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=l.DetermineComponentFrameRoot(),d=c[0],m=c[1];if(d&&m){var S=d.split(`
`),_=m.split(`
`);for(r=l=0;l<S.length&&!S[l].includes("DetermineComponentFrameRoot");)l++;for(;r<_.length&&!_[r].includes("DetermineComponentFrameRoot");)r++;if(l===S.length||r===_.length)for(l=S.length-1,r=_.length-1;1<=l&&0<=r&&S[l]!==_[r];)r--;for(;1<=l&&0<=r;l--,r--)if(S[l]!==_[r]){if(l!==1||r!==1)do if(l--,r--,0>r||S[l]!==_[r]){var G=`
`+S[l].replace(" at new "," at ");return t.displayName&&G.includes("<anonymous>")&&(G=G.replace("<anonymous>",t.displayName)),G}while(1<=l&&0<=r);break}}}finally{su=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?Ia(n):""}function cg(t){switch(t.tag){case 26:case 27:case 5:return Ia(t.type);case 16:return Ia("Lazy");case 13:return Ia("Suspense");case 19:return Ia("SuspenseList");case 0:case 15:return fu(t.type,!1);case 11:return fu(t.type.render,!1);case 1:return fu(t.type,!0);case 31:return Ia("Activity");default:return""}}function Ef(t){try{var e="";do e+=cg(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function We(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Af(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function sg(t){var e=Af(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),l=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var r=n.get,c=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return r.call(this)},set:function(d){l=""+d,c.call(this,d)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(d){l=""+d},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function vr(t){t._valueTracker||(t._valueTracker=sg(t))}function Rf(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),l="";return t&&(l=Af(t)?t.checked?"true":"false":t.value),t=l,t!==n?(e.setValue(t),!0):!1}function br(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var fg=/[\n"\\]/g;function Pe(t){return t.replace(fg,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function du(t,e,n,l,r,c,d,m){t.name="",d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?t.type=d:t.removeAttribute("type"),e!=null?d==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+We(e)):t.value!==""+We(e)&&(t.value=""+We(e)):d!=="submit"&&d!=="reset"||t.removeAttribute("value"),e!=null?pu(t,d,We(e)):n!=null?pu(t,d,We(n)):l!=null&&t.removeAttribute("value"),r==null&&c!=null&&(t.defaultChecked=!!c),r!=null&&(t.checked=r&&typeof r!="function"&&typeof r!="symbol"),m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"?t.name=""+We(m):t.removeAttribute("name")}function Of(t,e,n,l,r,c,d,m){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(t.type=c),e!=null||n!=null){if(!(c!=="submit"&&c!=="reset"||e!=null))return;n=n!=null?""+We(n):"",e=e!=null?""+We(e):n,m||e===t.value||(t.value=e),t.defaultValue=e}l=l??r,l=typeof l!="function"&&typeof l!="symbol"&&!!l,t.checked=m?t.checked:!!l,t.defaultChecked=!!l,d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"&&(t.name=d)}function pu(t,e,n){e==="number"&&br(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function tl(t,e,n,l){if(t=t.options,e){e={};for(var r=0;r<n.length;r++)e["$"+n[r]]=!0;for(n=0;n<t.length;n++)r=e.hasOwnProperty("$"+t[n].value),t[n].selected!==r&&(t[n].selected=r),r&&l&&(t[n].defaultSelected=!0)}else{for(n=""+We(n),e=null,r=0;r<t.length;r++){if(t[r].value===n){t[r].selected=!0,l&&(t[r].defaultSelected=!0);return}e!==null||t[r].disabled||(e=t[r])}e!==null&&(e.selected=!0)}}function Mf(t,e,n){if(e!=null&&(e=""+We(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+We(n):""}function zf(t,e,n,l){if(e==null){if(l!=null){if(n!=null)throw Error(u(92));if(ht(l)){if(1<l.length)throw Error(u(93));l=l[0]}n=l}n==null&&(n=""),e=n}n=We(e),t.defaultValue=n,l=t.textContent,l===n&&l!==""&&l!==null&&(t.value=l)}function el(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var dg=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function _f(t,e,n){var l=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":l?t.setProperty(e,n):typeof n!="number"||n===0||dg.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function Bf(t,e,n){if(e!=null&&typeof e!="object")throw Error(u(62));if(t=t.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||e!=null&&e.hasOwnProperty(l)||(l.indexOf("--")===0?t.setProperty(l,""):l==="float"?t.cssFloat="":t[l]="");for(var r in e)l=e[r],e.hasOwnProperty(r)&&n[r]!==l&&_f(t,r,l)}else for(var c in e)e.hasOwnProperty(c)&&_f(t,c,e[c])}function mu(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var pg=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),mg=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Sr(t){return mg.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var hu=null;function gu(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var nl=null,al=null;function Df(t){var e=Wa(t);if(e&&(t=e.stateNode)){var n=t[_e]||null;t:switch(t=e.stateNode,e.type){case"input":if(du(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Pe(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var l=n[e];if(l!==t&&l.form===t.form){var r=l[_e]||null;if(!r)throw Error(u(90));du(l,r.value,r.defaultValue,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name)}}for(e=0;e<n.length;e++)l=n[e],l.form===t.form&&Rf(l)}break t;case"textarea":Mf(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&tl(t,!!n.multiple,e,!1)}}}var yu=!1;function Nf(t,e,n){if(yu)return t(e,n);yu=!0;try{var l=t(e);return l}finally{if(yu=!1,(nl!==null||al!==null)&&(io(),nl&&(e=nl,t=al,al=nl=null,Df(e),t)))for(e=0;e<t.length;e++)Df(t[e])}}function Pl(t,e){var n=t.stateNode;if(n===null)return null;var l=n[_e]||null;if(l===null)return null;n=l[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(t=t.type,l=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!l;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(u(231,e,typeof n));return n}var Dn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),vu=!1;if(Dn)try{var Fl={};Object.defineProperty(Fl,"passive",{get:function(){vu=!0}}),window.addEventListener("test",Fl,Fl),window.removeEventListener("test",Fl,Fl)}catch{vu=!1}var In=null,bu=null,Cr=null;function $f(){if(Cr)return Cr;var t,e=bu,n=e.length,l,r="value"in In?In.value:In.textContent,c=r.length;for(t=0;t<n&&e[t]===r[t];t++);var d=n-t;for(l=1;l<=d&&e[n-l]===r[c-l];l++);return Cr=r.slice(t,1<l?1-l:void 0)}function Tr(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function xr(){return!0}function wf(){return!1}function Be(t){function e(n,l,r,c,d){this._reactName=n,this._targetInst=r,this.type=l,this.nativeEvent=c,this.target=d,this.currentTarget=null;for(var m in t)t.hasOwnProperty(m)&&(n=t[m],this[m]=n?n(c):c[m]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?xr:wf,this.isPropagationStopped=wf,this}return b(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=xr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=xr)},persist:function(){},isPersistent:xr}),e}var Oa={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Er=Be(Oa),Il=b({},Oa,{view:0,detail:0}),hg=Be(Il),Su,Cu,ti,Ar=b({},Il,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:xu,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==ti&&(ti&&t.type==="mousemove"?(Su=t.screenX-ti.screenX,Cu=t.screenY-ti.screenY):Cu=Su=0,ti=t),Su)},movementY:function(t){return"movementY"in t?t.movementY:Cu}}),Uf=Be(Ar),gg=b({},Ar,{dataTransfer:0}),yg=Be(gg),vg=b({},Il,{relatedTarget:0}),Tu=Be(vg),bg=b({},Oa,{animationName:0,elapsedTime:0,pseudoElement:0}),Sg=Be(bg),Cg=b({},Oa,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Tg=Be(Cg),xg=b({},Oa,{data:0}),jf=Be(xg),Eg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Ag={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Rg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Og(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Rg[t])?!!e[t]:!1}function xu(){return Og}var Mg=b({},Il,{key:function(t){if(t.key){var e=Eg[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Tr(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Ag[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:xu,charCode:function(t){return t.type==="keypress"?Tr(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Tr(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),zg=Be(Mg),_g=b({},Ar,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Hf=Be(_g),Bg=b({},Il,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:xu}),Dg=Be(Bg),Ng=b({},Oa,{propertyName:0,elapsedTime:0,pseudoElement:0}),$g=Be(Ng),wg=b({},Ar,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Ug=Be(wg),jg=b({},Oa,{newState:0,oldState:0}),Hg=Be(jg),Gg=[9,13,27,32],Eu=Dn&&"CompositionEvent"in window,ei=null;Dn&&"documentMode"in document&&(ei=document.documentMode);var Lg=Dn&&"TextEvent"in window&&!ei,Gf=Dn&&(!Eu||ei&&8<ei&&11>=ei),Lf=" ",kf=!1;function qf(t,e){switch(t){case"keyup":return Gg.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Yf(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var ll=!1;function kg(t,e){switch(t){case"compositionend":return Yf(e);case"keypress":return e.which!==32?null:(kf=!0,Lf);case"textInput":return t=e.data,t===Lf&&kf?null:t;default:return null}}function qg(t,e){if(ll)return t==="compositionend"||!Eu&&qf(t,e)?(t=$f(),Cr=bu=In=null,ll=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Gf&&e.locale!=="ko"?null:e.data;default:return null}}var Yg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Vf(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Yg[t.type]:e==="textarea"}function Xf(t,e,n,l){nl?al?al.push(l):al=[l]:nl=l,e=fo(e,"onChange"),0<e.length&&(n=new Er("onChange","change",null,n,l),t.push({event:n,listeners:e}))}var ni=null,ai=null;function Vg(t){Am(t,0)}function Rr(t){var e=Wl(t);if(Rf(e))return t}function Qf(t,e){if(t==="change")return e}var Zf=!1;if(Dn){var Au;if(Dn){var Ru="oninput"in document;if(!Ru){var Kf=document.createElement("div");Kf.setAttribute("oninput","return;"),Ru=typeof Kf.oninput=="function"}Au=Ru}else Au=!1;Zf=Au&&(!document.documentMode||9<document.documentMode)}function Jf(){ni&&(ni.detachEvent("onpropertychange",Wf),ai=ni=null)}function Wf(t){if(t.propertyName==="value"&&Rr(ai)){var e=[];Xf(e,ai,t,gu(t)),Nf(Vg,e)}}function Xg(t,e,n){t==="focusin"?(Jf(),ni=e,ai=n,ni.attachEvent("onpropertychange",Wf)):t==="focusout"&&Jf()}function Qg(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Rr(ai)}function Zg(t,e){if(t==="click")return Rr(e)}function Kg(t,e){if(t==="input"||t==="change")return Rr(e)}function Jg(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Le=typeof Object.is=="function"?Object.is:Jg;function li(t,e){if(Le(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),l=Object.keys(e);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var r=n[l];if(!Jn.call(e,r)||!Le(t[r],e[r]))return!1}return!0}function Pf(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Ff(t,e){var n=Pf(t);t=0;for(var l;n;){if(n.nodeType===3){if(l=t+n.textContent.length,t<=e&&l>=e)return{node:n,offset:e-t};t=l}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=Pf(n)}}function If(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?If(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function td(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=br(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=br(t.document)}return e}function Ou(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Wg=Dn&&"documentMode"in document&&11>=document.documentMode,il=null,Mu=null,ii=null,zu=!1;function ed(t,e,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;zu||il==null||il!==br(l)||(l=il,"selectionStart"in l&&Ou(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),ii&&li(ii,l)||(ii=l,l=fo(Mu,"onSelect"),0<l.length&&(e=new Er("onSelect","select",null,e,n),t.push({event:e,listeners:l}),e.target=il)))}function Ma(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var rl={animationend:Ma("Animation","AnimationEnd"),animationiteration:Ma("Animation","AnimationIteration"),animationstart:Ma("Animation","AnimationStart"),transitionrun:Ma("Transition","TransitionRun"),transitionstart:Ma("Transition","TransitionStart"),transitioncancel:Ma("Transition","TransitionCancel"),transitionend:Ma("Transition","TransitionEnd")},_u={},nd={};Dn&&(nd=document.createElement("div").style,"AnimationEvent"in window||(delete rl.animationend.animation,delete rl.animationiteration.animation,delete rl.animationstart.animation),"TransitionEvent"in window||delete rl.transitionend.transition);function za(t){if(_u[t])return _u[t];if(!rl[t])return t;var e=rl[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in nd)return _u[t]=e[n];return t}var ad=za("animationend"),ld=za("animationiteration"),id=za("animationstart"),Pg=za("transitionrun"),Fg=za("transitionstart"),Ig=za("transitioncancel"),rd=za("transitionend"),od=new Map,Bu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Bu.push("scrollEnd");function fn(t,e){od.set(t,e),Ra(e,[t])}var ud=new WeakMap;function Fe(t,e){if(typeof t=="object"&&t!==null){var n=ud.get(t);return n!==void 0?n:(e={value:t,source:e,stack:Ef(e)},ud.set(t,e),e)}return{value:t,source:e,stack:Ef(e)}}var Ie=[],ol=0,Du=0;function Or(){for(var t=ol,e=Du=ol=0;e<t;){var n=Ie[e];Ie[e++]=null;var l=Ie[e];Ie[e++]=null;var r=Ie[e];Ie[e++]=null;var c=Ie[e];if(Ie[e++]=null,l!==null&&r!==null){var d=l.pending;d===null?r.next=r:(r.next=d.next,d.next=r),l.pending=r}c!==0&&cd(n,r,c)}}function Mr(t,e,n,l){Ie[ol++]=t,Ie[ol++]=e,Ie[ol++]=n,Ie[ol++]=l,Du|=l,t.lanes|=l,t=t.alternate,t!==null&&(t.lanes|=l)}function Nu(t,e,n,l){return Mr(t,e,n,l),zr(t)}function ul(t,e){return Mr(t,null,null,e),zr(t)}function cd(t,e,n){t.lanes|=n;var l=t.alternate;l!==null&&(l.lanes|=n);for(var r=!1,c=t.return;c!==null;)c.childLanes|=n,l=c.alternate,l!==null&&(l.childLanes|=n),c.tag===22&&(t=c.stateNode,t===null||t._visibility&1||(r=!0)),t=c,c=c.return;return t.tag===3?(c=t.stateNode,r&&e!==null&&(r=31-ve(n),t=c.hiddenUpdates,l=t[r],l===null?t[r]=[e]:l.push(e),e.lane=n|536870912),c):null}function zr(t){if(50<_i)throw _i=0,Gc=null,Error(u(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var cl={};function ty(t,e,n,l){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ke(t,e,n,l){return new ty(t,e,n,l)}function $u(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Nn(t,e){var n=t.alternate;return n===null?(n=ke(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function sd(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function _r(t,e,n,l,r,c){var d=0;if(l=t,typeof t=="function")$u(t)&&(d=1);else if(typeof t=="string")d=nv(t,n,ut.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case nt:return t=ke(31,n,e,r),t.elementType=nt,t.lanes=c,t;case R:return _a(n.children,r,c,e);case T:d=8,r|=24;break;case L:return t=ke(12,n,e,r|2),t.elementType=L,t.lanes=c,t;case M:return t=ke(13,n,e,r),t.elementType=M,t.lanes=c,t;case Q:return t=ke(19,n,e,r),t.elementType=Q,t.lanes=c,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case V:case X:d=10;break t;case Z:d=9;break t;case U:d=11;break t;case F:d=14;break t;case W:d=16,l=null;break t}d=29,n=Error(u(130,t===null?"null":typeof t,"")),l=null}return e=ke(d,n,e,r),e.elementType=t,e.type=l,e.lanes=c,e}function _a(t,e,n,l){return t=ke(7,t,l,e),t.lanes=n,t}function wu(t,e,n){return t=ke(6,t,null,e),t.lanes=n,t}function Uu(t,e,n){return e=ke(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var sl=[],fl=0,Br=null,Dr=0,tn=[],en=0,Ba=null,$n=1,wn="";function Da(t,e){sl[fl++]=Dr,sl[fl++]=Br,Br=t,Dr=e}function fd(t,e,n){tn[en++]=$n,tn[en++]=wn,tn[en++]=Ba,Ba=t;var l=$n;t=wn;var r=32-ve(l)-1;l&=~(1<<r),n+=1;var c=32-ve(e)+r;if(30<c){var d=r-r%5;c=(l&(1<<d)-1).toString(32),l>>=d,r-=d,$n=1<<32-ve(e)+r|n<<r|l,wn=c+t}else $n=1<<c|n<<r|l,wn=t}function ju(t){t.return!==null&&(Da(t,1),fd(t,1,0))}function Hu(t){for(;t===Br;)Br=sl[--fl],sl[fl]=null,Dr=sl[--fl],sl[fl]=null;for(;t===Ba;)Ba=tn[--en],tn[en]=null,wn=tn[--en],tn[en]=null,$n=tn[--en],tn[en]=null}var Oe=null,Kt=null,Rt=!1,Na=null,vn=!1,Gu=Error(u(519));function $a(t){var e=Error(u(418,""));throw ui(Fe(e,t)),Gu}function dd(t){var e=t.stateNode,n=t.type,l=t.memoizedProps;switch(e[Ce]=t,e[_e]=l,n){case"dialog":Tt("cancel",e),Tt("close",e);break;case"iframe":case"object":case"embed":Tt("load",e);break;case"video":case"audio":for(n=0;n<Di.length;n++)Tt(Di[n],e);break;case"source":Tt("error",e);break;case"img":case"image":case"link":Tt("error",e),Tt("load",e);break;case"details":Tt("toggle",e);break;case"input":Tt("invalid",e),Of(e,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),vr(e);break;case"select":Tt("invalid",e);break;case"textarea":Tt("invalid",e),zf(e,l.value,l.defaultValue,l.children),vr(e)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||l.suppressHydrationWarning===!0||zm(e.textContent,n)?(l.popover!=null&&(Tt("beforetoggle",e),Tt("toggle",e)),l.onScroll!=null&&Tt("scroll",e),l.onScrollEnd!=null&&Tt("scrollend",e),l.onClick!=null&&(e.onclick=po),e=!0):e=!1,e||$a(t)}function pd(t){for(Oe=t.return;Oe;)switch(Oe.tag){case 5:case 13:vn=!1;return;case 27:case 3:vn=!0;return;default:Oe=Oe.return}}function ri(t){if(t!==Oe)return!1;if(!Rt)return pd(t),Rt=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||es(t.type,t.memoizedProps)),n=!n),n&&Kt&&$a(t),pd(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(u(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){Kt=pn(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}Kt=null}}else e===27?(e=Kt,ha(t.type)?(t=is,is=null,Kt=t):Kt=e):Kt=Oe?pn(t.stateNode.nextSibling):null;return!0}function oi(){Kt=Oe=null,Rt=!1}function md(){var t=Na;return t!==null&&($e===null?$e=t:$e.push.apply($e,t),Na=null),t}function ui(t){Na===null?Na=[t]:Na.push(t)}var Lu=H(null),wa=null,Un=null;function ta(t,e,n){I(Lu,e._currentValue),e._currentValue=n}function jn(t){t._currentValue=Lu.current,J(Lu)}function ku(t,e,n){for(;t!==null;){var l=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,l!==null&&(l.childLanes|=e)):l!==null&&(l.childLanes&e)!==e&&(l.childLanes|=e),t===n)break;t=t.return}}function qu(t,e,n,l){var r=t.child;for(r!==null&&(r.return=t);r!==null;){var c=r.dependencies;if(c!==null){var d=r.child;c=c.firstContext;t:for(;c!==null;){var m=c;c=r;for(var S=0;S<e.length;S++)if(m.context===e[S]){c.lanes|=n,m=c.alternate,m!==null&&(m.lanes|=n),ku(c.return,n,t),l||(d=null);break t}c=m.next}}else if(r.tag===18){if(d=r.return,d===null)throw Error(u(341));d.lanes|=n,c=d.alternate,c!==null&&(c.lanes|=n),ku(d,n,t),d=null}else d=r.child;if(d!==null)d.return=r;else for(d=r;d!==null;){if(d===t){d=null;break}if(r=d.sibling,r!==null){r.return=d.return,d=r;break}d=d.return}r=d}}function ci(t,e,n,l){t=null;for(var r=e,c=!1;r!==null;){if(!c){if((r.flags&524288)!==0)c=!0;else if((r.flags&262144)!==0)break}if(r.tag===10){var d=r.alternate;if(d===null)throw Error(u(387));if(d=d.memoizedProps,d!==null){var m=r.type;Le(r.pendingProps.value,d.value)||(t!==null?t.push(m):t=[m])}}else if(r===Zt.current){if(d=r.alternate,d===null)throw Error(u(387));d.memoizedState.memoizedState!==r.memoizedState.memoizedState&&(t!==null?t.push(Hi):t=[Hi])}r=r.return}t!==null&&qu(e,t,n,l),e.flags|=262144}function Nr(t){for(t=t.firstContext;t!==null;){if(!Le(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Ua(t){wa=t,Un=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Te(t){return hd(wa,t)}function $r(t,e){return wa===null&&Ua(t),hd(t,e)}function hd(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},Un===null){if(t===null)throw Error(u(308));Un=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Un=Un.next=e;return n}var ey=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,l){t.push(l)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},ny=a.unstable_scheduleCallback,ay=a.unstable_NormalPriority,ie={$$typeof:X,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Yu(){return{controller:new ey,data:new Map,refCount:0}}function si(t){t.refCount--,t.refCount===0&&ny(ay,function(){t.controller.abort()})}var fi=null,Vu=0,dl=0,pl=null;function ly(t,e){if(fi===null){var n=fi=[];Vu=0,dl=Qc(),pl={status:"pending",value:void 0,then:function(l){n.push(l)}}}return Vu++,e.then(gd,gd),e}function gd(){if(--Vu===0&&fi!==null){pl!==null&&(pl.status="fulfilled");var t=fi;fi=null,dl=0,pl=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function iy(t,e){var n=[],l={status:"pending",value:null,reason:null,then:function(r){n.push(r)}};return t.then(function(){l.status="fulfilled",l.value=e;for(var r=0;r<n.length;r++)(0,n[r])(e)},function(r){for(l.status="rejected",l.reason=r,r=0;r<n.length;r++)(0,n[r])(void 0)}),l}var yd=$.S;$.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&ly(t,e),yd!==null&&yd(t,e)};var ja=H(null);function Xu(){var t=ja.current;return t!==null?t:qt.pooledCache}function wr(t,e){e===null?I(ja,ja.current):I(ja,e.pool)}function vd(){var t=Xu();return t===null?null:{parent:ie._currentValue,pool:t}}var di=Error(u(460)),bd=Error(u(474)),Ur=Error(u(542)),Qu={then:function(){}};function Sd(t){return t=t.status,t==="fulfilled"||t==="rejected"}function jr(){}function Cd(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(jr,jr),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,xd(t),t;default:if(typeof e.status=="string")e.then(jr,jr);else{if(t=qt,t!==null&&100<t.shellSuspendCounter)throw Error(u(482));t=e,t.status="pending",t.then(function(l){if(e.status==="pending"){var r=e;r.status="fulfilled",r.value=l}},function(l){if(e.status==="pending"){var r=e;r.status="rejected",r.reason=l}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,xd(t),t}throw pi=e,di}}var pi=null;function Td(){if(pi===null)throw Error(u(459));var t=pi;return pi=null,t}function xd(t){if(t===di||t===Ur)throw Error(u(483))}var ea=!1;function Zu(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Ku(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function na(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function aa(t,e,n){var l=t.updateQueue;if(l===null)return null;if(l=l.shared,(Dt&2)!==0){var r=l.pending;return r===null?e.next=e:(e.next=r.next,r.next=e),l.pending=e,e=zr(t),cd(t,null,n),e}return Mr(t,l,e,n),zr(t)}function mi(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,gf(t,n)}}function Ju(t,e){var n=t.updateQueue,l=t.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var r=null,c=null;if(n=n.firstBaseUpdate,n!==null){do{var d={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};c===null?r=c=d:c=c.next=d,n=n.next}while(n!==null);c===null?r=c=e:c=c.next=e}else r=c=e;n={baseState:l.baseState,firstBaseUpdate:r,lastBaseUpdate:c,shared:l.shared,callbacks:l.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var Wu=!1;function hi(){if(Wu){var t=pl;if(t!==null)throw t}}function gi(t,e,n,l){Wu=!1;var r=t.updateQueue;ea=!1;var c=r.firstBaseUpdate,d=r.lastBaseUpdate,m=r.shared.pending;if(m!==null){r.shared.pending=null;var S=m,_=S.next;S.next=null,d===null?c=_:d.next=_,d=S;var G=t.alternate;G!==null&&(G=G.updateQueue,m=G.lastBaseUpdate,m!==d&&(m===null?G.firstBaseUpdate=_:m.next=_,G.lastBaseUpdate=S))}if(c!==null){var Y=r.baseState;d=0,G=_=S=null,m=c;do{var D=m.lane&-536870913,N=D!==m.lane;if(N?(Et&D)===D:(l&D)===D){D!==0&&D===dl&&(Wu=!0),G!==null&&(G=G.next={lane:0,tag:m.tag,payload:m.payload,callback:null,next:null});t:{var pt=t,ct=m;D=e;var Ut=n;switch(ct.tag){case 1:if(pt=ct.payload,typeof pt=="function"){Y=pt.call(Ut,Y,D);break t}Y=pt;break t;case 3:pt.flags=pt.flags&-65537|128;case 0:if(pt=ct.payload,D=typeof pt=="function"?pt.call(Ut,Y,D):pt,D==null)break t;Y=b({},Y,D);break t;case 2:ea=!0}}D=m.callback,D!==null&&(t.flags|=64,N&&(t.flags|=8192),N=r.callbacks,N===null?r.callbacks=[D]:N.push(D))}else N={lane:D,tag:m.tag,payload:m.payload,callback:m.callback,next:null},G===null?(_=G=N,S=Y):G=G.next=N,d|=D;if(m=m.next,m===null){if(m=r.shared.pending,m===null)break;N=m,m=N.next,N.next=null,r.lastBaseUpdate=N,r.shared.pending=null}}while(!0);G===null&&(S=Y),r.baseState=S,r.firstBaseUpdate=_,r.lastBaseUpdate=G,c===null&&(r.shared.lanes=0),fa|=d,t.lanes=d,t.memoizedState=Y}}function Ed(t,e){if(typeof t!="function")throw Error(u(191,t));t.call(e)}function Ad(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)Ed(n[t],e)}var ml=H(null),Hr=H(0);function Rd(t,e){t=Vn,I(Hr,t),I(ml,e),Vn=t|e.baseLanes}function Pu(){I(Hr,Vn),I(ml,ml.current)}function Fu(){Vn=Hr.current,J(ml),J(Hr)}var la=0,vt=null,$t=null,ne=null,Gr=!1,hl=!1,Ha=!1,Lr=0,yi=0,gl=null,ry=0;function Ft(){throw Error(u(321))}function Iu(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!Le(t[n],e[n]))return!1;return!0}function tc(t,e,n,l,r,c){return la=c,vt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,$.H=t===null||t.memoizedState===null?cp:sp,Ha=!1,c=n(l,r),Ha=!1,hl&&(c=Md(e,n,l,r)),Od(t),c}function Od(t){$.H=Qr;var e=$t!==null&&$t.next!==null;if(la=0,ne=$t=vt=null,Gr=!1,yi=0,gl=null,e)throw Error(u(300));t===null||ce||(t=t.dependencies,t!==null&&Nr(t)&&(ce=!0))}function Md(t,e,n,l){vt=t;var r=0;do{if(hl&&(gl=null),yi=0,hl=!1,25<=r)throw Error(u(301));if(r+=1,ne=$t=null,t.updateQueue!=null){var c=t.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}$.H=py,c=e(n,l)}while(hl);return c}function oy(){var t=$.H,e=t.useState()[0];return e=typeof e.then=="function"?vi(e):e,t=t.useState()[0],($t!==null?$t.memoizedState:null)!==t&&(vt.flags|=1024),e}function ec(){var t=Lr!==0;return Lr=0,t}function nc(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function ac(t){if(Gr){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Gr=!1}la=0,ne=$t=vt=null,hl=!1,yi=Lr=0,gl=null}function De(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ne===null?vt.memoizedState=ne=t:ne=ne.next=t,ne}function ae(){if($t===null){var t=vt.alternate;t=t!==null?t.memoizedState:null}else t=$t.next;var e=ne===null?vt.memoizedState:ne.next;if(e!==null)ne=e,$t=t;else{if(t===null)throw vt.alternate===null?Error(u(467)):Error(u(310));$t=t,t={memoizedState:$t.memoizedState,baseState:$t.baseState,baseQueue:$t.baseQueue,queue:$t.queue,next:null},ne===null?vt.memoizedState=ne=t:ne=ne.next=t}return ne}function lc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function vi(t){var e=yi;return yi+=1,gl===null&&(gl=[]),t=Cd(gl,t,e),e=vt,(ne===null?e.memoizedState:ne.next)===null&&(e=e.alternate,$.H=e===null||e.memoizedState===null?cp:sp),t}function kr(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return vi(t);if(t.$$typeof===X)return Te(t)}throw Error(u(438,String(t)))}function ic(t){var e=null,n=vt.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var l=vt.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(e={data:l.data.map(function(r){return r.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=lc(),vt.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),l=0;l<t;l++)n[l]=v;return e.index++,n}function Hn(t,e){return typeof e=="function"?e(t):e}function qr(t){var e=ae();return rc(e,$t,t)}function rc(t,e,n){var l=t.queue;if(l===null)throw Error(u(311));l.lastRenderedReducer=n;var r=t.baseQueue,c=l.pending;if(c!==null){if(r!==null){var d=r.next;r.next=c.next,c.next=d}e.baseQueue=r=c,l.pending=null}if(c=t.baseState,r===null)t.memoizedState=c;else{e=r.next;var m=d=null,S=null,_=e,G=!1;do{var Y=_.lane&-536870913;if(Y!==_.lane?(Et&Y)===Y:(la&Y)===Y){var D=_.revertLane;if(D===0)S!==null&&(S=S.next={lane:0,revertLane:0,action:_.action,hasEagerState:_.hasEagerState,eagerState:_.eagerState,next:null}),Y===dl&&(G=!0);else if((la&D)===D){_=_.next,D===dl&&(G=!0);continue}else Y={lane:0,revertLane:_.revertLane,action:_.action,hasEagerState:_.hasEagerState,eagerState:_.eagerState,next:null},S===null?(m=S=Y,d=c):S=S.next=Y,vt.lanes|=D,fa|=D;Y=_.action,Ha&&n(c,Y),c=_.hasEagerState?_.eagerState:n(c,Y)}else D={lane:Y,revertLane:_.revertLane,action:_.action,hasEagerState:_.hasEagerState,eagerState:_.eagerState,next:null},S===null?(m=S=D,d=c):S=S.next=D,vt.lanes|=Y,fa|=Y;_=_.next}while(_!==null&&_!==e);if(S===null?d=c:S.next=m,!Le(c,t.memoizedState)&&(ce=!0,G&&(n=pl,n!==null)))throw n;t.memoizedState=c,t.baseState=d,t.baseQueue=S,l.lastRenderedState=c}return r===null&&(l.lanes=0),[t.memoizedState,l.dispatch]}function oc(t){var e=ae(),n=e.queue;if(n===null)throw Error(u(311));n.lastRenderedReducer=t;var l=n.dispatch,r=n.pending,c=e.memoizedState;if(r!==null){n.pending=null;var d=r=r.next;do c=t(c,d.action),d=d.next;while(d!==r);Le(c,e.memoizedState)||(ce=!0),e.memoizedState=c,e.baseQueue===null&&(e.baseState=c),n.lastRenderedState=c}return[c,l]}function zd(t,e,n){var l=vt,r=ae(),c=Rt;if(c){if(n===void 0)throw Error(u(407));n=n()}else n=e();var d=!Le(($t||r).memoizedState,n);d&&(r.memoizedState=n,ce=!0),r=r.queue;var m=Dd.bind(null,l,r,t);if(bi(2048,8,m,[t]),r.getSnapshot!==e||d||ne!==null&&ne.memoizedState.tag&1){if(l.flags|=2048,yl(9,Yr(),Bd.bind(null,l,r,n,e),null),qt===null)throw Error(u(349));c||(la&124)!==0||_d(l,e,n)}return n}function _d(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=vt.updateQueue,e===null?(e=lc(),vt.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function Bd(t,e,n,l){e.value=n,e.getSnapshot=l,Nd(e)&&$d(t)}function Dd(t,e,n){return n(function(){Nd(e)&&$d(t)})}function Nd(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!Le(t,n)}catch{return!0}}function $d(t){var e=ul(t,2);e!==null&&Qe(e,t,2)}function uc(t){var e=De();if(typeof t=="function"){var n=t;if(t=n(),Ha){sn(!0);try{n()}finally{sn(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hn,lastRenderedState:t},e}function wd(t,e,n,l){return t.baseState=n,rc(t,$t,typeof l=="function"?l:Hn)}function uy(t,e,n,l,r){if(Xr(t))throw Error(u(485));if(t=e.action,t!==null){var c={payload:r,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(d){c.listeners.push(d)}};$.T!==null?n(!0):c.isTransition=!1,l(c),n=e.pending,n===null?(c.next=e.pending=c,Ud(e,c)):(c.next=n.next,e.pending=n.next=c)}}function Ud(t,e){var n=e.action,l=e.payload,r=t.state;if(e.isTransition){var c=$.T,d={};$.T=d;try{var m=n(r,l),S=$.S;S!==null&&S(d,m),jd(t,e,m)}catch(_){cc(t,e,_)}finally{$.T=c}}else try{c=n(r,l),jd(t,e,c)}catch(_){cc(t,e,_)}}function jd(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){Hd(t,e,l)},function(l){return cc(t,e,l)}):Hd(t,e,n)}function Hd(t,e,n){e.status="fulfilled",e.value=n,Gd(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,Ud(t,n)))}function cc(t,e,n){var l=t.pending;if(t.pending=null,l!==null){l=l.next;do e.status="rejected",e.reason=n,Gd(e),e=e.next;while(e!==l)}t.action=null}function Gd(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Ld(t,e){return e}function kd(t,e){if(Rt){var n=qt.formState;if(n!==null){t:{var l=vt;if(Rt){if(Kt){e:{for(var r=Kt,c=vn;r.nodeType!==8;){if(!c){r=null;break e}if(r=pn(r.nextSibling),r===null){r=null;break e}}c=r.data,r=c==="F!"||c==="F"?r:null}if(r){Kt=pn(r.nextSibling),l=r.data==="F!";break t}}$a(l)}l=!1}l&&(e=n[0])}}return n=De(),n.memoizedState=n.baseState=e,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ld,lastRenderedState:e},n.queue=l,n=rp.bind(null,vt,l),l.dispatch=n,l=uc(!1),c=mc.bind(null,vt,!1,l.queue),l=De(),r={state:e,dispatch:null,action:t,pending:null},l.queue=r,n=uy.bind(null,vt,r,c,n),r.dispatch=n,l.memoizedState=t,[e,n,!1]}function qd(t){var e=ae();return Yd(e,$t,t)}function Yd(t,e,n){if(e=rc(t,e,Ld)[0],t=qr(Hn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var l=vi(e)}catch(d){throw d===di?Ur:d}else l=e;e=ae();var r=e.queue,c=r.dispatch;return n!==e.memoizedState&&(vt.flags|=2048,yl(9,Yr(),cy.bind(null,r,n),null)),[l,c,t]}function cy(t,e){t.action=e}function Vd(t){var e=ae(),n=$t;if(n!==null)return Yd(e,n,t);ae(),e=e.memoizedState,n=ae();var l=n.queue.dispatch;return n.memoizedState=t,[e,l,!1]}function yl(t,e,n,l){return t={tag:t,create:n,deps:l,inst:e,next:null},e=vt.updateQueue,e===null&&(e=lc(),vt.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(l=n.next,n.next=t,t.next=l,e.lastEffect=t),t}function Yr(){return{destroy:void 0,resource:void 0}}function Xd(){return ae().memoizedState}function Vr(t,e,n,l){var r=De();l=l===void 0?null:l,vt.flags|=t,r.memoizedState=yl(1|e,Yr(),n,l)}function bi(t,e,n,l){var r=ae();l=l===void 0?null:l;var c=r.memoizedState.inst;$t!==null&&l!==null&&Iu(l,$t.memoizedState.deps)?r.memoizedState=yl(e,c,n,l):(vt.flags|=t,r.memoizedState=yl(1|e,c,n,l))}function Qd(t,e){Vr(8390656,8,t,e)}function Zd(t,e){bi(2048,8,t,e)}function Kd(t,e){return bi(4,2,t,e)}function Jd(t,e){return bi(4,4,t,e)}function Wd(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Pd(t,e,n){n=n!=null?n.concat([t]):null,bi(4,4,Wd.bind(null,e,t),n)}function sc(){}function Fd(t,e){var n=ae();e=e===void 0?null:e;var l=n.memoizedState;return e!==null&&Iu(e,l[1])?l[0]:(n.memoizedState=[t,e],t)}function Id(t,e){var n=ae();e=e===void 0?null:e;var l=n.memoizedState;if(e!==null&&Iu(e,l[1]))return l[0];if(l=t(),Ha){sn(!0);try{t()}finally{sn(!1)}}return n.memoizedState=[l,e],l}function fc(t,e,n){return n===void 0||(la&1073741824)!==0?t.memoizedState=e:(t.memoizedState=n,t=nm(),vt.lanes|=t,fa|=t,n)}function tp(t,e,n,l){return Le(n,e)?n:ml.current!==null?(t=fc(t,n,l),Le(t,e)||(ce=!0),t):(la&42)===0?(ce=!0,t.memoizedState=n):(t=nm(),vt.lanes|=t,fa|=t,e)}function ep(t,e,n,l,r){var c=K.p;K.p=c!==0&&8>c?c:8;var d=$.T,m={};$.T=m,mc(t,!1,e,n);try{var S=r(),_=$.S;if(_!==null&&_(m,S),S!==null&&typeof S=="object"&&typeof S.then=="function"){var G=iy(S,l);Si(t,e,G,Xe(t))}else Si(t,e,l,Xe(t))}catch(Y){Si(t,e,{then:function(){},status:"rejected",reason:Y},Xe())}finally{K.p=c,$.T=d}}function sy(){}function dc(t,e,n,l){if(t.tag!==5)throw Error(u(476));var r=np(t).queue;ep(t,r,e,at,n===null?sy:function(){return ap(t),n(l)})}function np(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:at,baseState:at,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hn,lastRenderedState:at},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Hn,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function ap(t){var e=np(t).next.queue;Si(t,e,{},Xe())}function pc(){return Te(Hi)}function lp(){return ae().memoizedState}function ip(){return ae().memoizedState}function fy(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=Xe();t=na(n);var l=aa(e,t,n);l!==null&&(Qe(l,e,n),mi(l,e,n)),e={cache:Yu()},t.payload=e;return}e=e.return}}function dy(t,e,n){var l=Xe();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Xr(t)?op(e,n):(n=Nu(t,e,n,l),n!==null&&(Qe(n,t,l),up(n,e,l)))}function rp(t,e,n){var l=Xe();Si(t,e,n,l)}function Si(t,e,n,l){var r={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Xr(t))op(e,r);else{var c=t.alternate;if(t.lanes===0&&(c===null||c.lanes===0)&&(c=e.lastRenderedReducer,c!==null))try{var d=e.lastRenderedState,m=c(d,n);if(r.hasEagerState=!0,r.eagerState=m,Le(m,d))return Mr(t,e,r,0),qt===null&&Or(),!1}catch{}finally{}if(n=Nu(t,e,r,l),n!==null)return Qe(n,t,l),up(n,e,l),!0}return!1}function mc(t,e,n,l){if(l={lane:2,revertLane:Qc(),action:l,hasEagerState:!1,eagerState:null,next:null},Xr(t)){if(e)throw Error(u(479))}else e=Nu(t,n,l,2),e!==null&&Qe(e,t,2)}function Xr(t){var e=t.alternate;return t===vt||e!==null&&e===vt}function op(t,e){hl=Gr=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function up(t,e,n){if((n&4194048)!==0){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,gf(t,n)}}var Qr={readContext:Te,use:kr,useCallback:Ft,useContext:Ft,useEffect:Ft,useImperativeHandle:Ft,useLayoutEffect:Ft,useInsertionEffect:Ft,useMemo:Ft,useReducer:Ft,useRef:Ft,useState:Ft,useDebugValue:Ft,useDeferredValue:Ft,useTransition:Ft,useSyncExternalStore:Ft,useId:Ft,useHostTransitionStatus:Ft,useFormState:Ft,useActionState:Ft,useOptimistic:Ft,useMemoCache:Ft,useCacheRefresh:Ft},cp={readContext:Te,use:kr,useCallback:function(t,e){return De().memoizedState=[t,e===void 0?null:e],t},useContext:Te,useEffect:Qd,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,Vr(4194308,4,Wd.bind(null,e,t),n)},useLayoutEffect:function(t,e){return Vr(4194308,4,t,e)},useInsertionEffect:function(t,e){Vr(4,2,t,e)},useMemo:function(t,e){var n=De();e=e===void 0?null:e;var l=t();if(Ha){sn(!0);try{t()}finally{sn(!1)}}return n.memoizedState=[l,e],l},useReducer:function(t,e,n){var l=De();if(n!==void 0){var r=n(e);if(Ha){sn(!0);try{n(e)}finally{sn(!1)}}}else r=e;return l.memoizedState=l.baseState=r,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:r},l.queue=t,t=t.dispatch=dy.bind(null,vt,t),[l.memoizedState,t]},useRef:function(t){var e=De();return t={current:t},e.memoizedState=t},useState:function(t){t=uc(t);var e=t.queue,n=rp.bind(null,vt,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:sc,useDeferredValue:function(t,e){var n=De();return fc(n,t,e)},useTransition:function(){var t=uc(!1);return t=ep.bind(null,vt,t.queue,!0,!1),De().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var l=vt,r=De();if(Rt){if(n===void 0)throw Error(u(407));n=n()}else{if(n=e(),qt===null)throw Error(u(349));(Et&124)!==0||_d(l,e,n)}r.memoizedState=n;var c={value:n,getSnapshot:e};return r.queue=c,Qd(Dd.bind(null,l,c,t),[t]),l.flags|=2048,yl(9,Yr(),Bd.bind(null,l,c,n,e),null),n},useId:function(){var t=De(),e=qt.identifierPrefix;if(Rt){var n=wn,l=$n;n=(l&~(1<<32-ve(l)-1)).toString(32)+n,e="«"+e+"R"+n,n=Lr++,0<n&&(e+="H"+n.toString(32)),e+="»"}else n=ry++,e="«"+e+"r"+n.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:pc,useFormState:kd,useActionState:kd,useOptimistic:function(t){var e=De();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=mc.bind(null,vt,!0,n),n.dispatch=e,[t,e]},useMemoCache:ic,useCacheRefresh:function(){return De().memoizedState=fy.bind(null,vt)}},sp={readContext:Te,use:kr,useCallback:Fd,useContext:Te,useEffect:Zd,useImperativeHandle:Pd,useInsertionEffect:Kd,useLayoutEffect:Jd,useMemo:Id,useReducer:qr,useRef:Xd,useState:function(){return qr(Hn)},useDebugValue:sc,useDeferredValue:function(t,e){var n=ae();return tp(n,$t.memoizedState,t,e)},useTransition:function(){var t=qr(Hn)[0],e=ae().memoizedState;return[typeof t=="boolean"?t:vi(t),e]},useSyncExternalStore:zd,useId:lp,useHostTransitionStatus:pc,useFormState:qd,useActionState:qd,useOptimistic:function(t,e){var n=ae();return wd(n,$t,t,e)},useMemoCache:ic,useCacheRefresh:ip},py={readContext:Te,use:kr,useCallback:Fd,useContext:Te,useEffect:Zd,useImperativeHandle:Pd,useInsertionEffect:Kd,useLayoutEffect:Jd,useMemo:Id,useReducer:oc,useRef:Xd,useState:function(){return oc(Hn)},useDebugValue:sc,useDeferredValue:function(t,e){var n=ae();return $t===null?fc(n,t,e):tp(n,$t.memoizedState,t,e)},useTransition:function(){var t=oc(Hn)[0],e=ae().memoizedState;return[typeof t=="boolean"?t:vi(t),e]},useSyncExternalStore:zd,useId:lp,useHostTransitionStatus:pc,useFormState:Vd,useActionState:Vd,useOptimistic:function(t,e){var n=ae();return $t!==null?wd(n,$t,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:ic,useCacheRefresh:ip},vl=null,Ci=0;function Zr(t){var e=Ci;return Ci+=1,vl===null&&(vl=[]),Cd(vl,t,e)}function Ti(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Kr(t,e){throw e.$$typeof===C?Error(u(525)):(t=Object.prototype.toString.call(e),Error(u(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function fp(t){var e=t._init;return e(t._payload)}function dp(t){function e(O,A){if(t){var z=O.deletions;z===null?(O.deletions=[A],O.flags|=16):z.push(A)}}function n(O,A){if(!t)return null;for(;A!==null;)e(O,A),A=A.sibling;return null}function l(O){for(var A=new Map;O!==null;)O.key!==null?A.set(O.key,O):A.set(O.index,O),O=O.sibling;return A}function r(O,A){return O=Nn(O,A),O.index=0,O.sibling=null,O}function c(O,A,z){return O.index=z,t?(z=O.alternate,z!==null?(z=z.index,z<A?(O.flags|=67108866,A):z):(O.flags|=67108866,A)):(O.flags|=1048576,A)}function d(O){return t&&O.alternate===null&&(O.flags|=67108866),O}function m(O,A,z,q){return A===null||A.tag!==6?(A=wu(z,O.mode,q),A.return=O,A):(A=r(A,z),A.return=O,A)}function S(O,A,z,q){var rt=z.type;return rt===R?G(O,A,z.props.children,q,z.key):A!==null&&(A.elementType===rt||typeof rt=="object"&&rt!==null&&rt.$$typeof===W&&fp(rt)===A.type)?(A=r(A,z.props),Ti(A,z),A.return=O,A):(A=_r(z.type,z.key,z.props,null,O.mode,q),Ti(A,z),A.return=O,A)}function _(O,A,z,q){return A===null||A.tag!==4||A.stateNode.containerInfo!==z.containerInfo||A.stateNode.implementation!==z.implementation?(A=Uu(z,O.mode,q),A.return=O,A):(A=r(A,z.children||[]),A.return=O,A)}function G(O,A,z,q,rt){return A===null||A.tag!==7?(A=_a(z,O.mode,q,rt),A.return=O,A):(A=r(A,z),A.return=O,A)}function Y(O,A,z){if(typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint")return A=wu(""+A,O.mode,z),A.return=O,A;if(typeof A=="object"&&A!==null){switch(A.$$typeof){case E:return z=_r(A.type,A.key,A.props,null,O.mode,z),Ti(z,A),z.return=O,z;case B:return A=Uu(A,O.mode,z),A.return=O,A;case W:var q=A._init;return A=q(A._payload),Y(O,A,z)}if(ht(A)||P(A))return A=_a(A,O.mode,z,null),A.return=O,A;if(typeof A.then=="function")return Y(O,Zr(A),z);if(A.$$typeof===X)return Y(O,$r(O,A),z);Kr(O,A)}return null}function D(O,A,z,q){var rt=A!==null?A.key:null;if(typeof z=="string"&&z!==""||typeof z=="number"||typeof z=="bigint")return rt!==null?null:m(O,A,""+z,q);if(typeof z=="object"&&z!==null){switch(z.$$typeof){case E:return z.key===rt?S(O,A,z,q):null;case B:return z.key===rt?_(O,A,z,q):null;case W:return rt=z._init,z=rt(z._payload),D(O,A,z,q)}if(ht(z)||P(z))return rt!==null?null:G(O,A,z,q,null);if(typeof z.then=="function")return D(O,A,Zr(z),q);if(z.$$typeof===X)return D(O,A,$r(O,z),q);Kr(O,z)}return null}function N(O,A,z,q,rt){if(typeof q=="string"&&q!==""||typeof q=="number"||typeof q=="bigint")return O=O.get(z)||null,m(A,O,""+q,rt);if(typeof q=="object"&&q!==null){switch(q.$$typeof){case E:return O=O.get(q.key===null?z:q.key)||null,S(A,O,q,rt);case B:return O=O.get(q.key===null?z:q.key)||null,_(A,O,q,rt);case W:var St=q._init;return q=St(q._payload),N(O,A,z,q,rt)}if(ht(q)||P(q))return O=O.get(z)||null,G(A,O,q,rt,null);if(typeof q.then=="function")return N(O,A,z,Zr(q),rt);if(q.$$typeof===X)return N(O,A,z,$r(A,q),rt);Kr(A,q)}return null}function pt(O,A,z,q){for(var rt=null,St=null,ot=A,st=A=0,fe=null;ot!==null&&st<z.length;st++){ot.index>st?(fe=ot,ot=null):fe=ot.sibling;var At=D(O,ot,z[st],q);if(At===null){ot===null&&(ot=fe);break}t&&ot&&At.alternate===null&&e(O,ot),A=c(At,A,st),St===null?rt=At:St.sibling=At,St=At,ot=fe}if(st===z.length)return n(O,ot),Rt&&Da(O,st),rt;if(ot===null){for(;st<z.length;st++)ot=Y(O,z[st],q),ot!==null&&(A=c(ot,A,st),St===null?rt=ot:St.sibling=ot,St=ot);return Rt&&Da(O,st),rt}for(ot=l(ot);st<z.length;st++)fe=N(ot,O,st,z[st],q),fe!==null&&(t&&fe.alternate!==null&&ot.delete(fe.key===null?st:fe.key),A=c(fe,A,st),St===null?rt=fe:St.sibling=fe,St=fe);return t&&ot.forEach(function(Sa){return e(O,Sa)}),Rt&&Da(O,st),rt}function ct(O,A,z,q){if(z==null)throw Error(u(151));for(var rt=null,St=null,ot=A,st=A=0,fe=null,At=z.next();ot!==null&&!At.done;st++,At=z.next()){ot.index>st?(fe=ot,ot=null):fe=ot.sibling;var Sa=D(O,ot,At.value,q);if(Sa===null){ot===null&&(ot=fe);break}t&&ot&&Sa.alternate===null&&e(O,ot),A=c(Sa,A,st),St===null?rt=Sa:St.sibling=Sa,St=Sa,ot=fe}if(At.done)return n(O,ot),Rt&&Da(O,st),rt;if(ot===null){for(;!At.done;st++,At=z.next())At=Y(O,At.value,q),At!==null&&(A=c(At,A,st),St===null?rt=At:St.sibling=At,St=At);return Rt&&Da(O,st),rt}for(ot=l(ot);!At.done;st++,At=z.next())At=N(ot,O,st,At.value,q),At!==null&&(t&&At.alternate!==null&&ot.delete(At.key===null?st:At.key),A=c(At,A,st),St===null?rt=At:St.sibling=At,St=At);return t&&ot.forEach(function(mv){return e(O,mv)}),Rt&&Da(O,st),rt}function Ut(O,A,z,q){if(typeof z=="object"&&z!==null&&z.type===R&&z.key===null&&(z=z.props.children),typeof z=="object"&&z!==null){switch(z.$$typeof){case E:t:{for(var rt=z.key;A!==null;){if(A.key===rt){if(rt=z.type,rt===R){if(A.tag===7){n(O,A.sibling),q=r(A,z.props.children),q.return=O,O=q;break t}}else if(A.elementType===rt||typeof rt=="object"&&rt!==null&&rt.$$typeof===W&&fp(rt)===A.type){n(O,A.sibling),q=r(A,z.props),Ti(q,z),q.return=O,O=q;break t}n(O,A);break}else e(O,A);A=A.sibling}z.type===R?(q=_a(z.props.children,O.mode,q,z.key),q.return=O,O=q):(q=_r(z.type,z.key,z.props,null,O.mode,q),Ti(q,z),q.return=O,O=q)}return d(O);case B:t:{for(rt=z.key;A!==null;){if(A.key===rt)if(A.tag===4&&A.stateNode.containerInfo===z.containerInfo&&A.stateNode.implementation===z.implementation){n(O,A.sibling),q=r(A,z.children||[]),q.return=O,O=q;break t}else{n(O,A);break}else e(O,A);A=A.sibling}q=Uu(z,O.mode,q),q.return=O,O=q}return d(O);case W:return rt=z._init,z=rt(z._payload),Ut(O,A,z,q)}if(ht(z))return pt(O,A,z,q);if(P(z)){if(rt=P(z),typeof rt!="function")throw Error(u(150));return z=rt.call(z),ct(O,A,z,q)}if(typeof z.then=="function")return Ut(O,A,Zr(z),q);if(z.$$typeof===X)return Ut(O,A,$r(O,z),q);Kr(O,z)}return typeof z=="string"&&z!==""||typeof z=="number"||typeof z=="bigint"?(z=""+z,A!==null&&A.tag===6?(n(O,A.sibling),q=r(A,z),q.return=O,O=q):(n(O,A),q=wu(z,O.mode,q),q.return=O,O=q),d(O)):n(O,A)}return function(O,A,z,q){try{Ci=0;var rt=Ut(O,A,z,q);return vl=null,rt}catch(ot){if(ot===di||ot===Ur)throw ot;var St=ke(29,ot,null,O.mode);return St.lanes=q,St.return=O,St}finally{}}}var bl=dp(!0),pp=dp(!1),nn=H(null),bn=null;function ia(t){var e=t.alternate;I(re,re.current&1),I(nn,t),bn===null&&(e===null||ml.current!==null||e.memoizedState!==null)&&(bn=t)}function mp(t){if(t.tag===22){if(I(re,re.current),I(nn,t),bn===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(bn=t)}}else ra()}function ra(){I(re,re.current),I(nn,nn.current)}function Gn(t){J(nn),bn===t&&(bn=null),J(re)}var re=H(0);function Jr(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||ls(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function hc(t,e,n,l){e=t.memoizedState,n=n(l,e),n=n==null?e:b({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var gc={enqueueSetState:function(t,e,n){t=t._reactInternals;var l=Xe(),r=na(l);r.payload=e,n!=null&&(r.callback=n),e=aa(t,r,l),e!==null&&(Qe(e,t,l),mi(e,t,l))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var l=Xe(),r=na(l);r.tag=1,r.payload=e,n!=null&&(r.callback=n),e=aa(t,r,l),e!==null&&(Qe(e,t,l),mi(e,t,l))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=Xe(),l=na(n);l.tag=2,e!=null&&(l.callback=e),e=aa(t,l,n),e!==null&&(Qe(e,t,n),mi(e,t,n))}};function hp(t,e,n,l,r,c,d){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(l,c,d):e.prototype&&e.prototype.isPureReactComponent?!li(n,l)||!li(r,c):!0}function gp(t,e,n,l){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,l),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,l),e.state!==t&&gc.enqueueReplaceState(e,e.state,null)}function Ga(t,e){var n=e;if("ref"in e){n={};for(var l in e)l!=="ref"&&(n[l]=e[l])}if(t=t.defaultProps){n===e&&(n=b({},n));for(var r in t)n[r]===void 0&&(n[r]=t[r])}return n}var Wr=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function yp(t){Wr(t)}function vp(t){console.error(t)}function bp(t){Wr(t)}function Pr(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(l){setTimeout(function(){throw l})}}function Sp(t,e,n){try{var l=t.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(r){setTimeout(function(){throw r})}}function yc(t,e,n){return n=na(n),n.tag=3,n.payload={element:null},n.callback=function(){Pr(t,e)},n}function Cp(t){return t=na(t),t.tag=3,t}function Tp(t,e,n,l){var r=n.type.getDerivedStateFromError;if(typeof r=="function"){var c=l.value;t.payload=function(){return r(c)},t.callback=function(){Sp(e,n,l)}}var d=n.stateNode;d!==null&&typeof d.componentDidCatch=="function"&&(t.callback=function(){Sp(e,n,l),typeof r!="function"&&(da===null?da=new Set([this]):da.add(this));var m=l.stack;this.componentDidCatch(l.value,{componentStack:m!==null?m:""})})}function my(t,e,n,l,r){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(e=n.alternate,e!==null&&ci(e,n,r,!0),n=nn.current,n!==null){switch(n.tag){case 13:return bn===null?kc():n.alternate===null&&Jt===0&&(Jt=3),n.flags&=-257,n.flags|=65536,n.lanes=r,l===Qu?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([l]):e.add(l),Yc(t,l,r)),!1;case 22:return n.flags|=65536,l===Qu?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([l]):n.add(l)),Yc(t,l,r)),!1}throw Error(u(435,n.tag))}return Yc(t,l,r),kc(),!1}if(Rt)return e=nn.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=r,l!==Gu&&(t=Error(u(422),{cause:l}),ui(Fe(t,n)))):(l!==Gu&&(e=Error(u(423),{cause:l}),ui(Fe(e,n))),t=t.current.alternate,t.flags|=65536,r&=-r,t.lanes|=r,l=Fe(l,n),r=yc(t.stateNode,l,r),Ju(t,r),Jt!==4&&(Jt=2)),!1;var c=Error(u(520),{cause:l});if(c=Fe(c,n),zi===null?zi=[c]:zi.push(c),Jt!==4&&(Jt=2),e===null)return!0;l=Fe(l,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=r&-r,n.lanes|=t,t=yc(n.stateNode,l,t),Ju(n,t),!1;case 1:if(e=n.type,c=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(da===null||!da.has(c))))return n.flags|=65536,r&=-r,n.lanes|=r,r=Cp(r),Tp(r,t,n,l),Ju(n,r),!1}n=n.return}while(n!==null);return!1}var xp=Error(u(461)),ce=!1;function de(t,e,n,l){e.child=t===null?pp(e,null,n,l):bl(e,t.child,n,l)}function Ep(t,e,n,l,r){n=n.render;var c=e.ref;if("ref"in l){var d={};for(var m in l)m!=="ref"&&(d[m]=l[m])}else d=l;return Ua(e),l=tc(t,e,n,d,c,r),m=ec(),t!==null&&!ce?(nc(t,e,r),Ln(t,e,r)):(Rt&&m&&ju(e),e.flags|=1,de(t,e,l,r),e.child)}function Ap(t,e,n,l,r){if(t===null){var c=n.type;return typeof c=="function"&&!$u(c)&&c.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=c,Rp(t,e,c,l,r)):(t=_r(n.type,null,l,e,e.mode,r),t.ref=e.ref,t.return=e,e.child=t)}if(c=t.child,!Ac(t,r)){var d=c.memoizedProps;if(n=n.compare,n=n!==null?n:li,n(d,l)&&t.ref===e.ref)return Ln(t,e,r)}return e.flags|=1,t=Nn(c,l),t.ref=e.ref,t.return=e,e.child=t}function Rp(t,e,n,l,r){if(t!==null){var c=t.memoizedProps;if(li(c,l)&&t.ref===e.ref)if(ce=!1,e.pendingProps=l=c,Ac(t,r))(t.flags&131072)!==0&&(ce=!0);else return e.lanes=t.lanes,Ln(t,e,r)}return vc(t,e,n,l,r)}function Op(t,e,n){var l=e.pendingProps,r=l.children,c=t!==null?t.memoizedState:null;if(l.mode==="hidden"){if((e.flags&128)!==0){if(l=c!==null?c.baseLanes|n:n,t!==null){for(r=e.child=t.child,c=0;r!==null;)c=c|r.lanes|r.childLanes,r=r.sibling;e.childLanes=c&~l}else e.childLanes=0,e.child=null;return Mp(t,e,l,n)}if((n&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&wr(e,c!==null?c.cachePool:null),c!==null?Rd(e,c):Pu(),mp(e);else return e.lanes=e.childLanes=536870912,Mp(t,e,c!==null?c.baseLanes|n:n,n)}else c!==null?(wr(e,c.cachePool),Rd(e,c),ra(),e.memoizedState=null):(t!==null&&wr(e,null),Pu(),ra());return de(t,e,r,n),e.child}function Mp(t,e,n,l){var r=Xu();return r=r===null?null:{parent:ie._currentValue,pool:r},e.memoizedState={baseLanes:n,cachePool:r},t!==null&&wr(e,null),Pu(),mp(e),t!==null&&ci(t,e,l,!0),null}function Fr(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(u(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function vc(t,e,n,l,r){return Ua(e),n=tc(t,e,n,l,void 0,r),l=ec(),t!==null&&!ce?(nc(t,e,r),Ln(t,e,r)):(Rt&&l&&ju(e),e.flags|=1,de(t,e,n,r),e.child)}function zp(t,e,n,l,r,c){return Ua(e),e.updateQueue=null,n=Md(e,l,n,r),Od(t),l=ec(),t!==null&&!ce?(nc(t,e,c),Ln(t,e,c)):(Rt&&l&&ju(e),e.flags|=1,de(t,e,n,c),e.child)}function _p(t,e,n,l,r){if(Ua(e),e.stateNode===null){var c=cl,d=n.contextType;typeof d=="object"&&d!==null&&(c=Te(d)),c=new n(l,c),e.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=gc,e.stateNode=c,c._reactInternals=e,c=e.stateNode,c.props=l,c.state=e.memoizedState,c.refs={},Zu(e),d=n.contextType,c.context=typeof d=="object"&&d!==null?Te(d):cl,c.state=e.memoizedState,d=n.getDerivedStateFromProps,typeof d=="function"&&(hc(e,n,d,l),c.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(d=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),d!==c.state&&gc.enqueueReplaceState(c,c.state,null),gi(e,l,c,r),hi(),c.state=e.memoizedState),typeof c.componentDidMount=="function"&&(e.flags|=4194308),l=!0}else if(t===null){c=e.stateNode;var m=e.memoizedProps,S=Ga(n,m);c.props=S;var _=c.context,G=n.contextType;d=cl,typeof G=="object"&&G!==null&&(d=Te(G));var Y=n.getDerivedStateFromProps;G=typeof Y=="function"||typeof c.getSnapshotBeforeUpdate=="function",m=e.pendingProps!==m,G||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(m||_!==d)&&gp(e,c,l,d),ea=!1;var D=e.memoizedState;c.state=D,gi(e,l,c,r),hi(),_=e.memoizedState,m||D!==_||ea?(typeof Y=="function"&&(hc(e,n,Y,l),_=e.memoizedState),(S=ea||hp(e,n,S,l,D,_,d))?(G||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(e.flags|=4194308)):(typeof c.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=l,e.memoizedState=_),c.props=l,c.state=_,c.context=d,l=S):(typeof c.componentDidMount=="function"&&(e.flags|=4194308),l=!1)}else{c=e.stateNode,Ku(t,e),d=e.memoizedProps,G=Ga(n,d),c.props=G,Y=e.pendingProps,D=c.context,_=n.contextType,S=cl,typeof _=="object"&&_!==null&&(S=Te(_)),m=n.getDerivedStateFromProps,(_=typeof m=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(d!==Y||D!==S)&&gp(e,c,l,S),ea=!1,D=e.memoizedState,c.state=D,gi(e,l,c,r),hi();var N=e.memoizedState;d!==Y||D!==N||ea||t!==null&&t.dependencies!==null&&Nr(t.dependencies)?(typeof m=="function"&&(hc(e,n,m,l),N=e.memoizedState),(G=ea||hp(e,n,G,l,D,N,S)||t!==null&&t.dependencies!==null&&Nr(t.dependencies))?(_||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(l,N,S),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(l,N,S)),typeof c.componentDidUpdate=="function"&&(e.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof c.componentDidUpdate!="function"||d===t.memoizedProps&&D===t.memoizedState||(e.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||d===t.memoizedProps&&D===t.memoizedState||(e.flags|=1024),e.memoizedProps=l,e.memoizedState=N),c.props=l,c.state=N,c.context=S,l=G):(typeof c.componentDidUpdate!="function"||d===t.memoizedProps&&D===t.memoizedState||(e.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||d===t.memoizedProps&&D===t.memoizedState||(e.flags|=1024),l=!1)}return c=l,Fr(t,e),l=(e.flags&128)!==0,c||l?(c=e.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:c.render(),e.flags|=1,t!==null&&l?(e.child=bl(e,t.child,null,r),e.child=bl(e,null,n,r)):de(t,e,n,r),e.memoizedState=c.state,t=e.child):t=Ln(t,e,r),t}function Bp(t,e,n,l){return oi(),e.flags|=256,de(t,e,n,l),e.child}var bc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Sc(t){return{baseLanes:t,cachePool:vd()}}function Cc(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=an),t}function Dp(t,e,n){var l=e.pendingProps,r=!1,c=(e.flags&128)!==0,d;if((d=c)||(d=t!==null&&t.memoizedState===null?!1:(re.current&2)!==0),d&&(r=!0,e.flags&=-129),d=(e.flags&32)!==0,e.flags&=-33,t===null){if(Rt){if(r?ia(e):ra(),Rt){var m=Kt,S;if(S=m){t:{for(S=m,m=vn;S.nodeType!==8;){if(!m){m=null;break t}if(S=pn(S.nextSibling),S===null){m=null;break t}}m=S}m!==null?(e.memoizedState={dehydrated:m,treeContext:Ba!==null?{id:$n,overflow:wn}:null,retryLane:536870912,hydrationErrors:null},S=ke(18,null,null,0),S.stateNode=m,S.return=e,e.child=S,Oe=e,Kt=null,S=!0):S=!1}S||$a(e)}if(m=e.memoizedState,m!==null&&(m=m.dehydrated,m!==null))return ls(m)?e.lanes=32:e.lanes=536870912,null;Gn(e)}return m=l.children,l=l.fallback,r?(ra(),r=e.mode,m=Ir({mode:"hidden",children:m},r),l=_a(l,r,n,null),m.return=e,l.return=e,m.sibling=l,e.child=m,r=e.child,r.memoizedState=Sc(n),r.childLanes=Cc(t,d,n),e.memoizedState=bc,l):(ia(e),Tc(e,m))}if(S=t.memoizedState,S!==null&&(m=S.dehydrated,m!==null)){if(c)e.flags&256?(ia(e),e.flags&=-257,e=xc(t,e,n)):e.memoizedState!==null?(ra(),e.child=t.child,e.flags|=128,e=null):(ra(),r=l.fallback,m=e.mode,l=Ir({mode:"visible",children:l.children},m),r=_a(r,m,n,null),r.flags|=2,l.return=e,r.return=e,l.sibling=r,e.child=l,bl(e,t.child,null,n),l=e.child,l.memoizedState=Sc(n),l.childLanes=Cc(t,d,n),e.memoizedState=bc,e=r);else if(ia(e),ls(m)){if(d=m.nextSibling&&m.nextSibling.dataset,d)var _=d.dgst;d=_,l=Error(u(419)),l.stack="",l.digest=d,ui({value:l,source:null,stack:null}),e=xc(t,e,n)}else if(ce||ci(t,e,n,!1),d=(n&t.childLanes)!==0,ce||d){if(d=qt,d!==null&&(l=n&-n,l=(l&42)!==0?1:iu(l),l=(l&(d.suspendedLanes|n))!==0?0:l,l!==0&&l!==S.retryLane))throw S.retryLane=l,ul(t,l),Qe(d,t,l),xp;m.data==="$?"||kc(),e=xc(t,e,n)}else m.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=S.treeContext,Kt=pn(m.nextSibling),Oe=e,Rt=!0,Na=null,vn=!1,t!==null&&(tn[en++]=$n,tn[en++]=wn,tn[en++]=Ba,$n=t.id,wn=t.overflow,Ba=e),e=Tc(e,l.children),e.flags|=4096);return e}return r?(ra(),r=l.fallback,m=e.mode,S=t.child,_=S.sibling,l=Nn(S,{mode:"hidden",children:l.children}),l.subtreeFlags=S.subtreeFlags&65011712,_!==null?r=Nn(_,r):(r=_a(r,m,n,null),r.flags|=2),r.return=e,l.return=e,l.sibling=r,e.child=l,l=r,r=e.child,m=t.child.memoizedState,m===null?m=Sc(n):(S=m.cachePool,S!==null?(_=ie._currentValue,S=S.parent!==_?{parent:_,pool:_}:S):S=vd(),m={baseLanes:m.baseLanes|n,cachePool:S}),r.memoizedState=m,r.childLanes=Cc(t,d,n),e.memoizedState=bc,l):(ia(e),n=t.child,t=n.sibling,n=Nn(n,{mode:"visible",children:l.children}),n.return=e,n.sibling=null,t!==null&&(d=e.deletions,d===null?(e.deletions=[t],e.flags|=16):d.push(t)),e.child=n,e.memoizedState=null,n)}function Tc(t,e){return e=Ir({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Ir(t,e){return t=ke(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function xc(t,e,n){return bl(e,t.child,null,n),t=Tc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Np(t,e,n){t.lanes|=e;var l=t.alternate;l!==null&&(l.lanes|=e),ku(t.return,e,n)}function Ec(t,e,n,l,r){var c=t.memoizedState;c===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:r}:(c.isBackwards=e,c.rendering=null,c.renderingStartTime=0,c.last=l,c.tail=n,c.tailMode=r)}function $p(t,e,n){var l=e.pendingProps,r=l.revealOrder,c=l.tail;if(de(t,e,l.children,n),l=re.current,(l&2)!==0)l=l&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Np(t,n,e);else if(t.tag===19)Np(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}l&=1}switch(I(re,l),r){case"forwards":for(n=e.child,r=null;n!==null;)t=n.alternate,t!==null&&Jr(t)===null&&(r=n),n=n.sibling;n=r,n===null?(r=e.child,e.child=null):(r=n.sibling,n.sibling=null),Ec(e,!1,r,n,c);break;case"backwards":for(n=null,r=e.child,e.child=null;r!==null;){if(t=r.alternate,t!==null&&Jr(t)===null){e.child=r;break}t=r.sibling,r.sibling=n,n=r,r=t}Ec(e,!0,n,null,c);break;case"together":Ec(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Ln(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),fa|=e.lanes,(n&e.childLanes)===0)if(t!==null){if(ci(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(u(153));if(e.child!==null){for(t=e.child,n=Nn(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=Nn(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function Ac(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Nr(t)))}function hy(t,e,n){switch(e.tag){case 3:Bt(e,e.stateNode.containerInfo),ta(e,ie,t.memoizedState.cache),oi();break;case 27:case 5:Aa(e);break;case 4:Bt(e,e.stateNode.containerInfo);break;case 10:ta(e,e.type,e.memoizedProps.value);break;case 13:var l=e.memoizedState;if(l!==null)return l.dehydrated!==null?(ia(e),e.flags|=128,null):(n&e.child.childLanes)!==0?Dp(t,e,n):(ia(e),t=Ln(t,e,n),t!==null?t.sibling:null);ia(e);break;case 19:var r=(t.flags&128)!==0;if(l=(n&e.childLanes)!==0,l||(ci(t,e,n,!1),l=(n&e.childLanes)!==0),r){if(l)return $p(t,e,n);e.flags|=128}if(r=e.memoizedState,r!==null&&(r.rendering=null,r.tail=null,r.lastEffect=null),I(re,re.current),l)break;return null;case 22:case 23:return e.lanes=0,Op(t,e,n);case 24:ta(e,ie,t.memoizedState.cache)}return Ln(t,e,n)}function wp(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)ce=!0;else{if(!Ac(t,n)&&(e.flags&128)===0)return ce=!1,hy(t,e,n);ce=(t.flags&131072)!==0}else ce=!1,Rt&&(e.flags&1048576)!==0&&fd(e,Dr,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var l=e.elementType,r=l._init;if(l=r(l._payload),e.type=l,typeof l=="function")$u(l)?(t=Ga(l,t),e.tag=1,e=_p(null,e,l,t,n)):(e.tag=0,e=vc(null,e,l,t,n));else{if(l!=null){if(r=l.$$typeof,r===U){e.tag=11,e=Ep(null,e,l,t,n);break t}else if(r===F){e.tag=14,e=Ap(null,e,l,t,n);break t}}throw e=ft(l)||l,Error(u(306,e,""))}}return e;case 0:return vc(t,e,e.type,e.pendingProps,n);case 1:return l=e.type,r=Ga(l,e.pendingProps),_p(t,e,l,r,n);case 3:t:{if(Bt(e,e.stateNode.containerInfo),t===null)throw Error(u(387));l=e.pendingProps;var c=e.memoizedState;r=c.element,Ku(t,e),gi(e,l,null,n);var d=e.memoizedState;if(l=d.cache,ta(e,ie,l),l!==c.cache&&qu(e,[ie],n,!0),hi(),l=d.element,c.isDehydrated)if(c={element:l,isDehydrated:!1,cache:d.cache},e.updateQueue.baseState=c,e.memoizedState=c,e.flags&256){e=Bp(t,e,l,n);break t}else if(l!==r){r=Fe(Error(u(424)),e),ui(r),e=Bp(t,e,l,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Kt=pn(t.firstChild),Oe=e,Rt=!0,Na=null,vn=!0,n=pp(e,null,l,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(oi(),l===r){e=Ln(t,e,n);break t}de(t,e,l,n)}e=e.child}return e;case 26:return Fr(t,e),t===null?(n=Gm(e.type,null,e.pendingProps,null))?e.memoizedState=n:Rt||(n=e.type,t=e.pendingProps,l=mo(dt.current).createElement(n),l[Ce]=e,l[_e]=t,me(l,n,t),ue(l),e.stateNode=l):e.memoizedState=Gm(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return Aa(e),t===null&&Rt&&(l=e.stateNode=Um(e.type,e.pendingProps,dt.current),Oe=e,vn=!0,r=Kt,ha(e.type)?(is=r,Kt=pn(l.firstChild)):Kt=r),de(t,e,e.pendingProps.children,n),Fr(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&Rt&&((r=l=Kt)&&(l=Yy(l,e.type,e.pendingProps,vn),l!==null?(e.stateNode=l,Oe=e,Kt=pn(l.firstChild),vn=!1,r=!0):r=!1),r||$a(e)),Aa(e),r=e.type,c=e.pendingProps,d=t!==null?t.memoizedProps:null,l=c.children,es(r,c)?l=null:d!==null&&es(r,d)&&(e.flags|=32),e.memoizedState!==null&&(r=tc(t,e,oy,null,null,n),Hi._currentValue=r),Fr(t,e),de(t,e,l,n),e.child;case 6:return t===null&&Rt&&((t=n=Kt)&&(n=Vy(n,e.pendingProps,vn),n!==null?(e.stateNode=n,Oe=e,Kt=null,t=!0):t=!1),t||$a(e)),null;case 13:return Dp(t,e,n);case 4:return Bt(e,e.stateNode.containerInfo),l=e.pendingProps,t===null?e.child=bl(e,null,l,n):de(t,e,l,n),e.child;case 11:return Ep(t,e,e.type,e.pendingProps,n);case 7:return de(t,e,e.pendingProps,n),e.child;case 8:return de(t,e,e.pendingProps.children,n),e.child;case 12:return de(t,e,e.pendingProps.children,n),e.child;case 10:return l=e.pendingProps,ta(e,e.type,l.value),de(t,e,l.children,n),e.child;case 9:return r=e.type._context,l=e.pendingProps.children,Ua(e),r=Te(r),l=l(r),e.flags|=1,de(t,e,l,n),e.child;case 14:return Ap(t,e,e.type,e.pendingProps,n);case 15:return Rp(t,e,e.type,e.pendingProps,n);case 19:return $p(t,e,n);case 31:return l=e.pendingProps,n=e.mode,l={mode:l.mode,children:l.children},t===null?(n=Ir(l,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=Nn(t.child,l),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return Op(t,e,n);case 24:return Ua(e),l=Te(ie),t===null?(r=Xu(),r===null&&(r=qt,c=Yu(),r.pooledCache=c,c.refCount++,c!==null&&(r.pooledCacheLanes|=n),r=c),e.memoizedState={parent:l,cache:r},Zu(e),ta(e,ie,r)):((t.lanes&n)!==0&&(Ku(t,e),gi(e,null,null,n),hi()),r=t.memoizedState,c=e.memoizedState,r.parent!==l?(r={parent:l,cache:l},e.memoizedState=r,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=r),ta(e,ie,l)):(l=c.cache,ta(e,ie,l),l!==r.cache&&qu(e,[ie],n,!0))),de(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(u(156,e.tag))}function kn(t){t.flags|=4}function Up(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Vm(e)){if(e=nn.current,e!==null&&((Et&4194048)===Et?bn!==null:(Et&62914560)!==Et&&(Et&536870912)===0||e!==bn))throw pi=Qu,bd;t.flags|=8192}}function to(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?mf():536870912,t.lanes|=e,xl|=e)}function xi(t,e){if(!Rt)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:l.sibling=null}}function Qt(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,l=0;if(e)for(var r=t.child;r!==null;)n|=r.lanes|r.childLanes,l|=r.subtreeFlags&65011712,l|=r.flags&65011712,r.return=t,r=r.sibling;else for(r=t.child;r!==null;)n|=r.lanes|r.childLanes,l|=r.subtreeFlags,l|=r.flags,r.return=t,r=r.sibling;return t.subtreeFlags|=l,t.childLanes=n,e}function gy(t,e,n){var l=e.pendingProps;switch(Hu(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Qt(e),null;case 1:return Qt(e),null;case 3:return n=e.stateNode,l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),jn(ie),Ge(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(ri(e)?kn(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,md())),Qt(e),null;case 26:return n=e.memoizedState,t===null?(kn(e),n!==null?(Qt(e),Up(e,n)):(Qt(e),e.flags&=-16777217)):n?n!==t.memoizedState?(kn(e),Qt(e),Up(e,n)):(Qt(e),e.flags&=-16777217):(t.memoizedProps!==l&&kn(e),Qt(e),e.flags&=-16777217),null;case 27:Kn(e),n=dt.current;var r=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==l&&kn(e);else{if(!l){if(e.stateNode===null)throw Error(u(166));return Qt(e),null}t=ut.current,ri(e)?dd(e):(t=Um(r,l,n),e.stateNode=t,kn(e))}return Qt(e),null;case 5:if(Kn(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==l&&kn(e);else{if(!l){if(e.stateNode===null)throw Error(u(166));return Qt(e),null}if(t=ut.current,ri(e))dd(e);else{switch(r=mo(dt.current),t){case 1:t=r.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=r.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=r.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=r.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=r.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof l.is=="string"?r.createElement("select",{is:l.is}):r.createElement("select"),l.multiple?t.multiple=!0:l.size&&(t.size=l.size);break;default:t=typeof l.is=="string"?r.createElement(n,{is:l.is}):r.createElement(n)}}t[Ce]=e,t[_e]=l;t:for(r=e.child;r!==null;){if(r.tag===5||r.tag===6)t.appendChild(r.stateNode);else if(r.tag!==4&&r.tag!==27&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===e)break t;for(;r.sibling===null;){if(r.return===null||r.return===e)break t;r=r.return}r.sibling.return=r.return,r=r.sibling}e.stateNode=t;t:switch(me(t,n,l),n){case"button":case"input":case"select":case"textarea":t=!!l.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&kn(e)}}return Qt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==l&&kn(e);else{if(typeof l!="string"&&e.stateNode===null)throw Error(u(166));if(t=dt.current,ri(e)){if(t=e.stateNode,n=e.memoizedProps,l=null,r=Oe,r!==null)switch(r.tag){case 27:case 5:l=r.memoizedProps}t[Ce]=e,t=!!(t.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||zm(t.nodeValue,n)),t||$a(e)}else t=mo(t).createTextNode(l),t[Ce]=e,e.stateNode=t}return Qt(e),null;case 13:if(l=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(r=ri(e),l!==null&&l.dehydrated!==null){if(t===null){if(!r)throw Error(u(318));if(r=e.memoizedState,r=r!==null?r.dehydrated:null,!r)throw Error(u(317));r[Ce]=e}else oi(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Qt(e),r=!1}else r=md(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=r),r=!0;if(!r)return e.flags&256?(Gn(e),e):(Gn(e),null)}if(Gn(e),(e.flags&128)!==0)return e.lanes=n,e;if(n=l!==null,t=t!==null&&t.memoizedState!==null,n){l=e.child,r=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(r=l.alternate.memoizedState.cachePool.pool);var c=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(c=l.memoizedState.cachePool.pool),c!==r&&(l.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),to(e,e.updateQueue),Qt(e),null;case 4:return Ge(),t===null&&Wc(e.stateNode.containerInfo),Qt(e),null;case 10:return jn(e.type),Qt(e),null;case 19:if(J(re),r=e.memoizedState,r===null)return Qt(e),null;if(l=(e.flags&128)!==0,c=r.rendering,c===null)if(l)xi(r,!1);else{if(Jt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(c=Jr(t),c!==null){for(e.flags|=128,xi(r,!1),t=c.updateQueue,e.updateQueue=t,to(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)sd(n,t),n=n.sibling;return I(re,re.current&1|2),e.child}t=t.sibling}r.tail!==null&&oe()>ao&&(e.flags|=128,l=!0,xi(r,!1),e.lanes=4194304)}else{if(!l)if(t=Jr(c),t!==null){if(e.flags|=128,l=!0,t=t.updateQueue,e.updateQueue=t,to(e,t),xi(r,!0),r.tail===null&&r.tailMode==="hidden"&&!c.alternate&&!Rt)return Qt(e),null}else 2*oe()-r.renderingStartTime>ao&&n!==536870912&&(e.flags|=128,l=!0,xi(r,!1),e.lanes=4194304);r.isBackwards?(c.sibling=e.child,e.child=c):(t=r.last,t!==null?t.sibling=c:e.child=c,r.last=c)}return r.tail!==null?(e=r.tail,r.rendering=e,r.tail=e.sibling,r.renderingStartTime=oe(),e.sibling=null,t=re.current,I(re,l?t&1|2:t&1),e):(Qt(e),null);case 22:case 23:return Gn(e),Fu(),l=e.memoizedState!==null,t!==null?t.memoizedState!==null!==l&&(e.flags|=8192):l&&(e.flags|=8192),l?(n&536870912)!==0&&(e.flags&128)===0&&(Qt(e),e.subtreeFlags&6&&(e.flags|=8192)):Qt(e),n=e.updateQueue,n!==null&&to(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),l=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),l!==n&&(e.flags|=2048),t!==null&&J(ja),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),jn(ie),Qt(e),null;case 25:return null;case 30:return null}throw Error(u(156,e.tag))}function yy(t,e){switch(Hu(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return jn(ie),Ge(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return Kn(e),null;case 13:if(Gn(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(u(340));oi()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return J(re),null;case 4:return Ge(),null;case 10:return jn(e.type),null;case 22:case 23:return Gn(e),Fu(),t!==null&&J(ja),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return jn(ie),null;case 25:return null;default:return null}}function jp(t,e){switch(Hu(e),e.tag){case 3:jn(ie),Ge();break;case 26:case 27:case 5:Kn(e);break;case 4:Ge();break;case 13:Gn(e);break;case 19:J(re);break;case 10:jn(e.type);break;case 22:case 23:Gn(e),Fu(),t!==null&&J(ja);break;case 24:jn(ie)}}function Ei(t,e){try{var n=e.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var r=l.next;n=r;do{if((n.tag&t)===t){l=void 0;var c=n.create,d=n.inst;l=c(),d.destroy=l}n=n.next}while(n!==r)}}catch(m){Ht(e,e.return,m)}}function oa(t,e,n){try{var l=e.updateQueue,r=l!==null?l.lastEffect:null;if(r!==null){var c=r.next;l=c;do{if((l.tag&t)===t){var d=l.inst,m=d.destroy;if(m!==void 0){d.destroy=void 0,r=e;var S=n,_=m;try{_()}catch(G){Ht(r,S,G)}}}l=l.next}while(l!==c)}}catch(G){Ht(e,e.return,G)}}function Hp(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{Ad(e,n)}catch(l){Ht(t,t.return,l)}}}function Gp(t,e,n){n.props=Ga(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(l){Ht(t,e,l)}}function Ai(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var l=t.stateNode;break;case 30:l=t.stateNode;break;default:l=t.stateNode}typeof n=="function"?t.refCleanup=n(l):n.current=l}}catch(r){Ht(t,e,r)}}function Sn(t,e){var n=t.ref,l=t.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(r){Ht(t,e,r)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(r){Ht(t,e,r)}else n.current=null}function Lp(t){var e=t.type,n=t.memoizedProps,l=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break t;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(r){Ht(t,t.return,r)}}function Rc(t,e,n){try{var l=t.stateNode;Hy(l,t.type,n,e),l[_e]=e}catch(r){Ht(t,t.return,r)}}function kp(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&ha(t.type)||t.tag===4}function Oc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||kp(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&ha(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Mc(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=po));else if(l!==4&&(l===27&&ha(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(Mc(t,e,n),t=t.sibling;t!==null;)Mc(t,e,n),t=t.sibling}function eo(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(l!==4&&(l===27&&ha(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(eo(t,e,n),t=t.sibling;t!==null;)eo(t,e,n),t=t.sibling}function qp(t){var e=t.stateNode,n=t.memoizedProps;try{for(var l=t.type,r=e.attributes;r.length;)e.removeAttributeNode(r[0]);me(e,l,n),e[Ce]=t,e[_e]=n}catch(c){Ht(t,t.return,c)}}var qn=!1,It=!1,zc=!1,Yp=typeof WeakSet=="function"?WeakSet:Set,se=null;function vy(t,e){if(t=t.containerInfo,Ic=So,t=td(t),Ou(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var r=l.anchorOffset,c=l.focusNode;l=l.focusOffset;try{n.nodeType,c.nodeType}catch{n=null;break t}var d=0,m=-1,S=-1,_=0,G=0,Y=t,D=null;e:for(;;){for(var N;Y!==n||r!==0&&Y.nodeType!==3||(m=d+r),Y!==c||l!==0&&Y.nodeType!==3||(S=d+l),Y.nodeType===3&&(d+=Y.nodeValue.length),(N=Y.firstChild)!==null;)D=Y,Y=N;for(;;){if(Y===t)break e;if(D===n&&++_===r&&(m=d),D===c&&++G===l&&(S=d),(N=Y.nextSibling)!==null)break;Y=D,D=Y.parentNode}Y=N}n=m===-1||S===-1?null:{start:m,end:S}}else n=null}n=n||{start:0,end:0}}else n=null;for(ts={focusedElem:t,selectionRange:n},So=!1,se=e;se!==null;)if(e=se,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,se=t;else for(;se!==null;){switch(e=se,c=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&c!==null){t=void 0,n=e,r=c.memoizedProps,c=c.memoizedState,l=n.stateNode;try{var pt=Ga(n.type,r,n.elementType===n.type);t=l.getSnapshotBeforeUpdate(pt,c),l.__reactInternalSnapshotBeforeUpdate=t}catch(ct){Ht(n,n.return,ct)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)as(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":as(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(u(163))}if(t=e.sibling,t!==null){t.return=e.return,se=t;break}se=e.return}}function Vp(t,e,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:ua(t,n),l&4&&Ei(5,n);break;case 1:if(ua(t,n),l&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(d){Ht(n,n.return,d)}else{var r=Ga(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(r,e,t.__reactInternalSnapshotBeforeUpdate)}catch(d){Ht(n,n.return,d)}}l&64&&Hp(n),l&512&&Ai(n,n.return);break;case 3:if(ua(t,n),l&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{Ad(t,e)}catch(d){Ht(n,n.return,d)}}break;case 27:e===null&&l&4&&qp(n);case 26:case 5:ua(t,n),e===null&&l&4&&Lp(n),l&512&&Ai(n,n.return);break;case 12:ua(t,n);break;case 13:ua(t,n),l&4&&Zp(t,n),l&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=Oy.bind(null,n),Xy(t,n))));break;case 22:if(l=n.memoizedState!==null||qn,!l){e=e!==null&&e.memoizedState!==null||It,r=qn;var c=It;qn=l,(It=e)&&!c?ca(t,n,(n.subtreeFlags&8772)!==0):ua(t,n),qn=r,It=c}break;case 30:break;default:ua(t,n)}}function Xp(t){var e=t.alternate;e!==null&&(t.alternate=null,Xp(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&uu(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Yt=null,Ne=!1;function Yn(t,e,n){for(n=n.child;n!==null;)Qp(t,e,n),n=n.sibling}function Qp(t,e,n){if(ye&&typeof ye.onCommitFiberUnmount=="function")try{ye.onCommitFiberUnmount(Pn,n)}catch{}switch(n.tag){case 26:It||Sn(n,e),Yn(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:It||Sn(n,e);var l=Yt,r=Ne;ha(n.type)&&(Yt=n.stateNode,Ne=!1),Yn(t,e,n),$i(n.stateNode),Yt=l,Ne=r;break;case 5:It||Sn(n,e);case 6:if(l=Yt,r=Ne,Yt=null,Yn(t,e,n),Yt=l,Ne=r,Yt!==null)if(Ne)try{(Yt.nodeType===9?Yt.body:Yt.nodeName==="HTML"?Yt.ownerDocument.body:Yt).removeChild(n.stateNode)}catch(c){Ht(n,e,c)}else try{Yt.removeChild(n.stateNode)}catch(c){Ht(n,e,c)}break;case 18:Yt!==null&&(Ne?(t=Yt,$m(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),qi(t)):$m(Yt,n.stateNode));break;case 4:l=Yt,r=Ne,Yt=n.stateNode.containerInfo,Ne=!0,Yn(t,e,n),Yt=l,Ne=r;break;case 0:case 11:case 14:case 15:It||oa(2,n,e),It||oa(4,n,e),Yn(t,e,n);break;case 1:It||(Sn(n,e),l=n.stateNode,typeof l.componentWillUnmount=="function"&&Gp(n,e,l)),Yn(t,e,n);break;case 21:Yn(t,e,n);break;case 22:It=(l=It)||n.memoizedState!==null,Yn(t,e,n),It=l;break;default:Yn(t,e,n)}}function Zp(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{qi(t)}catch(n){Ht(e,e.return,n)}}function by(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Yp),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Yp),e;default:throw Error(u(435,t.tag))}}function _c(t,e){var n=by(t);e.forEach(function(l){var r=My.bind(null,t,l);n.has(l)||(n.add(l),l.then(r,r))})}function qe(t,e){var n=e.deletions;if(n!==null)for(var l=0;l<n.length;l++){var r=n[l],c=t,d=e,m=d;t:for(;m!==null;){switch(m.tag){case 27:if(ha(m.type)){Yt=m.stateNode,Ne=!1;break t}break;case 5:Yt=m.stateNode,Ne=!1;break t;case 3:case 4:Yt=m.stateNode.containerInfo,Ne=!0;break t}m=m.return}if(Yt===null)throw Error(u(160));Qp(c,d,r),Yt=null,Ne=!1,c=r.alternate,c!==null&&(c.return=null),r.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Kp(e,t),e=e.sibling}var dn=null;function Kp(t,e){var n=t.alternate,l=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:qe(e,t),Ye(t),l&4&&(oa(3,t,t.return),Ei(3,t),oa(5,t,t.return));break;case 1:qe(e,t),Ye(t),l&512&&(It||n===null||Sn(n,n.return)),l&64&&qn&&(t=t.updateQueue,t!==null&&(l=t.callbacks,l!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var r=dn;if(qe(e,t),Ye(t),l&512&&(It||n===null||Sn(n,n.return)),l&4){var c=n!==null?n.memoizedState:null;if(l=t.memoizedState,n===null)if(l===null)if(t.stateNode===null){t:{l=t.type,n=t.memoizedProps,r=r.ownerDocument||r;e:switch(l){case"title":c=r.getElementsByTagName("title")[0],(!c||c[Jl]||c[Ce]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=r.createElement(l),r.head.insertBefore(c,r.querySelector("head > title"))),me(c,l,n),c[Ce]=t,ue(c),l=c;break t;case"link":var d=qm("link","href",r).get(l+(n.href||""));if(d){for(var m=0;m<d.length;m++)if(c=d[m],c.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&c.getAttribute("rel")===(n.rel==null?null:n.rel)&&c.getAttribute("title")===(n.title==null?null:n.title)&&c.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){d.splice(m,1);break e}}c=r.createElement(l),me(c,l,n),r.head.appendChild(c);break;case"meta":if(d=qm("meta","content",r).get(l+(n.content||""))){for(m=0;m<d.length;m++)if(c=d[m],c.getAttribute("content")===(n.content==null?null:""+n.content)&&c.getAttribute("name")===(n.name==null?null:n.name)&&c.getAttribute("property")===(n.property==null?null:n.property)&&c.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&c.getAttribute("charset")===(n.charSet==null?null:n.charSet)){d.splice(m,1);break e}}c=r.createElement(l),me(c,l,n),r.head.appendChild(c);break;default:throw Error(u(468,l))}c[Ce]=t,ue(c),l=c}t.stateNode=l}else Ym(r,t.type,t.stateNode);else t.stateNode=km(r,l,t.memoizedProps);else c!==l?(c===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):c.count--,l===null?Ym(r,t.type,t.stateNode):km(r,l,t.memoizedProps)):l===null&&t.stateNode!==null&&Rc(t,t.memoizedProps,n.memoizedProps)}break;case 27:qe(e,t),Ye(t),l&512&&(It||n===null||Sn(n,n.return)),n!==null&&l&4&&Rc(t,t.memoizedProps,n.memoizedProps);break;case 5:if(qe(e,t),Ye(t),l&512&&(It||n===null||Sn(n,n.return)),t.flags&32){r=t.stateNode;try{el(r,"")}catch(N){Ht(t,t.return,N)}}l&4&&t.stateNode!=null&&(r=t.memoizedProps,Rc(t,r,n!==null?n.memoizedProps:r)),l&1024&&(zc=!0);break;case 6:if(qe(e,t),Ye(t),l&4){if(t.stateNode===null)throw Error(u(162));l=t.memoizedProps,n=t.stateNode;try{n.nodeValue=l}catch(N){Ht(t,t.return,N)}}break;case 3:if(yo=null,r=dn,dn=ho(e.containerInfo),qe(e,t),dn=r,Ye(t),l&4&&n!==null&&n.memoizedState.isDehydrated)try{qi(e.containerInfo)}catch(N){Ht(t,t.return,N)}zc&&(zc=!1,Jp(t));break;case 4:l=dn,dn=ho(t.stateNode.containerInfo),qe(e,t),Ye(t),dn=l;break;case 12:qe(e,t),Ye(t);break;case 13:qe(e,t),Ye(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Uc=oe()),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,_c(t,l)));break;case 22:r=t.memoizedState!==null;var S=n!==null&&n.memoizedState!==null,_=qn,G=It;if(qn=_||r,It=G||S,qe(e,t),It=G,qn=_,Ye(t),l&8192)t:for(e=t.stateNode,e._visibility=r?e._visibility&-2:e._visibility|1,r&&(n===null||S||qn||It||La(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){S=n=e;try{if(c=S.stateNode,r)d=c.style,typeof d.setProperty=="function"?d.setProperty("display","none","important"):d.display="none";else{m=S.stateNode;var Y=S.memoizedProps.style,D=Y!=null&&Y.hasOwnProperty("display")?Y.display:null;m.style.display=D==null||typeof D=="boolean"?"":(""+D).trim()}}catch(N){Ht(S,S.return,N)}}}else if(e.tag===6){if(n===null){S=e;try{S.stateNode.nodeValue=r?"":S.memoizedProps}catch(N){Ht(S,S.return,N)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}l&4&&(l=t.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,_c(t,n))));break;case 19:qe(e,t),Ye(t),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,_c(t,l)));break;case 30:break;case 21:break;default:qe(e,t),Ye(t)}}function Ye(t){var e=t.flags;if(e&2){try{for(var n,l=t.return;l!==null;){if(kp(l)){n=l;break}l=l.return}if(n==null)throw Error(u(160));switch(n.tag){case 27:var r=n.stateNode,c=Oc(t);eo(t,c,r);break;case 5:var d=n.stateNode;n.flags&32&&(el(d,""),n.flags&=-33);var m=Oc(t);eo(t,m,d);break;case 3:case 4:var S=n.stateNode.containerInfo,_=Oc(t);Mc(t,_,S);break;default:throw Error(u(161))}}catch(G){Ht(t,t.return,G)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Jp(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Jp(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function ua(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Vp(t,e.alternate,e),e=e.sibling}function La(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:oa(4,e,e.return),La(e);break;case 1:Sn(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&Gp(e,e.return,n),La(e);break;case 27:$i(e.stateNode);case 26:case 5:Sn(e,e.return),La(e);break;case 22:e.memoizedState===null&&La(e);break;case 30:La(e);break;default:La(e)}t=t.sibling}}function ca(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var l=e.alternate,r=t,c=e,d=c.flags;switch(c.tag){case 0:case 11:case 15:ca(r,c,n),Ei(4,c);break;case 1:if(ca(r,c,n),l=c,r=l.stateNode,typeof r.componentDidMount=="function")try{r.componentDidMount()}catch(_){Ht(l,l.return,_)}if(l=c,r=l.updateQueue,r!==null){var m=l.stateNode;try{var S=r.shared.hiddenCallbacks;if(S!==null)for(r.shared.hiddenCallbacks=null,r=0;r<S.length;r++)Ed(S[r],m)}catch(_){Ht(l,l.return,_)}}n&&d&64&&Hp(c),Ai(c,c.return);break;case 27:qp(c);case 26:case 5:ca(r,c,n),n&&l===null&&d&4&&Lp(c),Ai(c,c.return);break;case 12:ca(r,c,n);break;case 13:ca(r,c,n),n&&d&4&&Zp(r,c);break;case 22:c.memoizedState===null&&ca(r,c,n),Ai(c,c.return);break;case 30:break;default:ca(r,c,n)}e=e.sibling}}function Bc(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&si(n))}function Dc(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&si(t))}function Cn(t,e,n,l){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Wp(t,e,n,l),e=e.sibling}function Wp(t,e,n,l){var r=e.flags;switch(e.tag){case 0:case 11:case 15:Cn(t,e,n,l),r&2048&&Ei(9,e);break;case 1:Cn(t,e,n,l);break;case 3:Cn(t,e,n,l),r&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&si(t)));break;case 12:if(r&2048){Cn(t,e,n,l),t=e.stateNode;try{var c=e.memoizedProps,d=c.id,m=c.onPostCommit;typeof m=="function"&&m(d,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(S){Ht(e,e.return,S)}}else Cn(t,e,n,l);break;case 13:Cn(t,e,n,l);break;case 23:break;case 22:c=e.stateNode,d=e.alternate,e.memoizedState!==null?c._visibility&2?Cn(t,e,n,l):Ri(t,e):c._visibility&2?Cn(t,e,n,l):(c._visibility|=2,Sl(t,e,n,l,(e.subtreeFlags&10256)!==0)),r&2048&&Bc(d,e);break;case 24:Cn(t,e,n,l),r&2048&&Dc(e.alternate,e);break;default:Cn(t,e,n,l)}}function Sl(t,e,n,l,r){for(r=r&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var c=t,d=e,m=n,S=l,_=d.flags;switch(d.tag){case 0:case 11:case 15:Sl(c,d,m,S,r),Ei(8,d);break;case 23:break;case 22:var G=d.stateNode;d.memoizedState!==null?G._visibility&2?Sl(c,d,m,S,r):Ri(c,d):(G._visibility|=2,Sl(c,d,m,S,r)),r&&_&2048&&Bc(d.alternate,d);break;case 24:Sl(c,d,m,S,r),r&&_&2048&&Dc(d.alternate,d);break;default:Sl(c,d,m,S,r)}e=e.sibling}}function Ri(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,l=e,r=l.flags;switch(l.tag){case 22:Ri(n,l),r&2048&&Bc(l.alternate,l);break;case 24:Ri(n,l),r&2048&&Dc(l.alternate,l);break;default:Ri(n,l)}e=e.sibling}}var Oi=8192;function Cl(t){if(t.subtreeFlags&Oi)for(t=t.child;t!==null;)Pp(t),t=t.sibling}function Pp(t){switch(t.tag){case 26:Cl(t),t.flags&Oi&&t.memoizedState!==null&&lv(dn,t.memoizedState,t.memoizedProps);break;case 5:Cl(t);break;case 3:case 4:var e=dn;dn=ho(t.stateNode.containerInfo),Cl(t),dn=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Oi,Oi=16777216,Cl(t),Oi=e):Cl(t));break;default:Cl(t)}}function Fp(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Mi(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];se=l,tm(l,t)}Fp(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Ip(t),t=t.sibling}function Ip(t){switch(t.tag){case 0:case 11:case 15:Mi(t),t.flags&2048&&oa(9,t,t.return);break;case 3:Mi(t);break;case 12:Mi(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,no(t)):Mi(t);break;default:Mi(t)}}function no(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];se=l,tm(l,t)}Fp(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:oa(8,e,e.return),no(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,no(e));break;default:no(e)}t=t.sibling}}function tm(t,e){for(;se!==null;){var n=se;switch(n.tag){case 0:case 11:case 15:oa(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:si(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,se=l;else t:for(n=t;se!==null;){l=se;var r=l.sibling,c=l.return;if(Xp(l),l===n){se=null;break t}if(r!==null){r.return=c,se=r;break t}se=c}}}var Sy={getCacheForType:function(t){var e=Te(ie),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},Cy=typeof WeakMap=="function"?WeakMap:Map,Dt=0,qt=null,Ct=null,Et=0,Nt=0,Ve=null,sa=!1,Tl=!1,Nc=!1,Vn=0,Jt=0,fa=0,ka=0,$c=0,an=0,xl=0,zi=null,$e=null,wc=!1,Uc=0,ao=1/0,lo=null,da=null,pe=0,pa=null,El=null,Al=0,jc=0,Hc=null,em=null,_i=0,Gc=null;function Xe(){if((Dt&2)!==0&&Et!==0)return Et&-Et;if($.T!==null){var t=dl;return t!==0?t:Qc()}return yf()}function nm(){an===0&&(an=(Et&536870912)===0||Rt?pf():536870912);var t=nn.current;return t!==null&&(t.flags|=32),an}function Qe(t,e,n){(t===qt&&(Nt===2||Nt===9)||t.cancelPendingCommit!==null)&&(Rl(t,0),ma(t,Et,an,!1)),Kl(t,n),((Dt&2)===0||t!==qt)&&(t===qt&&((Dt&2)===0&&(ka|=n),Jt===4&&ma(t,Et,an,!1)),Tn(t))}function am(t,e,n){if((Dt&6)!==0)throw Error(u(327));var l=!n&&(e&124)===0&&(e&t.expiredLanes)===0||Zl(t,e),r=l?Ey(t,e):qc(t,e,!0),c=l;do{if(r===0){Tl&&!l&&ma(t,e,0,!1);break}else{if(n=t.current.alternate,c&&!Ty(n)){r=qc(t,e,!1),c=!1;continue}if(r===2){if(c=e,t.errorRecoveryDisabledLanes&c)var d=0;else d=t.pendingLanes&-536870913,d=d!==0?d:d&536870912?536870912:0;if(d!==0){e=d;t:{var m=t;r=zi;var S=m.current.memoizedState.isDehydrated;if(S&&(Rl(m,d).flags|=256),d=qc(m,d,!1),d!==2){if(Nc&&!S){m.errorRecoveryDisabledLanes|=c,ka|=c,r=4;break t}c=$e,$e=r,c!==null&&($e===null?$e=c:$e.push.apply($e,c))}r=d}if(c=!1,r!==2)continue}}if(r===1){Rl(t,0),ma(t,e,0,!0);break}t:{switch(l=t,c=r,c){case 0:case 1:throw Error(u(345));case 4:if((e&4194048)!==e)break;case 6:ma(l,e,an,!sa);break t;case 2:$e=null;break;case 3:case 5:break;default:throw Error(u(329))}if((e&62914560)===e&&(r=Uc+300-oe(),10<r)){if(ma(l,e,an,!sa),hr(l,0,!0)!==0)break t;l.timeoutHandle=Dm(lm.bind(null,l,n,$e,lo,wc,e,an,ka,xl,sa,c,2,-0,0),r);break t}lm(l,n,$e,lo,wc,e,an,ka,xl,sa,c,0,-0,0)}}break}while(!0);Tn(t)}function lm(t,e,n,l,r,c,d,m,S,_,G,Y,D,N){if(t.timeoutHandle=-1,Y=e.subtreeFlags,(Y&8192||(Y&16785408)===16785408)&&(ji={stylesheets:null,count:0,unsuspend:av},Pp(e),Y=iv(),Y!==null)){t.cancelPendingCommit=Y(fm.bind(null,t,e,c,n,l,r,d,m,S,G,1,D,N)),ma(t,c,d,!_);return}fm(t,e,c,n,l,r,d,m,S)}function Ty(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var r=n[l],c=r.getSnapshot;r=r.value;try{if(!Le(c(),r))return!1}catch{return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function ma(t,e,n,l){e&=~$c,e&=~ka,t.suspendedLanes|=e,t.pingedLanes&=~e,l&&(t.warmLanes|=e),l=t.expirationTimes;for(var r=e;0<r;){var c=31-ve(r),d=1<<c;l[c]=-1,r&=~d}n!==0&&hf(t,n,e)}function io(){return(Dt&6)===0?(Bi(0),!1):!0}function Lc(){if(Ct!==null){if(Nt===0)var t=Ct.return;else t=Ct,Un=wa=null,ac(t),vl=null,Ci=0,t=Ct;for(;t!==null;)jp(t.alternate,t),t=t.return;Ct=null}}function Rl(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,Ly(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),Lc(),qt=t,Ct=n=Nn(t.current,null),Et=e,Nt=0,Ve=null,sa=!1,Tl=Zl(t,e),Nc=!1,xl=an=$c=ka=fa=Jt=0,$e=zi=null,wc=!1,(e&8)!==0&&(e|=e&32);var l=t.entangledLanes;if(l!==0)for(t=t.entanglements,l&=e;0<l;){var r=31-ve(l),c=1<<r;e|=t[r],l&=~c}return Vn=e,Or(),n}function im(t,e){vt=null,$.H=Qr,e===di||e===Ur?(e=Td(),Nt=3):e===bd?(e=Td(),Nt=4):Nt=e===xp?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,Ve=e,Ct===null&&(Jt=1,Pr(t,Fe(e,t.current)))}function rm(){var t=$.H;return $.H=Qr,t===null?Qr:t}function om(){var t=$.A;return $.A=Sy,t}function kc(){Jt=4,sa||(Et&4194048)!==Et&&nn.current!==null||(Tl=!0),(fa&134217727)===0&&(ka&134217727)===0||qt===null||ma(qt,Et,an,!1)}function qc(t,e,n){var l=Dt;Dt|=2;var r=rm(),c=om();(qt!==t||Et!==e)&&(lo=null,Rl(t,e)),e=!1;var d=Jt;t:do try{if(Nt!==0&&Ct!==null){var m=Ct,S=Ve;switch(Nt){case 8:Lc(),d=6;break t;case 3:case 2:case 9:case 6:nn.current===null&&(e=!0);var _=Nt;if(Nt=0,Ve=null,Ol(t,m,S,_),n&&Tl){d=0;break t}break;default:_=Nt,Nt=0,Ve=null,Ol(t,m,S,_)}}xy(),d=Jt;break}catch(G){im(t,G)}while(!0);return e&&t.shellSuspendCounter++,Un=wa=null,Dt=l,$.H=r,$.A=c,Ct===null&&(qt=null,Et=0,Or()),d}function xy(){for(;Ct!==null;)um(Ct)}function Ey(t,e){var n=Dt;Dt|=2;var l=rm(),r=om();qt!==t||Et!==e?(lo=null,ao=oe()+500,Rl(t,e)):Tl=Zl(t,e);t:do try{if(Nt!==0&&Ct!==null){e=Ct;var c=Ve;e:switch(Nt){case 1:Nt=0,Ve=null,Ol(t,e,c,1);break;case 2:case 9:if(Sd(c)){Nt=0,Ve=null,cm(e);break}e=function(){Nt!==2&&Nt!==9||qt!==t||(Nt=7),Tn(t)},c.then(e,e);break t;case 3:Nt=7;break t;case 4:Nt=5;break t;case 7:Sd(c)?(Nt=0,Ve=null,cm(e)):(Nt=0,Ve=null,Ol(t,e,c,7));break;case 5:var d=null;switch(Ct.tag){case 26:d=Ct.memoizedState;case 5:case 27:var m=Ct;if(!d||Vm(d)){Nt=0,Ve=null;var S=m.sibling;if(S!==null)Ct=S;else{var _=m.return;_!==null?(Ct=_,ro(_)):Ct=null}break e}}Nt=0,Ve=null,Ol(t,e,c,5);break;case 6:Nt=0,Ve=null,Ol(t,e,c,6);break;case 8:Lc(),Jt=6;break t;default:throw Error(u(462))}}Ay();break}catch(G){im(t,G)}while(!0);return Un=wa=null,$.H=l,$.A=r,Dt=n,Ct!==null?0:(qt=null,Et=0,Or(),Jt)}function Ay(){for(;Ct!==null&&!Za();)um(Ct)}function um(t){var e=wp(t.alternate,t,Vn);t.memoizedProps=t.pendingProps,e===null?ro(t):Ct=e}function cm(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=zp(n,e,e.pendingProps,e.type,void 0,Et);break;case 11:e=zp(n,e,e.pendingProps,e.type.render,e.ref,Et);break;case 5:ac(e);default:jp(n,e),e=Ct=sd(e,Vn),e=wp(n,e,Vn)}t.memoizedProps=t.pendingProps,e===null?ro(t):Ct=e}function Ol(t,e,n,l){Un=wa=null,ac(e),vl=null,Ci=0;var r=e.return;try{if(my(t,r,e,n,Et)){Jt=1,Pr(t,Fe(n,t.current)),Ct=null;return}}catch(c){if(r!==null)throw Ct=r,c;Jt=1,Pr(t,Fe(n,t.current)),Ct=null;return}e.flags&32768?(Rt||l===1?t=!0:Tl||(Et&536870912)!==0?t=!1:(sa=t=!0,(l===2||l===9||l===3||l===6)&&(l=nn.current,l!==null&&l.tag===13&&(l.flags|=16384))),sm(e,t)):ro(e)}function ro(t){var e=t;do{if((e.flags&32768)!==0){sm(e,sa);return}t=e.return;var n=gy(e.alternate,e,Vn);if(n!==null){Ct=n;return}if(e=e.sibling,e!==null){Ct=e;return}Ct=e=t}while(e!==null);Jt===0&&(Jt=5)}function sm(t,e){do{var n=yy(t.alternate,t);if(n!==null){n.flags&=32767,Ct=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){Ct=t;return}Ct=t=n}while(t!==null);Jt=6,Ct=null}function fm(t,e,n,l,r,c,d,m,S){t.cancelPendingCommit=null;do oo();while(pe!==0);if((Dt&6)!==0)throw Error(u(327));if(e!==null){if(e===t.current)throw Error(u(177));if(c=e.lanes|e.childLanes,c|=Du,ag(t,n,c,d,m,S),t===qt&&(Ct=qt=null,Et=0),El=e,pa=t,Al=n,jc=c,Hc=r,em=l,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,zy(Je,function(){return gm(),null})):(t.callbackNode=null,t.callbackPriority=0),l=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||l){l=$.T,$.T=null,r=K.p,K.p=2,d=Dt,Dt|=4;try{vy(t,e,n)}finally{Dt=d,K.p=r,$.T=l}}pe=1,dm(),pm(),mm()}}function dm(){if(pe===1){pe=0;var t=pa,e=El,n=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||n){n=$.T,$.T=null;var l=K.p;K.p=2;var r=Dt;Dt|=4;try{Kp(e,t);var c=ts,d=td(t.containerInfo),m=c.focusedElem,S=c.selectionRange;if(d!==m&&m&&m.ownerDocument&&If(m.ownerDocument.documentElement,m)){if(S!==null&&Ou(m)){var _=S.start,G=S.end;if(G===void 0&&(G=_),"selectionStart"in m)m.selectionStart=_,m.selectionEnd=Math.min(G,m.value.length);else{var Y=m.ownerDocument||document,D=Y&&Y.defaultView||window;if(D.getSelection){var N=D.getSelection(),pt=m.textContent.length,ct=Math.min(S.start,pt),Ut=S.end===void 0?ct:Math.min(S.end,pt);!N.extend&&ct>Ut&&(d=Ut,Ut=ct,ct=d);var O=Ff(m,ct),A=Ff(m,Ut);if(O&&A&&(N.rangeCount!==1||N.anchorNode!==O.node||N.anchorOffset!==O.offset||N.focusNode!==A.node||N.focusOffset!==A.offset)){var z=Y.createRange();z.setStart(O.node,O.offset),N.removeAllRanges(),ct>Ut?(N.addRange(z),N.extend(A.node,A.offset)):(z.setEnd(A.node,A.offset),N.addRange(z))}}}}for(Y=[],N=m;N=N.parentNode;)N.nodeType===1&&Y.push({element:N,left:N.scrollLeft,top:N.scrollTop});for(typeof m.focus=="function"&&m.focus(),m=0;m<Y.length;m++){var q=Y[m];q.element.scrollLeft=q.left,q.element.scrollTop=q.top}}So=!!Ic,ts=Ic=null}finally{Dt=r,K.p=l,$.T=n}}t.current=e,pe=2}}function pm(){if(pe===2){pe=0;var t=pa,e=El,n=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||n){n=$.T,$.T=null;var l=K.p;K.p=2;var r=Dt;Dt|=4;try{Vp(t,e.alternate,e)}finally{Dt=r,K.p=l,$.T=n}}pe=3}}function mm(){if(pe===4||pe===3){pe=0,Ql();var t=pa,e=El,n=Al,l=em;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?pe=5:(pe=0,El=pa=null,hm(t,t.pendingLanes));var r=t.pendingLanes;if(r===0&&(da=null),ru(n),e=e.stateNode,ye&&typeof ye.onCommitFiberRoot=="function")try{ye.onCommitFiberRoot(Pn,e,void 0,(e.current.flags&128)===128)}catch{}if(l!==null){e=$.T,r=K.p,K.p=2,$.T=null;try{for(var c=t.onRecoverableError,d=0;d<l.length;d++){var m=l[d];c(m.value,{componentStack:m.stack})}}finally{$.T=e,K.p=r}}(Al&3)!==0&&oo(),Tn(t),r=t.pendingLanes,(n&4194090)!==0&&(r&42)!==0?t===Gc?_i++:(_i=0,Gc=t):_i=0,Bi(0)}}function hm(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,si(e)))}function oo(t){return dm(),pm(),mm(),gm()}function gm(){if(pe!==5)return!1;var t=pa,e=jc;jc=0;var n=ru(Al),l=$.T,r=K.p;try{K.p=32>n?32:n,$.T=null,n=Hc,Hc=null;var c=pa,d=Al;if(pe=0,El=pa=null,Al=0,(Dt&6)!==0)throw Error(u(331));var m=Dt;if(Dt|=4,Ip(c.current),Wp(c,c.current,d,n),Dt=m,Bi(0,!1),ye&&typeof ye.onPostCommitFiberRoot=="function")try{ye.onPostCommitFiberRoot(Pn,c)}catch{}return!0}finally{K.p=r,$.T=l,hm(t,e)}}function ym(t,e,n){e=Fe(n,e),e=yc(t.stateNode,e,2),t=aa(t,e,2),t!==null&&(Kl(t,2),Tn(t))}function Ht(t,e,n){if(t.tag===3)ym(t,t,n);else for(;e!==null;){if(e.tag===3){ym(e,t,n);break}else if(e.tag===1){var l=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(da===null||!da.has(l))){t=Fe(n,t),n=Cp(2),l=aa(e,n,2),l!==null&&(Tp(n,l,e,t),Kl(l,2),Tn(l));break}}e=e.return}}function Yc(t,e,n){var l=t.pingCache;if(l===null){l=t.pingCache=new Cy;var r=new Set;l.set(e,r)}else r=l.get(e),r===void 0&&(r=new Set,l.set(e,r));r.has(n)||(Nc=!0,r.add(n),t=Ry.bind(null,t,e,n),e.then(t,t))}function Ry(t,e,n){var l=t.pingCache;l!==null&&l.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,qt===t&&(Et&n)===n&&(Jt===4||Jt===3&&(Et&62914560)===Et&&300>oe()-Uc?(Dt&2)===0&&Rl(t,0):$c|=n,xl===Et&&(xl=0)),Tn(t)}function vm(t,e){e===0&&(e=mf()),t=ul(t,e),t!==null&&(Kl(t,e),Tn(t))}function Oy(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),vm(t,n)}function My(t,e){var n=0;switch(t.tag){case 13:var l=t.stateNode,r=t.memoizedState;r!==null&&(n=r.retryLane);break;case 19:l=t.stateNode;break;case 22:l=t.stateNode._retryCache;break;default:throw Error(u(314))}l!==null&&l.delete(e),vm(t,n)}function zy(t,e){return Wn(t,e)}var uo=null,Ml=null,Vc=!1,co=!1,Xc=!1,qa=0;function Tn(t){t!==Ml&&t.next===null&&(Ml===null?uo=Ml=t:Ml=Ml.next=t),co=!0,Vc||(Vc=!0,By())}function Bi(t,e){if(!Xc&&co){Xc=!0;do for(var n=!1,l=uo;l!==null;){if(t!==0){var r=l.pendingLanes;if(r===0)var c=0;else{var d=l.suspendedLanes,m=l.pingedLanes;c=(1<<31-ve(42|t)+1)-1,c&=r&~(d&~m),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(n=!0,Tm(l,c))}else c=Et,c=hr(l,l===qt?c:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(c&3)===0||Zl(l,c)||(n=!0,Tm(l,c));l=l.next}while(n);Xc=!1}}function _y(){bm()}function bm(){co=Vc=!1;var t=0;qa!==0&&(Gy()&&(t=qa),qa=0);for(var e=oe(),n=null,l=uo;l!==null;){var r=l.next,c=Sm(l,e);c===0?(l.next=null,n===null?uo=r:n.next=r,r===null&&(Ml=n)):(n=l,(t!==0||(c&3)!==0)&&(co=!0)),l=r}Bi(t)}function Sm(t,e){for(var n=t.suspendedLanes,l=t.pingedLanes,r=t.expirationTimes,c=t.pendingLanes&-62914561;0<c;){var d=31-ve(c),m=1<<d,S=r[d];S===-1?((m&n)===0||(m&l)!==0)&&(r[d]=ng(m,e)):S<=e&&(t.expiredLanes|=m),c&=~m}if(e=qt,n=Et,n=hr(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l=t.callbackNode,n===0||t===e&&(Nt===2||Nt===9)||t.cancelPendingCommit!==null)return l!==null&&l!==null&&gn(l),t.callbackNode=null,t.callbackPriority=0;if((n&3)===0||Zl(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(l!==null&&gn(l),ru(n)){case 2:case 8:n=yn;break;case 32:n=Je;break;case 268435456:n=dr;break;default:n=Je}return l=Cm.bind(null,t),n=Wn(n,l),t.callbackPriority=e,t.callbackNode=n,e}return l!==null&&l!==null&&gn(l),t.callbackPriority=2,t.callbackNode=null,2}function Cm(t,e){if(pe!==0&&pe!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(oo()&&t.callbackNode!==n)return null;var l=Et;return l=hr(t,t===qt?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l===0?null:(am(t,l,e),Sm(t,oe()),t.callbackNode!=null&&t.callbackNode===n?Cm.bind(null,t):null)}function Tm(t,e){if(oo())return null;am(t,e,!0)}function By(){ky(function(){(Dt&6)!==0?Wn(ge,_y):bm()})}function Qc(){return qa===0&&(qa=pf()),qa}function xm(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Sr(""+t)}function Em(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function Dy(t,e,n,l,r){if(e==="submit"&&n&&n.stateNode===r){var c=xm((r[_e]||null).action),d=l.submitter;d&&(e=(e=d[_e]||null)?xm(e.formAction):d.getAttribute("formAction"),e!==null&&(c=e,d=null));var m=new Er("action","action",null,l,r);t.push({event:m,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(qa!==0){var S=d?Em(r,d):new FormData(r);dc(n,{pending:!0,data:S,method:r.method,action:c},null,S)}}else typeof c=="function"&&(m.preventDefault(),S=d?Em(r,d):new FormData(r),dc(n,{pending:!0,data:S,method:r.method,action:c},c,S))},currentTarget:r}]})}}for(var Zc=0;Zc<Bu.length;Zc++){var Kc=Bu[Zc],Ny=Kc.toLowerCase(),$y=Kc[0].toUpperCase()+Kc.slice(1);fn(Ny,"on"+$y)}fn(ad,"onAnimationEnd"),fn(ld,"onAnimationIteration"),fn(id,"onAnimationStart"),fn("dblclick","onDoubleClick"),fn("focusin","onFocus"),fn("focusout","onBlur"),fn(Pg,"onTransitionRun"),fn(Fg,"onTransitionStart"),fn(Ig,"onTransitionCancel"),fn(rd,"onTransitionEnd"),Fa("onMouseEnter",["mouseout","mouseover"]),Fa("onMouseLeave",["mouseout","mouseover"]),Fa("onPointerEnter",["pointerout","pointerover"]),Fa("onPointerLeave",["pointerout","pointerover"]),Ra("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ra("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ra("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ra("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ra("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ra("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Di="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),wy=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Di));function Am(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var l=t[n],r=l.event;l=l.listeners;t:{var c=void 0;if(e)for(var d=l.length-1;0<=d;d--){var m=l[d],S=m.instance,_=m.currentTarget;if(m=m.listener,S!==c&&r.isPropagationStopped())break t;c=m,r.currentTarget=_;try{c(r)}catch(G){Wr(G)}r.currentTarget=null,c=S}else for(d=0;d<l.length;d++){if(m=l[d],S=m.instance,_=m.currentTarget,m=m.listener,S!==c&&r.isPropagationStopped())break t;c=m,r.currentTarget=_;try{c(r)}catch(G){Wr(G)}r.currentTarget=null,c=S}}}}function Tt(t,e){var n=e[ou];n===void 0&&(n=e[ou]=new Set);var l=t+"__bubble";n.has(l)||(Rm(e,t,2,!1),n.add(l))}function Jc(t,e,n){var l=0;e&&(l|=4),Rm(n,t,l,e)}var so="_reactListening"+Math.random().toString(36).slice(2);function Wc(t){if(!t[so]){t[so]=!0,bf.forEach(function(n){n!=="selectionchange"&&(wy.has(n)||Jc(n,!1,t),Jc(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[so]||(e[so]=!0,Jc("selectionchange",!1,e))}}function Rm(t,e,n,l){switch(Wm(e)){case 2:var r=uv;break;case 8:r=cv;break;default:r=ss}n=r.bind(null,e,n,t),r=void 0,!vu||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(r=!0),l?r!==void 0?t.addEventListener(e,n,{capture:!0,passive:r}):t.addEventListener(e,n,!0):r!==void 0?t.addEventListener(e,n,{passive:r}):t.addEventListener(e,n,!1)}function Pc(t,e,n,l,r){var c=l;if((e&1)===0&&(e&2)===0&&l!==null)t:for(;;){if(l===null)return;var d=l.tag;if(d===3||d===4){var m=l.stateNode.containerInfo;if(m===r)break;if(d===4)for(d=l.return;d!==null;){var S=d.tag;if((S===3||S===4)&&d.stateNode.containerInfo===r)return;d=d.return}for(;m!==null;){if(d=Ja(m),d===null)return;if(S=d.tag,S===5||S===6||S===26||S===27){l=c=d;continue t}m=m.parentNode}}l=l.return}Nf(function(){var _=c,G=gu(n),Y=[];t:{var D=od.get(t);if(D!==void 0){var N=Er,pt=t;switch(t){case"keypress":if(Tr(n)===0)break t;case"keydown":case"keyup":N=zg;break;case"focusin":pt="focus",N=Tu;break;case"focusout":pt="blur",N=Tu;break;case"beforeblur":case"afterblur":N=Tu;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":N=Uf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":N=yg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":N=Dg;break;case ad:case ld:case id:N=Sg;break;case rd:N=$g;break;case"scroll":case"scrollend":N=hg;break;case"wheel":N=Ug;break;case"copy":case"cut":case"paste":N=Tg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":N=Hf;break;case"toggle":case"beforetoggle":N=Hg}var ct=(e&4)!==0,Ut=!ct&&(t==="scroll"||t==="scrollend"),O=ct?D!==null?D+"Capture":null:D;ct=[];for(var A=_,z;A!==null;){var q=A;if(z=q.stateNode,q=q.tag,q!==5&&q!==26&&q!==27||z===null||O===null||(q=Pl(A,O),q!=null&&ct.push(Ni(A,q,z))),Ut)break;A=A.return}0<ct.length&&(D=new N(D,pt,null,n,G),Y.push({event:D,listeners:ct}))}}if((e&7)===0){t:{if(D=t==="mouseover"||t==="pointerover",N=t==="mouseout"||t==="pointerout",D&&n!==hu&&(pt=n.relatedTarget||n.fromElement)&&(Ja(pt)||pt[Ka]))break t;if((N||D)&&(D=G.window===G?G:(D=G.ownerDocument)?D.defaultView||D.parentWindow:window,N?(pt=n.relatedTarget||n.toElement,N=_,pt=pt?Ja(pt):null,pt!==null&&(Ut=f(pt),ct=pt.tag,pt!==Ut||ct!==5&&ct!==27&&ct!==6)&&(pt=null)):(N=null,pt=_),N!==pt)){if(ct=Uf,q="onMouseLeave",O="onMouseEnter",A="mouse",(t==="pointerout"||t==="pointerover")&&(ct=Hf,q="onPointerLeave",O="onPointerEnter",A="pointer"),Ut=N==null?D:Wl(N),z=pt==null?D:Wl(pt),D=new ct(q,A+"leave",N,n,G),D.target=Ut,D.relatedTarget=z,q=null,Ja(G)===_&&(ct=new ct(O,A+"enter",pt,n,G),ct.target=z,ct.relatedTarget=Ut,q=ct),Ut=q,N&&pt)e:{for(ct=N,O=pt,A=0,z=ct;z;z=zl(z))A++;for(z=0,q=O;q;q=zl(q))z++;for(;0<A-z;)ct=zl(ct),A--;for(;0<z-A;)O=zl(O),z--;for(;A--;){if(ct===O||O!==null&&ct===O.alternate)break e;ct=zl(ct),O=zl(O)}ct=null}else ct=null;N!==null&&Om(Y,D,N,ct,!1),pt!==null&&Ut!==null&&Om(Y,Ut,pt,ct,!0)}}t:{if(D=_?Wl(_):window,N=D.nodeName&&D.nodeName.toLowerCase(),N==="select"||N==="input"&&D.type==="file")var rt=Qf;else if(Vf(D))if(Zf)rt=Kg;else{rt=Qg;var St=Xg}else N=D.nodeName,!N||N.toLowerCase()!=="input"||D.type!=="checkbox"&&D.type!=="radio"?_&&mu(_.elementType)&&(rt=Qf):rt=Zg;if(rt&&(rt=rt(t,_))){Xf(Y,rt,n,G);break t}St&&St(t,D,_),t==="focusout"&&_&&D.type==="number"&&_.memoizedProps.value!=null&&pu(D,"number",D.value)}switch(St=_?Wl(_):window,t){case"focusin":(Vf(St)||St.contentEditable==="true")&&(il=St,Mu=_,ii=null);break;case"focusout":ii=Mu=il=null;break;case"mousedown":zu=!0;break;case"contextmenu":case"mouseup":case"dragend":zu=!1,ed(Y,n,G);break;case"selectionchange":if(Wg)break;case"keydown":case"keyup":ed(Y,n,G)}var ot;if(Eu)t:{switch(t){case"compositionstart":var st="onCompositionStart";break t;case"compositionend":st="onCompositionEnd";break t;case"compositionupdate":st="onCompositionUpdate";break t}st=void 0}else ll?qf(t,n)&&(st="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(st="onCompositionStart");st&&(Gf&&n.locale!=="ko"&&(ll||st!=="onCompositionStart"?st==="onCompositionEnd"&&ll&&(ot=$f()):(In=G,bu="value"in In?In.value:In.textContent,ll=!0)),St=fo(_,st),0<St.length&&(st=new jf(st,t,null,n,G),Y.push({event:st,listeners:St}),ot?st.data=ot:(ot=Yf(n),ot!==null&&(st.data=ot)))),(ot=Lg?kg(t,n):qg(t,n))&&(st=fo(_,"onBeforeInput"),0<st.length&&(St=new jf("onBeforeInput","beforeinput",null,n,G),Y.push({event:St,listeners:st}),St.data=ot)),Dy(Y,t,_,n,G)}Am(Y,e)})}function Ni(t,e,n){return{instance:t,listener:e,currentTarget:n}}function fo(t,e){for(var n=e+"Capture",l=[];t!==null;){var r=t,c=r.stateNode;if(r=r.tag,r!==5&&r!==26&&r!==27||c===null||(r=Pl(t,n),r!=null&&l.unshift(Ni(t,r,c)),r=Pl(t,e),r!=null&&l.push(Ni(t,r,c))),t.tag===3)return l;t=t.return}return[]}function zl(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Om(t,e,n,l,r){for(var c=e._reactName,d=[];n!==null&&n!==l;){var m=n,S=m.alternate,_=m.stateNode;if(m=m.tag,S!==null&&S===l)break;m!==5&&m!==26&&m!==27||_===null||(S=_,r?(_=Pl(n,c),_!=null&&d.unshift(Ni(n,_,S))):r||(_=Pl(n,c),_!=null&&d.push(Ni(n,_,S)))),n=n.return}d.length!==0&&t.push({event:e,listeners:d})}var Uy=/\r\n?/g,jy=/\u0000|\uFFFD/g;function Mm(t){return(typeof t=="string"?t:""+t).replace(Uy,`
`).replace(jy,"")}function zm(t,e){return e=Mm(e),Mm(t)===e}function po(){}function wt(t,e,n,l,r,c){switch(n){case"children":typeof l=="string"?e==="body"||e==="textarea"&&l===""||el(t,l):(typeof l=="number"||typeof l=="bigint")&&e!=="body"&&el(t,""+l);break;case"className":yr(t,"class",l);break;case"tabIndex":yr(t,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":yr(t,n,l);break;case"style":Bf(t,l,c);break;case"data":if(e!=="object"){yr(t,"data",l);break}case"src":case"href":if(l===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=Sr(""+l),t.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(n==="formAction"?(e!=="input"&&wt(t,e,"name",r.name,r,null),wt(t,e,"formEncType",r.formEncType,r,null),wt(t,e,"formMethod",r.formMethod,r,null),wt(t,e,"formTarget",r.formTarget,r,null)):(wt(t,e,"encType",r.encType,r,null),wt(t,e,"method",r.method,r,null),wt(t,e,"target",r.target,r,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=Sr(""+l),t.setAttribute(n,l);break;case"onClick":l!=null&&(t.onclick=po);break;case"onScroll":l!=null&&Tt("scroll",t);break;case"onScrollEnd":l!=null&&Tt("scrollend",t);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(u(61));if(n=l.__html,n!=null){if(r.children!=null)throw Error(u(60));t.innerHTML=n}}break;case"multiple":t.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":t.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){t.removeAttribute("xlink:href");break}n=Sr(""+l),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""+l):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":l===!0?t.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,l):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?t.setAttribute(n,l):t.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?t.removeAttribute(n):t.setAttribute(n,l);break;case"popover":Tt("beforetoggle",t),Tt("toggle",t),gr(t,"popover",l);break;case"xlinkActuate":Bn(t,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Bn(t,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Bn(t,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Bn(t,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Bn(t,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Bn(t,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Bn(t,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Bn(t,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Bn(t,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":gr(t,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=pg.get(n)||n,gr(t,n,l))}}function Fc(t,e,n,l,r,c){switch(n){case"style":Bf(t,l,c);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(u(61));if(n=l.__html,n!=null){if(r.children!=null)throw Error(u(60));t.innerHTML=n}}break;case"children":typeof l=="string"?el(t,l):(typeof l=="number"||typeof l=="bigint")&&el(t,""+l);break;case"onScroll":l!=null&&Tt("scroll",t);break;case"onScrollEnd":l!=null&&Tt("scrollend",t);break;case"onClick":l!=null&&(t.onclick=po);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Sf.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(r=n.endsWith("Capture"),e=n.slice(2,r?n.length-7:void 0),c=t[_e]||null,c=c!=null?c[n]:null,typeof c=="function"&&t.removeEventListener(e,c,r),typeof l=="function")){typeof c!="function"&&c!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,l,r);break t}n in t?t[n]=l:l===!0?t.setAttribute(n,""):gr(t,n,l)}}}function me(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Tt("error",t),Tt("load",t);var l=!1,r=!1,c;for(c in n)if(n.hasOwnProperty(c)){var d=n[c];if(d!=null)switch(c){case"src":l=!0;break;case"srcSet":r=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(u(137,e));default:wt(t,e,c,d,n,null)}}r&&wt(t,e,"srcSet",n.srcSet,n,null),l&&wt(t,e,"src",n.src,n,null);return;case"input":Tt("invalid",t);var m=c=d=r=null,S=null,_=null;for(l in n)if(n.hasOwnProperty(l)){var G=n[l];if(G!=null)switch(l){case"name":r=G;break;case"type":d=G;break;case"checked":S=G;break;case"defaultChecked":_=G;break;case"value":c=G;break;case"defaultValue":m=G;break;case"children":case"dangerouslySetInnerHTML":if(G!=null)throw Error(u(137,e));break;default:wt(t,e,l,G,n,null)}}Of(t,c,m,S,_,d,r,!1),vr(t);return;case"select":Tt("invalid",t),l=d=c=null;for(r in n)if(n.hasOwnProperty(r)&&(m=n[r],m!=null))switch(r){case"value":c=m;break;case"defaultValue":d=m;break;case"multiple":l=m;default:wt(t,e,r,m,n,null)}e=c,n=d,t.multiple=!!l,e!=null?tl(t,!!l,e,!1):n!=null&&tl(t,!!l,n,!0);return;case"textarea":Tt("invalid",t),c=r=l=null;for(d in n)if(n.hasOwnProperty(d)&&(m=n[d],m!=null))switch(d){case"value":l=m;break;case"defaultValue":r=m;break;case"children":c=m;break;case"dangerouslySetInnerHTML":if(m!=null)throw Error(u(91));break;default:wt(t,e,d,m,n,null)}zf(t,l,r,c),vr(t);return;case"option":for(S in n)if(n.hasOwnProperty(S)&&(l=n[S],l!=null))switch(S){case"selected":t.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:wt(t,e,S,l,n,null)}return;case"dialog":Tt("beforetoggle",t),Tt("toggle",t),Tt("cancel",t),Tt("close",t);break;case"iframe":case"object":Tt("load",t);break;case"video":case"audio":for(l=0;l<Di.length;l++)Tt(Di[l],t);break;case"image":Tt("error",t),Tt("load",t);break;case"details":Tt("toggle",t);break;case"embed":case"source":case"link":Tt("error",t),Tt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(_ in n)if(n.hasOwnProperty(_)&&(l=n[_],l!=null))switch(_){case"children":case"dangerouslySetInnerHTML":throw Error(u(137,e));default:wt(t,e,_,l,n,null)}return;default:if(mu(e)){for(G in n)n.hasOwnProperty(G)&&(l=n[G],l!==void 0&&Fc(t,e,G,l,n,void 0));return}}for(m in n)n.hasOwnProperty(m)&&(l=n[m],l!=null&&wt(t,e,m,l,n,null))}function Hy(t,e,n,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var r=null,c=null,d=null,m=null,S=null,_=null,G=null;for(N in n){var Y=n[N];if(n.hasOwnProperty(N)&&Y!=null)switch(N){case"checked":break;case"value":break;case"defaultValue":S=Y;default:l.hasOwnProperty(N)||wt(t,e,N,null,l,Y)}}for(var D in l){var N=l[D];if(Y=n[D],l.hasOwnProperty(D)&&(N!=null||Y!=null))switch(D){case"type":c=N;break;case"name":r=N;break;case"checked":_=N;break;case"defaultChecked":G=N;break;case"value":d=N;break;case"defaultValue":m=N;break;case"children":case"dangerouslySetInnerHTML":if(N!=null)throw Error(u(137,e));break;default:N!==Y&&wt(t,e,D,N,l,Y)}}du(t,d,m,S,_,G,c,r);return;case"select":N=d=m=D=null;for(c in n)if(S=n[c],n.hasOwnProperty(c)&&S!=null)switch(c){case"value":break;case"multiple":N=S;default:l.hasOwnProperty(c)||wt(t,e,c,null,l,S)}for(r in l)if(c=l[r],S=n[r],l.hasOwnProperty(r)&&(c!=null||S!=null))switch(r){case"value":D=c;break;case"defaultValue":m=c;break;case"multiple":d=c;default:c!==S&&wt(t,e,r,c,l,S)}e=m,n=d,l=N,D!=null?tl(t,!!n,D,!1):!!l!=!!n&&(e!=null?tl(t,!!n,e,!0):tl(t,!!n,n?[]:"",!1));return;case"textarea":N=D=null;for(m in n)if(r=n[m],n.hasOwnProperty(m)&&r!=null&&!l.hasOwnProperty(m))switch(m){case"value":break;case"children":break;default:wt(t,e,m,null,l,r)}for(d in l)if(r=l[d],c=n[d],l.hasOwnProperty(d)&&(r!=null||c!=null))switch(d){case"value":D=r;break;case"defaultValue":N=r;break;case"children":break;case"dangerouslySetInnerHTML":if(r!=null)throw Error(u(91));break;default:r!==c&&wt(t,e,d,r,l,c)}Mf(t,D,N);return;case"option":for(var pt in n)if(D=n[pt],n.hasOwnProperty(pt)&&D!=null&&!l.hasOwnProperty(pt))switch(pt){case"selected":t.selected=!1;break;default:wt(t,e,pt,null,l,D)}for(S in l)if(D=l[S],N=n[S],l.hasOwnProperty(S)&&D!==N&&(D!=null||N!=null))switch(S){case"selected":t.selected=D&&typeof D!="function"&&typeof D!="symbol";break;default:wt(t,e,S,D,l,N)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ct in n)D=n[ct],n.hasOwnProperty(ct)&&D!=null&&!l.hasOwnProperty(ct)&&wt(t,e,ct,null,l,D);for(_ in l)if(D=l[_],N=n[_],l.hasOwnProperty(_)&&D!==N&&(D!=null||N!=null))switch(_){case"children":case"dangerouslySetInnerHTML":if(D!=null)throw Error(u(137,e));break;default:wt(t,e,_,D,l,N)}return;default:if(mu(e)){for(var Ut in n)D=n[Ut],n.hasOwnProperty(Ut)&&D!==void 0&&!l.hasOwnProperty(Ut)&&Fc(t,e,Ut,void 0,l,D);for(G in l)D=l[G],N=n[G],!l.hasOwnProperty(G)||D===N||D===void 0&&N===void 0||Fc(t,e,G,D,l,N);return}}for(var O in n)D=n[O],n.hasOwnProperty(O)&&D!=null&&!l.hasOwnProperty(O)&&wt(t,e,O,null,l,D);for(Y in l)D=l[Y],N=n[Y],!l.hasOwnProperty(Y)||D===N||D==null&&N==null||wt(t,e,Y,D,l,N)}var Ic=null,ts=null;function mo(t){return t.nodeType===9?t:t.ownerDocument}function _m(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Bm(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function es(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var ns=null;function Gy(){var t=window.event;return t&&t.type==="popstate"?t===ns?!1:(ns=t,!0):(ns=null,!1)}var Dm=typeof setTimeout=="function"?setTimeout:void 0,Ly=typeof clearTimeout=="function"?clearTimeout:void 0,Nm=typeof Promise=="function"?Promise:void 0,ky=typeof queueMicrotask=="function"?queueMicrotask:typeof Nm<"u"?function(t){return Nm.resolve(null).then(t).catch(qy)}:Dm;function qy(t){setTimeout(function(){throw t})}function ha(t){return t==="head"}function $m(t,e){var n=e,l=0,r=0;do{var c=n.nextSibling;if(t.removeChild(n),c&&c.nodeType===8)if(n=c.data,n==="/$"){if(0<l&&8>l){n=l;var d=t.ownerDocument;if(n&1&&$i(d.documentElement),n&2&&$i(d.body),n&4)for(n=d.head,$i(n),d=n.firstChild;d;){var m=d.nextSibling,S=d.nodeName;d[Jl]||S==="SCRIPT"||S==="STYLE"||S==="LINK"&&d.rel.toLowerCase()==="stylesheet"||n.removeChild(d),d=m}}if(r===0){t.removeChild(c),qi(e);return}r--}else n==="$"||n==="$?"||n==="$!"?r++:l=n.charCodeAt(0)-48;else l=0;n=c}while(n);qi(e)}function as(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":as(n),uu(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function Yy(t,e,n,l){for(;t.nodeType===1;){var r=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!l&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(l){if(!t[Jl])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(c=t.getAttribute("rel"),c==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(c!==r.rel||t.getAttribute("href")!==(r.href==null||r.href===""?null:r.href)||t.getAttribute("crossorigin")!==(r.crossOrigin==null?null:r.crossOrigin)||t.getAttribute("title")!==(r.title==null?null:r.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(c=t.getAttribute("src"),(c!==(r.src==null?null:r.src)||t.getAttribute("type")!==(r.type==null?null:r.type)||t.getAttribute("crossorigin")!==(r.crossOrigin==null?null:r.crossOrigin))&&c&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var c=r.name==null?null:""+r.name;if(r.type==="hidden"&&t.getAttribute("name")===c)return t}else return t;if(t=pn(t.nextSibling),t===null)break}return null}function Vy(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=pn(t.nextSibling),t===null))return null;return t}function ls(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function Xy(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var l=function(){e(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),t._reactRetry=l}}function pn(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var is=null;function wm(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function Um(t,e,n){switch(e=mo(n),t){case"html":if(t=e.documentElement,!t)throw Error(u(452));return t;case"head":if(t=e.head,!t)throw Error(u(453));return t;case"body":if(t=e.body,!t)throw Error(u(454));return t;default:throw Error(u(451))}}function $i(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);uu(t)}var ln=new Map,jm=new Set;function ho(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Xn=K.d;K.d={f:Qy,r:Zy,D:Ky,C:Jy,L:Wy,m:Py,X:Iy,S:Fy,M:tv};function Qy(){var t=Xn.f(),e=io();return t||e}function Zy(t){var e=Wa(t);e!==null&&e.tag===5&&e.type==="form"?ap(e):Xn.r(t)}var _l=typeof document>"u"?null:document;function Hm(t,e,n){var l=_l;if(l&&typeof e=="string"&&e){var r=Pe(e);r='link[rel="'+t+'"][href="'+r+'"]',typeof n=="string"&&(r+='[crossorigin="'+n+'"]'),jm.has(r)||(jm.add(r),t={rel:t,crossOrigin:n,href:e},l.querySelector(r)===null&&(e=l.createElement("link"),me(e,"link",t),ue(e),l.head.appendChild(e)))}}function Ky(t){Xn.D(t),Hm("dns-prefetch",t,null)}function Jy(t,e){Xn.C(t,e),Hm("preconnect",t,e)}function Wy(t,e,n){Xn.L(t,e,n);var l=_l;if(l&&t&&e){var r='link[rel="preload"][as="'+Pe(e)+'"]';e==="image"&&n&&n.imageSrcSet?(r+='[imagesrcset="'+Pe(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(r+='[imagesizes="'+Pe(n.imageSizes)+'"]')):r+='[href="'+Pe(t)+'"]';var c=r;switch(e){case"style":c=Bl(t);break;case"script":c=Dl(t)}ln.has(c)||(t=b({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),ln.set(c,t),l.querySelector(r)!==null||e==="style"&&l.querySelector(wi(c))||e==="script"&&l.querySelector(Ui(c))||(e=l.createElement("link"),me(e,"link",t),ue(e),l.head.appendChild(e)))}}function Py(t,e){Xn.m(t,e);var n=_l;if(n&&t){var l=e&&typeof e.as=="string"?e.as:"script",r='link[rel="modulepreload"][as="'+Pe(l)+'"][href="'+Pe(t)+'"]',c=r;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=Dl(t)}if(!ln.has(c)&&(t=b({rel:"modulepreload",href:t},e),ln.set(c,t),n.querySelector(r)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Ui(c)))return}l=n.createElement("link"),me(l,"link",t),ue(l),n.head.appendChild(l)}}}function Fy(t,e,n){Xn.S(t,e,n);var l=_l;if(l&&t){var r=Pa(l).hoistableStyles,c=Bl(t);e=e||"default";var d=r.get(c);if(!d){var m={loading:0,preload:null};if(d=l.querySelector(wi(c)))m.loading=5;else{t=b({rel:"stylesheet",href:t,"data-precedence":e},n),(n=ln.get(c))&&rs(t,n);var S=d=l.createElement("link");ue(S),me(S,"link",t),S._p=new Promise(function(_,G){S.onload=_,S.onerror=G}),S.addEventListener("load",function(){m.loading|=1}),S.addEventListener("error",function(){m.loading|=2}),m.loading|=4,go(d,e,l)}d={type:"stylesheet",instance:d,count:1,state:m},r.set(c,d)}}}function Iy(t,e){Xn.X(t,e);var n=_l;if(n&&t){var l=Pa(n).hoistableScripts,r=Dl(t),c=l.get(r);c||(c=n.querySelector(Ui(r)),c||(t=b({src:t,async:!0},e),(e=ln.get(r))&&os(t,e),c=n.createElement("script"),ue(c),me(c,"link",t),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},l.set(r,c))}}function tv(t,e){Xn.M(t,e);var n=_l;if(n&&t){var l=Pa(n).hoistableScripts,r=Dl(t),c=l.get(r);c||(c=n.querySelector(Ui(r)),c||(t=b({src:t,async:!0,type:"module"},e),(e=ln.get(r))&&os(t,e),c=n.createElement("script"),ue(c),me(c,"link",t),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},l.set(r,c))}}function Gm(t,e,n,l){var r=(r=dt.current)?ho(r):null;if(!r)throw Error(u(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=Bl(n.href),n=Pa(r).hoistableStyles,l=n.get(e),l||(l={type:"style",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=Bl(n.href);var c=Pa(r).hoistableStyles,d=c.get(t);if(d||(r=r.ownerDocument||r,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(t,d),(c=r.querySelector(wi(t)))&&!c._p&&(d.instance=c,d.state.loading=5),ln.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},ln.set(t,n),c||ev(r,t,n,d.state))),e&&l===null)throw Error(u(528,""));return d}if(e&&l!==null)throw Error(u(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Dl(n),n=Pa(r).hoistableScripts,l=n.get(e),l||(l={type:"script",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(u(444,t))}}function Bl(t){return'href="'+Pe(t)+'"'}function wi(t){return'link[rel="stylesheet"]['+t+"]"}function Lm(t){return b({},t,{"data-precedence":t.precedence,precedence:null})}function ev(t,e,n,l){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?l.loading=1:(e=t.createElement("link"),l.preload=e,e.addEventListener("load",function(){return l.loading|=1}),e.addEventListener("error",function(){return l.loading|=2}),me(e,"link",n),ue(e),t.head.appendChild(e))}function Dl(t){return'[src="'+Pe(t)+'"]'}function Ui(t){return"script[async]"+t}function km(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var l=t.querySelector('style[data-href~="'+Pe(n.href)+'"]');if(l)return e.instance=l,ue(l),l;var r=b({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(t.ownerDocument||t).createElement("style"),ue(l),me(l,"style",r),go(l,n.precedence,t),e.instance=l;case"stylesheet":r=Bl(n.href);var c=t.querySelector(wi(r));if(c)return e.state.loading|=4,e.instance=c,ue(c),c;l=Lm(n),(r=ln.get(r))&&rs(l,r),c=(t.ownerDocument||t).createElement("link"),ue(c);var d=c;return d._p=new Promise(function(m,S){d.onload=m,d.onerror=S}),me(c,"link",l),e.state.loading|=4,go(c,n.precedence,t),e.instance=c;case"script":return c=Dl(n.src),(r=t.querySelector(Ui(c)))?(e.instance=r,ue(r),r):(l=n,(r=ln.get(c))&&(l=b({},n),os(l,r)),t=t.ownerDocument||t,r=t.createElement("script"),ue(r),me(r,"link",l),t.head.appendChild(r),e.instance=r);case"void":return null;default:throw Error(u(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(l=e.instance,e.state.loading|=4,go(l,n.precedence,t));return e.instance}function go(t,e,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),r=l.length?l[l.length-1]:null,c=r,d=0;d<l.length;d++){var m=l[d];if(m.dataset.precedence===e)c=m;else if(c!==r)break}c?c.parentNode.insertBefore(t,c.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function rs(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function os(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var yo=null;function qm(t,e,n){if(yo===null){var l=new Map,r=yo=new Map;r.set(n,l)}else r=yo,l=r.get(n),l||(l=new Map,r.set(n,l));if(l.has(t))return l;for(l.set(t,null),n=n.getElementsByTagName(t),r=0;r<n.length;r++){var c=n[r];if(!(c[Jl]||c[Ce]||t==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var d=c.getAttribute(e)||"";d=t+d;var m=l.get(d);m?m.push(c):l.set(d,[c])}}return l}function Ym(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function nv(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Vm(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var ji=null;function av(){}function lv(t,e,n){if(ji===null)throw Error(u(475));var l=ji;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var r=Bl(n.href),c=t.querySelector(wi(r));if(c){t=c._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(l.count++,l=vo.bind(l),t.then(l,l)),e.state.loading|=4,e.instance=c,ue(c);return}c=t.ownerDocument||t,n=Lm(n),(r=ln.get(r))&&rs(n,r),c=c.createElement("link"),ue(c);var d=c;d._p=new Promise(function(m,S){d.onload=m,d.onerror=S}),me(c,"link",n),e.instance=c}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(l.count++,e=vo.bind(l),t.addEventListener("load",e),t.addEventListener("error",e))}}function iv(){if(ji===null)throw Error(u(475));var t=ji;return t.stylesheets&&t.count===0&&us(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&us(t,t.stylesheets),t.unsuspend){var l=t.unsuspend;t.unsuspend=null,l()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function vo(){if(this.count--,this.count===0){if(this.stylesheets)us(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var bo=null;function us(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,bo=new Map,e.forEach(rv,t),bo=null,vo.call(t))}function rv(t,e){if(!(e.state.loading&4)){var n=bo.get(t);if(n)var l=n.get(null);else{n=new Map,bo.set(t,n);for(var r=t.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<r.length;c++){var d=r[c];(d.nodeName==="LINK"||d.getAttribute("media")!=="not all")&&(n.set(d.dataset.precedence,d),l=d)}l&&n.set(null,l)}r=e.instance,d=r.getAttribute("data-precedence"),c=n.get(d)||l,c===l&&n.set(null,r),n.set(d,r),this.count++,l=vo.bind(this),r.addEventListener("load",l),r.addEventListener("error",l),c?c.parentNode.insertBefore(r,c.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(r,t.firstChild)),e.state.loading|=4}}var Hi={$$typeof:X,Provider:null,Consumer:null,_currentValue:at,_currentValue2:at,_threadCount:0};function ov(t,e,n,l,r,c,d,m){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=lu(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=lu(0),this.hiddenUpdates=lu(null),this.identifierPrefix=l,this.onUncaughtError=r,this.onCaughtError=c,this.onRecoverableError=d,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=m,this.incompleteTransitions=new Map}function Xm(t,e,n,l,r,c,d,m,S,_,G,Y){return t=new ov(t,e,n,d,m,S,_,Y),e=1,c===!0&&(e|=24),c=ke(3,null,null,e),t.current=c,c.stateNode=t,e=Yu(),e.refCount++,t.pooledCache=e,e.refCount++,c.memoizedState={element:l,isDehydrated:n,cache:e},Zu(c),t}function Qm(t){return t?(t=cl,t):cl}function Zm(t,e,n,l,r,c){r=Qm(r),l.context===null?l.context=r:l.pendingContext=r,l=na(e),l.payload={element:n},c=c===void 0?null:c,c!==null&&(l.callback=c),n=aa(t,l,e),n!==null&&(Qe(n,t,e),mi(n,t,e))}function Km(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function cs(t,e){Km(t,e),(t=t.alternate)&&Km(t,e)}function Jm(t){if(t.tag===13){var e=ul(t,67108864);e!==null&&Qe(e,t,67108864),cs(t,67108864)}}var So=!0;function uv(t,e,n,l){var r=$.T;$.T=null;var c=K.p;try{K.p=2,ss(t,e,n,l)}finally{K.p=c,$.T=r}}function cv(t,e,n,l){var r=$.T;$.T=null;var c=K.p;try{K.p=8,ss(t,e,n,l)}finally{K.p=c,$.T=r}}function ss(t,e,n,l){if(So){var r=fs(l);if(r===null)Pc(t,e,l,Co,n),Pm(t,l);else if(fv(r,t,e,n,l))l.stopPropagation();else if(Pm(t,l),e&4&&-1<sv.indexOf(t)){for(;r!==null;){var c=Wa(r);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var d=_n(c.pendingLanes);if(d!==0){var m=c;for(m.pendingLanes|=2,m.entangledLanes|=2;d;){var S=1<<31-ve(d);m.entanglements[1]|=S,d&=~S}Tn(c),(Dt&6)===0&&(ao=oe()+500,Bi(0))}}break;case 13:m=ul(c,2),m!==null&&Qe(m,c,2),io(),cs(c,2)}if(c=fs(l),c===null&&Pc(t,e,l,Co,n),c===r)break;r=c}r!==null&&l.stopPropagation()}else Pc(t,e,l,null,n)}}function fs(t){return t=gu(t),ds(t)}var Co=null;function ds(t){if(Co=null,t=Ja(t),t!==null){var e=f(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=p(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Co=t,null}function Wm(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(cn()){case ge:return 2;case yn:return 8;case Je:case mt:return 32;case dr:return 268435456;default:return 32}default:return 32}}var ps=!1,ga=null,ya=null,va=null,Gi=new Map,Li=new Map,ba=[],sv="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Pm(t,e){switch(t){case"focusin":case"focusout":ga=null;break;case"dragenter":case"dragleave":ya=null;break;case"mouseover":case"mouseout":va=null;break;case"pointerover":case"pointerout":Gi.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Li.delete(e.pointerId)}}function ki(t,e,n,l,r,c){return t===null||t.nativeEvent!==c?(t={blockedOn:e,domEventName:n,eventSystemFlags:l,nativeEvent:c,targetContainers:[r]},e!==null&&(e=Wa(e),e!==null&&Jm(e)),t):(t.eventSystemFlags|=l,e=t.targetContainers,r!==null&&e.indexOf(r)===-1&&e.push(r),t)}function fv(t,e,n,l,r){switch(e){case"focusin":return ga=ki(ga,t,e,n,l,r),!0;case"dragenter":return ya=ki(ya,t,e,n,l,r),!0;case"mouseover":return va=ki(va,t,e,n,l,r),!0;case"pointerover":var c=r.pointerId;return Gi.set(c,ki(Gi.get(c)||null,t,e,n,l,r)),!0;case"gotpointercapture":return c=r.pointerId,Li.set(c,ki(Li.get(c)||null,t,e,n,l,r)),!0}return!1}function Fm(t){var e=Ja(t.target);if(e!==null){var n=f(e);if(n!==null){if(e=n.tag,e===13){if(e=p(n),e!==null){t.blockedOn=e,lg(t.priority,function(){if(n.tag===13){var l=Xe();l=iu(l);var r=ul(n,l);r!==null&&Qe(r,n,l),cs(n,l)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function To(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=fs(t.nativeEvent);if(n===null){n=t.nativeEvent;var l=new n.constructor(n.type,n);hu=l,n.target.dispatchEvent(l),hu=null}else return e=Wa(n),e!==null&&Jm(e),t.blockedOn=n,!1;e.shift()}return!0}function Im(t,e,n){To(t)&&n.delete(e)}function dv(){ps=!1,ga!==null&&To(ga)&&(ga=null),ya!==null&&To(ya)&&(ya=null),va!==null&&To(va)&&(va=null),Gi.forEach(Im),Li.forEach(Im)}function xo(t,e){t.blockedOn===e&&(t.blockedOn=null,ps||(ps=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,dv)))}var Eo=null;function th(t){Eo!==t&&(Eo=t,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){Eo===t&&(Eo=null);for(var e=0;e<t.length;e+=3){var n=t[e],l=t[e+1],r=t[e+2];if(typeof l!="function"){if(ds(l||n)===null)continue;break}var c=Wa(n);c!==null&&(t.splice(e,3),e-=3,dc(c,{pending:!0,data:r,method:n.method,action:l},l,r))}}))}function qi(t){function e(S){return xo(S,t)}ga!==null&&xo(ga,t),ya!==null&&xo(ya,t),va!==null&&xo(va,t),Gi.forEach(e),Li.forEach(e);for(var n=0;n<ba.length;n++){var l=ba[n];l.blockedOn===t&&(l.blockedOn=null)}for(;0<ba.length&&(n=ba[0],n.blockedOn===null);)Fm(n),n.blockedOn===null&&ba.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var r=n[l],c=n[l+1],d=r[_e]||null;if(typeof c=="function")d||th(n);else if(d){var m=null;if(c&&c.hasAttribute("formAction")){if(r=c,d=c[_e]||null)m=d.formAction;else if(ds(r)!==null)continue}else m=d.action;typeof m=="function"?n[l+1]=m:(n.splice(l,3),l-=3),th(n)}}}function ms(t){this._internalRoot=t}Ao.prototype.render=ms.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(u(409));var n=e.current,l=Xe();Zm(n,l,t,e,null,null)},Ao.prototype.unmount=ms.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Zm(t.current,2,null,t,null,null),io(),e[Ka]=null}};function Ao(t){this._internalRoot=t}Ao.prototype.unstable_scheduleHydration=function(t){if(t){var e=yf();t={blockedOn:null,target:t,priority:e};for(var n=0;n<ba.length&&e!==0&&e<ba[n].priority;n++);ba.splice(n,0,t),n===0&&Fm(t)}};var eh=i.version;if(eh!=="19.1.0")throw Error(u(527,eh,"19.1.0"));K.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(u(188)):(t=Object.keys(t).join(","),Error(u(268,t)));return t=y(e),t=t!==null?g(t):null,t=t===null?null:t.stateNode,t};var pv={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:$,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ro=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ro.isDisabled&&Ro.supportsFiber)try{Pn=Ro.inject(pv),ye=Ro}catch{}}return Xi.createRoot=function(t,e){if(!s(t))throw Error(u(299));var n=!1,l="",r=yp,c=vp,d=bp,m=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(l=e.identifierPrefix),e.onUncaughtError!==void 0&&(r=e.onUncaughtError),e.onCaughtError!==void 0&&(c=e.onCaughtError),e.onRecoverableError!==void 0&&(d=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(m=e.unstable_transitionCallbacks)),e=Xm(t,1,!1,null,null,n,l,r,c,d,m,null),t[Ka]=e.current,Wc(t),new ms(e)},Xi.hydrateRoot=function(t,e,n){if(!s(t))throw Error(u(299));var l=!1,r="",c=yp,d=vp,m=bp,S=null,_=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(r=n.identifierPrefix),n.onUncaughtError!==void 0&&(c=n.onUncaughtError),n.onCaughtError!==void 0&&(d=n.onCaughtError),n.onRecoverableError!==void 0&&(m=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(S=n.unstable_transitionCallbacks),n.formState!==void 0&&(_=n.formState)),e=Xm(t,1,!0,e,n??null,l,r,c,d,m,S,_),e.context=Qm(null),n=e.current,l=Xe(),l=iu(l),r=na(l),r.callback=null,aa(n,r,l),n=l,e.current.lanes=n,Kl(e,n),Tn(e),t[Ka]=e.current,Wc(t),new Ao(e)},Xi.version="19.1.0",Xi}var fh;function Rv(){if(fh)return gs.exports;fh=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(i){console.error(i)}}return a(),gs.exports=Av(),gs.exports}var Ov=Rv();const uT=n0(Ov);var w=Vs();const Gl=n0(w),_s=yv({__proto__:null,default:Gl},[w]),Pi={black:"#000",white:"#fff"},Nl={300:"#e57373",400:"#ef5350",500:"#f44336",700:"#d32f2f",800:"#c62828"},$l={50:"#f3e5f5",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",700:"#7b1fa2"},wl={50:"#e3f2fd",200:"#90caf9",400:"#42a5f5",700:"#1976d2",800:"#1565c0"},Ul={300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",700:"#0288d1",900:"#01579b"},jl={300:"#81c784",400:"#66bb6a",500:"#4caf50",700:"#388e3c",800:"#2e7d32",900:"#1b5e20"},Qi={300:"#ffb74d",400:"#ffa726",500:"#ff9800",700:"#f57c00",900:"#e65100"},Mv={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"};function Xa(a,...i){const o=new URL(`https://mui.com/production-error/?code=${a}`);return i.forEach(u=>o.searchParams.append("args[]",u)),`Minified MUI error #${a}; visit ${o} for the full message.`}const On="$$material";function jo(){return jo=Object.assign?Object.assign.bind():function(a){for(var i=1;i<arguments.length;i++){var o=arguments[i];for(var u in o)({}).hasOwnProperty.call(o,u)&&(a[u]=o[u])}return a},jo.apply(null,arguments)}function zv(a){if(a.sheet)return a.sheet;for(var i=0;i<document.styleSheets.length;i++)if(document.styleSheets[i].ownerNode===a)return document.styleSheets[i]}function _v(a){var i=document.createElement("style");return i.setAttribute("data-emotion",a.key),a.nonce!==void 0&&i.setAttribute("nonce",a.nonce),i.appendChild(document.createTextNode("")),i.setAttribute("data-s",""),i}var Bv=function(){function a(o){var u=this;this._insertTag=function(s){var f;u.tags.length===0?u.insertionPoint?f=u.insertionPoint.nextSibling:u.prepend?f=u.container.firstChild:f=u.before:f=u.tags[u.tags.length-1].nextSibling,u.container.insertBefore(s,f),u.tags.push(s)},this.isSpeedy=o.speedy===void 0?!0:o.speedy,this.tags=[],this.ctr=0,this.nonce=o.nonce,this.key=o.key,this.container=o.container,this.prepend=o.prepend,this.insertionPoint=o.insertionPoint,this.before=null}var i=a.prototype;return i.hydrate=function(u){u.forEach(this._insertTag)},i.insert=function(u){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(_v(this));var s=this.tags[this.tags.length-1];if(this.isSpeedy){var f=zv(s);try{f.insertRule(u,f.cssRules.length)}catch{}}else s.appendChild(document.createTextNode(u));this.ctr++},i.flush=function(){this.tags.forEach(function(u){var s;return(s=u.parentNode)==null?void 0:s.removeChild(u)}),this.tags=[],this.ctr=0},a}(),Ee="-ms-",Ho="-moz-",Ot="-webkit-",a0="comm",Xs="rule",Qs="decl",Dv="@import",l0="@keyframes",Nv="@layer",$v=Math.abs,Vo=String.fromCharCode,wv=Object.assign;function Uv(a,i){return he(a,0)^45?(((i<<2^he(a,0))<<2^he(a,1))<<2^he(a,2))<<2^he(a,3):0}function i0(a){return a.trim()}function jv(a,i){return(a=i.exec(a))?a[0]:a}function Mt(a,i,o){return a.replace(i,o)}function Bs(a,i){return a.indexOf(i)}function he(a,i){return a.charCodeAt(i)|0}function Fi(a,i,o){return a.slice(i,o)}function En(a){return a.length}function Zs(a){return a.length}function Oo(a,i){return i.push(a),a}function Hv(a,i){return a.map(i).join("")}var Xo=1,Yl=1,r0=0,Ue=0,le=0,Vl="";function Qo(a,i,o,u,s,f,p){return{value:a,root:i,parent:o,type:u,props:s,children:f,line:Xo,column:Yl,length:p,return:""}}function Zi(a,i){return wv(Qo("",null,null,"",null,null,0),a,{length:-a.length},i)}function Gv(){return le}function Lv(){return le=Ue>0?he(Vl,--Ue):0,Yl--,le===10&&(Yl=1,Xo--),le}function Ke(){return le=Ue<r0?he(Vl,Ue++):0,Yl++,le===10&&(Yl=1,Xo++),le}function Mn(){return he(Vl,Ue)}function Bo(){return Ue}function lr(a,i){return Fi(Vl,a,i)}function Ii(a){switch(a){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function o0(a){return Xo=Yl=1,r0=En(Vl=a),Ue=0,[]}function u0(a){return Vl="",a}function Do(a){return i0(lr(Ue-1,Ds(a===91?a+2:a===40?a+1:a)))}function kv(a){for(;(le=Mn())&&le<33;)Ke();return Ii(a)>2||Ii(le)>3?"":" "}function qv(a,i){for(;--i&&Ke()&&!(le<48||le>102||le>57&&le<65||le>70&&le<97););return lr(a,Bo()+(i<6&&Mn()==32&&Ke()==32))}function Ds(a){for(;Ke();)switch(le){case a:return Ue;case 34:case 39:a!==34&&a!==39&&Ds(le);break;case 40:a===41&&Ds(a);break;case 92:Ke();break}return Ue}function Yv(a,i){for(;Ke()&&a+le!==57;)if(a+le===84&&Mn()===47)break;return"/*"+lr(i,Ue-1)+"*"+Vo(a===47?a:Ke())}function Vv(a){for(;!Ii(Mn());)Ke();return lr(a,Ue)}function Xv(a){return u0(No("",null,null,null,[""],a=o0(a),0,[0],a))}function No(a,i,o,u,s,f,p,h,y){for(var g=0,b=0,C=p,E=0,B=0,R=0,T=1,L=1,V=1,Z=0,X="",U=s,M=f,Q=u,F=X;L;)switch(R=Z,Z=Ke()){case 40:if(R!=108&&he(F,C-1)==58){Bs(F+=Mt(Do(Z),"&","&\f"),"&\f")!=-1&&(V=-1);break}case 34:case 39:case 91:F+=Do(Z);break;case 9:case 10:case 13:case 32:F+=kv(R);break;case 92:F+=qv(Bo()-1,7);continue;case 47:switch(Mn()){case 42:case 47:Oo(Qv(Yv(Ke(),Bo()),i,o),y);break;default:F+="/"}break;case 123*T:h[g++]=En(F)*V;case 125*T:case 59:case 0:switch(Z){case 0:case 125:L=0;case 59+b:V==-1&&(F=Mt(F,/\f/g,"")),B>0&&En(F)-C&&Oo(B>32?ph(F+";",u,o,C-1):ph(Mt(F," ","")+";",u,o,C-2),y);break;case 59:F+=";";default:if(Oo(Q=dh(F,i,o,g,b,s,h,X,U=[],M=[],C),f),Z===123)if(b===0)No(F,i,Q,Q,U,f,C,h,M);else switch(E===99&&he(F,3)===110?100:E){case 100:case 108:case 109:case 115:No(a,Q,Q,u&&Oo(dh(a,Q,Q,0,0,s,h,X,s,U=[],C),M),s,M,C,h,u?U:M);break;default:No(F,Q,Q,Q,[""],M,0,h,M)}}g=b=B=0,T=V=1,X=F="",C=p;break;case 58:C=1+En(F),B=R;default:if(T<1){if(Z==123)--T;else if(Z==125&&T++==0&&Lv()==125)continue}switch(F+=Vo(Z),Z*T){case 38:V=b>0?1:(F+="\f",-1);break;case 44:h[g++]=(En(F)-1)*V,V=1;break;case 64:Mn()===45&&(F+=Do(Ke())),E=Mn(),b=C=En(X=F+=Vv(Bo())),Z++;break;case 45:R===45&&En(F)==2&&(T=0)}}return f}function dh(a,i,o,u,s,f,p,h,y,g,b){for(var C=s-1,E=s===0?f:[""],B=Zs(E),R=0,T=0,L=0;R<u;++R)for(var V=0,Z=Fi(a,C+1,C=$v(T=p[R])),X=a;V<B;++V)(X=i0(T>0?E[V]+" "+Z:Mt(Z,/&\f/g,E[V])))&&(y[L++]=X);return Qo(a,i,o,s===0?Xs:h,y,g,b)}function Qv(a,i,o){return Qo(a,i,o,a0,Vo(Gv()),Fi(a,2,-2),0)}function ph(a,i,o,u){return Qo(a,i,o,Qs,Fi(a,0,u),Fi(a,u+1,-1),u)}function Ll(a,i){for(var o="",u=Zs(a),s=0;s<u;s++)o+=i(a[s],s,a,i)||"";return o}function Zv(a,i,o,u){switch(a.type){case Nv:if(a.children.length)break;case Dv:case Qs:return a.return=a.return||a.value;case a0:return"";case l0:return a.return=a.value+"{"+Ll(a.children,u)+"}";case Xs:a.value=a.props.join(",")}return En(o=Ll(a.children,u))?a.return=a.value+"{"+o+"}":""}function Kv(a){var i=Zs(a);return function(o,u,s,f){for(var p="",h=0;h<i;h++)p+=a[h](o,u,s,f)||"";return p}}function Jv(a){return function(i){i.root||(i=i.return)&&a(i)}}function c0(a){var i=Object.create(null);return function(o){return i[o]===void 0&&(i[o]=a(o)),i[o]}}var Wv=function(i,o,u){for(var s=0,f=0;s=f,f=Mn(),s===38&&f===12&&(o[u]=1),!Ii(f);)Ke();return lr(i,Ue)},Pv=function(i,o){var u=-1,s=44;do switch(Ii(s)){case 0:s===38&&Mn()===12&&(o[u]=1),i[u]+=Wv(Ue-1,o,u);break;case 2:i[u]+=Do(s);break;case 4:if(s===44){i[++u]=Mn()===58?"&\f":"",o[u]=i[u].length;break}default:i[u]+=Vo(s)}while(s=Ke());return i},Fv=function(i,o){return u0(Pv(o0(i),o))},mh=new WeakMap,Iv=function(i){if(!(i.type!=="rule"||!i.parent||i.length<1)){for(var o=i.value,u=i.parent,s=i.column===u.column&&i.line===u.line;u.type!=="rule";)if(u=u.parent,!u)return;if(!(i.props.length===1&&o.charCodeAt(0)!==58&&!mh.get(u))&&!s){mh.set(i,!0);for(var f=[],p=Fv(o,f),h=u.props,y=0,g=0;y<p.length;y++)for(var b=0;b<h.length;b++,g++)i.props[g]=f[y]?p[y].replace(/&\f/g,h[b]):h[b]+" "+p[y]}}},t1=function(i){if(i.type==="decl"){var o=i.value;o.charCodeAt(0)===108&&o.charCodeAt(2)===98&&(i.return="",i.value="")}};function s0(a,i){switch(Uv(a,i)){case 5103:return Ot+"print-"+a+a;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return Ot+a+a;case 5349:case 4246:case 4810:case 6968:case 2756:return Ot+a+Ho+a+Ee+a+a;case 6828:case 4268:return Ot+a+Ee+a+a;case 6165:return Ot+a+Ee+"flex-"+a+a;case 5187:return Ot+a+Mt(a,/(\w+).+(:[^]+)/,Ot+"box-$1$2"+Ee+"flex-$1$2")+a;case 5443:return Ot+a+Ee+"flex-item-"+Mt(a,/flex-|-self/,"")+a;case 4675:return Ot+a+Ee+"flex-line-pack"+Mt(a,/align-content|flex-|-self/,"")+a;case 5548:return Ot+a+Ee+Mt(a,"shrink","negative")+a;case 5292:return Ot+a+Ee+Mt(a,"basis","preferred-size")+a;case 6060:return Ot+"box-"+Mt(a,"-grow","")+Ot+a+Ee+Mt(a,"grow","positive")+a;case 4554:return Ot+Mt(a,/([^-])(transform)/g,"$1"+Ot+"$2")+a;case 6187:return Mt(Mt(Mt(a,/(zoom-|grab)/,Ot+"$1"),/(image-set)/,Ot+"$1"),a,"")+a;case 5495:case 3959:return Mt(a,/(image-set\([^]*)/,Ot+"$1$`$1");case 4968:return Mt(Mt(a,/(.+:)(flex-)?(.*)/,Ot+"box-pack:$3"+Ee+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+Ot+a+a;case 4095:case 3583:case 4068:case 2532:return Mt(a,/(.+)-inline(.+)/,Ot+"$1$2")+a;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(En(a)-1-i>6)switch(he(a,i+1)){case 109:if(he(a,i+4)!==45)break;case 102:return Mt(a,/(.+:)(.+)-([^]+)/,"$1"+Ot+"$2-$3$1"+Ho+(he(a,i+3)==108?"$3":"$2-$3"))+a;case 115:return~Bs(a,"stretch")?s0(Mt(a,"stretch","fill-available"),i)+a:a}break;case 4949:if(he(a,i+1)!==115)break;case 6444:switch(he(a,En(a)-3-(~Bs(a,"!important")&&10))){case 107:return Mt(a,":",":"+Ot)+a;case 101:return Mt(a,/(.+:)([^;!]+)(;|!.+)?/,"$1"+Ot+(he(a,14)===45?"inline-":"")+"box$3$1"+Ot+"$2$3$1"+Ee+"$2box$3")+a}break;case 5936:switch(he(a,i+11)){case 114:return Ot+a+Ee+Mt(a,/[svh]\w+-[tblr]{2}/,"tb")+a;case 108:return Ot+a+Ee+Mt(a,/[svh]\w+-[tblr]{2}/,"tb-rl")+a;case 45:return Ot+a+Ee+Mt(a,/[svh]\w+-[tblr]{2}/,"lr")+a}return Ot+a+Ee+a+a}return a}var e1=function(i,o,u,s){if(i.length>-1&&!i.return)switch(i.type){case Qs:i.return=s0(i.value,i.length);break;case l0:return Ll([Zi(i,{value:Mt(i.value,"@","@"+Ot)})],s);case Xs:if(i.length)return Hv(i.props,function(f){switch(jv(f,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return Ll([Zi(i,{props:[Mt(f,/:(read-\w+)/,":"+Ho+"$1")]})],s);case"::placeholder":return Ll([Zi(i,{props:[Mt(f,/:(plac\w+)/,":"+Ot+"input-$1")]}),Zi(i,{props:[Mt(f,/:(plac\w+)/,":"+Ho+"$1")]}),Zi(i,{props:[Mt(f,/:(plac\w+)/,Ee+"input-$1")]})],s)}return""})}},n1=[e1],a1=function(i){var o=i.key;if(o==="css"){var u=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(u,function(T){var L=T.getAttribute("data-emotion");L.indexOf(" ")!==-1&&(document.head.appendChild(T),T.setAttribute("data-s",""))})}var s=i.stylisPlugins||n1,f={},p,h=[];p=i.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+o+' "]'),function(T){for(var L=T.getAttribute("data-emotion").split(" "),V=1;V<L.length;V++)f[L[V]]=!0;h.push(T)});var y,g=[Iv,t1];{var b,C=[Zv,Jv(function(T){b.insert(T)})],E=Kv(g.concat(s,C)),B=function(L){return Ll(Xv(L),E)};y=function(L,V,Z,X){b=Z,B(L?L+"{"+V.styles+"}":V.styles),X&&(R.inserted[V.name]=!0)}}var R={key:o,sheet:new Bv({key:o,container:p,nonce:i.nonce,speedy:i.speedy,prepend:i.prepend,insertionPoint:i.insertionPoint}),nonce:i.nonce,inserted:f,registered:{},insert:y};return R.sheet.hydrate(h),R},Cs={exports:{}},zt={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var hh;function l1(){if(hh)return zt;hh=1;var a=typeof Symbol=="function"&&Symbol.for,i=a?Symbol.for("react.element"):60103,o=a?Symbol.for("react.portal"):60106,u=a?Symbol.for("react.fragment"):60107,s=a?Symbol.for("react.strict_mode"):60108,f=a?Symbol.for("react.profiler"):60114,p=a?Symbol.for("react.provider"):60109,h=a?Symbol.for("react.context"):60110,y=a?Symbol.for("react.async_mode"):60111,g=a?Symbol.for("react.concurrent_mode"):60111,b=a?Symbol.for("react.forward_ref"):60112,C=a?Symbol.for("react.suspense"):60113,E=a?Symbol.for("react.suspense_list"):60120,B=a?Symbol.for("react.memo"):60115,R=a?Symbol.for("react.lazy"):60116,T=a?Symbol.for("react.block"):60121,L=a?Symbol.for("react.fundamental"):60117,V=a?Symbol.for("react.responder"):60118,Z=a?Symbol.for("react.scope"):60119;function X(M){if(typeof M=="object"&&M!==null){var Q=M.$$typeof;switch(Q){case i:switch(M=M.type,M){case y:case g:case u:case f:case s:case C:return M;default:switch(M=M&&M.$$typeof,M){case h:case b:case R:case B:case p:return M;default:return Q}}case o:return Q}}}function U(M){return X(M)===g}return zt.AsyncMode=y,zt.ConcurrentMode=g,zt.ContextConsumer=h,zt.ContextProvider=p,zt.Element=i,zt.ForwardRef=b,zt.Fragment=u,zt.Lazy=R,zt.Memo=B,zt.Portal=o,zt.Profiler=f,zt.StrictMode=s,zt.Suspense=C,zt.isAsyncMode=function(M){return U(M)||X(M)===y},zt.isConcurrentMode=U,zt.isContextConsumer=function(M){return X(M)===h},zt.isContextProvider=function(M){return X(M)===p},zt.isElement=function(M){return typeof M=="object"&&M!==null&&M.$$typeof===i},zt.isForwardRef=function(M){return X(M)===b},zt.isFragment=function(M){return X(M)===u},zt.isLazy=function(M){return X(M)===R},zt.isMemo=function(M){return X(M)===B},zt.isPortal=function(M){return X(M)===o},zt.isProfiler=function(M){return X(M)===f},zt.isStrictMode=function(M){return X(M)===s},zt.isSuspense=function(M){return X(M)===C},zt.isValidElementType=function(M){return typeof M=="string"||typeof M=="function"||M===u||M===g||M===f||M===s||M===C||M===E||typeof M=="object"&&M!==null&&(M.$$typeof===R||M.$$typeof===B||M.$$typeof===p||M.$$typeof===h||M.$$typeof===b||M.$$typeof===L||M.$$typeof===V||M.$$typeof===Z||M.$$typeof===T)},zt.typeOf=X,zt}var gh;function i1(){return gh||(gh=1,Cs.exports=l1()),Cs.exports}var Ts,yh;function r1(){if(yh)return Ts;yh=1;var a=i1(),i={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},o={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},u={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},s={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},f={};f[a.ForwardRef]=u,f[a.Memo]=s;function p(R){return a.isMemo(R)?s:f[R.$$typeof]||i}var h=Object.defineProperty,y=Object.getOwnPropertyNames,g=Object.getOwnPropertySymbols,b=Object.getOwnPropertyDescriptor,C=Object.getPrototypeOf,E=Object.prototype;function B(R,T,L){if(typeof T!="string"){if(E){var V=C(T);V&&V!==E&&B(R,V,L)}var Z=y(T);g&&(Z=Z.concat(g(T)));for(var X=p(R),U=p(T),M=0;M<Z.length;++M){var Q=Z[M];if(!o[Q]&&!(L&&L[Q])&&!(U&&U[Q])&&!(X&&X[Q])){var F=b(T,Q);try{h(R,Q,F)}catch{}}}}return R}return Ts=B,Ts}r1();var o1=!0;function f0(a,i,o){var u="";return o.split(" ").forEach(function(s){a[s]!==void 0?i.push(a[s]+";"):s&&(u+=s+" ")}),u}var Ks=function(i,o,u){var s=i.key+"-"+o.name;(u===!1||o1===!1)&&i.registered[s]===void 0&&(i.registered[s]=o.styles)},Js=function(i,o,u){Ks(i,o,u);var s=i.key+"-"+o.name;if(i.inserted[o.name]===void 0){var f=o;do i.insert(o===f?"."+s:"",f,i.sheet,!0),f=f.next;while(f!==void 0)}};function u1(a){for(var i=0,o,u=0,s=a.length;s>=4;++u,s-=4)o=a.charCodeAt(u)&255|(a.charCodeAt(++u)&255)<<8|(a.charCodeAt(++u)&255)<<16|(a.charCodeAt(++u)&255)<<24,o=(o&65535)*1540483477+((o>>>16)*59797<<16),o^=o>>>24,i=(o&65535)*1540483477+((o>>>16)*59797<<16)^(i&65535)*1540483477+((i>>>16)*59797<<16);switch(s){case 3:i^=(a.charCodeAt(u+2)&255)<<16;case 2:i^=(a.charCodeAt(u+1)&255)<<8;case 1:i^=a.charCodeAt(u)&255,i=(i&65535)*1540483477+((i>>>16)*59797<<16)}return i^=i>>>13,i=(i&65535)*1540483477+((i>>>16)*59797<<16),((i^i>>>15)>>>0).toString(36)}var c1={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},s1=/[A-Z]|^ms/g,f1=/_EMO_([^_]+?)_([^]*?)_EMO_/g,d0=function(i){return i.charCodeAt(1)===45},vh=function(i){return i!=null&&typeof i!="boolean"},xs=c0(function(a){return d0(a)?a:a.replace(s1,"-$&").toLowerCase()}),bh=function(i,o){switch(i){case"animation":case"animationName":if(typeof o=="string")return o.replace(f1,function(u,s,f){return An={name:s,styles:f,next:An},s})}return c1[i]!==1&&!d0(i)&&typeof o=="number"&&o!==0?o+"px":o};function tr(a,i,o){if(o==null)return"";var u=o;if(u.__emotion_styles!==void 0)return u;switch(typeof o){case"boolean":return"";case"object":{var s=o;if(s.anim===1)return An={name:s.name,styles:s.styles,next:An},s.name;var f=o;if(f.styles!==void 0){var p=f.next;if(p!==void 0)for(;p!==void 0;)An={name:p.name,styles:p.styles,next:An},p=p.next;var h=f.styles+";";return h}return d1(a,i,o)}case"function":{if(a!==void 0){var y=An,g=o(a);return An=y,tr(a,i,g)}break}}var b=o;if(i==null)return b;var C=i[b];return C!==void 0?C:b}function d1(a,i,o){var u="";if(Array.isArray(o))for(var s=0;s<o.length;s++)u+=tr(a,i,o[s])+";";else for(var f in o){var p=o[f];if(typeof p!="object"){var h=p;i!=null&&i[h]!==void 0?u+=f+"{"+i[h]+"}":vh(h)&&(u+=xs(f)+":"+bh(f,h)+";")}else if(Array.isArray(p)&&typeof p[0]=="string"&&(i==null||i[p[0]]===void 0))for(var y=0;y<p.length;y++)vh(p[y])&&(u+=xs(f)+":"+bh(f,p[y])+";");else{var g=tr(a,i,p);switch(f){case"animation":case"animationName":{u+=xs(f)+":"+g+";";break}default:u+=f+"{"+g+"}"}}}return u}var Sh=/label:\s*([^\s;{]+)\s*(;|$)/g,An;function ir(a,i,o){if(a.length===1&&typeof a[0]=="object"&&a[0]!==null&&a[0].styles!==void 0)return a[0];var u=!0,s="";An=void 0;var f=a[0];if(f==null||f.raw===void 0)u=!1,s+=tr(o,i,f);else{var p=f;s+=p[0]}for(var h=1;h<a.length;h++)if(s+=tr(o,i,a[h]),u){var y=f;s+=y[h]}Sh.lastIndex=0;for(var g="",b;(b=Sh.exec(s))!==null;)g+="-"+b[1];var C=u1(s)+g;return{name:C,styles:s,next:An}}var p1=function(i){return i()},p0=_s.useInsertionEffect?_s.useInsertionEffect:!1,m0=p0||p1,Ch=p0||w.useLayoutEffect,h0=w.createContext(typeof HTMLElement<"u"?a1({key:"css"}):null);h0.Provider;var Ws=function(i){return w.forwardRef(function(o,u){var s=w.useContext(h0);return i(o,s,u)})},rr=w.createContext({}),Ps={}.hasOwnProperty,Ns="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",m1=function(i,o){var u={};for(var s in o)Ps.call(o,s)&&(u[s]=o[s]);return u[Ns]=i,u},h1=function(i){var o=i.cache,u=i.serialized,s=i.isStringTag;return Ks(o,u,s),m0(function(){return Js(o,u,s)}),null},g1=Ws(function(a,i,o){var u=a.css;typeof u=="string"&&i.registered[u]!==void 0&&(u=i.registered[u]);var s=a[Ns],f=[u],p="";typeof a.className=="string"?p=f0(i.registered,f,a.className):a.className!=null&&(p=a.className+" ");var h=ir(f,void 0,w.useContext(rr));p+=i.key+"-"+h.name;var y={};for(var g in a)Ps.call(a,g)&&g!=="css"&&g!==Ns&&(y[g]=a[g]);return y.className=p,o&&(y.ref=o),w.createElement(w.Fragment,null,w.createElement(h1,{cache:i,serialized:h,isStringTag:typeof s=="string"}),w.createElement(s,y))}),y1=g1,Th=function(i,o){var u=arguments;if(o==null||!Ps.call(o,"css"))return w.createElement.apply(void 0,u);var s=u.length,f=new Array(s);f[0]=y1,f[1]=m1(i,o);for(var p=2;p<s;p++)f[p]=u[p];return w.createElement.apply(null,f)};(function(a){var i;i||(i=a.JSX||(a.JSX={}))})(Th||(Th={}));var v1=Ws(function(a,i){var o=a.styles,u=ir([o],void 0,w.useContext(rr)),s=w.useRef();return Ch(function(){var f=i.key+"-global",p=new i.sheet.constructor({key:f,nonce:i.sheet.nonce,container:i.sheet.container,speedy:i.sheet.isSpeedy}),h=!1,y=document.querySelector('style[data-emotion="'+f+" "+u.name+'"]');return i.sheet.tags.length&&(p.before=i.sheet.tags[0]),y!==null&&(h=!0,y.setAttribute("data-emotion",f),p.hydrate([y])),s.current=[p,h],function(){p.flush()}},[i]),Ch(function(){var f=s.current,p=f[0],h=f[1];if(h){f[1]=!1;return}if(u.next!==void 0&&Js(i,u.next,!0),p.tags.length){var y=p.tags[p.tags.length-1].nextElementSibling;p.before=y,p.flush()}i.insert("",u,p,!1)},[i,u.name]),null});function Fs(){for(var a=arguments.length,i=new Array(a),o=0;o<a;o++)i[o]=arguments[o];return ir(i)}function or(){var a=Fs.apply(void 0,arguments),i="animation-"+a.name;return{name:i,styles:"@keyframes "+i+"{"+a.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}var b1=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,S1=c0(function(a){return b1.test(a)||a.charCodeAt(0)===111&&a.charCodeAt(1)===110&&a.charCodeAt(2)<91}),C1=S1,T1=function(i){return i!=="theme"},xh=function(i){return typeof i=="string"&&i.charCodeAt(0)>96?C1:T1},Eh=function(i,o,u){var s;if(o){var f=o.shouldForwardProp;s=i.__emotion_forwardProp&&f?function(p){return i.__emotion_forwardProp(p)&&f(p)}:f}return typeof s!="function"&&u&&(s=i.__emotion_forwardProp),s},x1=function(i){var o=i.cache,u=i.serialized,s=i.isStringTag;return Ks(o,u,s),m0(function(){return Js(o,u,s)}),null},E1=function a(i,o){var u=i.__emotion_real===i,s=u&&i.__emotion_base||i,f,p;o!==void 0&&(f=o.label,p=o.target);var h=Eh(i,o,u),y=h||xh(s),g=!y("as");return function(){var b=arguments,C=u&&i.__emotion_styles!==void 0?i.__emotion_styles.slice(0):[];if(f!==void 0&&C.push("label:"+f+";"),b[0]==null||b[0].raw===void 0)C.push.apply(C,b);else{var E=b[0];C.push(E[0]);for(var B=b.length,R=1;R<B;R++)C.push(b[R],E[R])}var T=Ws(function(L,V,Z){var X=g&&L.as||s,U="",M=[],Q=L;if(L.theme==null){Q={};for(var F in L)Q[F]=L[F];Q.theme=w.useContext(rr)}typeof L.className=="string"?U=f0(V.registered,M,L.className):L.className!=null&&(U=L.className+" ");var W=ir(C.concat(M),V.registered,Q);U+=V.key+"-"+W.name,p!==void 0&&(U+=" "+p);var nt=g&&h===void 0?xh(X):y,v={};for(var k in L)g&&k==="as"||nt(k)&&(v[k]=L[k]);return v.className=U,Z&&(v.ref=Z),w.createElement(w.Fragment,null,w.createElement(x1,{cache:V,serialized:W,isStringTag:typeof X=="string"}),w.createElement(X,v))});return T.displayName=f!==void 0?f:"Styled("+(typeof s=="string"?s:s.displayName||s.name||"Component")+")",T.defaultProps=i.defaultProps,T.__emotion_real=T,T.__emotion_base=s,T.__emotion_styles=C,T.__emotion_forwardProp=h,Object.defineProperty(T,"toString",{value:function(){return"."+p}}),T.withComponent=function(L,V){var Z=a(L,jo({},o,V,{shouldForwardProp:Eh(T,V,!0)}));return Z.apply(void 0,C)},T}},A1=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"],$s=E1.bind(null);A1.forEach(function(a){$s[a]=$s(a)});function R1(a){return a==null||Object.keys(a).length===0}function g0(a){const{styles:i,defaultTheme:o={}}=a,u=typeof i=="function"?s=>i(R1(s)?o:s):i;return tt.jsx(v1,{styles:u})}function y0(a,i){return $s(a,i)}function O1(a,i){Array.isArray(a.__emotion_styles)&&(a.__emotion_styles=i(a.__emotion_styles))}const Ah=[];function Rh(a){return Ah[0]=a,ir(Ah)}var Es={exports:{}},jt={};/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Oh;function M1(){if(Oh)return jt;Oh=1;var a=Symbol.for("react.transitional.element"),i=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),u=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),p=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),g=Symbol.for("react.suspense_list"),b=Symbol.for("react.memo"),C=Symbol.for("react.lazy"),E=Symbol.for("react.view_transition"),B=Symbol.for("react.client.reference");function R(T){if(typeof T=="object"&&T!==null){var L=T.$$typeof;switch(L){case a:switch(T=T.type,T){case o:case s:case u:case y:case g:case E:return T;default:switch(T=T&&T.$$typeof,T){case p:case h:case C:case b:return T;case f:return T;default:return L}}case i:return L}}}return jt.ContextConsumer=f,jt.ContextProvider=p,jt.Element=a,jt.ForwardRef=h,jt.Fragment=o,jt.Lazy=C,jt.Memo=b,jt.Portal=i,jt.Profiler=s,jt.StrictMode=u,jt.Suspense=y,jt.SuspenseList=g,jt.isContextConsumer=function(T){return R(T)===f},jt.isContextProvider=function(T){return R(T)===p},jt.isElement=function(T){return typeof T=="object"&&T!==null&&T.$$typeof===a},jt.isForwardRef=function(T){return R(T)===h},jt.isFragment=function(T){return R(T)===o},jt.isLazy=function(T){return R(T)===C},jt.isMemo=function(T){return R(T)===b},jt.isPortal=function(T){return R(T)===i},jt.isProfiler=function(T){return R(T)===s},jt.isStrictMode=function(T){return R(T)===u},jt.isSuspense=function(T){return R(T)===y},jt.isSuspenseList=function(T){return R(T)===g},jt.isValidElementType=function(T){return typeof T=="string"||typeof T=="function"||T===o||T===s||T===u||T===y||T===g||typeof T=="object"&&T!==null&&(T.$$typeof===C||T.$$typeof===b||T.$$typeof===p||T.$$typeof===f||T.$$typeof===h||T.$$typeof===B||T.getModuleId!==void 0)},jt.typeOf=R,jt}var Mh;function z1(){return Mh||(Mh=1,Es.exports=M1()),Es.exports}var v0=z1();function Rn(a){if(typeof a!="object"||a===null)return!1;const i=Object.getPrototypeOf(a);return(i===null||i===Object.prototype||Object.getPrototypeOf(i)===null)&&!(Symbol.toStringTag in a)&&!(Symbol.iterator in a)}function b0(a){if(w.isValidElement(a)||v0.isValidElementType(a)||!Rn(a))return a;const i={};return Object.keys(a).forEach(o=>{i[o]=b0(a[o])}),i}function Me(a,i,o={clone:!0}){const u=o.clone?{...a}:a;return Rn(a)&&Rn(i)&&Object.keys(i).forEach(s=>{w.isValidElement(i[s])||v0.isValidElementType(i[s])?u[s]=i[s]:Rn(i[s])&&Object.prototype.hasOwnProperty.call(a,s)&&Rn(a[s])?u[s]=Me(a[s],i[s],o):o.clone?u[s]=Rn(i[s])?b0(i[s]):i[s]:u[s]=i[s]}),u}const _1=a=>{const i=Object.keys(a).map(o=>({key:o,val:a[o]}))||[];return i.sort((o,u)=>o.val-u.val),i.reduce((o,u)=>({...o,[u.key]:u.val}),{})};function B1(a){const{values:i={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:o="px",step:u=5,...s}=a,f=_1(i),p=Object.keys(f);function h(E){return`@media (min-width:${typeof i[E]=="number"?i[E]:E}${o})`}function y(E){return`@media (max-width:${(typeof i[E]=="number"?i[E]:E)-u/100}${o})`}function g(E,B){const R=p.indexOf(B);return`@media (min-width:${typeof i[E]=="number"?i[E]:E}${o}) and (max-width:${(R!==-1&&typeof i[p[R]]=="number"?i[p[R]]:B)-u/100}${o})`}function b(E){return p.indexOf(E)+1<p.length?g(E,p[p.indexOf(E)+1]):h(E)}function C(E){const B=p.indexOf(E);return B===0?h(p[1]):B===p.length-1?y(p[B]):g(E,p[p.indexOf(E)+1]).replace("@media","@media not all and")}return{keys:p,values:f,up:h,down:y,between:g,only:b,not:C,unit:o,...s}}function D1(a,i){if(!a.containerQueries)return i;const o=Object.keys(i).filter(u=>u.startsWith("@container")).sort((u,s)=>{var p,h;const f=/min-width:\s*([0-9.]+)/;return+(((p=u.match(f))==null?void 0:p[1])||0)-+(((h=s.match(f))==null?void 0:h[1])||0)});return o.length?o.reduce((u,s)=>{const f=i[s];return delete u[s],u[s]=f,u},{...i}):i}function N1(a,i){return i==="@"||i.startsWith("@")&&(a.some(o=>i.startsWith(`@${o}`))||!!i.match(/^@\d/))}function $1(a,i){const o=i.match(/^@([^/]+)?\/?(.+)?$/);if(!o)return null;const[,u,s]=o,f=Number.isNaN(+u)?u||0:+u;return a.containerQueries(s).up(f)}function w1(a){const i=(f,p)=>f.replace("@media",p?`@container ${p}`:"@container");function o(f,p){f.up=(...h)=>i(a.breakpoints.up(...h),p),f.down=(...h)=>i(a.breakpoints.down(...h),p),f.between=(...h)=>i(a.breakpoints.between(...h),p),f.only=(...h)=>i(a.breakpoints.only(...h),p),f.not=(...h)=>{const y=i(a.breakpoints.not(...h),p);return y.includes("not all and")?y.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):y}}const u={},s=f=>(o(u,f),u);return o(s),{...a,containerQueries:s}}const U1={borderRadius:4};function Wi(a,i){return i?Me(a,i,{clone:!1}):a}const Zo={xs:0,sm:600,md:900,lg:1200,xl:1536},zh={keys:["xs","sm","md","lg","xl"],up:a=>`@media (min-width:${Zo[a]}px)`},j1={containerQueries:a=>({up:i=>{let o=typeof i=="number"?i:Zo[i]||i;return typeof o=="number"&&(o=`${o}px`),a?`@container ${a} (min-width:${o})`:`@container (min-width:${o})`}})};function mn(a,i,o){const u=a.theme||{};if(Array.isArray(i)){const f=u.breakpoints||zh;return i.reduce((p,h,y)=>(p[f.up(f.keys[y])]=o(i[y]),p),{})}if(typeof i=="object"){const f=u.breakpoints||zh;return Object.keys(i).reduce((p,h)=>{if(N1(f.keys,h)){const y=$1(u.containerQueries?u:j1,h);y&&(p[y]=o(i[h],h))}else if(Object.keys(f.values||Zo).includes(h)){const y=f.up(h);p[y]=o(i[h],h)}else{const y=h;p[y]=i[y]}return p},{})}return o(i)}function S0(a={}){var o;return((o=a.keys)==null?void 0:o.reduce((u,s)=>{const f=a.up(s);return u[f]={},u},{}))||{}}function C0(a,i){return a.reduce((o,u)=>{const s=o[u];return(!s||Object.keys(s).length===0)&&delete o[u],o},i)}function H1(a,...i){const o=S0(a),u=[o,...i].reduce((s,f)=>Me(s,f),{});return C0(Object.keys(o),u)}function G1(a,i){if(typeof a!="object")return{};const o={},u=Object.keys(i);return Array.isArray(a)?u.forEach((s,f)=>{f<a.length&&(o[s]=!0)}):u.forEach(s=>{a[s]!=null&&(o[s]=!0)}),o}function As({values:a,breakpoints:i,base:o}){const u=o||G1(a,i),s=Object.keys(u);if(s.length===0)return a;let f;return s.reduce((p,h,y)=>(Array.isArray(a)?(p[h]=a[y]!=null?a[y]:a[f],f=y):typeof a=="object"?(p[h]=a[h]!=null?a[h]:a[f],f=h):p[h]=a,p),{})}function et(a){if(typeof a!="string")throw new Error(Xa(7));return a.charAt(0).toUpperCase()+a.slice(1)}function Ko(a,i,o=!0){if(!i||typeof i!="string")return null;if(a&&a.vars&&o){const u=`vars.${i}`.split(".").reduce((s,f)=>s&&s[f]?s[f]:null,a);if(u!=null)return u}return i.split(".").reduce((u,s)=>u&&u[s]!=null?u[s]:null,a)}function Go(a,i,o,u=o){let s;return typeof a=="function"?s=a(o):Array.isArray(a)?s=a[o]||u:s=Ko(a,o)||u,i&&(s=i(s,u,a)),s}function ee(a){const{prop:i,cssProperty:o=a.prop,themeKey:u,transform:s}=a,f=p=>{if(p[i]==null)return null;const h=p[i],y=p.theme,g=Ko(y,u)||{};return mn(p,h,C=>{let E=Go(g,s,C);return C===E&&typeof C=="string"&&(E=Go(g,s,`${i}${C==="default"?"":et(C)}`,C)),o===!1?E:{[o]:E}})};return f.propTypes={},f.filterProps=[i],f}function L1(a){const i={};return o=>(i[o]===void 0&&(i[o]=a(o)),i[o])}const k1={m:"margin",p:"padding"},q1={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},_h={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},Y1=L1(a=>{if(a.length>2)if(_h[a])a=_h[a];else return[a];const[i,o]=a.split(""),u=k1[i],s=q1[o]||"";return Array.isArray(s)?s.map(f=>u+f):[u+s]}),Is=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],tf=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"];[...Is,...tf];function ur(a,i,o,u){const s=Ko(a,i,!0)??o;return typeof s=="number"||typeof s=="string"?f=>typeof f=="string"?f:typeof s=="string"?s.startsWith("var(")&&f===0?0:s.startsWith("var(")&&f===1?s:`calc(${f} * ${s})`:s*f:Array.isArray(s)?f=>{if(typeof f=="string")return f;const p=Math.abs(f),h=s[p];return f>=0?h:typeof h=="number"?-h:typeof h=="string"&&h.startsWith("var(")?`calc(-1 * ${h})`:`-${h}`}:typeof s=="function"?s:()=>{}}function Jo(a){return ur(a,"spacing",8)}function Qa(a,i){return typeof i=="string"||i==null?i:a(i)}function V1(a,i){return o=>a.reduce((u,s)=>(u[s]=Qa(i,o),u),{})}function X1(a,i,o,u){if(!i.includes(o))return null;const s=Y1(o),f=V1(s,u),p=a[o];return mn(a,p,f)}function T0(a,i){const o=Jo(a.theme);return Object.keys(a).map(u=>X1(a,i,u,o)).reduce(Wi,{})}function Wt(a){return T0(a,Is)}Wt.propTypes={};Wt.filterProps=Is;function Pt(a){return T0(a,tf)}Pt.propTypes={};Pt.filterProps=tf;function x0(a=8,i=Jo({spacing:a})){if(a.mui)return a;const o=(...u)=>(u.length===0?[1]:u).map(f=>{const p=i(f);return typeof p=="number"?`${p}px`:p}).join(" ");return o.mui=!0,o}function Wo(...a){const i=a.reduce((u,s)=>(s.filterProps.forEach(f=>{u[f]=s}),u),{}),o=u=>Object.keys(u).reduce((s,f)=>i[f]?Wi(s,i[f](u)):s,{});return o.propTypes={},o.filterProps=a.reduce((u,s)=>u.concat(s.filterProps),[]),o}function on(a){return typeof a!="number"?a:`${a}px solid`}function un(a,i){return ee({prop:a,themeKey:"borders",transform:i})}const Q1=un("border",on),Z1=un("borderTop",on),K1=un("borderRight",on),J1=un("borderBottom",on),W1=un("borderLeft",on),P1=un("borderColor"),F1=un("borderTopColor"),I1=un("borderRightColor"),tb=un("borderBottomColor"),eb=un("borderLeftColor"),nb=un("outline",on),ab=un("outlineColor"),Po=a=>{if(a.borderRadius!==void 0&&a.borderRadius!==null){const i=ur(a.theme,"shape.borderRadius",4),o=u=>({borderRadius:Qa(i,u)});return mn(a,a.borderRadius,o)}return null};Po.propTypes={};Po.filterProps=["borderRadius"];Wo(Q1,Z1,K1,J1,W1,P1,F1,I1,tb,eb,Po,nb,ab);const Fo=a=>{if(a.gap!==void 0&&a.gap!==null){const i=ur(a.theme,"spacing",8),o=u=>({gap:Qa(i,u)});return mn(a,a.gap,o)}return null};Fo.propTypes={};Fo.filterProps=["gap"];const Io=a=>{if(a.columnGap!==void 0&&a.columnGap!==null){const i=ur(a.theme,"spacing",8),o=u=>({columnGap:Qa(i,u)});return mn(a,a.columnGap,o)}return null};Io.propTypes={};Io.filterProps=["columnGap"];const tu=a=>{if(a.rowGap!==void 0&&a.rowGap!==null){const i=ur(a.theme,"spacing",8),o=u=>({rowGap:Qa(i,u)});return mn(a,a.rowGap,o)}return null};tu.propTypes={};tu.filterProps=["rowGap"];const lb=ee({prop:"gridColumn"}),ib=ee({prop:"gridRow"}),rb=ee({prop:"gridAutoFlow"}),ob=ee({prop:"gridAutoColumns"}),ub=ee({prop:"gridAutoRows"}),cb=ee({prop:"gridTemplateColumns"}),sb=ee({prop:"gridTemplateRows"}),fb=ee({prop:"gridTemplateAreas"}),db=ee({prop:"gridArea"});Wo(Fo,Io,tu,lb,ib,rb,ob,ub,cb,sb,fb,db);function kl(a,i){return i==="grey"?i:a}const pb=ee({prop:"color",themeKey:"palette",transform:kl}),mb=ee({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:kl}),hb=ee({prop:"backgroundColor",themeKey:"palette",transform:kl});Wo(pb,mb,hb);function Ze(a){return a<=1&&a!==0?`${a*100}%`:a}const gb=ee({prop:"width",transform:Ze}),ef=a=>{if(a.maxWidth!==void 0&&a.maxWidth!==null){const i=o=>{var s,f,p,h,y;const u=((p=(f=(s=a.theme)==null?void 0:s.breakpoints)==null?void 0:f.values)==null?void 0:p[o])||Zo[o];return u?((y=(h=a.theme)==null?void 0:h.breakpoints)==null?void 0:y.unit)!=="px"?{maxWidth:`${u}${a.theme.breakpoints.unit}`}:{maxWidth:u}:{maxWidth:Ze(o)}};return mn(a,a.maxWidth,i)}return null};ef.filterProps=["maxWidth"];const yb=ee({prop:"minWidth",transform:Ze}),vb=ee({prop:"height",transform:Ze}),bb=ee({prop:"maxHeight",transform:Ze}),Sb=ee({prop:"minHeight",transform:Ze});ee({prop:"size",cssProperty:"width",transform:Ze});ee({prop:"size",cssProperty:"height",transform:Ze});const Cb=ee({prop:"boxSizing"});Wo(gb,ef,yb,vb,bb,Sb,Cb);const cr={border:{themeKey:"borders",transform:on},borderTop:{themeKey:"borders",transform:on},borderRight:{themeKey:"borders",transform:on},borderBottom:{themeKey:"borders",transform:on},borderLeft:{themeKey:"borders",transform:on},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:on},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:Po},color:{themeKey:"palette",transform:kl},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:kl},backgroundColor:{themeKey:"palette",transform:kl},p:{style:Pt},pt:{style:Pt},pr:{style:Pt},pb:{style:Pt},pl:{style:Pt},px:{style:Pt},py:{style:Pt},padding:{style:Pt},paddingTop:{style:Pt},paddingRight:{style:Pt},paddingBottom:{style:Pt},paddingLeft:{style:Pt},paddingX:{style:Pt},paddingY:{style:Pt},paddingInline:{style:Pt},paddingInlineStart:{style:Pt},paddingInlineEnd:{style:Pt},paddingBlock:{style:Pt},paddingBlockStart:{style:Pt},paddingBlockEnd:{style:Pt},m:{style:Wt},mt:{style:Wt},mr:{style:Wt},mb:{style:Wt},ml:{style:Wt},mx:{style:Wt},my:{style:Wt},margin:{style:Wt},marginTop:{style:Wt},marginRight:{style:Wt},marginBottom:{style:Wt},marginLeft:{style:Wt},marginX:{style:Wt},marginY:{style:Wt},marginInline:{style:Wt},marginInlineStart:{style:Wt},marginInlineEnd:{style:Wt},marginBlock:{style:Wt},marginBlockStart:{style:Wt},marginBlockEnd:{style:Wt},displayPrint:{cssProperty:!1,transform:a=>({"@media print":{display:a}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:Fo},rowGap:{style:tu},columnGap:{style:Io},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:Ze},maxWidth:{style:ef},minWidth:{transform:Ze},height:{transform:Ze},maxHeight:{transform:Ze},minHeight:{transform:Ze},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}};function Tb(...a){const i=a.reduce((u,s)=>u.concat(Object.keys(s)),[]),o=new Set(i);return a.every(u=>o.size===Object.keys(u).length)}function xb(a,i){return typeof a=="function"?a(i):a}function Eb(){function a(o,u,s,f){const p={[o]:u,theme:s},h=f[o];if(!h)return{[o]:u};const{cssProperty:y=o,themeKey:g,transform:b,style:C}=h;if(u==null)return null;if(g==="typography"&&u==="inherit")return{[o]:u};const E=Ko(s,g)||{};return C?C(p):mn(p,u,R=>{let T=Go(E,b,R);return R===T&&typeof R=="string"&&(T=Go(E,b,`${o}${R==="default"?"":et(R)}`,R)),y===!1?T:{[y]:T}})}function i(o){const{sx:u,theme:s={}}=o||{};if(!u)return null;const f=s.unstable_sxConfig??cr;function p(h){let y=h;if(typeof h=="function")y=h(s);else if(typeof h!="object")return h;if(!y)return null;const g=S0(s.breakpoints),b=Object.keys(g);let C=g;return Object.keys(y).forEach(E=>{const B=xb(y[E],s);if(B!=null)if(typeof B=="object")if(f[E])C=Wi(C,a(E,B,s,f));else{const R=mn({theme:s},B,T=>({[E]:T}));Tb(R,B)?C[E]=i({sx:B,theme:s}):C=Wi(C,R)}else C=Wi(C,a(E,B,s,f))}),D1(s,C0(b,C))}return Array.isArray(u)?u.map(p):p(u)}return i}const Ta=Eb();Ta.filterProps=["sx"];function Ab(a,i){var u;const o=this;if(o.vars){if(!((u=o.colorSchemes)!=null&&u[a])||typeof o.getColorSchemeSelector!="function")return{};let s=o.getColorSchemeSelector(a);return s==="&"?i:((s.includes("data-")||s.includes("."))&&(s=`*:where(${s.replace(/\s*&$/,"")}) &`),{[s]:i})}return o.palette.mode===a?i:{}}function sr(a={},...i){const{breakpoints:o={},palette:u={},spacing:s,shape:f={},...p}=a,h=B1(o),y=x0(s);let g=Me({breakpoints:h,direction:"ltr",components:{},palette:{mode:"light",...u},spacing:y,shape:{...U1,...f}},p);return g=w1(g),g.applyStyles=Ab,g=i.reduce((b,C)=>Me(b,C),g),g.unstable_sxConfig={...cr,...p==null?void 0:p.unstable_sxConfig},g.unstable_sx=function(C){return Ta({sx:C,theme:this})},g}function Rb(a){return Object.keys(a).length===0}function E0(a=null){const i=w.useContext(rr);return!i||Rb(i)?a:i}const Ob=sr();function fr(a=Ob){return E0(a)}function Mb({styles:a,themeId:i,defaultTheme:o={}}){const u=fr(o),s=typeof a=="function"?a(i&&u[i]||u):a;return tt.jsx(g0,{styles:s})}const zb=a=>{var u;const i={systemProps:{},otherProps:{}},o=((u=a==null?void 0:a.theme)==null?void 0:u.unstable_sxConfig)??cr;return Object.keys(a).forEach(s=>{o[s]?i.systemProps[s]=a[s]:i.otherProps[s]=a[s]}),i};function eu(a){const{sx:i,...o}=a,{systemProps:u,otherProps:s}=zb(o);let f;return Array.isArray(i)?f=[u,...i]:typeof i=="function"?f=(...p)=>{const h=i(...p);return Rn(h)?{...u,...h}:u}:f={...u,...i},{...s,sx:f}}const Bh=a=>a,_b=()=>{let a=Bh;return{configure(i){a=i},generate(i){return a(i)},reset(){a=Bh}}},A0=_b();function R0(a){var i,o,u="";if(typeof a=="string"||typeof a=="number")u+=a;else if(typeof a=="object")if(Array.isArray(a)){var s=a.length;for(i=0;i<s;i++)a[i]&&(o=R0(a[i]))&&(u&&(u+=" "),u+=o)}else for(o in a)a[o]&&(u&&(u+=" "),u+=o);return u}function _t(){for(var a,i,o=0,u="",s=arguments.length;o<s;o++)(a=arguments[o])&&(i=R0(a))&&(u&&(u+=" "),u+=i);return u}function Bb(a={}){const{themeId:i,defaultTheme:o,defaultClassName:u="MuiBox-root",generateClassName:s}=a,f=y0("div",{shouldForwardProp:h=>h!=="theme"&&h!=="sx"&&h!=="as"})(Ta);return w.forwardRef(function(y,g){const b=fr(o),{className:C,component:E="div",...B}=eu(y);return tt.jsx(f,{as:E,ref:g,className:_t(C,s?s(u):u),theme:i&&b[i]||b,...B})})}const Db={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function ze(a,i,o="Mui"){const u=Db[i];return u?`${o}-${u}`:`${A0.generate(a)}-${i}`}function je(a,i,o="Mui"){const u={};return i.forEach(s=>{u[s]=ze(a,s,o)}),u}function O0(a){const{variants:i,...o}=a,u={variants:i,style:Rh(o),isProcessed:!0};return u.style===o||i&&i.forEach(s=>{typeof s.style!="function"&&(s.style=Rh(s.style))}),u}const Nb=sr();function Rs(a){return a!=="ownerState"&&a!=="theme"&&a!=="sx"&&a!=="as"}function $b(a){return a?(i,o)=>o[a]:null}function wb(a,i,o){a.theme=jb(a.theme)?o:a.theme[i]||a.theme}function $o(a,i){const o=typeof i=="function"?i(a):i;if(Array.isArray(o))return o.flatMap(u=>$o(a,u));if(Array.isArray(o==null?void 0:o.variants)){let u;if(o.isProcessed)u=o.style;else{const{variants:s,...f}=o;u=f}return M0(a,o.variants,[u])}return o!=null&&o.isProcessed?o.style:o}function M0(a,i,o=[]){var s;let u;t:for(let f=0;f<i.length;f+=1){const p=i[f];if(typeof p.props=="function"){if(u??(u={...a,...a.ownerState,ownerState:a.ownerState}),!p.props(u))continue}else for(const h in p.props)if(a[h]!==p.props[h]&&((s=a.ownerState)==null?void 0:s[h])!==p.props[h])continue t;typeof p.style=="function"?(u??(u={...a,...a.ownerState,ownerState:a.ownerState}),o.push(p.style(u))):o.push(p.style)}return o}function z0(a={}){const{themeId:i,defaultTheme:o=Nb,rootShouldForwardProp:u=Rs,slotShouldForwardProp:s=Rs}=a;function f(h){wb(h,i,o)}return(h,y={})=>{O1(h,M=>M.filter(Q=>Q!==Ta));const{name:g,slot:b,skipVariantsResolver:C,skipSx:E,overridesResolver:B=$b(Gb(b)),...R}=y,T=C!==void 0?C:b&&b!=="Root"&&b!=="root"||!1,L=E||!1;let V=Rs;b==="Root"||b==="root"?V=u:b?V=s:Hb(h)&&(V=void 0);const Z=y0(h,{shouldForwardProp:V,label:Ub(),...R}),X=M=>{if(typeof M=="function"&&M.__emotion_real!==M)return function(F){return $o(F,M)};if(Rn(M)){const Q=O0(M);return Q.variants?function(W){return $o(W,Q)}:Q.style}return M},U=(...M)=>{const Q=[],F=M.map(X),W=[];if(Q.push(f),g&&B&&W.push(function(P){var $,K;const ft=(K=($=P.theme.components)==null?void 0:$[g])==null?void 0:K.styleOverrides;if(!ft)return null;const ht={};for(const at in ft)ht[at]=$o(P,ft[at]);return B(P,ht)}),g&&!T&&W.push(function(P){var ht,$;const it=P.theme,ft=($=(ht=it==null?void 0:it.components)==null?void 0:ht[g])==null?void 0:$.variants;return ft?M0(P,ft):null}),L||W.push(Ta),Array.isArray(F[0])){const k=F.shift(),P=new Array(Q.length).fill(""),it=new Array(W.length).fill("");let ft;ft=[...P,...k,...it],ft.raw=[...P,...k.raw,...it],Q.unshift(ft)}const nt=[...Q,...F,...W],v=Z(...nt);return h.muiName&&(v.muiName=h.muiName),v};return Z.withConfig&&(U.withConfig=Z.withConfig),U}}function Ub(a,i){return void 0}function jb(a){for(const i in a)return!1;return!0}function Hb(a){return typeof a=="string"&&a.charCodeAt(0)>96}function Gb(a){return a&&a.charAt(0).toLowerCase()+a.slice(1)}const _0=z0();function er(a,i){const o={...i};for(const u in a)if(Object.prototype.hasOwnProperty.call(a,u)){const s=u;if(s==="components"||s==="slots")o[s]={...a[s],...o[s]};else if(s==="componentsProps"||s==="slotProps"){const f=a[s],p=i[s];if(!p)o[s]=f||{};else if(!f)o[s]=p;else{o[s]={...p};for(const h in f)if(Object.prototype.hasOwnProperty.call(f,h)){const y=h;o[s][y]=er(f[y],p[y])}}}else o[s]===void 0&&(o[s]=a[s])}return o}function Lb(a){const{theme:i,name:o,props:u}=a;return!i||!i.components||!i.components[o]||!i.components[o].defaultProps?u:er(i.components[o].defaultProps,u)}function B0({props:a,name:i,defaultTheme:o,themeId:u}){let s=fr(o);return u&&(s=s[u]||s),Lb({theme:s,name:i,props:a})}const D0=typeof window<"u"?w.useLayoutEffect:w.useEffect;function kb(a,i=Number.MIN_SAFE_INTEGER,o=Number.MAX_SAFE_INTEGER){return Math.max(i,Math.min(a,o))}function nf(a,i=0,o=1){return kb(a,i,o)}function qb(a){a=a.slice(1);const i=new RegExp(`.{1,${a.length>=6?2:1}}`,"g");let o=a.match(i);return o&&o[0].length===1&&(o=o.map(u=>u+u)),o?`rgb${o.length===4?"a":""}(${o.map((u,s)=>s<3?parseInt(u,16):Math.round(parseInt(u,16)/255*1e3)/1e3).join(", ")})`:""}function xa(a){if(a.type)return a;if(a.charAt(0)==="#")return xa(qb(a));const i=a.indexOf("("),o=a.substring(0,i);if(!["rgb","rgba","hsl","hsla","color"].includes(o))throw new Error(Xa(9,a));let u=a.substring(i+1,a.length-1),s;if(o==="color"){if(u=u.split(" "),s=u.shift(),u.length===4&&u[3].charAt(0)==="/"&&(u[3]=u[3].slice(1)),!["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].includes(s))throw new Error(Xa(10,s))}else u=u.split(",");return u=u.map(f=>parseFloat(f)),{type:o,values:u,colorSpace:s}}const Yb=a=>{const i=xa(a);return i.values.slice(0,3).map((o,u)=>i.type.includes("hsl")&&u!==0?`${o}%`:o).join(" ")},Ki=(a,i)=>{try{return Yb(a)}catch{return a}};function nu(a){const{type:i,colorSpace:o}=a;let{values:u}=a;return i.includes("rgb")?u=u.map((s,f)=>f<3?parseInt(s,10):s):i.includes("hsl")&&(u[1]=`${u[1]}%`,u[2]=`${u[2]}%`),i.includes("color")?u=`${o} ${u.join(" ")}`:u=`${u.join(", ")}`,`${i}(${u})`}function N0(a){a=xa(a);const{values:i}=a,o=i[0],u=i[1]/100,s=i[2]/100,f=u*Math.min(s,1-s),p=(g,b=(g+o/30)%12)=>s-f*Math.max(Math.min(b-3,9-b,1),-1);let h="rgb";const y=[Math.round(p(0)*255),Math.round(p(8)*255),Math.round(p(4)*255)];return a.type==="hsla"&&(h+="a",y.push(i[3])),nu({type:h,values:y})}function ws(a){a=xa(a);let i=a.type==="hsl"||a.type==="hsla"?xa(N0(a)).values:a.values;return i=i.map(o=>(a.type!=="color"&&(o/=255),o<=.03928?o/12.92:((o+.055)/1.055)**2.4)),Number((.2126*i[0]+.7152*i[1]+.0722*i[2]).toFixed(3))}function Vb(a,i){const o=ws(a),u=ws(i);return(Math.max(o,u)+.05)/(Math.min(o,u)+.05)}function te(a,i){return a=xa(a),i=nf(i),(a.type==="rgb"||a.type==="hsl")&&(a.type+="a"),a.type==="color"?a.values[3]=`/${i}`:a.values[3]=i,nu(a)}function Mo(a,i,o){try{return te(a,i)}catch{return a}}function nr(a,i){if(a=xa(a),i=nf(i),a.type.includes("hsl"))a.values[2]*=1-i;else if(a.type.includes("rgb")||a.type.includes("color"))for(let o=0;o<3;o+=1)a.values[o]*=1-i;return nu(a)}function Gt(a,i,o){try{return nr(a,i)}catch{return a}}function ar(a,i){if(a=xa(a),i=nf(i),a.type.includes("hsl"))a.values[2]+=(100-a.values[2])*i;else if(a.type.includes("rgb"))for(let o=0;o<3;o+=1)a.values[o]+=(255-a.values[o])*i;else if(a.type.includes("color"))for(let o=0;o<3;o+=1)a.values[o]+=(1-a.values[o])*i;return nu(a)}function Lt(a,i,o){try{return ar(a,i)}catch{return a}}function Xb(a,i=.15){return ws(a)>.5?nr(a,i):ar(a,i)}function zo(a,i,o){try{return Xb(a,i)}catch{return a}}const $0=w.createContext(null);function af(){return w.useContext($0)}const Qb=typeof Symbol=="function"&&Symbol.for,Zb=Qb?Symbol.for("mui.nested"):"__THEME_NESTED__";function Kb(a,i){return typeof i=="function"?i(a):{...a,...i}}function Jb(a){const{children:i,theme:o}=a,u=af(),s=w.useMemo(()=>{const f=u===null?{...o}:Kb(u,o);return f!=null&&(f[Zb]=u!==null),f},[o,u]);return tt.jsx($0.Provider,{value:s,children:i})}const w0=w.createContext();function Wb({value:a,...i}){return tt.jsx(w0.Provider,{value:a??!0,...i})}const cT=()=>w.useContext(w0)??!1,U0=w.createContext(void 0);function Pb({value:a,children:i}){return tt.jsx(U0.Provider,{value:a,children:i})}function Fb(a){const{theme:i,name:o,props:u}=a;if(!i||!i.components||!i.components[o])return u;const s=i.components[o];return s.defaultProps?er(s.defaultProps,u):!s.styleOverrides&&!s.variants?er(s,u):u}function Ib({props:a,name:i}){const o=w.useContext(U0);return Fb({props:a,name:i,theme:{components:o}})}const Dh={};function Nh(a,i,o,u=!1){return w.useMemo(()=>{const s=a&&i[a]||i;if(typeof o=="function"){const f=o(s),p=a?{...i,[a]:f}:f;return u?()=>p:p}return a?{...i,[a]:o}:{...i,...o}},[a,i,o,u])}function j0(a){const{children:i,theme:o,themeId:u}=a,s=E0(Dh),f=af()||Dh,p=Nh(u,s,o),h=Nh(u,f,o,!0),y=(u?p[u]:p).direction==="rtl";return tt.jsx(Jb,{theme:h,children:tt.jsx(rr.Provider,{value:p,children:tt.jsx(Wb,{value:y,children:tt.jsx(Pb,{value:u?p[u].components:p.components,children:i})})})})}const $h={theme:void 0};function tS(a){let i,o;return function(s){let f=i;return(f===void 0||s.theme!==o)&&($h.theme=s.theme,f=O0(a($h)),i=f,o=s.theme),f}}const lf="mode",rf="color-scheme",eS="data-color-scheme";function nS(a){const{defaultMode:i="system",defaultLightColorScheme:o="light",defaultDarkColorScheme:u="dark",modeStorageKey:s=lf,colorSchemeStorageKey:f=rf,attribute:p=eS,colorSchemeNode:h="document.documentElement",nonce:y}=a||{};let g="",b=p;if(p==="class"&&(b=".%s"),p==="data"&&(b="[data-%s]"),b.startsWith(".")){const E=b.substring(1);g+=`${h}.classList.remove('${E}'.replace('%s', light), '${E}'.replace('%s', dark));
      ${h}.classList.add('${E}'.replace('%s', colorScheme));`}const C=b.match(/\[([^\]]+)\]/);if(C){const[E,B]=C[1].split("=");B||(g+=`${h}.removeAttribute('${E}'.replace('%s', light));
      ${h}.removeAttribute('${E}'.replace('%s', dark));`),g+=`
      ${h}.setAttribute('${E}'.replace('%s', colorScheme), ${B?`${B}.replace('%s', colorScheme)`:'""'});`}else g+=`${h}.setAttribute('${b}', colorScheme);`;return tt.jsx("script",{suppressHydrationWarning:!0,nonce:typeof window>"u"?y:"",dangerouslySetInnerHTML:{__html:`(function() {
try {
  let colorScheme = '';
  const mode = localStorage.getItem('${s}') || '${i}';
  const dark = localStorage.getItem('${f}-dark') || '${u}';
  const light = localStorage.getItem('${f}-light') || '${o}';
  if (mode === 'system') {
    // handle system mode
    const mql = window.matchMedia('(prefers-color-scheme: dark)');
    if (mql.matches) {
      colorScheme = dark
    } else {
      colorScheme = light
    }
  }
  if (mode === 'light') {
    colorScheme = light;
  }
  if (mode === 'dark') {
    colorScheme = dark;
  }
  if (colorScheme) {
    ${g}
  }
} catch(e){}})();`}},"mui-color-scheme-init")}function aS(){}const lS=({key:a,storageWindow:i})=>(!i&&typeof window<"u"&&(i=window),{get(o){if(typeof window>"u")return;if(!i)return o;let u;try{u=i.localStorage.getItem(a)}catch{}return u||o},set:o=>{if(i)try{i.localStorage.setItem(a,o)}catch{}},subscribe:o=>{if(!i)return aS;const u=s=>{const f=s.newValue;s.key===a&&o(f)};return i.addEventListener("storage",u),()=>{i.removeEventListener("storage",u)}}});function Os(){}function wh(a){if(typeof window<"u"&&typeof window.matchMedia=="function"&&a==="system")return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}function H0(a,i){if(a.mode==="light"||a.mode==="system"&&a.systemMode==="light")return i("light");if(a.mode==="dark"||a.mode==="system"&&a.systemMode==="dark")return i("dark")}function iS(a){return H0(a,i=>{if(i==="light")return a.lightColorScheme;if(i==="dark")return a.darkColorScheme})}function rS(a){const{defaultMode:i="light",defaultLightColorScheme:o,defaultDarkColorScheme:u,supportedColorSchemes:s=[],modeStorageKey:f=lf,colorSchemeStorageKey:p=rf,storageWindow:h=typeof window>"u"?void 0:window,storageManager:y=lS,noSsr:g=!1}=a,b=s.join(","),C=s.length>1,E=w.useMemo(()=>y==null?void 0:y({key:f,storageWindow:h}),[y,f,h]),B=w.useMemo(()=>y==null?void 0:y({key:`${p}-light`,storageWindow:h}),[y,p,h]),R=w.useMemo(()=>y==null?void 0:y({key:`${p}-dark`,storageWindow:h}),[y,p,h]),[T,L]=w.useState(()=>{const W=(E==null?void 0:E.get(i))||i,nt=(B==null?void 0:B.get(o))||o,v=(R==null?void 0:R.get(u))||u;return{mode:W,systemMode:wh(W),lightColorScheme:nt,darkColorScheme:v}}),[V,Z]=w.useState(g||!C);w.useEffect(()=>{Z(!0)},[]);const X=iS(T),U=w.useCallback(W=>{L(nt=>{if(W===nt.mode)return nt;const v=W??i;return E==null||E.set(v),{...nt,mode:v,systemMode:wh(v)}})},[E,i]),M=w.useCallback(W=>{W?typeof W=="string"?W&&!b.includes(W)?console.error(`\`${W}\` does not exist in \`theme.colorSchemes\`.`):L(nt=>{const v={...nt};return H0(nt,k=>{k==="light"&&(B==null||B.set(W),v.lightColorScheme=W),k==="dark"&&(R==null||R.set(W),v.darkColorScheme=W)}),v}):L(nt=>{const v={...nt},k=W.light===null?o:W.light,P=W.dark===null?u:W.dark;return k&&(b.includes(k)?(v.lightColorScheme=k,B==null||B.set(k)):console.error(`\`${k}\` does not exist in \`theme.colorSchemes\`.`)),P&&(b.includes(P)?(v.darkColorScheme=P,R==null||R.set(P)):console.error(`\`${P}\` does not exist in \`theme.colorSchemes\`.`)),v}):L(nt=>(B==null||B.set(o),R==null||R.set(u),{...nt,lightColorScheme:o,darkColorScheme:u}))},[b,B,R,o,u]),Q=w.useCallback(W=>{T.mode==="system"&&L(nt=>{const v=W!=null&&W.matches?"dark":"light";return nt.systemMode===v?nt:{...nt,systemMode:v}})},[T.mode]),F=w.useRef(Q);return F.current=Q,w.useEffect(()=>{if(typeof window.matchMedia!="function"||!C)return;const W=(...v)=>F.current(...v),nt=window.matchMedia("(prefers-color-scheme: dark)");return nt.addListener(W),W(nt),()=>{nt.removeListener(W)}},[C]),w.useEffect(()=>{if(C){const W=(E==null?void 0:E.subscribe(k=>{(!k||["light","dark","system"].includes(k))&&U(k||i)}))||Os,nt=(B==null?void 0:B.subscribe(k=>{(!k||b.match(k))&&M({light:k})}))||Os,v=(R==null?void 0:R.subscribe(k=>{(!k||b.match(k))&&M({dark:k})}))||Os;return()=>{W(),nt(),v()}}},[M,U,b,i,h,C,E,B,R]),{...T,mode:V?T.mode:void 0,systemMode:V?T.systemMode:void 0,colorScheme:V?X:void 0,setMode:U,setColorScheme:M}}const oS="*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function uS(a){const{themeId:i,theme:o={},modeStorageKey:u=lf,colorSchemeStorageKey:s=rf,disableTransitionOnChange:f=!1,defaultColorScheme:p,resolveTheme:h}=a,y={allColorSchemes:[],colorScheme:void 0,darkColorScheme:void 0,lightColorScheme:void 0,mode:void 0,setColorScheme:()=>{},setMode:()=>{},systemMode:void 0},g=w.createContext(void 0),b=()=>w.useContext(g)||y,C={},E={};function B(V){var Pn,ye,sn,ve;const{children:Z,theme:X,modeStorageKey:U=u,colorSchemeStorageKey:M=s,disableTransitionOnChange:Q=f,storageManager:F,storageWindow:W=typeof window>"u"?void 0:window,documentNode:nt=typeof document>"u"?void 0:document,colorSchemeNode:v=typeof document>"u"?void 0:document.documentElement,disableNestedContext:k=!1,disableStyleSheetGeneration:P=!1,defaultMode:it="system",forceThemeRerender:ft=!1,noSsr:ht}=V,$=w.useRef(!1),K=af(),at=w.useContext(g),lt=!!at&&!k,x=w.useMemo(()=>X||(typeof o=="function"?o():o),[X]),H=x[i],J=H||x,{colorSchemes:I=C,components:ut=E,cssVarPrefix:bt}=J,dt=Object.keys(I).filter(be=>!!I[be]).join(","),Zt=w.useMemo(()=>dt.split(","),[dt]),Bt=typeof p=="string"?p:p.light,Ge=typeof p=="string"?p:p.dark,Aa=I[Bt]&&I[Ge]?it:((ye=(Pn=I[J.defaultColorScheme])==null?void 0:Pn.palette)==null?void 0:ye.mode)||((sn=J.palette)==null?void 0:sn.mode),{mode:Kn,setMode:Jn,systemMode:Wn,lightColorScheme:gn,darkColorScheme:Za,colorScheme:Ql,setColorScheme:oe}=rS({supportedColorSchemes:Zt,defaultLightColorScheme:Bt,defaultDarkColorScheme:Ge,modeStorageKey:U,colorSchemeStorageKey:M,defaultMode:Aa,storageManager:F,storageWindow:W,noSsr:ht});let cn=Kn,ge=Ql;lt&&(cn=at.mode,ge=at.colorScheme);let yn=ge||J.defaultColorScheme;J.vars&&!ft&&(yn=J.defaultColorScheme);const Je=w.useMemo(()=>{var zn;const be=((zn=J.generateThemeVars)==null?void 0:zn.call(J))||J.vars,Xt={...J,components:ut,colorSchemes:I,cssVarPrefix:bt,vars:be};if(typeof Xt.generateSpacing=="function"&&(Xt.spacing=Xt.generateSpacing()),yn){const Re=I[yn];Re&&typeof Re=="object"&&Object.keys(Re).forEach(Se=>{Re[Se]&&typeof Re[Se]=="object"?Xt[Se]={...Xt[Se],...Re[Se]}:Xt[Se]=Re[Se]})}return h?h(Xt):Xt},[J,yn,ut,I,bt]),mt=J.colorSchemeSelector;D0(()=>{if(ge&&v&&mt&&mt!=="media"){const be=mt;let Xt=mt;if(be==="class"&&(Xt=".%s"),be==="data"&&(Xt="[data-%s]"),be!=null&&be.startsWith("data-")&&!be.includes("%s")&&(Xt=`[${be}="%s"]`),Xt.startsWith("."))v.classList.remove(...Zt.map(zn=>Xt.substring(1).replace("%s",zn))),v.classList.add(Xt.substring(1).replace("%s",ge));else{const zn=Xt.replace("%s",ge).match(/\[([^\]]+)\]/);if(zn){const[Re,Se]=zn[1].split("=");Se||Zt.forEach(_n=>{v.removeAttribute(Re.replace(ge,_n))}),v.setAttribute(Re,Se?Se.replace(/"|'/g,""):"")}else v.setAttribute(Xt,ge)}}},[ge,mt,v,Zt]),w.useEffect(()=>{let be;if(Q&&$.current&&nt){const Xt=nt.createElement("style");Xt.appendChild(nt.createTextNode(oS)),nt.head.appendChild(Xt),window.getComputedStyle(nt.body),be=setTimeout(()=>{nt.head.removeChild(Xt)},1)}return()=>{clearTimeout(be)}},[ge,Q,nt]),w.useEffect(()=>($.current=!0,()=>{$.current=!1}),[]);const dr=w.useMemo(()=>({allColorSchemes:Zt,colorScheme:ge,darkColorScheme:Za,lightColorScheme:gn,mode:cn,setColorScheme:oe,setMode:Jn,systemMode:Wn}),[Zt,ge,Za,gn,cn,oe,Jn,Wn,Je.colorSchemeSelector]);let pr=!0;(P||J.cssVariables===!1||lt&&(K==null?void 0:K.cssVarPrefix)===bt)&&(pr=!1);const mr=tt.jsxs(w.Fragment,{children:[tt.jsx(j0,{themeId:H?i:void 0,theme:Je,children:Z}),pr&&tt.jsx(g0,{styles:((ve=Je.generateStyleSheets)==null?void 0:ve.call(Je))||[]})]});return lt?mr:tt.jsx(g.Provider,{value:dr,children:mr})}const R=typeof p=="string"?p:p.light,T=typeof p=="string"?p:p.dark;return{CssVarsProvider:B,useColorScheme:b,getInitColorSchemeScript:V=>nS({colorSchemeStorageKey:s,defaultLightColorScheme:R,defaultDarkColorScheme:T,modeStorageKey:u,...V})}}function cS(a=""){function i(...u){if(!u.length)return"";const s=u[0];return typeof s=="string"&&!s.match(/(#|\(|\)|(-?(\d*\.)?\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\d*\.)?\d+)$|(\d+ \d+ \d+)/)?`, var(--${a?`${a}-`:""}${s}${i(...u.slice(1))})`:`, ${s}`}return(u,...s)=>`var(--${a?`${a}-`:""}${u}${i(...s)})`}const Uh=(a,i,o,u=[])=>{let s=a;i.forEach((f,p)=>{p===i.length-1?Array.isArray(s)?s[Number(f)]=o:s&&typeof s=="object"&&(s[f]=o):s&&typeof s=="object"&&(s[f]||(s[f]=u.includes(f)?[]:{}),s=s[f])})},sS=(a,i,o)=>{function u(s,f=[],p=[]){Object.entries(s).forEach(([h,y])=>{(!o||o&&!o([...f,h]))&&y!=null&&(typeof y=="object"&&Object.keys(y).length>0?u(y,[...f,h],Array.isArray(y)?[...p,h]:p):i([...f,h],y,p))})}u(a)},fS=(a,i)=>typeof i=="number"?["lineHeight","fontWeight","opacity","zIndex"].some(u=>a.includes(u))||a[a.length-1].toLowerCase().includes("opacity")?i:`${i}px`:i;function Ms(a,i){const{prefix:o,shouldSkipGeneratingVar:u}=i||{},s={},f={},p={};return sS(a,(h,y,g)=>{if((typeof y=="string"||typeof y=="number")&&(!u||!u(h,y))){const b=`--${o?`${o}-`:""}${h.join("-")}`,C=fS(h,y);Object.assign(s,{[b]:C}),Uh(f,h,`var(${b})`,g),Uh(p,h,`var(${b}, ${C})`,g)}},h=>h[0]==="vars"),{css:s,vars:f,varsWithDefaults:p}}function dS(a,i={}){const{getSelector:o=L,disableCssColorScheme:u,colorSchemeSelector:s}=i,{colorSchemes:f={},components:p,defaultColorScheme:h="light",...y}=a,{vars:g,css:b,varsWithDefaults:C}=Ms(y,i);let E=C;const B={},{[h]:R,...T}=f;if(Object.entries(T||{}).forEach(([X,U])=>{const{vars:M,css:Q,varsWithDefaults:F}=Ms(U,i);E=Me(E,F),B[X]={css:Q,vars:M}}),R){const{css:X,vars:U,varsWithDefaults:M}=Ms(R,i);E=Me(E,M),B[h]={css:X,vars:U}}function L(X,U){var Q,F;let M=s;if(s==="class"&&(M=".%s"),s==="data"&&(M="[data-%s]"),s!=null&&s.startsWith("data-")&&!s.includes("%s")&&(M=`[${s}="%s"]`),X){if(M==="media")return a.defaultColorScheme===X?":root":{[`@media (prefers-color-scheme: ${((F=(Q=f[X])==null?void 0:Q.palette)==null?void 0:F.mode)||X})`]:{":root":U}};if(M)return a.defaultColorScheme===X?`:root, ${M.replace("%s",String(X))}`:M.replace("%s",String(X))}return":root"}return{vars:E,generateThemeVars:()=>{let X={...g};return Object.entries(B).forEach(([,{vars:U}])=>{X=Me(X,U)}),X},generateStyleSheets:()=>{var W,nt;const X=[],U=a.defaultColorScheme||"light";function M(v,k){Object.keys(k).length&&X.push(typeof v=="string"?{[v]:{...k}}:v)}M(o(void 0,{...b}),b);const{[U]:Q,...F}=B;if(Q){const{css:v}=Q,k=(nt=(W=f[U])==null?void 0:W.palette)==null?void 0:nt.mode,P=!u&&k?{colorScheme:k,...v}:{...v};M(o(U,{...P}),P)}return Object.entries(F).forEach(([v,{css:k}])=>{var ft,ht;const P=(ht=(ft=f[v])==null?void 0:ft.palette)==null?void 0:ht.mode,it=!u&&P?{colorScheme:P,...k}:{...k};M(o(v,{...it}),it)}),X}}}function pS(a){return function(o){return a==="media"?`@media (prefers-color-scheme: ${o})`:a?a.startsWith("data-")&&!a.includes("%s")?`[${a}="${o}"] &`:a==="class"?`.${o} &`:a==="data"?`[data-${o}] &`:`${a.replace("%s",o)} &`:"&"}}function He(a,i,o=void 0){const u={};for(const s in a){const f=a[s];let p="",h=!0;for(let y=0;y<f.length;y+=1){const g=f[y];g&&(p+=(h===!0?"":" ")+i(g),h=!1,o&&o[g]&&(p+=" "+o[g]))}u[s]=p}return u}function mS(a,i){var o,u,s;return w.isValidElement(a)&&i.indexOf(a.type.muiName??((s=(u=(o=a.type)==null?void 0:o._payload)==null?void 0:u.value)==null?void 0:s.muiName))!==-1}const hS=(a,i)=>a.filter(o=>i.includes(o)),Xl=(a,i,o)=>{const u=a.keys[0];Array.isArray(i)?i.forEach((s,f)=>{o((p,h)=>{f<=a.keys.length-1&&(f===0?Object.assign(p,h):p[a.up(a.keys[f])]=h)},s)}):i&&typeof i=="object"?(Object.keys(i).length>a.keys.length?a.keys:hS(a.keys,Object.keys(i))).forEach(f=>{if(a.keys.includes(f)){const p=i[f];p!==void 0&&o((h,y)=>{u===f?Object.assign(h,y):h[a.up(f)]=y},p)}}):(typeof i=="number"||typeof i=="string")&&o((s,f)=>{Object.assign(s,f)},i)};function Lo(a){return`--Grid-${a}Spacing`}function au(a){return`--Grid-parent-${a}Spacing`}const jh="--Grid-columns",ql="--Grid-parent-columns",gS=({theme:a,ownerState:i})=>{const o={};return Xl(a.breakpoints,i.size,(u,s)=>{let f={};s==="grow"&&(f={flexBasis:0,flexGrow:1,maxWidth:"100%"}),s==="auto"&&(f={flexBasis:"auto",flexGrow:0,flexShrink:0,maxWidth:"none",width:"auto"}),typeof s=="number"&&(f={flexGrow:0,flexBasis:"auto",width:`calc(100% * ${s} / var(${ql}) - (var(${ql}) - ${s}) * (var(${au("column")}) / var(${ql})))`}),u(o,f)}),o},yS=({theme:a,ownerState:i})=>{const o={};return Xl(a.breakpoints,i.offset,(u,s)=>{let f={};s==="auto"&&(f={marginLeft:"auto"}),typeof s=="number"&&(f={marginLeft:s===0?"0px":`calc(100% * ${s} / var(${ql}) + var(${au("column")}) * ${s} / var(${ql}))`}),u(o,f)}),o},vS=({theme:a,ownerState:i})=>{if(!i.container)return{};const o={[jh]:12};return Xl(a.breakpoints,i.columns,(u,s)=>{const f=s??12;u(o,{[jh]:f,"> *":{[ql]:f}})}),o},bS=({theme:a,ownerState:i})=>{if(!i.container)return{};const o={};return Xl(a.breakpoints,i.rowSpacing,(u,s)=>{var p;const f=typeof s=="string"?s:(p=a.spacing)==null?void 0:p.call(a,s);u(o,{[Lo("row")]:f,"> *":{[au("row")]:f}})}),o},SS=({theme:a,ownerState:i})=>{if(!i.container)return{};const o={};return Xl(a.breakpoints,i.columnSpacing,(u,s)=>{var p;const f=typeof s=="string"?s:(p=a.spacing)==null?void 0:p.call(a,s);u(o,{[Lo("column")]:f,"> *":{[au("column")]:f}})}),o},CS=({theme:a,ownerState:i})=>{if(!i.container)return{};const o={};return Xl(a.breakpoints,i.direction,(u,s)=>{u(o,{flexDirection:s})}),o},TS=({ownerState:a})=>({minWidth:0,boxSizing:"border-box",...a.container&&{display:"flex",flexWrap:"wrap",...a.wrap&&a.wrap!=="wrap"&&{flexWrap:a.wrap},gap:`var(${Lo("row")}) var(${Lo("column")})`}}),xS=a=>{const i=[];return Object.entries(a).forEach(([o,u])=>{u!==!1&&u!==void 0&&i.push(`grid-${o}-${String(u)}`)}),i},ES=(a,i="xs")=>{function o(u){return u===void 0?!1:typeof u=="string"&&!Number.isNaN(Number(u))||typeof u=="number"&&u>0}if(o(a))return[`spacing-${i}-${String(a)}`];if(typeof a=="object"&&!Array.isArray(a)){const u=[];return Object.entries(a).forEach(([s,f])=>{o(f)&&u.push(`spacing-${s}-${String(f)}`)}),u}return[]},AS=a=>a===void 0?[]:typeof a=="object"?Object.entries(a).map(([i,o])=>`direction-${i}-${o}`):[`direction-xs-${String(a)}`];function RS(a,i){a.item!==void 0&&delete a.item,a.zeroMinWidth!==void 0&&delete a.zeroMinWidth,i.keys.forEach(o=>{a[o]!==void 0&&delete a[o]})}const OS=sr(),MS=_0("div",{name:"MuiGrid",slot:"Root"});function zS(a){return B0({props:a,name:"MuiGrid",defaultTheme:OS})}function _S(a={}){const{createStyledComponent:i=MS,useThemeProps:o=zS,useTheme:u=fr,componentName:s="MuiGrid"}=a,f=(g,b)=>{const{container:C,direction:E,spacing:B,wrap:R,size:T}=g,L={root:["root",C&&"container",R!=="wrap"&&`wrap-xs-${String(R)}`,...AS(E),...xS(T),...C?ES(B,b.breakpoints.keys[0]):[]]};return He(L,V=>ze(s,V),{})};function p(g,b,C=()=>!0){const E={};return g===null||(Array.isArray(g)?g.forEach((B,R)=>{B!==null&&C(B)&&b.keys[R]&&(E[b.keys[R]]=B)}):typeof g=="object"?Object.keys(g).forEach(B=>{const R=g[B];R!=null&&C(R)&&(E[B]=R)}):E[b.keys[0]]=g),E}const h=i(vS,SS,bS,gS,CS,TS,yS),y=w.forwardRef(function(b,C){const E=u(),B=o(b),R=eu(B);RS(R,E.breakpoints);const{className:T,children:L,columns:V=12,container:Z=!1,component:X="div",direction:U="row",wrap:M="wrap",size:Q={},offset:F={},spacing:W=0,rowSpacing:nt=W,columnSpacing:v=W,unstable_level:k=0,...P}=R,it=p(Q,E.breakpoints,H=>H!==!1),ft=p(F,E.breakpoints),ht=b.columns??(k?void 0:V),$=b.spacing??(k?void 0:W),K=b.rowSpacing??b.spacing??(k?void 0:nt),at=b.columnSpacing??b.spacing??(k?void 0:v),lt={...R,level:k,columns:ht,container:Z,direction:U,wrap:M,spacing:$,rowSpacing:K,columnSpacing:at,size:it,offset:ft},x=f(lt,E);return tt.jsx(h,{ref:C,as:X,ownerState:lt,className:_t(x.root,T),...P,children:w.Children.map(L,H=>{var J;return w.isValidElement(H)&&mS(H,["Grid"])&&Z&&H.props.container?w.cloneElement(H,{unstable_level:((J=H.props)==null?void 0:J.unstable_level)??k+1}):H})})});return y.muiName="Grid",y}const BS=sr(),DS=_0("div",{name:"MuiStack",slot:"Root"});function NS(a){return B0({props:a,name:"MuiStack",defaultTheme:BS})}function $S(a,i){const o=w.Children.toArray(a).filter(Boolean);return o.reduce((u,s,f)=>(u.push(s),f<o.length-1&&u.push(w.cloneElement(i,{key:`separator-${f}`})),u),[])}const wS=a=>({row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"})[a],US=({ownerState:a,theme:i})=>{let o={display:"flex",flexDirection:"column",...mn({theme:i},As({values:a.direction,breakpoints:i.breakpoints.values}),u=>({flexDirection:u}))};if(a.spacing){const u=Jo(i),s=Object.keys(i.breakpoints.values).reduce((y,g)=>((typeof a.spacing=="object"&&a.spacing[g]!=null||typeof a.direction=="object"&&a.direction[g]!=null)&&(y[g]=!0),y),{}),f=As({values:a.direction,base:s}),p=As({values:a.spacing,base:s});typeof f=="object"&&Object.keys(f).forEach((y,g,b)=>{if(!f[y]){const E=g>0?f[b[g-1]]:"column";f[y]=E}}),o=Me(o,mn({theme:i},p,(y,g)=>a.useFlexGap?{gap:Qa(u,y)}:{"& > :not(style):not(style)":{margin:0},"& > :not(style) ~ :not(style)":{[`margin${wS(g?f[g]:a.direction)}`]:Qa(u,y)}}))}return o=H1(i.breakpoints,o),o};function jS(a={}){const{createStyledComponent:i=DS,useThemeProps:o=NS,componentName:u="MuiStack"}=a,s=()=>He({root:["root"]},y=>ze(u,y),{}),f=i(US);return w.forwardRef(function(y,g){const b=o(y),C=eu(b),{component:E="div",direction:B="column",spacing:R=0,divider:T,children:L,className:V,useFlexGap:Z=!1,...X}=C,U={direction:B,spacing:R,useFlexGap:Z},M=s();return tt.jsx(f,{as:E,ownerState:U,ref:g,className:_t(M.root,V),...X,children:T?$S(L,T):L})})}function G0(){return{text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:Pi.white,default:Pi.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}}}const HS=G0();function L0(){return{text:{primary:Pi.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:Pi.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}}}const Hh=L0();function Gh(a,i,o,u){const s=u.light||u,f=u.dark||u*1.5;a[i]||(a.hasOwnProperty(o)?a[i]=a[o]:i==="light"?a.light=ar(a.main,s):i==="dark"&&(a.dark=nr(a.main,f)))}function GS(a="light"){return a==="dark"?{main:wl[200],light:wl[50],dark:wl[400]}:{main:wl[700],light:wl[400],dark:wl[800]}}function LS(a="light"){return a==="dark"?{main:$l[200],light:$l[50],dark:$l[400]}:{main:$l[500],light:$l[300],dark:$l[700]}}function kS(a="light"){return a==="dark"?{main:Nl[500],light:Nl[300],dark:Nl[700]}:{main:Nl[700],light:Nl[400],dark:Nl[800]}}function qS(a="light"){return a==="dark"?{main:Ul[400],light:Ul[300],dark:Ul[700]}:{main:Ul[700],light:Ul[500],dark:Ul[900]}}function YS(a="light"){return a==="dark"?{main:jl[400],light:jl[300],dark:jl[700]}:{main:jl[800],light:jl[500],dark:jl[900]}}function VS(a="light"){return a==="dark"?{main:Qi[400],light:Qi[300],dark:Qi[700]}:{main:"#ed6c02",light:Qi[500],dark:Qi[900]}}function of(a){const{mode:i="light",contrastThreshold:o=3,tonalOffset:u=.2,...s}=a,f=a.primary||GS(i),p=a.secondary||LS(i),h=a.error||kS(i),y=a.info||qS(i),g=a.success||YS(i),b=a.warning||VS(i);function C(T){return Vb(T,Hh.text.primary)>=o?Hh.text.primary:HS.text.primary}const E=({color:T,name:L,mainShade:V=500,lightShade:Z=300,darkShade:X=700})=>{if(T={...T},!T.main&&T[V]&&(T.main=T[V]),!T.hasOwnProperty("main"))throw new Error(Xa(11,L?` (${L})`:"",V));if(typeof T.main!="string")throw new Error(Xa(12,L?` (${L})`:"",JSON.stringify(T.main)));return Gh(T,"light",Z,u),Gh(T,"dark",X,u),T.contrastText||(T.contrastText=C(T.main)),T};let B;return i==="light"?B=G0():i==="dark"&&(B=L0()),Me({common:{...Pi},mode:i,primary:E({color:f,name:"primary"}),secondary:E({color:p,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:E({color:h,name:"error"}),warning:E({color:b,name:"warning"}),info:E({color:y,name:"info"}),success:E({color:g,name:"success"}),grey:Mv,contrastThreshold:o,getContrastText:C,augmentColor:E,tonalOffset:u,...B},s)}function XS(a){const i={};return Object.entries(a).forEach(u=>{const[s,f]=u;typeof f=="object"&&(i[s]=`${f.fontStyle?`${f.fontStyle} `:""}${f.fontVariant?`${f.fontVariant} `:""}${f.fontWeight?`${f.fontWeight} `:""}${f.fontStretch?`${f.fontStretch} `:""}${f.fontSize||""}${f.lineHeight?`/${f.lineHeight} `:""}${f.fontFamily||""}`)}),i}function QS(a,i){return{toolbar:{minHeight:56,[a.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[a.up("sm")]:{minHeight:64}},...i}}function ZS(a){return Math.round(a*1e5)/1e5}const Lh={textTransform:"uppercase"},kh='"Roboto", "Helvetica", "Arial", sans-serif';function k0(a,i){const{fontFamily:o=kh,fontSize:u=14,fontWeightLight:s=300,fontWeightRegular:f=400,fontWeightMedium:p=500,fontWeightBold:h=700,htmlFontSize:y=16,allVariants:g,pxToRem:b,...C}=typeof i=="function"?i(a):i,E=u/14,B=b||(L=>`${L/y*E}rem`),R=(L,V,Z,X,U)=>({fontFamily:o,fontWeight:L,fontSize:B(V),lineHeight:Z,...o===kh?{letterSpacing:`${ZS(X/V)}em`}:{},...U,...g}),T={h1:R(s,96,1.167,-1.5),h2:R(s,60,1.2,-.5),h3:R(f,48,1.167,0),h4:R(f,34,1.235,.25),h5:R(f,24,1.334,0),h6:R(p,20,1.6,.15),subtitle1:R(f,16,1.75,.15),subtitle2:R(p,14,1.57,.1),body1:R(f,16,1.5,.15),body2:R(f,14,1.43,.15),button:R(p,14,1.75,.4,Lh),caption:R(f,12,1.66,.4),overline:R(f,12,2.66,1,Lh),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return Me({htmlFontSize:y,pxToRem:B,fontFamily:o,fontSize:u,fontWeightLight:s,fontWeightRegular:f,fontWeightMedium:p,fontWeightBold:h,...T},C,{clone:!1})}const KS=.2,JS=.14,WS=.12;function Vt(...a){return[`${a[0]}px ${a[1]}px ${a[2]}px ${a[3]}px rgba(0,0,0,${KS})`,`${a[4]}px ${a[5]}px ${a[6]}px ${a[7]}px rgba(0,0,0,${JS})`,`${a[8]}px ${a[9]}px ${a[10]}px ${a[11]}px rgba(0,0,0,${WS})`].join(",")}const PS=["none",Vt(0,2,1,-1,0,1,1,0,0,1,3,0),Vt(0,3,1,-2,0,2,2,0,0,1,5,0),Vt(0,3,3,-2,0,3,4,0,0,1,8,0),Vt(0,2,4,-1,0,4,5,0,0,1,10,0),Vt(0,3,5,-1,0,5,8,0,0,1,14,0),Vt(0,3,5,-1,0,6,10,0,0,1,18,0),Vt(0,4,5,-2,0,7,10,1,0,2,16,1),Vt(0,5,5,-3,0,8,10,1,0,3,14,2),Vt(0,5,6,-3,0,9,12,1,0,3,16,2),Vt(0,6,6,-3,0,10,14,1,0,4,18,3),Vt(0,6,7,-4,0,11,15,1,0,4,20,3),Vt(0,7,8,-4,0,12,17,2,0,5,22,4),Vt(0,7,8,-4,0,13,19,2,0,5,24,4),Vt(0,7,9,-4,0,14,21,2,0,5,26,4),Vt(0,8,9,-5,0,15,22,2,0,6,28,5),Vt(0,8,10,-5,0,16,24,2,0,6,30,5),Vt(0,8,11,-5,0,17,26,2,0,6,32,5),Vt(0,9,11,-5,0,18,28,2,0,7,34,6),Vt(0,9,12,-6,0,19,29,2,0,7,36,6),Vt(0,10,13,-6,0,20,31,3,0,8,38,7),Vt(0,10,13,-6,0,21,33,3,0,8,40,7),Vt(0,10,14,-6,0,22,35,3,0,8,42,7),Vt(0,11,14,-7,0,23,36,3,0,9,44,8),Vt(0,11,15,-7,0,24,38,3,0,9,46,8)],FS={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},IS={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function qh(a){return`${Math.round(a)}ms`}function t2(a){if(!a)return 0;const i=a/36;return Math.min(Math.round((4+15*i**.25+i/5)*10),3e3)}function e2(a){const i={...FS,...a.easing},o={...IS,...a.duration};return{getAutoHeightDuration:t2,create:(s=["all"],f={})=>{const{duration:p=o.standard,easing:h=i.easeInOut,delay:y=0,...g}=f;return(Array.isArray(s)?s:[s]).map(b=>`${b} ${typeof p=="string"?p:qh(p)} ${h} ${typeof y=="string"?y:qh(y)}`).join(",")},...a,easing:i,duration:o}}const n2={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500};function a2(a){return Rn(a)||typeof a>"u"||typeof a=="string"||typeof a=="boolean"||typeof a=="number"||Array.isArray(a)}function q0(a={}){const i={...a};function o(u){const s=Object.entries(u);for(let f=0;f<s.length;f++){const[p,h]=s[f];!a2(h)||p.startsWith("unstable_")?delete u[p]:Rn(h)&&(u[p]={...h},o(u[p]))}}return o(i),`import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';

const theme = ${JSON.stringify(i,null,2)};

theme.breakpoints = createBreakpoints(theme.breakpoints || {});
theme.transitions = createTransitions(theme.transitions || {});

export default theme;`}function Us(a={},...i){const{breakpoints:o,mixins:u={},spacing:s,palette:f={},transitions:p={},typography:h={},shape:y,...g}=a;if(a.vars&&a.generateThemeVars===void 0)throw new Error(Xa(20));const b=of(f),C=sr(a);let E=Me(C,{mixins:QS(C.breakpoints,u),palette:b,shadows:PS.slice(),typography:k0(b,h),transitions:e2(p),zIndex:{...n2}});return E=Me(E,g),E=i.reduce((B,R)=>Me(B,R),E),E.unstable_sxConfig={...cr,...g==null?void 0:g.unstable_sxConfig},E.unstable_sx=function(R){return Ta({sx:R,theme:this})},E.toRuntimeSource=q0,E}function js(a){let i;return a<1?i=5.11916*a**2:i=4.5*Math.log(a+1)+2,Math.round(i*10)/1e3}const l2=[...Array(25)].map((a,i)=>{if(i===0)return"none";const o=js(i);return`linear-gradient(rgba(255 255 255 / ${o}), rgba(255 255 255 / ${o}))`});function Y0(a){return{inputPlaceholder:a==="dark"?.5:.42,inputUnderline:a==="dark"?.7:.42,switchTrackDisabled:a==="dark"?.2:.12,switchTrack:a==="dark"?.3:.38}}function V0(a){return a==="dark"?l2:[]}function i2(a){const{palette:i={mode:"light"},opacity:o,overlays:u,...s}=a,f=of(i);return{palette:f,opacity:{...Y0(f.mode),...o},overlays:u||V0(f.mode),...s}}function r2(a){var i;return!!a[0].match(/(cssVarPrefix|colorSchemeSelector|rootSelector|typography|mixins|breakpoints|direction|transitions)/)||!!a[0].match(/sxConfig$/)||a[0]==="palette"&&!!((i=a[1])!=null&&i.match(/(mode|contrastThreshold|tonalOffset)/))}const o2=a=>[...[...Array(25)].map((i,o)=>`--${a?`${a}-`:""}overlays-${o}`),`--${a?`${a}-`:""}palette-AppBar-darkBg`,`--${a?`${a}-`:""}palette-AppBar-darkColor`],u2=a=>(i,o)=>{const u=a.rootSelector||":root",s=a.colorSchemeSelector;let f=s;if(s==="class"&&(f=".%s"),s==="data"&&(f="[data-%s]"),s!=null&&s.startsWith("data-")&&!s.includes("%s")&&(f=`[${s}="%s"]`),a.defaultColorScheme===i){if(i==="dark"){const p={};return o2(a.cssVarPrefix).forEach(h=>{p[h]=o[h],delete o[h]}),f==="media"?{[u]:o,"@media (prefers-color-scheme: dark)":{[u]:p}}:f?{[f.replace("%s",i)]:p,[`${u}, ${f.replace("%s",i)}`]:o}:{[u]:{...o,...p}}}if(f&&f!=="media")return`${u}, ${f.replace("%s",String(i))}`}else if(i){if(f==="media")return{[`@media (prefers-color-scheme: ${String(i)})`]:{[u]:o}};if(f)return f.replace("%s",String(i))}return u};function c2(a,i){i.forEach(o=>{a[o]||(a[o]={})})}function j(a,i,o){!a[i]&&o&&(a[i]=o)}function Ji(a){return typeof a!="string"||!a.startsWith("hsl")?a:N0(a)}function Qn(a,i){`${i}Channel`in a||(a[`${i}Channel`]=Ki(Ji(a[i])))}function s2(a){return typeof a=="number"?`${a}px`:typeof a=="string"||typeof a=="function"||Array.isArray(a)?a:"8px"}const xn=a=>{try{return a()}catch{}},f2=(a="mui")=>cS(a);function zs(a,i,o,u){if(!i)return;i=i===!0?{}:i;const s=u==="dark"?"dark":"light";if(!o){a[u]=i2({...i,palette:{mode:s,...i==null?void 0:i.palette}});return}const{palette:f,...p}=Us({...o,palette:{mode:s,...i==null?void 0:i.palette}});return a[u]={...i,palette:f,opacity:{...Y0(s),...i==null?void 0:i.opacity},overlays:(i==null?void 0:i.overlays)||V0(s)},p}function d2(a={},...i){const{colorSchemes:o={light:!0},defaultColorScheme:u,disableCssColorScheme:s=!1,cssVarPrefix:f="mui",shouldSkipGeneratingVar:p=r2,colorSchemeSelector:h=o.light&&o.dark?"media":void 0,rootSelector:y=":root",...g}=a,b=Object.keys(o)[0],C=u||(o.light&&b!=="light"?"light":b),E=f2(f),{[C]:B,light:R,dark:T,...L}=o,V={...L};let Z=B;if((C==="dark"&&!("dark"in o)||C==="light"&&!("light"in o))&&(Z=!0),!Z)throw new Error(Xa(21,C));const X=zs(V,Z,g,C);R&&!V.light&&zs(V,R,void 0,"light"),T&&!V.dark&&zs(V,T,void 0,"dark");let U={defaultColorScheme:C,...X,cssVarPrefix:f,colorSchemeSelector:h,rootSelector:y,getCssVar:E,colorSchemes:V,font:{...XS(X.typography),...X.font},spacing:s2(g.spacing)};Object.keys(U.colorSchemes).forEach(nt=>{const v=U.colorSchemes[nt].palette,k=P=>{const it=P.split("-"),ft=it[1],ht=it[2];return E(P,v[ft][ht])};if(v.mode==="light"&&(j(v.common,"background","#fff"),j(v.common,"onBackground","#000")),v.mode==="dark"&&(j(v.common,"background","#000"),j(v.common,"onBackground","#fff")),c2(v,["Alert","AppBar","Avatar","Button","Chip","FilledInput","LinearProgress","Skeleton","Slider","SnackbarContent","SpeedDialAction","StepConnector","StepContent","Switch","TableCell","Tooltip"]),v.mode==="light"){j(v.Alert,"errorColor",Gt(v.error.light,.6)),j(v.Alert,"infoColor",Gt(v.info.light,.6)),j(v.Alert,"successColor",Gt(v.success.light,.6)),j(v.Alert,"warningColor",Gt(v.warning.light,.6)),j(v.Alert,"errorFilledBg",k("palette-error-main")),j(v.Alert,"infoFilledBg",k("palette-info-main")),j(v.Alert,"successFilledBg",k("palette-success-main")),j(v.Alert,"warningFilledBg",k("palette-warning-main")),j(v.Alert,"errorFilledColor",xn(()=>v.getContrastText(v.error.main))),j(v.Alert,"infoFilledColor",xn(()=>v.getContrastText(v.info.main))),j(v.Alert,"successFilledColor",xn(()=>v.getContrastText(v.success.main))),j(v.Alert,"warningFilledColor",xn(()=>v.getContrastText(v.warning.main))),j(v.Alert,"errorStandardBg",Lt(v.error.light,.9)),j(v.Alert,"infoStandardBg",Lt(v.info.light,.9)),j(v.Alert,"successStandardBg",Lt(v.success.light,.9)),j(v.Alert,"warningStandardBg",Lt(v.warning.light,.9)),j(v.Alert,"errorIconColor",k("palette-error-main")),j(v.Alert,"infoIconColor",k("palette-info-main")),j(v.Alert,"successIconColor",k("palette-success-main")),j(v.Alert,"warningIconColor",k("palette-warning-main")),j(v.AppBar,"defaultBg",k("palette-grey-100")),j(v.Avatar,"defaultBg",k("palette-grey-400")),j(v.Button,"inheritContainedBg",k("palette-grey-300")),j(v.Button,"inheritContainedHoverBg",k("palette-grey-A100")),j(v.Chip,"defaultBorder",k("palette-grey-400")),j(v.Chip,"defaultAvatarColor",k("palette-grey-700")),j(v.Chip,"defaultIconColor",k("palette-grey-700")),j(v.FilledInput,"bg","rgba(0, 0, 0, 0.06)"),j(v.FilledInput,"hoverBg","rgba(0, 0, 0, 0.09)"),j(v.FilledInput,"disabledBg","rgba(0, 0, 0, 0.12)"),j(v.LinearProgress,"primaryBg",Lt(v.primary.main,.62)),j(v.LinearProgress,"secondaryBg",Lt(v.secondary.main,.62)),j(v.LinearProgress,"errorBg",Lt(v.error.main,.62)),j(v.LinearProgress,"infoBg",Lt(v.info.main,.62)),j(v.LinearProgress,"successBg",Lt(v.success.main,.62)),j(v.LinearProgress,"warningBg",Lt(v.warning.main,.62)),j(v.Skeleton,"bg",`rgba(${k("palette-text-primaryChannel")} / 0.11)`),j(v.Slider,"primaryTrack",Lt(v.primary.main,.62)),j(v.Slider,"secondaryTrack",Lt(v.secondary.main,.62)),j(v.Slider,"errorTrack",Lt(v.error.main,.62)),j(v.Slider,"infoTrack",Lt(v.info.main,.62)),j(v.Slider,"successTrack",Lt(v.success.main,.62)),j(v.Slider,"warningTrack",Lt(v.warning.main,.62));const P=zo(v.background.default,.8);j(v.SnackbarContent,"bg",P),j(v.SnackbarContent,"color",xn(()=>v.getContrastText(P))),j(v.SpeedDialAction,"fabHoverBg",zo(v.background.paper,.15)),j(v.StepConnector,"border",k("palette-grey-400")),j(v.StepContent,"border",k("palette-grey-400")),j(v.Switch,"defaultColor",k("palette-common-white")),j(v.Switch,"defaultDisabledColor",k("palette-grey-100")),j(v.Switch,"primaryDisabledColor",Lt(v.primary.main,.62)),j(v.Switch,"secondaryDisabledColor",Lt(v.secondary.main,.62)),j(v.Switch,"errorDisabledColor",Lt(v.error.main,.62)),j(v.Switch,"infoDisabledColor",Lt(v.info.main,.62)),j(v.Switch,"successDisabledColor",Lt(v.success.main,.62)),j(v.Switch,"warningDisabledColor",Lt(v.warning.main,.62)),j(v.TableCell,"border",Lt(Mo(v.divider,1),.88)),j(v.Tooltip,"bg",Mo(v.grey[700],.92))}if(v.mode==="dark"){j(v.Alert,"errorColor",Lt(v.error.light,.6)),j(v.Alert,"infoColor",Lt(v.info.light,.6)),j(v.Alert,"successColor",Lt(v.success.light,.6)),j(v.Alert,"warningColor",Lt(v.warning.light,.6)),j(v.Alert,"errorFilledBg",k("palette-error-dark")),j(v.Alert,"infoFilledBg",k("palette-info-dark")),j(v.Alert,"successFilledBg",k("palette-success-dark")),j(v.Alert,"warningFilledBg",k("palette-warning-dark")),j(v.Alert,"errorFilledColor",xn(()=>v.getContrastText(v.error.dark))),j(v.Alert,"infoFilledColor",xn(()=>v.getContrastText(v.info.dark))),j(v.Alert,"successFilledColor",xn(()=>v.getContrastText(v.success.dark))),j(v.Alert,"warningFilledColor",xn(()=>v.getContrastText(v.warning.dark))),j(v.Alert,"errorStandardBg",Gt(v.error.light,.9)),j(v.Alert,"infoStandardBg",Gt(v.info.light,.9)),j(v.Alert,"successStandardBg",Gt(v.success.light,.9)),j(v.Alert,"warningStandardBg",Gt(v.warning.light,.9)),j(v.Alert,"errorIconColor",k("palette-error-main")),j(v.Alert,"infoIconColor",k("palette-info-main")),j(v.Alert,"successIconColor",k("palette-success-main")),j(v.Alert,"warningIconColor",k("palette-warning-main")),j(v.AppBar,"defaultBg",k("palette-grey-900")),j(v.AppBar,"darkBg",k("palette-background-paper")),j(v.AppBar,"darkColor",k("palette-text-primary")),j(v.Avatar,"defaultBg",k("palette-grey-600")),j(v.Button,"inheritContainedBg",k("palette-grey-800")),j(v.Button,"inheritContainedHoverBg",k("palette-grey-700")),j(v.Chip,"defaultBorder",k("palette-grey-700")),j(v.Chip,"defaultAvatarColor",k("palette-grey-300")),j(v.Chip,"defaultIconColor",k("palette-grey-300")),j(v.FilledInput,"bg","rgba(255, 255, 255, 0.09)"),j(v.FilledInput,"hoverBg","rgba(255, 255, 255, 0.13)"),j(v.FilledInput,"disabledBg","rgba(255, 255, 255, 0.12)"),j(v.LinearProgress,"primaryBg",Gt(v.primary.main,.5)),j(v.LinearProgress,"secondaryBg",Gt(v.secondary.main,.5)),j(v.LinearProgress,"errorBg",Gt(v.error.main,.5)),j(v.LinearProgress,"infoBg",Gt(v.info.main,.5)),j(v.LinearProgress,"successBg",Gt(v.success.main,.5)),j(v.LinearProgress,"warningBg",Gt(v.warning.main,.5)),j(v.Skeleton,"bg",`rgba(${k("palette-text-primaryChannel")} / 0.13)`),j(v.Slider,"primaryTrack",Gt(v.primary.main,.5)),j(v.Slider,"secondaryTrack",Gt(v.secondary.main,.5)),j(v.Slider,"errorTrack",Gt(v.error.main,.5)),j(v.Slider,"infoTrack",Gt(v.info.main,.5)),j(v.Slider,"successTrack",Gt(v.success.main,.5)),j(v.Slider,"warningTrack",Gt(v.warning.main,.5));const P=zo(v.background.default,.98);j(v.SnackbarContent,"bg",P),j(v.SnackbarContent,"color",xn(()=>v.getContrastText(P))),j(v.SpeedDialAction,"fabHoverBg",zo(v.background.paper,.15)),j(v.StepConnector,"border",k("palette-grey-600")),j(v.StepContent,"border",k("palette-grey-600")),j(v.Switch,"defaultColor",k("palette-grey-300")),j(v.Switch,"defaultDisabledColor",k("palette-grey-600")),j(v.Switch,"primaryDisabledColor",Gt(v.primary.main,.55)),j(v.Switch,"secondaryDisabledColor",Gt(v.secondary.main,.55)),j(v.Switch,"errorDisabledColor",Gt(v.error.main,.55)),j(v.Switch,"infoDisabledColor",Gt(v.info.main,.55)),j(v.Switch,"successDisabledColor",Gt(v.success.main,.55)),j(v.Switch,"warningDisabledColor",Gt(v.warning.main,.55)),j(v.TableCell,"border",Gt(Mo(v.divider,1),.68)),j(v.Tooltip,"bg",Mo(v.grey[700],.92))}Qn(v.background,"default"),Qn(v.background,"paper"),Qn(v.common,"background"),Qn(v.common,"onBackground"),Qn(v,"divider"),Object.keys(v).forEach(P=>{const it=v[P];P!=="tonalOffset"&&it&&typeof it=="object"&&(it.main&&j(v[P],"mainChannel",Ki(Ji(it.main))),it.light&&j(v[P],"lightChannel",Ki(Ji(it.light))),it.dark&&j(v[P],"darkChannel",Ki(Ji(it.dark))),it.contrastText&&j(v[P],"contrastTextChannel",Ki(Ji(it.contrastText))),P==="text"&&(Qn(v[P],"primary"),Qn(v[P],"secondary")),P==="action"&&(it.active&&Qn(v[P],"active"),it.selected&&Qn(v[P],"selected")))})}),U=i.reduce((nt,v)=>Me(nt,v),U);const M={prefix:f,disableCssColorScheme:s,shouldSkipGeneratingVar:p,getSelector:u2(U)},{vars:Q,generateThemeVars:F,generateStyleSheets:W}=dS(U,M);return U.vars=Q,Object.entries(U.colorSchemes[U.defaultColorScheme]).forEach(([nt,v])=>{U[nt]=v}),U.generateThemeVars=F,U.generateStyleSheets=W,U.generateSpacing=function(){return x0(g.spacing,Jo(this))},U.getColorSchemeSelector=pS(h),U.spacing=U.generateSpacing(),U.shouldSkipGeneratingVar=p,U.unstable_sxConfig={...cr,...g==null?void 0:g.unstable_sxConfig},U.unstable_sx=function(v){return Ta({sx:v,theme:this})},U.toRuntimeSource=q0,U}function Yh(a,i,o){a.colorSchemes&&o&&(a.colorSchemes[i]={...o!==!0&&o,palette:of({...o===!0?{}:o.palette,mode:i})})}function uf(a={},...i){const{palette:o,cssVariables:u=!1,colorSchemes:s=o?void 0:{light:!0},defaultColorScheme:f=o==null?void 0:o.mode,...p}=a,h=f||"light",y=s==null?void 0:s[h],g={...s,...o?{[h]:{...typeof y!="boolean"&&y,palette:o}}:void 0};if(u===!1){if(!("colorSchemes"in a))return Us(a,...i);let b=o;"palette"in a||g[h]&&(g[h]!==!0?b=g[h].palette:h==="dark"&&(b={mode:"dark"}));const C=Us({...a,palette:b},...i);return C.defaultColorScheme=h,C.colorSchemes=g,C.palette.mode==="light"&&(C.colorSchemes.light={...g.light!==!0&&g.light,palette:C.palette},Yh(C,"dark",g.dark)),C.palette.mode==="dark"&&(C.colorSchemes.dark={...g.dark!==!0&&g.dark,palette:C.palette},Yh(C,"light",g.light)),C}return!o&&!("light"in g)&&h==="light"&&(g.light=!0),d2({...p,colorSchemes:g,defaultColorScheme:h,...typeof u!="boolean"&&u},...i)}const cf=uf();function X0(){const a=fr(cf);return a[On]||a}function p2(a){return a!=="ownerState"&&a!=="theme"&&a!=="sx"&&a!=="as"}const Q0=a=>p2(a)&&a!=="classes",kt=z0({themeId:On,defaultTheme:cf,rootShouldForwardProp:Q0});function m2({theme:a,...i}){const o=On in a?a[On]:void 0;return tt.jsx(j0,{...i,themeId:o?On:void 0,theme:o||a})}const _o={colorSchemeStorageKey:"mui-color-scheme",defaultLightColorScheme:"light",defaultDarkColorScheme:"dark",modeStorageKey:"mui-mode"},{CssVarsProvider:h2}=uS({themeId:On,theme:()=>uf({cssVariables:!0}),colorSchemeStorageKey:_o.colorSchemeStorageKey,modeStorageKey:_o.modeStorageKey,defaultColorScheme:{light:_o.defaultLightColorScheme,dark:_o.defaultDarkColorScheme},resolveTheme:a=>{const i={...a,typography:k0(a.palette,a.typography)};return i.unstable_sx=function(u){return Ta({sx:u,theme:this})},i}}),g2=h2;function sT({theme:a,...i}){const o=w.useMemo(()=>{if(typeof a=="function")return a;const u=On in a?a[On]:a;return"colorSchemes"in u?null:"vars"in u?a:{...a,vars:null}},[a]);return o?tt.jsx(m2,{theme:o,...i}):tt.jsx(g2,{theme:a,...i})}function y2(a){return tt.jsx(Mb,{...a,defaultTheme:cf,themeId:On})}function Z0(a){return function(o){return tt.jsx(y2,{styles:typeof a=="function"?u=>a({theme:u,...o}):a})}}function v2(){return eu}const hn=tS;function Ae(a){return Ib(a)}function b2(a){return ze("MuiSvgIcon",a)}je("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);const S2=a=>{const{color:i,fontSize:o,classes:u}=a,s={root:["root",i!=="inherit"&&`color${et(i)}`,`fontSize${et(o)}`]};return He(s,b2,u)},C2=kt("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(a,i)=>{const{ownerState:o}=a;return[i.root,o.color!=="inherit"&&i[`color${et(o.color)}`],i[`fontSize${et(o.fontSize)}`]]}})(hn(({theme:a})=>{var i,o,u,s,f,p,h,y,g,b,C,E,B,R;return{userSelect:"none",width:"1em",height:"1em",display:"inline-block",flexShrink:0,transition:(s=(i=a.transitions)==null?void 0:i.create)==null?void 0:s.call(i,"fill",{duration:(u=(o=(a.vars??a).transitions)==null?void 0:o.duration)==null?void 0:u.shorter}),variants:[{props:T=>!T.hasSvgAsChild,style:{fill:"currentColor"}},{props:{fontSize:"inherit"},style:{fontSize:"inherit"}},{props:{fontSize:"small"},style:{fontSize:((p=(f=a.typography)==null?void 0:f.pxToRem)==null?void 0:p.call(f,20))||"1.25rem"}},{props:{fontSize:"medium"},style:{fontSize:((y=(h=a.typography)==null?void 0:h.pxToRem)==null?void 0:y.call(h,24))||"1.5rem"}},{props:{fontSize:"large"},style:{fontSize:((b=(g=a.typography)==null?void 0:g.pxToRem)==null?void 0:b.call(g,35))||"2.1875rem"}},...Object.entries((a.vars??a).palette).filter(([,T])=>T&&T.main).map(([T])=>{var L,V;return{props:{color:T},style:{color:(V=(L=(a.vars??a).palette)==null?void 0:L[T])==null?void 0:V.main}}}),{props:{color:"action"},style:{color:(E=(C=(a.vars??a).palette)==null?void 0:C.action)==null?void 0:E.active}},{props:{color:"disabled"},style:{color:(R=(B=(a.vars??a).palette)==null?void 0:B.action)==null?void 0:R.disabled}},{props:{color:"inherit"},style:{color:void 0}}]}})),Hs=w.forwardRef(function(i,o){const u=Ae({props:i,name:"MuiSvgIcon"}),{children:s,className:f,color:p="inherit",component:h="svg",fontSize:y="medium",htmlColor:g,inheritViewBox:b=!1,titleAccess:C,viewBox:E="0 0 24 24",...B}=u,R=w.isValidElement(s)&&s.type==="svg",T={...u,color:p,component:h,fontSize:y,instanceFontSize:i.fontSize,inheritViewBox:b,viewBox:E,hasSvgAsChild:R},L={};b||(L.viewBox=E);const V=S2(T);return tt.jsxs(C2,{as:h,className:_t(V.root,f),focusable:"false",color:g,"aria-hidden":C?void 0:!0,role:C?"img":void 0,ref:o,...L,...B,...R&&s.props,ownerState:T,children:[R?s.props.children:s,C?tt.jsx("title",{children:C}):null]})});Hs.muiName="SvgIcon";function Ea(a,i){function o(u,s){return tt.jsx(Hs,{"data-testid":void 0,ref:s,...u,children:a})}return o.muiName=Hs.muiName,w.memo(w.forwardRef(o))}let Vh=0;function T2(a){const[i,o]=w.useState(a),u=a||i;return w.useEffect(()=>{i==null&&(Vh+=1,o(`mui-${Vh}`))},[i]),u}const x2={..._s},Xh=x2.useId;function K0(a){if(Xh!==void 0){const i=Xh();return a??i}return T2(a)}function wo(a){const i=w.useRef(a);return D0(()=>{i.current=a}),w.useRef((...o)=>(0,i.current)(...o)).current}function ko(...a){const i=w.useRef(void 0),o=w.useCallback(u=>{const s=a.map(f=>{if(f==null)return null;if(typeof f=="function"){const p=f,h=p(u);return typeof h=="function"?h:()=>{p(null)}}return f.current=u,()=>{f.current=null}});return()=>{s.forEach(f=>f==null?void 0:f())}},a);return w.useMemo(()=>a.every(u=>u==null)?null:u=>{i.current&&(i.current(),i.current=void 0),u!=null&&(i.current=o(u))},a)}function E2(a,i){if(a==null)return{};var o={};for(var u in a)if({}.hasOwnProperty.call(a,u)){if(i.indexOf(u)!==-1)continue;o[u]=a[u]}return o}function Gs(a,i){return Gs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(o,u){return o.__proto__=u,o},Gs(a,i)}function A2(a,i){a.prototype=Object.create(i.prototype),a.prototype.constructor=a,Gs(a,i)}const Qh=Gl.createContext(null);function R2(a){if(a===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a}function sf(a,i){var o=function(f){return i&&w.isValidElement(f)?i(f):f},u=Object.create(null);return a&&w.Children.map(a,function(s){return s}).forEach(function(s){u[s.key]=o(s)}),u}function O2(a,i){a=a||{},i=i||{};function o(b){return b in i?i[b]:a[b]}var u=Object.create(null),s=[];for(var f in a)f in i?s.length&&(u[f]=s,s=[]):s.push(f);var p,h={};for(var y in i){if(u[y])for(p=0;p<u[y].length;p++){var g=u[y][p];h[u[y][p]]=o(g)}h[y]=o(y)}for(p=0;p<s.length;p++)h[s[p]]=o(s[p]);return h}function Va(a,i,o){return o[i]!=null?o[i]:a.props[i]}function M2(a,i){return sf(a.children,function(o){return w.cloneElement(o,{onExited:i.bind(null,o),in:!0,appear:Va(o,"appear",a),enter:Va(o,"enter",a),exit:Va(o,"exit",a)})})}function z2(a,i,o){var u=sf(a.children),s=O2(i,u);return Object.keys(s).forEach(function(f){var p=s[f];if(w.isValidElement(p)){var h=f in i,y=f in u,g=i[f],b=w.isValidElement(g)&&!g.props.in;y&&(!h||b)?s[f]=w.cloneElement(p,{onExited:o.bind(null,p),in:!0,exit:Va(p,"exit",a),enter:Va(p,"enter",a)}):!y&&h&&!b?s[f]=w.cloneElement(p,{in:!1}):y&&h&&w.isValidElement(g)&&(s[f]=w.cloneElement(p,{onExited:o.bind(null,p),in:g.props.in,exit:Va(p,"exit",a),enter:Va(p,"enter",a)}))}}),s}var _2=Object.values||function(a){return Object.keys(a).map(function(i){return a[i]})},B2={component:"div",childFactory:function(i){return i}},ff=function(a){A2(i,a);function i(u,s){var f;f=a.call(this,u,s)||this;var p=f.handleExited.bind(R2(f));return f.state={contextValue:{isMounting:!0},handleExited:p,firstRender:!0},f}var o=i.prototype;return o.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},o.componentWillUnmount=function(){this.mounted=!1},i.getDerivedStateFromProps=function(s,f){var p=f.children,h=f.handleExited,y=f.firstRender;return{children:y?M2(s,h):z2(s,p,h),firstRender:!1}},o.handleExited=function(s,f){var p=sf(this.props.children);s.key in p||(s.props.onExited&&s.props.onExited(f),this.mounted&&this.setState(function(h){var y=jo({},h.children);return delete y[s.key],{children:y}}))},o.render=function(){var s=this.props,f=s.component,p=s.childFactory,h=E2(s,["component","childFactory"]),y=this.state.contextValue,g=_2(this.state.children).map(p);return delete h.appear,delete h.enter,delete h.exit,f===null?Gl.createElement(Qh.Provider,{value:y},g):Gl.createElement(Qh.Provider,{value:y},Gl.createElement(f,h,g))},i}(Gl.Component);ff.propTypes={};ff.defaultProps=B2;const Zh={};function J0(a,i){const o=w.useRef(Zh);return o.current===Zh&&(o.current=a(i)),o}const D2=[];function N2(a){w.useEffect(a,D2)}class df{constructor(){Yi(this,"currentId",null);Yi(this,"clear",()=>{this.currentId!==null&&(clearTimeout(this.currentId),this.currentId=null)});Yi(this,"disposeEffect",()=>this.clear)}static create(){return new df}start(i,o){this.clear(),this.currentId=setTimeout(()=>{this.currentId=null,o()},i)}}function $2(){const a=J0(df.create).current;return N2(a.disposeEffect),a}function w2(a){return ze("MuiPaper",a)}je("MuiPaper",["root","rounded","outlined","elevation","elevation0","elevation1","elevation2","elevation3","elevation4","elevation5","elevation6","elevation7","elevation8","elevation9","elevation10","elevation11","elevation12","elevation13","elevation14","elevation15","elevation16","elevation17","elevation18","elevation19","elevation20","elevation21","elevation22","elevation23","elevation24"]);const U2=a=>{const{square:i,elevation:o,variant:u,classes:s}=a,f={root:["root",u,!i&&"rounded",u==="elevation"&&`elevation${o}`]};return He(f,w2,s)},j2=kt("div",{name:"MuiPaper",slot:"Root",overridesResolver:(a,i)=>{const{ownerState:o}=a;return[i.root,i[o.variant],!o.square&&i.rounded,o.variant==="elevation"&&i[`elevation${o.elevation}`]]}})(hn(({theme:a})=>({backgroundColor:(a.vars||a).palette.background.paper,color:(a.vars||a).palette.text.primary,transition:a.transitions.create("box-shadow"),variants:[{props:({ownerState:i})=>!i.square,style:{borderRadius:a.shape.borderRadius}},{props:{variant:"outlined"},style:{border:`1px solid ${(a.vars||a).palette.divider}`}},{props:{variant:"elevation"},style:{boxShadow:"var(--Paper-shadow)",backgroundImage:"var(--Paper-overlay)"}}]}))),W0=w.forwardRef(function(i,o){var B;const u=Ae({props:i,name:"MuiPaper"}),s=X0(),{className:f,component:p="div",elevation:h=1,square:y=!1,variant:g="elevation",...b}=u,C={...u,component:p,elevation:h,square:y,variant:g},E=U2(C);return tt.jsx(j2,{as:p,ownerState:C,className:_t(E.root,f),ref:o,...b,style:{...g==="elevation"&&{"--Paper-shadow":(s.vars||s).shadows[h],...s.vars&&{"--Paper-overlay":(B=s.vars.overlays)==null?void 0:B[h]},...!s.vars&&s.palette.mode==="dark"&&{"--Paper-overlay":`linear-gradient(${te("#fff",js(h))}, ${te("#fff",js(h))})`}},...b.style}})});function H2(a){return typeof a=="string"}function G2(a,i,o){return a===void 0||H2(a)?i:{...i,ownerState:{...i.ownerState,...o}}}function L2(a,i,o){return typeof a=="function"?a(i,o):a}function k2(a,i=[]){if(a===void 0)return{};const o={};return Object.keys(a).filter(u=>u.match(/^on[A-Z]/)&&typeof a[u]=="function"&&!i.includes(u)).forEach(u=>{o[u]=a[u]}),o}function Kh(a){if(a===void 0)return{};const i={};return Object.keys(a).filter(o=>!(o.match(/^on[A-Z]/)&&typeof a[o]=="function")).forEach(o=>{i[o]=a[o]}),i}function q2(a){const{getSlotProps:i,additionalProps:o,externalSlotProps:u,externalForwardedProps:s,className:f}=a;if(!i){const B=_t(o==null?void 0:o.className,f,s==null?void 0:s.className,u==null?void 0:u.className),R={...o==null?void 0:o.style,...s==null?void 0:s.style,...u==null?void 0:u.style},T={...o,...s,...u};return B.length>0&&(T.className=B),Object.keys(R).length>0&&(T.style=R),{props:T,internalRef:void 0}}const p=k2({...s,...u}),h=Kh(u),y=Kh(s),g=i(p),b=_t(g==null?void 0:g.className,o==null?void 0:o.className,f,s==null?void 0:s.className,u==null?void 0:u.className),C={...g==null?void 0:g.style,...o==null?void 0:o.style,...s==null?void 0:s.style,...u==null?void 0:u.style},E={...g,...o,...y,...h};return b.length>0&&(E.className=b),Object.keys(C).length>0&&(E.style=C),{props:E,internalRef:g.ref}}function Hl(a,i){const{className:o,elementType:u,ownerState:s,externalForwardedProps:f,internalForwardedProps:p,shouldForwardComponentProp:h=!1,...y}=i,{component:g,slots:b={[a]:void 0},slotProps:C={[a]:void 0},...E}=f,B=b[a]||u,R=L2(C[a],s),{props:{component:T,...L},internalRef:V}=q2({className:o,...y,externalForwardedProps:a==="root"?E:void 0,externalSlotProps:R}),Z=ko(V,R==null?void 0:R.ref,i.ref),X=a==="root"?T||g:T,U=G2(B,{...a==="root"&&!g&&!b[a]&&p,...a!=="root"&&!b[a]&&p,...L,...X&&!h&&{as:X},...X&&h&&{component:X},ref:Z},s);return[B,U]}function Jh(a){try{return a.matches(":focus-visible")}catch{}return!1}class qo{constructor(){Yi(this,"mountEffect",()=>{this.shouldMount&&!this.didMount&&this.ref.current!==null&&(this.didMount=!0,this.mounted.resolve())});this.ref={current:null},this.mounted=null,this.didMount=!1,this.shouldMount=!1,this.setShouldMount=null}static create(){return new qo}static use(){const i=J0(qo.create).current,[o,u]=w.useState(!1);return i.shouldMount=o,i.setShouldMount=u,w.useEffect(i.mountEffect,[o]),i}mount(){return this.mounted||(this.mounted=V2(),this.shouldMount=!0,this.setShouldMount(this.shouldMount)),this.mounted}start(...i){this.mount().then(()=>{var o;return(o=this.ref.current)==null?void 0:o.start(...i)})}stop(...i){this.mount().then(()=>{var o;return(o=this.ref.current)==null?void 0:o.stop(...i)})}pulsate(...i){this.mount().then(()=>{var o;return(o=this.ref.current)==null?void 0:o.pulsate(...i)})}}function Y2(){return qo.use()}function V2(){let a,i;const o=new Promise((u,s)=>{a=u,i=s});return o.resolve=a,o.reject=i,o}function X2(a){const{className:i,classes:o,pulsate:u=!1,rippleX:s,rippleY:f,rippleSize:p,in:h,onExited:y,timeout:g}=a,[b,C]=w.useState(!1),E=_t(i,o.ripple,o.rippleVisible,u&&o.ripplePulsate),B={width:p,height:p,top:-(p/2)+f,left:-(p/2)+s},R=_t(o.child,b&&o.childLeaving,u&&o.childPulsate);return!h&&!b&&C(!0),w.useEffect(()=>{if(!h&&y!=null){const T=setTimeout(y,g);return()=>{clearTimeout(T)}}},[y,h,g]),tt.jsx("span",{className:E,style:B,children:tt.jsx("span",{className:R})})}const rn=je("MuiTouchRipple",["root","ripple","rippleVisible","ripplePulsate","child","childLeaving","childPulsate"]),Ls=550,Q2=80,Z2=or`
  0% {
    transform: scale(0);
    opacity: 0.1;
  }

  100% {
    transform: scale(1);
    opacity: 0.3;
  }
`,K2=or`
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
`,J2=or`
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.92);
  }

  100% {
    transform: scale(1);
  }
`,W2=kt("span",{name:"MuiTouchRipple",slot:"Root"})({overflow:"hidden",pointerEvents:"none",position:"absolute",zIndex:0,top:0,right:0,bottom:0,left:0,borderRadius:"inherit"}),P2=kt(X2,{name:"MuiTouchRipple",slot:"Ripple"})`
  opacity: 0;
  position: absolute;

  &.${rn.rippleVisible} {
    opacity: 0.3;
    transform: scale(1);
    animation-name: ${Z2};
    animation-duration: ${Ls}ms;
    animation-timing-function: ${({theme:a})=>a.transitions.easing.easeInOut};
  }

  &.${rn.ripplePulsate} {
    animation-duration: ${({theme:a})=>a.transitions.duration.shorter}ms;
  }

  & .${rn.child} {
    opacity: 1;
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: currentColor;
  }

  & .${rn.childLeaving} {
    opacity: 0;
    animation-name: ${K2};
    animation-duration: ${Ls}ms;
    animation-timing-function: ${({theme:a})=>a.transitions.easing.easeInOut};
  }

  & .${rn.childPulsate} {
    position: absolute;
    /* @noflip */
    left: 0px;
    top: 0;
    animation-name: ${J2};
    animation-duration: 2500ms;
    animation-timing-function: ${({theme:a})=>a.transitions.easing.easeInOut};
    animation-iteration-count: infinite;
    animation-delay: 200ms;
  }
`,F2=w.forwardRef(function(i,o){const u=Ae({props:i,name:"MuiTouchRipple"}),{center:s=!1,classes:f={},className:p,...h}=u,[y,g]=w.useState([]),b=w.useRef(0),C=w.useRef(null);w.useEffect(()=>{C.current&&(C.current(),C.current=null)},[y]);const E=w.useRef(!1),B=$2(),R=w.useRef(null),T=w.useRef(null),L=w.useCallback(U=>{const{pulsate:M,rippleX:Q,rippleY:F,rippleSize:W,cb:nt}=U;g(v=>[...v,tt.jsx(P2,{classes:{ripple:_t(f.ripple,rn.ripple),rippleVisible:_t(f.rippleVisible,rn.rippleVisible),ripplePulsate:_t(f.ripplePulsate,rn.ripplePulsate),child:_t(f.child,rn.child),childLeaving:_t(f.childLeaving,rn.childLeaving),childPulsate:_t(f.childPulsate,rn.childPulsate)},timeout:Ls,pulsate:M,rippleX:Q,rippleY:F,rippleSize:W},b.current)]),b.current+=1,C.current=nt},[f]),V=w.useCallback((U={},M={},Q=()=>{})=>{const{pulsate:F=!1,center:W=s||M.pulsate,fakeElement:nt=!1}=M;if((U==null?void 0:U.type)==="mousedown"&&E.current){E.current=!1;return}(U==null?void 0:U.type)==="touchstart"&&(E.current=!0);const v=nt?null:T.current,k=v?v.getBoundingClientRect():{width:0,height:0,left:0,top:0};let P,it,ft;if(W||U===void 0||U.clientX===0&&U.clientY===0||!U.clientX&&!U.touches)P=Math.round(k.width/2),it=Math.round(k.height/2);else{const{clientX:ht,clientY:$}=U.touches&&U.touches.length>0?U.touches[0]:U;P=Math.round(ht-k.left),it=Math.round($-k.top)}if(W)ft=Math.sqrt((2*k.width**2+k.height**2)/3),ft%2===0&&(ft+=1);else{const ht=Math.max(Math.abs((v?v.clientWidth:0)-P),P)*2+2,$=Math.max(Math.abs((v?v.clientHeight:0)-it),it)*2+2;ft=Math.sqrt(ht**2+$**2)}U!=null&&U.touches?R.current===null&&(R.current=()=>{L({pulsate:F,rippleX:P,rippleY:it,rippleSize:ft,cb:Q})},B.start(Q2,()=>{R.current&&(R.current(),R.current=null)})):L({pulsate:F,rippleX:P,rippleY:it,rippleSize:ft,cb:Q})},[s,L,B]),Z=w.useCallback(()=>{V({},{pulsate:!0})},[V]),X=w.useCallback((U,M)=>{if(B.clear(),(U==null?void 0:U.type)==="touchend"&&R.current){R.current(),R.current=null,B.start(0,()=>{X(U,M)});return}R.current=null,g(Q=>Q.length>0?Q.slice(1):Q),C.current=M},[B]);return w.useImperativeHandle(o,()=>({pulsate:Z,start:V,stop:X}),[Z,V,X]),tt.jsx(W2,{className:_t(rn.root,f.root,p),ref:T,...h,children:tt.jsx(ff,{component:null,exit:!0,children:y})})});function I2(a){return ze("MuiButtonBase",a)}const tC=je("MuiButtonBase",["root","disabled","focusVisible"]),eC=a=>{const{disabled:i,focusVisible:o,focusVisibleClassName:u,classes:s}=a,p=He({root:["root",i&&"disabled",o&&"focusVisible"]},I2,s);return o&&u&&(p.root+=` ${u}`),p},nC=kt("button",{name:"MuiButtonBase",slot:"Root"})({display:"inline-flex",alignItems:"center",justifyContent:"center",position:"relative",boxSizing:"border-box",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none",textDecoration:"none",color:"inherit","&::-moz-focus-inner":{borderStyle:"none"},[`&.${tC.disabled}`]:{pointerEvents:"none",cursor:"default"},"@media print":{colorAdjust:"exact"}}),Yo=w.forwardRef(function(i,o){const u=Ae({props:i,name:"MuiButtonBase"}),{action:s,centerRipple:f=!1,children:p,className:h,component:y="button",disabled:g=!1,disableRipple:b=!1,disableTouchRipple:C=!1,focusRipple:E=!1,focusVisibleClassName:B,LinkComponent:R="a",onBlur:T,onClick:L,onContextMenu:V,onDragLeave:Z,onFocus:X,onFocusVisible:U,onKeyDown:M,onKeyUp:Q,onMouseDown:F,onMouseLeave:W,onMouseUp:nt,onTouchEnd:v,onTouchMove:k,onTouchStart:P,tabIndex:it=0,TouchRippleProps:ft,touchRippleRef:ht,type:$,...K}=u,at=w.useRef(null),lt=Y2(),x=ko(lt.ref,ht),[H,J]=w.useState(!1);g&&H&&J(!1),w.useImperativeHandle(s,()=>({focusVisible:()=>{J(!0),at.current.focus()}}),[]);const I=lt.shouldMount&&!b&&!g;w.useEffect(()=>{H&&E&&!b&&lt.pulsate()},[b,E,H,lt]);const ut=Zn(lt,"start",F,C),bt=Zn(lt,"stop",V,C),dt=Zn(lt,"stop",Z,C),Zt=Zn(lt,"stop",nt,C),Bt=Zn(lt,"stop",mt=>{H&&mt.preventDefault(),W&&W(mt)},C),Ge=Zn(lt,"start",P,C),Aa=Zn(lt,"stop",v,C),Kn=Zn(lt,"stop",k,C),Jn=Zn(lt,"stop",mt=>{Jh(mt.target)||J(!1),T&&T(mt)},!1),Wn=wo(mt=>{at.current||(at.current=mt.currentTarget),Jh(mt.target)&&(J(!0),U&&U(mt)),X&&X(mt)}),gn=()=>{const mt=at.current;return y&&y!=="button"&&!(mt.tagName==="A"&&mt.href)},Za=wo(mt=>{E&&!mt.repeat&&H&&mt.key===" "&&lt.stop(mt,()=>{lt.start(mt)}),mt.target===mt.currentTarget&&gn()&&mt.key===" "&&mt.preventDefault(),M&&M(mt),mt.target===mt.currentTarget&&gn()&&mt.key==="Enter"&&!g&&(mt.preventDefault(),L&&L(mt))}),Ql=wo(mt=>{E&&mt.key===" "&&H&&!mt.defaultPrevented&&lt.stop(mt,()=>{lt.pulsate(mt)}),Q&&Q(mt),L&&mt.target===mt.currentTarget&&gn()&&mt.key===" "&&!mt.defaultPrevented&&L(mt)});let oe=y;oe==="button"&&(K.href||K.to)&&(oe=R);const cn={};oe==="button"?(cn.type=$===void 0?"button":$,cn.disabled=g):(!K.href&&!K.to&&(cn.role="button"),g&&(cn["aria-disabled"]=g));const ge=ko(o,at),yn={...u,centerRipple:f,component:y,disabled:g,disableRipple:b,disableTouchRipple:C,focusRipple:E,tabIndex:it,focusVisible:H},Je=eC(yn);return tt.jsxs(nC,{as:oe,className:_t(Je.root,h),ownerState:yn,onBlur:Jn,onClick:L,onContextMenu:bt,onFocus:Wn,onKeyDown:Za,onKeyUp:Ql,onMouseDown:ut,onMouseLeave:Bt,onMouseUp:Zt,onDragLeave:dt,onTouchEnd:Aa,onTouchMove:Kn,onTouchStart:Ge,ref:ge,tabIndex:g?-1:it,type:$,...cn,...K,children:[p,I?tt.jsx(F2,{ref:x,center:f,...ft}):null]})});function Zn(a,i,o,u=!1){return wo(s=>(o&&o(s),u||a[i](s),!0))}function aC(a){return typeof a.main=="string"}function lC(a,i=[]){if(!aC(a))return!1;for(const o of i)if(!a.hasOwnProperty(o)||typeof a[o]!="string")return!1;return!0}function we(a=[]){return([,i])=>i&&lC(i,a)}function iC(a){return ze("MuiAlert",a)}const Wh=je("MuiAlert",["root","action","icon","message","filled","colorSuccess","colorInfo","colorWarning","colorError","filledSuccess","filledInfo","filledWarning","filledError","outlined","outlinedSuccess","outlinedInfo","outlinedWarning","outlinedError","standard","standardSuccess","standardInfo","standardWarning","standardError"]);function rC(a){return ze("MuiCircularProgress",a)}je("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);const Ca=44,ks=or`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`,qs=or`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: -126px;
  }
`,oC=typeof ks!="string"?Fs`
        animation: ${ks} 1.4s linear infinite;
      `:null,uC=typeof qs!="string"?Fs`
        animation: ${qs} 1.4s ease-in-out infinite;
      `:null,cC=a=>{const{classes:i,variant:o,color:u,disableShrink:s}=a,f={root:["root",o,`color${et(u)}`],svg:["svg"],circle:["circle",`circle${et(o)}`,s&&"circleDisableShrink"]};return He(f,rC,i)},sC=kt("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(a,i)=>{const{ownerState:o}=a;return[i.root,i[o.variant],i[`color${et(o.color)}`]]}})(hn(({theme:a})=>({display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:a.transitions.create("transform")}},{props:{variant:"indeterminate"},style:oC||{animation:`${ks} 1.4s linear infinite`}},...Object.entries(a.palette).filter(we()).map(([i])=>({props:{color:i},style:{color:(a.vars||a).palette[i].main}}))]}))),fC=kt("svg",{name:"MuiCircularProgress",slot:"Svg"})({display:"block"}),dC=kt("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(a,i)=>{const{ownerState:o}=a;return[i.circle,i[`circle${et(o.variant)}`],o.disableShrink&&i.circleDisableShrink]}})(hn(({theme:a})=>({stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:a.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:({ownerState:i})=>i.variant==="indeterminate"&&!i.disableShrink,style:uC||{animation:`${qs} 1.4s ease-in-out infinite`}}]}))),P0=w.forwardRef(function(i,o){const u=Ae({props:i,name:"MuiCircularProgress"}),{className:s,color:f="primary",disableShrink:p=!1,size:h=40,style:y,thickness:g=3.6,value:b=0,variant:C="indeterminate",...E}=u,B={...u,color:f,disableShrink:p,size:h,thickness:g,value:b,variant:C},R=cC(B),T={},L={},V={};if(C==="determinate"){const Z=2*Math.PI*((Ca-g)/2);T.strokeDasharray=Z.toFixed(3),V["aria-valuenow"]=Math.round(b),T.strokeDashoffset=`${((100-b)/100*Z).toFixed(3)}px`,L.transform="rotate(-90deg)"}return tt.jsx(sC,{className:_t(R.root,s),style:{width:h,height:h,...L,...y},ownerState:B,ref:o,role:"progressbar",...V,...E,children:tt.jsx(fC,{className:R.svg,ownerState:B,viewBox:`${Ca/2} ${Ca/2} ${Ca} ${Ca}`,children:tt.jsx(dC,{className:R.circle,style:T,ownerState:B,cx:Ca,cy:Ca,r:(Ca-g)/2,fill:"none",strokeWidth:g})})})});function pC(a){return ze("MuiIconButton",a)}const Ph=je("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge","loading","loadingIndicator","loadingWrapper"]),mC=a=>{const{classes:i,disabled:o,color:u,edge:s,size:f,loading:p}=a,h={root:["root",p&&"loading",o&&"disabled",u!=="default"&&`color${et(u)}`,s&&`edge${et(s)}`,`size${et(f)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]};return He(h,pC,i)},hC=kt(Yo,{name:"MuiIconButton",slot:"Root",overridesResolver:(a,i)=>{const{ownerState:o}=a;return[i.root,o.loading&&i.loading,o.color!=="default"&&i[`color${et(o.color)}`],o.edge&&i[`edge${et(o.edge)}`],i[`size${et(o.size)}`]]}})(hn(({theme:a})=>({textAlign:"center",flex:"0 0 auto",fontSize:a.typography.pxToRem(24),padding:8,borderRadius:"50%",color:(a.vars||a).palette.action.active,transition:a.transitions.create("background-color",{duration:a.transitions.duration.shortest}),variants:[{props:i=>!i.disableRipple,style:{"--IconButton-hoverBg":a.vars?`rgba(${a.vars.palette.action.activeChannel} / ${a.vars.palette.action.hoverOpacity})`:te(a.palette.action.active,a.palette.action.hoverOpacity),"&:hover":{backgroundColor:"var(--IconButton-hoverBg)","@media (hover: none)":{backgroundColor:"transparent"}}}},{props:{edge:"start"},style:{marginLeft:-12}},{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:{edge:"end"},style:{marginRight:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}}]})),hn(({theme:a})=>({variants:[{props:{color:"inherit"},style:{color:"inherit"}},...Object.entries(a.palette).filter(we()).map(([i])=>({props:{color:i},style:{color:(a.vars||a).palette[i].main}})),...Object.entries(a.palette).filter(we()).map(([i])=>({props:{color:i},style:{"--IconButton-hoverBg":a.vars?`rgba(${(a.vars||a).palette[i].mainChannel} / ${a.vars.palette.action.hoverOpacity})`:te((a.vars||a).palette[i].main,a.palette.action.hoverOpacity)}})),{props:{size:"small"},style:{padding:5,fontSize:a.typography.pxToRem(18)}},{props:{size:"large"},style:{padding:12,fontSize:a.typography.pxToRem(28)}}],[`&.${Ph.disabled}`]:{backgroundColor:"transparent",color:(a.vars||a).palette.action.disabled},[`&.${Ph.loading}`]:{color:"transparent"}}))),gC=kt("span",{name:"MuiIconButton",slot:"LoadingIndicator"})(({theme:a})=>({display:"none",position:"absolute",visibility:"visible",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:(a.vars||a).palette.action.disabled,variants:[{props:{loading:!0},style:{display:"flex"}}]})),yC=w.forwardRef(function(i,o){const u=Ae({props:i,name:"MuiIconButton"}),{edge:s=!1,children:f,className:p,color:h="default",disabled:y=!1,disableFocusRipple:g=!1,size:b="medium",id:C,loading:E=null,loadingIndicator:B,...R}=u,T=K0(C),L=B??tt.jsx(P0,{"aria-labelledby":T,color:"inherit",size:16}),V={...u,edge:s,color:h,disabled:y,disableFocusRipple:g,loading:E,loadingIndicator:L,size:b},Z=mC(V);return tt.jsxs(hC,{id:E?T:C,className:_t(Z.root,p),centerRipple:!0,focusRipple:!g,disabled:y||E,ref:o,...R,ownerState:V,children:[typeof E=="boolean"&&tt.jsx("span",{className:Z.loadingWrapper,style:{display:"contents"},children:tt.jsx(gC,{className:Z.loadingIndicator,ownerState:V,children:E&&L})}),f]})}),vC=Ea(tt.jsx("path",{d:"M20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.76,4 13.5,4.11 14.2, 4.31L15.77,2.74C14.61,2.26 13.34,2 12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0, 0 22,12M7.91,10.08L6.5,11.5L11,16L21,6L19.59,4.58L11,13.17L7.91,10.08Z"})),bC=Ea(tt.jsx("path",{d:"M12 5.99L19.53 19H4.47L12 5.99M12 2L1 21h22L12 2zm1 14h-2v2h2v-2zm0-6h-2v4h2v-4z"})),SC=Ea(tt.jsx("path",{d:"M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"})),CC=Ea(tt.jsx("path",{d:"M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20, 12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10, 10 0 0,0 12,2M11,17H13V11H11V17Z"})),TC=Ea(tt.jsx("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})),xC=a=>{const{variant:i,color:o,severity:u,classes:s}=a,f={root:["root",`color${et(o||u)}`,`${i}${et(o||u)}`,`${i}`],icon:["icon"],message:["message"],action:["action"]};return He(f,iC,s)},EC=kt(W0,{name:"MuiAlert",slot:"Root",overridesResolver:(a,i)=>{const{ownerState:o}=a;return[i.root,i[o.variant],i[`${o.variant}${et(o.color||o.severity)}`]]}})(hn(({theme:a})=>{const i=a.palette.mode==="light"?nr:ar,o=a.palette.mode==="light"?ar:nr;return{...a.typography.body2,backgroundColor:"transparent",display:"flex",padding:"6px 16px",variants:[...Object.entries(a.palette).filter(we(["light"])).map(([u])=>({props:{colorSeverity:u,variant:"standard"},style:{color:a.vars?a.vars.palette.Alert[`${u}Color`]:i(a.palette[u].light,.6),backgroundColor:a.vars?a.vars.palette.Alert[`${u}StandardBg`]:o(a.palette[u].light,.9),[`& .${Wh.icon}`]:a.vars?{color:a.vars.palette.Alert[`${u}IconColor`]}:{color:a.palette[u].main}}})),...Object.entries(a.palette).filter(we(["light"])).map(([u])=>({props:{colorSeverity:u,variant:"outlined"},style:{color:a.vars?a.vars.palette.Alert[`${u}Color`]:i(a.palette[u].light,.6),border:`1px solid ${(a.vars||a).palette[u].light}`,[`& .${Wh.icon}`]:a.vars?{color:a.vars.palette.Alert[`${u}IconColor`]}:{color:a.palette[u].main}}})),...Object.entries(a.palette).filter(we(["dark"])).map(([u])=>({props:{colorSeverity:u,variant:"filled"},style:{fontWeight:a.typography.fontWeightMedium,...a.vars?{color:a.vars.palette.Alert[`${u}FilledColor`],backgroundColor:a.vars.palette.Alert[`${u}FilledBg`]}:{backgroundColor:a.palette.mode==="dark"?a.palette[u].dark:a.palette[u].main,color:a.palette.getContrastText(a.palette[u].main)}}}))]}})),AC=kt("div",{name:"MuiAlert",slot:"Icon"})({marginRight:12,padding:"7px 0",display:"flex",fontSize:22,opacity:.9}),RC=kt("div",{name:"MuiAlert",slot:"Message"})({padding:"8px 0",minWidth:0,overflow:"auto"}),OC=kt("div",{name:"MuiAlert",slot:"Action"})({display:"flex",alignItems:"flex-start",padding:"4px 0 0 16px",marginLeft:"auto",marginRight:-8}),Fh={success:tt.jsx(vC,{fontSize:"inherit"}),warning:tt.jsx(bC,{fontSize:"inherit"}),error:tt.jsx(SC,{fontSize:"inherit"}),info:tt.jsx(CC,{fontSize:"inherit"})},fT=w.forwardRef(function(i,o){const u=Ae({props:i,name:"MuiAlert"}),{action:s,children:f,className:p,closeText:h="Close",color:y,components:g={},componentsProps:b={},icon:C,iconMapping:E=Fh,onClose:B,role:R="alert",severity:T="success",slotProps:L={},slots:V={},variant:Z="standard",...X}=u,U={...u,color:y,severity:T,variant:Z,colorSeverity:y||T},M=xC(U),Q={slots:{closeButton:g.CloseButton,closeIcon:g.CloseIcon,...V},slotProps:{...b,...L}},[F,W]=Hl("root",{ref:o,shouldForwardComponentProp:!0,className:_t(M.root,p),elementType:EC,externalForwardedProps:{...Q,...X},ownerState:U,additionalProps:{role:R,elevation:0}}),[nt,v]=Hl("icon",{className:M.icon,elementType:AC,externalForwardedProps:Q,ownerState:U}),[k,P]=Hl("message",{className:M.message,elementType:RC,externalForwardedProps:Q,ownerState:U}),[it,ft]=Hl("action",{className:M.action,elementType:OC,externalForwardedProps:Q,ownerState:U}),[ht,$]=Hl("closeButton",{elementType:yC,externalForwardedProps:Q,ownerState:U}),[K,at]=Hl("closeIcon",{elementType:TC,externalForwardedProps:Q,ownerState:U});return tt.jsxs(F,{...W,children:[C!==!1?tt.jsx(nt,{...v,children:C||E[T]||Fh[T]}):null,tt.jsx(k,{...P,children:f}),s!=null?tt.jsx(it,{...ft,children:s}):null,s==null&&B?tt.jsx(it,{...ft,children:tt.jsx(ht,{size:"small","aria-label":h,title:h,color:"inherit",onClick:B,...$,children:tt.jsx(K,{fontSize:"small",...at})})}):null]})});function MC(a){return ze("MuiTypography",a)}je("MuiTypography",["root","h1","h2","h3","h4","h5","h6","subtitle1","subtitle2","body1","body2","inherit","button","caption","overline","alignLeft","alignRight","alignCenter","alignJustify","noWrap","gutterBottom","paragraph"]);const zC={primary:!0,secondary:!0,error:!0,info:!0,success:!0,warning:!0,textPrimary:!0,textSecondary:!0,textDisabled:!0},_C=v2(),BC=a=>{const{align:i,gutterBottom:o,noWrap:u,paragraph:s,variant:f,classes:p}=a,h={root:["root",f,a.align!=="inherit"&&`align${et(i)}`,o&&"gutterBottom",u&&"noWrap",s&&"paragraph"]};return He(h,MC,p)},DC=kt("span",{name:"MuiTypography",slot:"Root",overridesResolver:(a,i)=>{const{ownerState:o}=a;return[i.root,o.variant&&i[o.variant],o.align!=="inherit"&&i[`align${et(o.align)}`],o.noWrap&&i.noWrap,o.gutterBottom&&i.gutterBottom,o.paragraph&&i.paragraph]}})(hn(({theme:a})=>{var i;return{margin:0,variants:[{props:{variant:"inherit"},style:{font:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}},...Object.entries(a.typography).filter(([o,u])=>o!=="inherit"&&u&&typeof u=="object").map(([o,u])=>({props:{variant:o},style:u})),...Object.entries(a.palette).filter(we()).map(([o])=>({props:{color:o},style:{color:(a.vars||a).palette[o].main}})),...Object.entries(((i=a.palette)==null?void 0:i.text)||{}).filter(([,o])=>typeof o=="string").map(([o])=>({props:{color:`text${et(o)}`},style:{color:(a.vars||a).palette.text[o]}})),{props:({ownerState:o})=>o.align!=="inherit",style:{textAlign:"var(--Typography-textAlign)"}},{props:({ownerState:o})=>o.noWrap,style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},{props:({ownerState:o})=>o.gutterBottom,style:{marginBottom:"0.35em"}},{props:({ownerState:o})=>o.paragraph,style:{marginBottom:16}}]}})),Ih={h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",subtitle1:"h6",subtitle2:"h6",body1:"p",body2:"p",inherit:"p"},dT=w.forwardRef(function(i,o){const{color:u,...s}=Ae({props:i,name:"MuiTypography"}),f=!zC[u],p=_C({...s,...f&&{color:u}}),{align:h="inherit",className:y,component:g,gutterBottom:b=!1,noWrap:C=!1,paragraph:E=!1,variant:B="body1",variantMapping:R=Ih,...T}=p,L={...p,align:h,color:u,className:y,component:g,gutterBottom:b,noWrap:C,paragraph:E,variant:B,variantMapping:R},V=g||(E?"p":R[B]||Ih[B])||"span",Z=BC(L);return tt.jsx(DC,{as:V,ref:o,className:_t(Z.root,y),...T,ownerState:L,style:{...h!=="inherit"&&{"--Typography-textAlign":h},...T.style}})}),NC=Ea(tt.jsx("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"}));function $C(a){return ze("MuiChip",a)}const xt=je("MuiChip",["root","sizeSmall","sizeMedium","colorDefault","colorError","colorInfo","colorPrimary","colorSecondary","colorSuccess","colorWarning","disabled","clickable","clickableColorPrimary","clickableColorSecondary","deletable","deletableColorPrimary","deletableColorSecondary","outlined","filled","outlinedPrimary","outlinedSecondary","filledPrimary","filledSecondary","avatar","avatarSmall","avatarMedium","avatarColorPrimary","avatarColorSecondary","icon","iconSmall","iconMedium","iconColorPrimary","iconColorSecondary","label","labelSmall","labelMedium","deleteIcon","deleteIconSmall","deleteIconMedium","deleteIconColorPrimary","deleteIconColorSecondary","deleteIconOutlinedColorPrimary","deleteIconOutlinedColorSecondary","deleteIconFilledColorPrimary","deleteIconFilledColorSecondary","focusVisible"]),wC=a=>{const{classes:i,disabled:o,size:u,color:s,iconColor:f,onDelete:p,clickable:h,variant:y}=a,g={root:["root",y,o&&"disabled",`size${et(u)}`,`color${et(s)}`,h&&"clickable",h&&`clickableColor${et(s)}`,p&&"deletable",p&&`deletableColor${et(s)}`,`${y}${et(s)}`],label:["label",`label${et(u)}`],avatar:["avatar",`avatar${et(u)}`,`avatarColor${et(s)}`],icon:["icon",`icon${et(u)}`,`iconColor${et(f)}`],deleteIcon:["deleteIcon",`deleteIcon${et(u)}`,`deleteIconColor${et(s)}`,`deleteIcon${et(y)}Color${et(s)}`]};return He(g,$C,i)},UC=kt("div",{name:"MuiChip",slot:"Root",overridesResolver:(a,i)=>{const{ownerState:o}=a,{color:u,iconColor:s,clickable:f,onDelete:p,size:h,variant:y}=o;return[{[`& .${xt.avatar}`]:i.avatar},{[`& .${xt.avatar}`]:i[`avatar${et(h)}`]},{[`& .${xt.avatar}`]:i[`avatarColor${et(u)}`]},{[`& .${xt.icon}`]:i.icon},{[`& .${xt.icon}`]:i[`icon${et(h)}`]},{[`& .${xt.icon}`]:i[`iconColor${et(s)}`]},{[`& .${xt.deleteIcon}`]:i.deleteIcon},{[`& .${xt.deleteIcon}`]:i[`deleteIcon${et(h)}`]},{[`& .${xt.deleteIcon}`]:i[`deleteIconColor${et(u)}`]},{[`& .${xt.deleteIcon}`]:i[`deleteIcon${et(y)}Color${et(u)}`]},i.root,i[`size${et(h)}`],i[`color${et(u)}`],f&&i.clickable,f&&u!=="default"&&i[`clickableColor${et(u)})`],p&&i.deletable,p&&u!=="default"&&i[`deletableColor${et(u)}`],i[y],i[`${y}${et(u)}`]]}})(hn(({theme:a})=>{const i=a.palette.mode==="light"?a.palette.grey[700]:a.palette.grey[300];return{maxWidth:"100%",fontFamily:a.typography.fontFamily,fontSize:a.typography.pxToRem(13),display:"inline-flex",alignItems:"center",justifyContent:"center",height:32,color:(a.vars||a).palette.text.primary,backgroundColor:(a.vars||a).palette.action.selected,borderRadius:32/2,whiteSpace:"nowrap",transition:a.transitions.create(["background-color","box-shadow"]),cursor:"unset",outline:0,textDecoration:"none",border:0,padding:0,verticalAlign:"middle",boxSizing:"border-box",[`&.${xt.disabled}`]:{opacity:(a.vars||a).palette.action.disabledOpacity,pointerEvents:"none"},[`& .${xt.avatar}`]:{marginLeft:5,marginRight:-6,width:24,height:24,color:a.vars?a.vars.palette.Chip.defaultAvatarColor:i,fontSize:a.typography.pxToRem(12)},[`& .${xt.avatarColorPrimary}`]:{color:(a.vars||a).palette.primary.contrastText,backgroundColor:(a.vars||a).palette.primary.dark},[`& .${xt.avatarColorSecondary}`]:{color:(a.vars||a).palette.secondary.contrastText,backgroundColor:(a.vars||a).palette.secondary.dark},[`& .${xt.avatarSmall}`]:{marginLeft:4,marginRight:-4,width:18,height:18,fontSize:a.typography.pxToRem(10)},[`& .${xt.icon}`]:{marginLeft:5,marginRight:-6},[`& .${xt.deleteIcon}`]:{WebkitTapHighlightColor:"transparent",color:a.vars?`rgba(${a.vars.palette.text.primaryChannel} / 0.26)`:te(a.palette.text.primary,.26),fontSize:22,cursor:"pointer",margin:"0 5px 0 -6px","&:hover":{color:a.vars?`rgba(${a.vars.palette.text.primaryChannel} / 0.4)`:te(a.palette.text.primary,.4)}},variants:[{props:{size:"small"},style:{height:24,[`& .${xt.icon}`]:{fontSize:18,marginLeft:4,marginRight:-4},[`& .${xt.deleteIcon}`]:{fontSize:16,marginRight:4,marginLeft:-4}}},...Object.entries(a.palette).filter(we(["contrastText"])).map(([o])=>({props:{color:o},style:{backgroundColor:(a.vars||a).palette[o].main,color:(a.vars||a).palette[o].contrastText,[`& .${xt.deleteIcon}`]:{color:a.vars?`rgba(${a.vars.palette[o].contrastTextChannel} / 0.7)`:te(a.palette[o].contrastText,.7),"&:hover, &:active":{color:(a.vars||a).palette[o].contrastText}}}})),{props:o=>o.iconColor===o.color,style:{[`& .${xt.icon}`]:{color:a.vars?a.vars.palette.Chip.defaultIconColor:i}}},{props:o=>o.iconColor===o.color&&o.color!=="default",style:{[`& .${xt.icon}`]:{color:"inherit"}}},{props:{onDelete:!0},style:{[`&.${xt.focusVisible}`]:{backgroundColor:a.vars?`rgba(${a.vars.palette.action.selectedChannel} / calc(${a.vars.palette.action.selectedOpacity} + ${a.vars.palette.action.focusOpacity}))`:te(a.palette.action.selected,a.palette.action.selectedOpacity+a.palette.action.focusOpacity)}}},...Object.entries(a.palette).filter(we(["dark"])).map(([o])=>({props:{color:o,onDelete:!0},style:{[`&.${xt.focusVisible}`]:{background:(a.vars||a).palette[o].dark}}})),{props:{clickable:!0},style:{userSelect:"none",WebkitTapHighlightColor:"transparent",cursor:"pointer","&:hover":{backgroundColor:a.vars?`rgba(${a.vars.palette.action.selectedChannel} / calc(${a.vars.palette.action.selectedOpacity} + ${a.vars.palette.action.hoverOpacity}))`:te(a.palette.action.selected,a.palette.action.selectedOpacity+a.palette.action.hoverOpacity)},[`&.${xt.focusVisible}`]:{backgroundColor:a.vars?`rgba(${a.vars.palette.action.selectedChannel} / calc(${a.vars.palette.action.selectedOpacity} + ${a.vars.palette.action.focusOpacity}))`:te(a.palette.action.selected,a.palette.action.selectedOpacity+a.palette.action.focusOpacity)},"&:active":{boxShadow:(a.vars||a).shadows[1]}}},...Object.entries(a.palette).filter(we(["dark"])).map(([o])=>({props:{color:o,clickable:!0},style:{[`&:hover, &.${xt.focusVisible}`]:{backgroundColor:(a.vars||a).palette[o].dark}}})),{props:{variant:"outlined"},style:{backgroundColor:"transparent",border:a.vars?`1px solid ${a.vars.palette.Chip.defaultBorder}`:`1px solid ${a.palette.mode==="light"?a.palette.grey[400]:a.palette.grey[700]}`,[`&.${xt.clickable}:hover`]:{backgroundColor:(a.vars||a).palette.action.hover},[`&.${xt.focusVisible}`]:{backgroundColor:(a.vars||a).palette.action.focus},[`& .${xt.avatar}`]:{marginLeft:4},[`& .${xt.avatarSmall}`]:{marginLeft:2},[`& .${xt.icon}`]:{marginLeft:4},[`& .${xt.iconSmall}`]:{marginLeft:2},[`& .${xt.deleteIcon}`]:{marginRight:5},[`& .${xt.deleteIconSmall}`]:{marginRight:3}}},...Object.entries(a.palette).filter(we()).map(([o])=>({props:{variant:"outlined",color:o},style:{color:(a.vars||a).palette[o].main,border:`1px solid ${a.vars?`rgba(${a.vars.palette[o].mainChannel} / 0.7)`:te(a.palette[o].main,.7)}`,[`&.${xt.clickable}:hover`]:{backgroundColor:a.vars?`rgba(${a.vars.palette[o].mainChannel} / ${a.vars.palette.action.hoverOpacity})`:te(a.palette[o].main,a.palette.action.hoverOpacity)},[`&.${xt.focusVisible}`]:{backgroundColor:a.vars?`rgba(${a.vars.palette[o].mainChannel} / ${a.vars.palette.action.focusOpacity})`:te(a.palette[o].main,a.palette.action.focusOpacity)},[`& .${xt.deleteIcon}`]:{color:a.vars?`rgba(${a.vars.palette[o].mainChannel} / 0.7)`:te(a.palette[o].main,.7),"&:hover, &:active":{color:(a.vars||a).palette[o].main}}}}))]}})),jC=kt("span",{name:"MuiChip",slot:"Label",overridesResolver:(a,i)=>{const{ownerState:o}=a,{size:u}=o;return[i.label,i[`label${et(u)}`]]}})({overflow:"hidden",textOverflow:"ellipsis",paddingLeft:12,paddingRight:12,whiteSpace:"nowrap",variants:[{props:{variant:"outlined"},style:{paddingLeft:11,paddingRight:11}},{props:{size:"small"},style:{paddingLeft:8,paddingRight:8}},{props:{size:"small",variant:"outlined"},style:{paddingLeft:7,paddingRight:7}}]});function t0(a){return a.key==="Backspace"||a.key==="Delete"}const pT=w.forwardRef(function(i,o){const u=Ae({props:i,name:"MuiChip"}),{avatar:s,className:f,clickable:p,color:h="default",component:y,deleteIcon:g,disabled:b=!1,icon:C,label:E,onClick:B,onDelete:R,onKeyDown:T,onKeyUp:L,size:V="medium",variant:Z="filled",tabIndex:X,skipFocusWhenDisabled:U=!1,...M}=u,Q=w.useRef(null),F=ko(Q,o),W=lt=>{lt.stopPropagation(),R&&R(lt)},nt=lt=>{lt.currentTarget===lt.target&&t0(lt)&&lt.preventDefault(),T&&T(lt)},v=lt=>{lt.currentTarget===lt.target&&R&&t0(lt)&&R(lt),L&&L(lt)},k=p!==!1&&B?!0:p,P=k||R?Yo:y||"div",it={...u,component:P,disabled:b,size:V,color:h,iconColor:w.isValidElement(C)&&C.props.color||h,onDelete:!!R,clickable:k,variant:Z},ft=wC(it),ht=P===Yo?{component:y||"div",focusVisibleClassName:ft.focusVisible,...R&&{disableRipple:!0}}:{};let $=null;R&&($=g&&w.isValidElement(g)?w.cloneElement(g,{className:_t(g.props.className,ft.deleteIcon),onClick:W}):tt.jsx(NC,{className:ft.deleteIcon,onClick:W}));let K=null;s&&w.isValidElement(s)&&(K=w.cloneElement(s,{className:_t(ft.avatar,s.props.className)}));let at=null;return C&&w.isValidElement(C)&&(at=w.cloneElement(C,{className:_t(ft.icon,C.props.className)})),tt.jsxs(UC,{as:P,className:_t(ft.root,f),disabled:k&&b?!0:void 0,onClick:B,onKeyDown:nt,onKeyUp:v,ref:F,tabIndex:U&&b?-1:X,ownerState:it,...ht,...M,children:[K||at,tt.jsx(jC,{className:ft.label,ownerState:it,children:E}),$]})}),HC=je("MuiBox",["root"]),GC=uf(),mT=Bb({themeId:On,defaultTheme:GC,defaultClassName:HC.root,generateClassName:A0.generate});function LC(a){return ze("MuiButton",a)}const Ya=je("MuiButton",["root","text","textInherit","textPrimary","textSecondary","textSuccess","textError","textInfo","textWarning","outlined","outlinedInherit","outlinedPrimary","outlinedSecondary","outlinedSuccess","outlinedError","outlinedInfo","outlinedWarning","contained","containedInherit","containedPrimary","containedSecondary","containedSuccess","containedError","containedInfo","containedWarning","disableElevation","focusVisible","disabled","colorInherit","colorPrimary","colorSecondary","colorSuccess","colorError","colorInfo","colorWarning","textSizeSmall","textSizeMedium","textSizeLarge","outlinedSizeSmall","outlinedSizeMedium","outlinedSizeLarge","containedSizeSmall","containedSizeMedium","containedSizeLarge","sizeMedium","sizeSmall","sizeLarge","fullWidth","startIcon","endIcon","icon","iconSizeSmall","iconSizeMedium","iconSizeLarge","loading","loadingWrapper","loadingIconPlaceholder","loadingIndicator","loadingPositionCenter","loadingPositionStart","loadingPositionEnd"]),F0=w.createContext({}),I0=w.createContext(void 0),kC=a=>{const{color:i,disableElevation:o,fullWidth:u,size:s,variant:f,loading:p,loadingPosition:h,classes:y}=a,g={root:["root",p&&"loading",f,`${f}${et(i)}`,`size${et(s)}`,`${f}Size${et(s)}`,`color${et(i)}`,o&&"disableElevation",u&&"fullWidth",p&&`loadingPosition${et(h)}`],startIcon:["icon","startIcon",`iconSize${et(s)}`],endIcon:["icon","endIcon",`iconSize${et(s)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]},b=He(g,LC,y);return{...y,...b}},tg=[{props:{size:"small"},style:{"& > *:nth-of-type(1)":{fontSize:18}}},{props:{size:"medium"},style:{"& > *:nth-of-type(1)":{fontSize:20}}},{props:{size:"large"},style:{"& > *:nth-of-type(1)":{fontSize:22}}}],qC=kt(Yo,{shouldForwardProp:a=>Q0(a)||a==="classes",name:"MuiButton",slot:"Root",overridesResolver:(a,i)=>{const{ownerState:o}=a;return[i.root,i[o.variant],i[`${o.variant}${et(o.color)}`],i[`size${et(o.size)}`],i[`${o.variant}Size${et(o.size)}`],o.color==="inherit"&&i.colorInherit,o.disableElevation&&i.disableElevation,o.fullWidth&&i.fullWidth,o.loading&&i.loading]}})(hn(({theme:a})=>{const i=a.palette.mode==="light"?a.palette.grey[300]:a.palette.grey[800],o=a.palette.mode==="light"?a.palette.grey.A100:a.palette.grey[700];return{...a.typography.button,minWidth:64,padding:"6px 16px",border:0,borderRadius:(a.vars||a).shape.borderRadius,transition:a.transitions.create(["background-color","box-shadow","border-color","color"],{duration:a.transitions.duration.short}),"&:hover":{textDecoration:"none"},[`&.${Ya.disabled}`]:{color:(a.vars||a).palette.action.disabled},variants:[{props:{variant:"contained"},style:{color:"var(--variant-containedColor)",backgroundColor:"var(--variant-containedBg)",boxShadow:(a.vars||a).shadows[2],"&:hover":{boxShadow:(a.vars||a).shadows[4],"@media (hover: none)":{boxShadow:(a.vars||a).shadows[2]}},"&:active":{boxShadow:(a.vars||a).shadows[8]},[`&.${Ya.focusVisible}`]:{boxShadow:(a.vars||a).shadows[6]},[`&.${Ya.disabled}`]:{color:(a.vars||a).palette.action.disabled,boxShadow:(a.vars||a).shadows[0],backgroundColor:(a.vars||a).palette.action.disabledBackground}}},{props:{variant:"outlined"},style:{padding:"5px 15px",border:"1px solid currentColor",borderColor:"var(--variant-outlinedBorder, currentColor)",backgroundColor:"var(--variant-outlinedBg)",color:"var(--variant-outlinedColor)",[`&.${Ya.disabled}`]:{border:`1px solid ${(a.vars||a).palette.action.disabledBackground}`}}},{props:{variant:"text"},style:{padding:"6px 8px",color:"var(--variant-textColor)",backgroundColor:"var(--variant-textBg)"}},...Object.entries(a.palette).filter(we()).map(([u])=>({props:{color:u},style:{"--variant-textColor":(a.vars||a).palette[u].main,"--variant-outlinedColor":(a.vars||a).palette[u].main,"--variant-outlinedBorder":a.vars?`rgba(${a.vars.palette[u].mainChannel} / 0.5)`:te(a.palette[u].main,.5),"--variant-containedColor":(a.vars||a).palette[u].contrastText,"--variant-containedBg":(a.vars||a).palette[u].main,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":(a.vars||a).palette[u].dark,"--variant-textBg":a.vars?`rgba(${a.vars.palette[u].mainChannel} / ${a.vars.palette.action.hoverOpacity})`:te(a.palette[u].main,a.palette.action.hoverOpacity),"--variant-outlinedBorder":(a.vars||a).palette[u].main,"--variant-outlinedBg":a.vars?`rgba(${a.vars.palette[u].mainChannel} / ${a.vars.palette.action.hoverOpacity})`:te(a.palette[u].main,a.palette.action.hoverOpacity)}}}})),{props:{color:"inherit"},style:{color:"inherit",borderColor:"currentColor","--variant-containedBg":a.vars?a.vars.palette.Button.inheritContainedBg:i,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":a.vars?a.vars.palette.Button.inheritContainedHoverBg:o,"--variant-textBg":a.vars?`rgba(${a.vars.palette.text.primaryChannel} / ${a.vars.palette.action.hoverOpacity})`:te(a.palette.text.primary,a.palette.action.hoverOpacity),"--variant-outlinedBg":a.vars?`rgba(${a.vars.palette.text.primaryChannel} / ${a.vars.palette.action.hoverOpacity})`:te(a.palette.text.primary,a.palette.action.hoverOpacity)}}}},{props:{size:"small",variant:"text"},style:{padding:"4px 5px",fontSize:a.typography.pxToRem(13)}},{props:{size:"large",variant:"text"},style:{padding:"8px 11px",fontSize:a.typography.pxToRem(15)}},{props:{size:"small",variant:"outlined"},style:{padding:"3px 9px",fontSize:a.typography.pxToRem(13)}},{props:{size:"large",variant:"outlined"},style:{padding:"7px 21px",fontSize:a.typography.pxToRem(15)}},{props:{size:"small",variant:"contained"},style:{padding:"4px 10px",fontSize:a.typography.pxToRem(13)}},{props:{size:"large",variant:"contained"},style:{padding:"8px 22px",fontSize:a.typography.pxToRem(15)}},{props:{disableElevation:!0},style:{boxShadow:"none","&:hover":{boxShadow:"none"},[`&.${Ya.focusVisible}`]:{boxShadow:"none"},"&:active":{boxShadow:"none"},[`&.${Ya.disabled}`]:{boxShadow:"none"}}},{props:{fullWidth:!0},style:{width:"100%"}},{props:{loadingPosition:"center"},style:{transition:a.transitions.create(["background-color","box-shadow","border-color"],{duration:a.transitions.duration.short}),[`&.${Ya.loading}`]:{color:"transparent"}}}]}})),YC=kt("span",{name:"MuiButton",slot:"StartIcon",overridesResolver:(a,i)=>{const{ownerState:o}=a;return[i.startIcon,o.loading&&i.startIconLoadingStart,i[`iconSize${et(o.size)}`]]}})(({theme:a})=>({display:"inherit",marginRight:8,marginLeft:-4,variants:[{props:{size:"small"},style:{marginLeft:-2}},{props:{loadingPosition:"start",loading:!0},style:{transition:a.transitions.create(["opacity"],{duration:a.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"start",loading:!0,fullWidth:!0},style:{marginRight:-8}},...tg]})),VC=kt("span",{name:"MuiButton",slot:"EndIcon",overridesResolver:(a,i)=>{const{ownerState:o}=a;return[i.endIcon,o.loading&&i.endIconLoadingEnd,i[`iconSize${et(o.size)}`]]}})(({theme:a})=>({display:"inherit",marginRight:-4,marginLeft:8,variants:[{props:{size:"small"},style:{marginRight:-2}},{props:{loadingPosition:"end",loading:!0},style:{transition:a.transitions.create(["opacity"],{duration:a.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"end",loading:!0,fullWidth:!0},style:{marginLeft:-8}},...tg]})),XC=kt("span",{name:"MuiButton",slot:"LoadingIndicator"})(({theme:a})=>({display:"none",position:"absolute",visibility:"visible",variants:[{props:{loading:!0},style:{display:"flex"}},{props:{loadingPosition:"start"},style:{left:14}},{props:{loadingPosition:"start",size:"small"},style:{left:10}},{props:{variant:"text",loadingPosition:"start"},style:{left:6}},{props:{loadingPosition:"center"},style:{left:"50%",transform:"translate(-50%)",color:(a.vars||a).palette.action.disabled}},{props:{loadingPosition:"end"},style:{right:14}},{props:{loadingPosition:"end",size:"small"},style:{right:10}},{props:{variant:"text",loadingPosition:"end"},style:{right:6}},{props:{loadingPosition:"start",fullWidth:!0},style:{position:"relative",left:-10}},{props:{loadingPosition:"end",fullWidth:!0},style:{position:"relative",right:-10}}]})),e0=kt("span",{name:"MuiButton",slot:"LoadingIconPlaceholder"})({display:"inline-block",width:"1em",height:"1em"}),hT=w.forwardRef(function(i,o){const u=w.useContext(F0),s=w.useContext(I0),f=er(u,i),p=Ae({props:f,name:"MuiButton"}),{children:h,color:y="primary",component:g="button",className:b,disabled:C=!1,disableElevation:E=!1,disableFocusRipple:B=!1,endIcon:R,focusVisibleClassName:T,fullWidth:L=!1,id:V,loading:Z=null,loadingIndicator:X,loadingPosition:U="center",size:M="medium",startIcon:Q,type:F,variant:W="text",...nt}=p,v=K0(V),k=X??tt.jsx(P0,{"aria-labelledby":v,color:"inherit",size:16}),P={...p,color:y,component:g,disabled:C,disableElevation:E,disableFocusRipple:B,fullWidth:L,loading:Z,loadingIndicator:k,loadingPosition:U,size:M,type:F,variant:W},it=kC(P),ft=(Q||Z&&U==="start")&&tt.jsx(YC,{className:it.startIcon,ownerState:P,children:Q||tt.jsx(e0,{className:it.loadingIconPlaceholder,ownerState:P})}),ht=(R||Z&&U==="end")&&tt.jsx(VC,{className:it.endIcon,ownerState:P,children:R||tt.jsx(e0,{className:it.loadingIconPlaceholder,ownerState:P})}),$=s||"",K=typeof Z=="boolean"?tt.jsx("span",{className:it.loadingWrapper,style:{display:"contents"},children:Z&&tt.jsx(XC,{className:it.loadingIndicator,ownerState:P,children:k})}):null;return tt.jsxs(qC,{ownerState:P,className:_t(u.className,it.root,b,$),component:g,disabled:C||Z,focusRipple:!B,focusVisibleClassName:_t(it.focusVisible,T),ref:o,type:F,id:Z?v:V,...nt,classes:it,children:[ft,U!=="end"&&K,h,U==="end"&&K,ht]})});function QC(a){return w.Children.toArray(a).filter(i=>w.isValidElement(i))}function ZC(a){return ze("MuiButtonGroup",a)}const yt=je("MuiButtonGroup",["root","contained","outlined","text","disableElevation","disabled","firstButton","fullWidth","horizontal","vertical","colorPrimary","colorSecondary","grouped","groupedHorizontal","groupedVertical","groupedText","groupedTextHorizontal","groupedTextVertical","groupedTextPrimary","groupedTextSecondary","groupedOutlined","groupedOutlinedHorizontal","groupedOutlinedVertical","groupedOutlinedPrimary","groupedOutlinedSecondary","groupedContained","groupedContainedHorizontal","groupedContainedVertical","groupedContainedPrimary","groupedContainedSecondary","lastButton","middleButton"]),KC=(a,i)=>{const{ownerState:o}=a;return[{[`& .${yt.grouped}`]:i.grouped},{[`& .${yt.grouped}`]:i[`grouped${et(o.orientation)}`]},{[`& .${yt.grouped}`]:i[`grouped${et(o.variant)}`]},{[`& .${yt.grouped}`]:i[`grouped${et(o.variant)}${et(o.orientation)}`]},{[`& .${yt.grouped}`]:i[`grouped${et(o.variant)}${et(o.color)}`]},{[`& .${yt.firstButton}`]:i.firstButton},{[`& .${yt.lastButton}`]:i.lastButton},{[`& .${yt.middleButton}`]:i.middleButton},i.root,i[o.variant],o.disableElevation===!0&&i.disableElevation,o.fullWidth&&i.fullWidth,o.orientation==="vertical"&&i.vertical]},JC=a=>{const{classes:i,color:o,disabled:u,disableElevation:s,fullWidth:f,orientation:p,variant:h}=a,y={root:["root",h,p,f&&"fullWidth",s&&"disableElevation",`color${et(o)}`],grouped:["grouped",`grouped${et(p)}`,`grouped${et(h)}`,`grouped${et(h)}${et(p)}`,`grouped${et(h)}${et(o)}`,u&&"disabled"],firstButton:["firstButton"],lastButton:["lastButton"],middleButton:["middleButton"]};return He(y,ZC,i)},WC=kt("div",{name:"MuiButtonGroup",slot:"Root",overridesResolver:KC})(hn(({theme:a})=>({display:"inline-flex",borderRadius:(a.vars||a).shape.borderRadius,variants:[{props:{variant:"contained"},style:{boxShadow:(a.vars||a).shadows[2]}},{props:{disableElevation:!0},style:{boxShadow:"none"}},{props:{fullWidth:!0},style:{width:"100%"}},{props:{orientation:"vertical"},style:{flexDirection:"column",[`& .${yt.lastButton},& .${yt.middleButton}`]:{borderTopRightRadius:0,borderTopLeftRadius:0},[`& .${yt.firstButton},& .${yt.middleButton}`]:{borderBottomRightRadius:0,borderBottomLeftRadius:0}}},{props:{orientation:"horizontal"},style:{[`& .${yt.firstButton},& .${yt.middleButton}`]:{borderTopRightRadius:0,borderBottomRightRadius:0},[`& .${yt.lastButton},& .${yt.middleButton}`]:{borderTopLeftRadius:0,borderBottomLeftRadius:0}}},{props:{variant:"text",orientation:"horizontal"},style:{[`& .${yt.firstButton},& .${yt.middleButton}`]:{borderRight:a.vars?`1px solid rgba(${a.vars.palette.common.onBackgroundChannel} / 0.23)`:`1px solid ${a.palette.mode==="light"?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)"}`,[`&.${yt.disabled}`]:{borderRight:`1px solid ${(a.vars||a).palette.action.disabled}`}}}},{props:{variant:"text",orientation:"vertical"},style:{[`& .${yt.firstButton},& .${yt.middleButton}`]:{borderBottom:a.vars?`1px solid rgba(${a.vars.palette.common.onBackgroundChannel} / 0.23)`:`1px solid ${a.palette.mode==="light"?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)"}`,[`&.${yt.disabled}`]:{borderBottom:`1px solid ${(a.vars||a).palette.action.disabled}`}}}},...Object.entries(a.palette).filter(we()).flatMap(([i])=>[{props:{variant:"text",color:i},style:{[`& .${yt.firstButton},& .${yt.middleButton}`]:{borderColor:a.vars?`rgba(${a.vars.palette[i].mainChannel} / 0.5)`:te(a.palette[i].main,.5)}}}]),{props:{variant:"outlined",orientation:"horizontal"},style:{[`& .${yt.firstButton},& .${yt.middleButton}`]:{borderRightColor:"transparent","&:hover":{borderRightColor:"currentColor"}},[`& .${yt.lastButton},& .${yt.middleButton}`]:{marginLeft:-1}}},{props:{variant:"outlined",orientation:"vertical"},style:{[`& .${yt.firstButton},& .${yt.middleButton}`]:{borderBottomColor:"transparent","&:hover":{borderBottomColor:"currentColor"}},[`& .${yt.lastButton},& .${yt.middleButton}`]:{marginTop:-1}}},{props:{variant:"contained",orientation:"horizontal"},style:{[`& .${yt.firstButton},& .${yt.middleButton}`]:{borderRight:`1px solid ${(a.vars||a).palette.grey[400]}`,[`&.${yt.disabled}`]:{borderRight:`1px solid ${(a.vars||a).palette.action.disabled}`}}}},{props:{variant:"contained",orientation:"vertical"},style:{[`& .${yt.firstButton},& .${yt.middleButton}`]:{borderBottom:`1px solid ${(a.vars||a).palette.grey[400]}`,[`&.${yt.disabled}`]:{borderBottom:`1px solid ${(a.vars||a).palette.action.disabled}`}}}},...Object.entries(a.palette).filter(we(["dark"])).map(([i])=>({props:{variant:"contained",color:i},style:{[`& .${yt.firstButton},& .${yt.middleButton}`]:{borderColor:(a.vars||a).palette[i].dark}}}))],[`& .${yt.grouped}`]:{minWidth:40,boxShadow:"none",props:{variant:"contained"},style:{"&:hover":{boxShadow:"none"}}}}))),gT=w.forwardRef(function(i,o){const u=Ae({props:i,name:"MuiButtonGroup"}),{children:s,className:f,color:p="primary",component:h="div",disabled:y=!1,disableElevation:g=!1,disableFocusRipple:b=!1,disableRipple:C=!1,fullWidth:E=!1,orientation:B="horizontal",size:R="medium",variant:T="outlined",...L}=u,V={...u,color:p,component:h,disabled:y,disableElevation:g,disableFocusRipple:b,disableRipple:C,fullWidth:E,orientation:B,size:R,variant:T},Z=JC(V),X=w.useMemo(()=>({className:Z.grouped,color:p,disabled:y,disableElevation:g,disableFocusRipple:b,disableRipple:C,fullWidth:E,size:R,variant:T}),[p,y,g,b,C,E,R,T,Z.grouped]),U=QC(s),M=U.length,Q=F=>{const W=F===0,nt=F===M-1;return W&&nt?"":W?Z.firstButton:nt?Z.lastButton:Z.middleButton};return tt.jsx(WC,{as:h,role:"group",className:_t(Z.root,f),ref:o,ownerState:V,...L,children:tt.jsx(F0.Provider,{value:X,children:U.map((F,W)=>tt.jsx(I0.Provider,{value:Q(W),children:F},W))})})});function PC(a){return ze("MuiCard",a)}je("MuiCard",["root"]);const FC=a=>{const{classes:i}=a;return He({root:["root"]},PC,i)},IC=kt(W0,{name:"MuiCard",slot:"Root"})({overflow:"hidden"}),yT=w.forwardRef(function(i,o){const u=Ae({props:i,name:"MuiCard"}),{className:s,raised:f=!1,...p}=u,h={...u,raised:f},y=FC(h);return tt.jsx(IC,{className:_t(y.root,s),elevation:f?8:void 0,ref:o,ownerState:h,...p})});function tT(a){return ze("MuiCardContent",a)}je("MuiCardContent",["root"]);const eT=a=>{const{classes:i}=a;return He({root:["root"]},tT,i)},nT=kt("div",{name:"MuiCardContent",slot:"Root"})({padding:16,"&:last-child":{paddingBottom:24}}),vT=w.forwardRef(function(i,o){const u=Ae({props:i,name:"MuiCardContent"}),{className:s,component:f="div",...p}=u,h={...u,component:f},y=eT(h);return tt.jsx(nT,{as:f,className:_t(y.root,s),ownerState:h,ref:o,...p})}),Ys=typeof Z0({})=="function",aT=(a,i)=>({WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",boxSizing:"border-box",WebkitTextSizeAdjust:"100%",...i&&!a.vars&&{colorScheme:a.palette.mode}}),lT=a=>({color:(a.vars||a).palette.text.primary,...a.typography.body1,backgroundColor:(a.vars||a).palette.background.default,"@media print":{backgroundColor:(a.vars||a).palette.common.white}}),eg=(a,i=!1)=>{var f,p;const o={};i&&a.colorSchemes&&typeof a.getColorSchemeSelector=="function"&&Object.entries(a.colorSchemes).forEach(([h,y])=>{var b,C;const g=a.getColorSchemeSelector(h);g.startsWith("@")?o[g]={":root":{colorScheme:(b=y.palette)==null?void 0:b.mode}}:o[g.replace(/\s*&/,"")]={colorScheme:(C=y.palette)==null?void 0:C.mode}});let u={html:aT(a,i),"*, *::before, *::after":{boxSizing:"inherit"},"strong, b":{fontWeight:a.typography.fontWeightBold},body:{margin:0,...lT(a),"&::backdrop":{backgroundColor:(a.vars||a).palette.background.default}},...o};const s=(p=(f=a.components)==null?void 0:f.MuiCssBaseline)==null?void 0:p.styleOverrides;return s&&(u=[u,s]),u},Uo="mui-ecs",iT=a=>{const i=eg(a,!1),o=Array.isArray(i)?i[0]:i;return!a.vars&&o&&(o.html[`:root:has(${Uo})`]={colorScheme:a.palette.mode}),a.colorSchemes&&Object.entries(a.colorSchemes).forEach(([u,s])=>{var p,h;const f=a.getColorSchemeSelector(u);f.startsWith("@")?o[f]={[`:root:not(:has(.${Uo}))`]:{colorScheme:(p=s.palette)==null?void 0:p.mode}}:o[f.replace(/\s*&/,"")]={[`&:not(:has(.${Uo}))`]:{colorScheme:(h=s.palette)==null?void 0:h.mode}}}),i},rT=Z0(Ys?({theme:a,enableColorScheme:i})=>eg(a,i):({theme:a})=>iT(a));function bT(a){const i=Ae({props:a,name:"MuiCssBaseline"}),{children:o,enableColorScheme:u=!1}=i;return tt.jsxs(w.Fragment,{children:[Ys&&tt.jsx(rT,{enableColorScheme:u}),!Ys&&!u&&tt.jsx("span",{className:Uo,style:{display:"none"}}),o]})}const ST=_S({createStyledComponent:kt("div",{name:"MuiGrid",slot:"Root",overridesResolver:(a,i)=>{const{ownerState:o}=a;return[i.root,o.container&&i.container]}}),componentName:"MuiGrid",useThemeProps:a=>Ae({props:a,name:"MuiGrid"}),useTheme:X0}),CT=jS({createStyledComponent:kt("div",{name:"MuiStack",slot:"Root"}),useThemeProps:a=>Ae({props:a,name:"MuiStack"})}),TT=Ea(tt.jsx("path",{d:"M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6"})),xT=Ea(tt.jsx("path",{d:"m16 6 2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"}));export{$2 as $,fT as A,mT as B,bT as C,G2 as D,D0 as E,cT as F,ST as G,wo as H,yC as I,Z0 as J,hn as K,et as L,Xa as M,X0 as N,Hl as O,W0 as P,Yo as Q,uT as R,TT as S,sT as T,Q0 as U,k2 as V,K0 as W,Me as X,we as Y,mS as Z,A2 as _,je as a,te as a0,p2 as a1,kb as a2,Jh as a3,ar as a4,nr as a5,df as a6,P0 as a7,Ov as a8,He as b,_t as c,Ea as d,uf as e,dT as f,ze as g,yT as h,vT as i,tt as j,xT as k,hT as l,pT as m,CT as n,gT as o,n0 as p,Ev as q,w as r,kt as s,E2 as t,Ae as u,Gl as v,Qh as w,L2 as x,q2 as y,ko as z};
