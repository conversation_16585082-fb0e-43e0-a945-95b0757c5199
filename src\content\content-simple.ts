// Binomo Trading Assistant - Simple Content Script
// All components inline, no external dependencies

// Interface definitions
interface ModalAction {
  text: string;
  primary?: boolean;
  onClick: () => void;
}

console.log('🎯 Binomo Trading Assistant - Simple Content Script Loaded');

class BinomoTradingAssistant {
  private currentFlow: 'goals' | 'psychology' | 'method' | 'analysis' | 'trading' | null = null;
  private apiBase = 'http://127.0.0.1:3001';
  private methodSettings: any = {};
  private isBlocked = false;

  // Helper method for fetch with timeout and better error handling
  private async fetchWithTimeout(url: string, options: RequestInit = {}, timeout = 5000): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        }
      });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      if ((error as Error).name === 'AbortError') {
        throw new Error('Request timeout - JSON Server may not be running');
      }
      throw error;
    }
  }

  constructor() {
    this.init();
  }

  private async init() {
    console.log('✅ Page ready, initializing Binomo Trading Assistant...');

    // Check if we're on the correct page
    if (!window.location.href.includes('binomo1.com/trading')) {
      console.log('Not on Binomo trading page, exiting...');
      return;
    }

    // Check if trading is blocked
    await this.checkTradingBlock();

    if (this.isBlocked) {
      this.showBlockedMessage();
      return;
    }

    // Load method settings
    await this.loadMethodSettings();

    // Wait for page to be ready
    await this.waitForPageReady();

    // Add some delay to ensure page is fully loaded
    setTimeout(() => {
      this.startTradingFlow();
    }, 2000);

    // Setup message listener
    this.setupMessageListener();
  }

  private async checkTradingBlock() {
    try {
      const result = await chrome.storage.local.get(['tradingBlocked', 'blockDate']);
      if (result.tradingBlocked && result.blockDate) {
        const blockDate = new Date(result.blockDate);
        const today = new Date();

        // Check if it's a new day (reset at midnight)
        const isNewDay = today.toDateString() !== blockDate.toDateString();

        if (isNewDay) {
          // Unblock for new day
          await chrome.storage.local.remove(['tradingBlocked', 'blockDate']);
          this.isBlocked = false;
          console.log('✅ New day - trading unblocked');
        } else {
          this.isBlocked = true;
          console.log('🚫 Trading still blocked for today');
        }
      } else {
        this.isBlocked = false;
      }
    } catch (error) {
      console.error('Error checking trading block:', error);
      this.isBlocked = false;
    }
  }

  private showBlockedMessage() {
    // Create full-screen overlay
    const overlay = document.createElement('div');
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    overlay.innerHTML = `
      <div style="
        background: white;
        padding: 40px;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        text-align: center;
        max-width: 500px;
        margin: 20px;
      ">
        <div style="font-size: 64px; margin-bottom: 20px;">🧘‍♂️</div>
        <h1 style="color: #333; margin-bottom: 20px; font-size: 28px;">
          Thời gian nghỉ ngơi
        </h1>
        <p style="color: #666; margin-bottom: 30px; font-size: 16px; line-height: 1.6;">
          Tâm lý hiện tại không phù hợp để giao dịch.<br>
          Hãy dành thời gian thiền để tái tạo năng lượng tích cực.<br>
          <strong>Giao dịch sẽ được mở lại vào ngày mai.</strong>
        </p>
        <button id="meditation-btn" style="
          background: linear-gradient(45deg, #667eea, #764ba2);
          color: white;
          border: none;
          padding: 15px 30px;
          border-radius: 25px;
          font-size: 16px;
          cursor: pointer;
          margin-right: 10px;
          transition: transform 0.2s;
        ">
          🧘‍♂️ Đi thiền
        </button>
        <button id="close-tab-btn" style="
          background: #f44336;
          color: white;
          border: none;
          padding: 15px 30px;
          border-radius: 25px;
          font-size: 16px;
          cursor: pointer;
          transition: transform 0.2s;
        ">
          🚪 Đóng tab
        </button>
      </div>
    `;

    document.body.appendChild(overlay);

    // Add event listeners
    document.getElementById('meditation-btn')?.addEventListener('click', () => {
      this.sendMessageSafely({ action: 'openOptions', tab: 'meditation' });
      window.close();
    });

    document.getElementById('close-tab-btn')?.addEventListener('click', () => {
      window.close();
    });

    // Hide page content
    document.body.style.overflow = 'hidden';
  }

  private async sendMessageSafely(message: any) {
    try {
      // Check if chrome.runtime is available
      if (!chrome?.runtime?.id) {
        throw new Error('Chrome runtime not available');
      }

      const response = await chrome.runtime.sendMessage(message);
      console.log('Message sent successfully:', response);
      return response;
    } catch (error) {
      console.log('Could not send message to background script:', error);

      // Fallback: try to open options page directly
      if (message.action === 'openOptions') {
        try {
          // Check if chrome.runtime is available for getURL
          if (chrome?.runtime?.getURL) {
            const optionsUrl = chrome.runtime.getURL('options.html');

            // Store requested tab for options page
            if (message.tab && chrome?.storage?.local) {
              await chrome.storage.local.set({ requestedTab: message.tab });
            }

            // Open in new tab
            window.open(optionsUrl, '_blank');
            console.log('Opened options page via fallback method');
            return { success: true };
          } else {
            console.log('Chrome runtime getURL not available');
          }
        } catch (fallbackError) {
          console.log('Could not open options page via fallback:', fallbackError);
        }
      }

      return { success: false, error: (error as Error).message || 'Unknown error' };
    }
  }

  private startManualTradingMode() {
    console.log('🎯 Starting manual trading mode...');

    // Set current flow to trading
    this.currentFlow = 'trading';

    // Start monitoring for trade activities
    this.setupTradingMonitoring();

    // Show floating assistant
    this.showFloatingAssistant();
  }

  private setupTradingMonitoring() {
    // Monitor for trade buttons and results
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          // Check for trade result notifications
          this.checkForTradeResults();
        }
      });
    });

    // Start observing the document
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    console.log('✅ Trading monitoring started');
  }

  private checkForTradeResults() {
    // This is a simplified version - you might need to adjust selectors based on Binomo's actual DOM
    const winElements = document.querySelectorAll('[class*="win"], [class*="profit"], [class*="success"]');
    const lossElements = document.querySelectorAll('[class*="loss"], [class*="lose"], [class*="fail"]');

    // Check for new win/loss notifications
    winElements.forEach(element => {
      if (!element.hasAttribute('data-processed')) {
        element.setAttribute('data-processed', 'true');
        // Extract profit amount if possible
        const profitMatch = element.textContent?.match(/\$?(\d+\.?\d*)/);
        const profit = profitMatch ? parseFloat(profitMatch[1]) : undefined;
        this.handleTradeResult('win', profit);
      }
    });

    lossElements.forEach(element => {
      if (!element.hasAttribute('data-processed')) {
        element.setAttribute('data-processed', 'true');
        this.handleTradeResult('loss');
      }
    });
  }

  private showFloatingAssistant() {
    // Remove existing assistant if any
    const existing = document.getElementById('trading-assistant-float');
    if (existing) {
      existing.remove();
    }

    const assistant = document.createElement('div');
    assistant.id = 'trading-assistant-float';
    assistant.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 300px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.15);
      z-index: 999999;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      border: 2px solid #1976d2;
    `;

    assistant.innerHTML = `
      <div style="padding: 16px; border-bottom: 1px solid #e0e0e0;">
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <div style="display: flex; align-items: center;">
            <span style="font-size: 20px; margin-right: 8px;">🤖</span>
            <strong style="color: #1976d2;">Trading Assistant</strong>
          </div>
          <button id="close-assistant" style="
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #666;
          ">×</button>
        </div>
      </div>
      <div style="padding: 16px;">
        <div style="background: #e8f5e8; padding: 12px; border-radius: 8px; margin-bottom: 12px;">
          <div style="display: flex; align-items: center; margin-bottom: 8px;">
            <span style="font-size: 16px; margin-right: 8px;">✅</span>
            <strong style="color: #2e7d32;">Chế độ giao dịch thủ công</strong>
          </div>
          <p style="margin: 0; font-size: 14px; color: #2e7d32;">
            Extension đang theo dõi và sẽ hiển thị thông điệp chánh niệm khi có kết quả giao dịch.
          </p>
        </div>
        <div style="display: grid; gap: 8px;">
          <button id="restart-flow" style="
            background: #1976d2;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
          ">🔄 Khởi động lại flow</button>
          <button id="meditation-break" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
          ">🧘‍♂️ Nghỉ thiền</button>
        </div>
      </div>
    `;

    document.body.appendChild(assistant);

    // Add event listeners
    document.getElementById('close-assistant')?.addEventListener('click', () => {
      assistant.remove();
    });

    document.getElementById('restart-flow')?.addEventListener('click', () => {
      assistant.remove();
      this.startTradingFlow();
    });

    document.getElementById('meditation-break')?.addEventListener('click', () => {
      assistant.remove();
      this.sendMessageSafely({ action: 'openOptions', tab: 'meditation' });
    });
  }

  private async blockTradingUntilTomorrow() {
    try {
      const now = new Date();
      await chrome.storage.local.set({
        tradingBlocked: true,
        blockDate: now.toISOString()
      });
      console.log('🚫 Trading blocked until tomorrow');
    } catch (error) {
      console.error('Error blocking trading:', error);
    }
  }

  private handleTradeResult(result: 'win' | 'loss', profit?: number) {
    if (result === 'win') {
      this.showMindfulWinMessage(profit);
    } else {
      this.showMindfulLossMessage();
    }
  }

  private showMindfulWinMessage(profit?: number) {
    const messages = [
      '🙏 Tôi cảm ơn thị trường đã tạo ra nơi để tôi rèn luyện tâm và kiếm lợi nhuận đủ sống.',
      '🌟 Tôi mong tôi khiêm nhường và biết ơn với thành công này.',
      '💪 Tôi biết sẽ còn nhiều khó khăn trong luyện tâm nhưng tôi vẫn sẽ học hỏi và rèn luyện tiếp.',
      '🧘‍♂️ Thành công này là kết quả của sự kiên nhẫn và kỷ luật.'
    ];

    const content = `
      <div style="text-align: center; padding: 20px;">
        <div style="font-size: 48px; margin-bottom: 20px;">🎉</div>
        <h3 style="color: #4caf50; margin-bottom: 20px;">Lệnh Thắng${profit ? ` (+$${profit})` : ''}</h3>
        <div style="background: #e8f5e9; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
          <p style="font-style: italic; color: #2e7d32; line-height: 1.6; margin: 0;">
            "${messages[Math.floor(Math.random() * messages.length)]}"
          </p>
        </div>
        <p style="color: #666; font-size: 14px;">
          Hãy duy trì tâm thái khiêm nhường và tiếp tục theo đúng kế hoạch giao dịch.
        </p>
      </div>
    `;

    this.createModal('trade-result', '🧠 Tâm Niệm Sau Giao Dịch', content, [
      {
        text: 'Tiếp tục giao dịch',
        primary: true,
        onClick: () => this.removeModal('trade-result')
      }
    ]);
  }

  private showMindfulLossMessage() {
    const messages = [
      '🙏 Tôi cũng cảm ơn thị trường đã tạo ra nơi để tôi rèn luyện tâm.',
      '⏳ Dù thế nào đi nữa tôi vẫn sẽ chờ đợi, phân tích và giao dịch theo đúng nguyên tắc của phương pháp.',
      '🎯 Tôi chờ đợi tín hiệu tốt, hiểu chúng và chờ và quyết định vào lệnh.',
      '🧘‍♂️ Mỗi lần thua là một bài học quý giá cho hành trình rèn luyện tâm.'
    ];

    const content = `
      <div style="text-align: center; padding: 20px;">
        <div style="font-size: 48px; margin-bottom: 20px;">🤲</div>
        <h3 style="color: #ff9800; margin-bottom: 20px;">Lệnh Thua</h3>
        <div style="background: #fff3e0; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
          <p style="font-style: italic; color: #f57c00; line-height: 1.6; margin: 0;">
            "${messages[Math.floor(Math.random() * messages.length)]}"
          </p>
        </div>
        <p style="color: #666; font-size: 14px;">
          Hãy giữ bình tĩnh, tuân thủ kế hoạch và chờ đợi cơ hội tốt hơn.
        </p>
      </div>
    `;

    this.createModal('trade-result', '🧠 Tâm Niệm Sau Giao Dịch', content, [
      {
        text: 'Tiếp tục quan sát',
        primary: true,
        onClick: () => this.removeModal('trade-result')
      }
    ]);
  }

  private async loadMethodSettings() {
    try {
      const result = await chrome.storage.local.get(['methodSettings']);
      if (result.methodSettings) {
        this.methodSettings = result.methodSettings;
        console.log('✅ Method settings loaded:', this.methodSettings);
      } else {
        // Default: all built-in methods enabled
        this.methodSettings = {
          bollinger_bands: true,
          rsi_divergence: true,
          support_resistance: true,
          moving_average: true,
          price_action: true
        };
      }
    } catch (error) {
      console.error('Error loading method settings:', error);
      // Fallback to all enabled
      this.methodSettings = {
        bollinger_bands: true,
        rsi_divergence: true,
        support_resistance: true,
        moving_average: true,
        price_action: true
      };
    }
  }

  private waitForPageReady(): Promise<void> {
    return new Promise((resolve) => {
      if (document.readyState === 'complete') {
        resolve();
      } else {
        window.addEventListener('load', () => resolve());
      }
    });
  }

  // Create modal with Material UI-like styling
  private createModal(id: string, title: string, content: string, actions: ModalAction[] = []): HTMLElement {
    this.removeModal(id);

    const overlay = document.createElement('div');
    overlay.id = id;
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      animation: fadeIn 0.3s ease-out;
    `;

    const modal = document.createElement('div');
    modal.style.cssText = `
      background: white;
      border-radius: 12px;
      padding: 0;
      max-width: 600px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      animation: slideIn 0.3s ease-out;
    `;

    // Add CSS animations
    if (!document.getElementById('binomo-animations')) {
      const style = document.createElement('style');
      style.id = 'binomo-animations';
      style.textContent = `
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        @keyframes slideIn {
          from { opacity: 0; transform: scale(0.9) translateY(-20px); }
          to { opacity: 1; transform: scale(1) translateY(0); }
        }
      `;
      document.head.appendChild(style);
    }

    // Header
    const header = document.createElement('div');
    header.style.cssText = `
      padding: 24px 24px 16px;
      border-bottom: 1px solid #e0e0e0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    `;
    header.innerHTML = `
      <h2 style="margin: 0; color: #1976d2; font-size: 20px; font-weight: 600;">${title}</h2>
      <button onclick="document.getElementById('${id}').remove()" style="
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #666;
        padding: 4px;
        border-radius: 4px;
        transition: background-color 0.2s;
      " onmouseover="this.style.backgroundColor='#f5f5f5'" onmouseout="this.style.backgroundColor='transparent'">×</button>
    `;

    // Content
    const contentDiv = document.createElement('div');
    contentDiv.style.cssText = `
      padding: 24px;
    `;
    contentDiv.innerHTML = content;

    // Actions
    if (actions.length > 0) {
      const actionsDiv = document.createElement('div');
      actionsDiv.style.cssText = `
        padding: 16px 24px 24px;
        display: flex;
        gap: 12px;
        justify-content: flex-end;
        border-top: 1px solid #e0e0e0;
      `;
      
      actions.forEach(action => {
        const button = document.createElement('button');
        button.textContent = action.text;
        button.style.cssText = `
          padding: 10px 20px;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
          transition: all 0.2s;
          ${action.primary ? 
            'background: #1976d2; color: white;' : 
            'background: #f5f5f5; color: #666;'
          }
        `;
        
        if (action.primary) {
          button.onmouseover = () => button.style.backgroundColor = '#1565c0';
          button.onmouseout = () => button.style.backgroundColor = '#1976d2';
        } else {
          button.onmouseover = () => button.style.backgroundColor = '#e0e0e0';
          button.onmouseout = () => button.style.backgroundColor = '#f5f5f5';
        }
        
        button.onclick = action.onClick;
        actionsDiv.appendChild(button);
      });
      
      modal.appendChild(header);
      modal.appendChild(contentDiv);
      modal.appendChild(actionsDiv);
    } else {
      modal.appendChild(header);
      modal.appendChild(contentDiv);
    }

    overlay.appendChild(modal);
    document.body.appendChild(overlay);
    
    return overlay;
  }

  private removeModal(id: string) {
    const existing = document.getElementById(id);
    if (existing && existing.parentNode) {
      existing.parentNode.removeChild(existing);
    }
  }

  private showSuccessMessage(message: string) {
    const toast = document.createElement('div');
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4caf50;
      color: white;
      padding: 16px 24px;
      border-radius: 8px;
      z-index: 1000000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      animation: slideInRight 0.3s ease-out;
    `;
    toast.textContent = message;

    // Add animation
    if (!document.getElementById('toast-animations')) {
      const style = document.createElement('style');
      style.id = 'toast-animations';
      style.textContent = `
        @keyframes slideInRight {
          from { opacity: 0; transform: translateX(100%); }
          to { opacity: 1; transform: translateX(0); }
        }
      `;
      document.head.appendChild(style);
    }

    document.body.appendChild(toast);
    
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 3000);
  }

  // Daily Goals Modal
  public async showDailyGoals() {
    console.log('🎯 Showing daily goals modal...');
    this.currentFlow = 'goals';

    // Check if goals already exist for today
    const existingGoals = await this.getTodayGoals();
    
    const content = `
      <div style="margin-bottom: 20px;">
        <p style="color: #666; margin-bottom: 16px;">Thiết lập mục tiêu giao dịch hàng ngày để duy trì kỷ luật và quản lý rủi ro hiệu quả.</p>
        ${existingGoals ? `
          <div style="background: #e8f5e8; padding: 12px; border-radius: 6px; margin-bottom: 16px;">
            <small style="color: #2e7d32;">✅ <strong>Đã có mục tiêu hôm nay.</strong> Bạn có thể cập nhật lại nếu cần.</small>
          </div>
        ` : ''}
      </div>
      
      <div style="display: grid; gap: 16px;">
        <div>
          <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #1976d2; font-size: 16px;">🧘‍♂️ Mục đích tâm linh khi giao dịch</label>
          <textarea id="spiritual-purpose" placeholder="Ví dụ: Tôi giao dịch để rèn luyện tâm, giảm bớt tham lam, rèn luyện chánh niệm, giảm tham sân si sợ, biết đủ - đủ ăn đủ uống đủ mặc, biết vô thường - có được thì có mất, rèn luyện kỹ năng giao dịch..." style="
            width: 100%;
            padding: 16px;
            border: 2px solid #e3f2fd;
            border-radius: 8px;
            font-size: 15px;
            min-height: 120px;
            resize: vertical;
            transition: all 0.2s;
            box-sizing: border-box;
            background: #fafafa;
            line-height: 1.5;
          " onfocus="this.style.borderColor='#1976d2'; this.style.background='white'; this.style.boxShadow='0 0 0 3px rgba(25,118,210,0.1)'" onblur="this.style.borderColor='#e3f2fd'; this.style.background='#fafafa'; this.style.boxShadow='none'">${existingGoals?.spiritualPurpose || ''}</textarea>
          <small style="color: #666; font-style: italic;">Hãy viết về mục đích tâm linh và sự rèn luyện nội tâm qua giao dịch</small>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 12px;">
          <div>
            <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #333; font-size: 14px;">💰 Mục tiêu lợi nhuận ($)</label>
            <input type="number" id="profit-target" placeholder="50" value="${existingGoals?.profitTarget || ''}" style="
              width: 100%;
              padding: 10px;
              border: 2px solid #e0e0e0;
              border-radius: 6px;
              font-size: 14px;
              transition: border-color 0.2s;
              box-sizing: border-box;
            " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'">
          </div>

          <div>
            <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #333; font-size: 14px;">🛑 Giới hạn thua lỗ ($)</label>
            <input type="number" id="loss-limit" placeholder="20" value="${existingGoals?.lossLimit || ''}" style="
              width: 100%;
              padding: 10px;
              border: 2px solid #e0e0e0;
              border-radius: 6px;
              font-size: 14px;
              transition: border-color 0.2s;
              box-sizing: border-box;
            " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'">
          </div>

          <div>
            <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #333; font-size: 14px;">📊 Số lệnh tối đa</label>
            <input type="number" id="max-trades" placeholder="20" value="${existingGoals?.maxTrades || ''}" style="
              width: 100%;
              padding: 10px;
              border: 2px solid #e0e0e0;
              border-radius: 6px;
              font-size: 14px;
              transition: border-color 0.2s;
              box-sizing: border-box;
            " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'">
          </div>
        </div>

        <div>
          <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #333;">📝 Mục tiêu học tập hôm nay</label>
          <textarea id="learning-goal" placeholder="Ví dụ: Thực hành phương pháp Bollinger Bands với kỷ luật..." style="
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            min-height: 80px;
            resize: vertical;
            transition: border-color 0.2s;
            box-sizing: border-box;
          " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'">${existingGoals?.tradingGoal || ''}</textarea>
        </div>
      </div>
    `;

    const actions = [
      {
        text: 'Hủy',
        onClick: () => this.removeModal('daily-goals-modal')
      },
      {
        text: existingGoals ? 'Cập nhật mục tiêu' : 'Lưu mục tiêu',
        primary: true,
        onClick: () => this.saveDailyGoals()
      }
    ];

    this.createModal('daily-goals-modal', '🎯 Thiết lập Mục tiêu Hàng ngày', content, actions);
  }

  private async saveDailyGoals() {
    const spiritualPurpose = (document.getElementById('spiritual-purpose') as HTMLTextAreaElement)?.value;
    const profitTarget = (document.getElementById('profit-target') as HTMLInputElement)?.value;
    const lossLimit = (document.getElementById('loss-limit') as HTMLInputElement)?.value;
    const maxTrades = (document.getElementById('max-trades') as HTMLInputElement)?.value;
    const learningGoal = (document.getElementById('learning-goal') as HTMLTextAreaElement)?.value;

    if (!spiritualPurpose || spiritualPurpose.length < 20) {
      alert('Vui lòng viết mục đích tâm linh ít nhất 20 ký tự!');
      return;
    }

    if (!profitTarget || !lossLimit || !maxTrades) {
      alert('Vui lòng điền đầy đủ thông tin số liệu!');
      return;
    }

    const today = new Date().toISOString().split('T')[0];
    const goals = {
      id: `${today}-${Date.now()}`, // Unique ID with timestamp
      date: today,
      spiritualPurpose: spiritualPurpose,
      profitTarget: parseFloat(profitTarget),
      lossLimit: parseFloat(lossLimit),
      maxTrades: parseInt(maxTrades),
      tradingGoal: learningGoal || 'Giao dịch có kỷ luật',
      lessons: [],
      createdAt: new Date().toISOString()
    };

    try {
      // Test API connection first
      console.log('🔍 Testing API connection...');
      const testResponse = await this.fetchWithTimeout(`${this.apiBase}/dailyGoals`, { method: 'GET' });
      if (!testResponse.ok) {
        throw new Error(`API connection failed: ${testResponse.status} ${testResponse.statusText}`);
      }
      console.log('✅ API connection successful');

      // Check if goals already exist for today
      const existingGoals = await this.getTodayGoals();

      let response;
      if (existingGoals) {
        // Update existing goals
        const updatedGoals = { ...existingGoals, ...goals, id: existingGoals.id };
        response = await this.fetchWithTimeout(`${this.apiBase}/dailyGoals/${existingGoals.id}`, {
          method: 'PUT',
          body: JSON.stringify(updatedGoals)
        });
        console.log('✅ Daily goals updated:', updatedGoals);
      } else {
        // Create new goals
        response = await this.fetchWithTimeout(`${this.apiBase}/dailyGoals`, {
          method: 'POST',
          body: JSON.stringify(goals)
        });
        console.log('✅ Daily goals created:', goals);
      }

      if (response.ok) {
        this.removeModal('daily-goals-modal');

        // Show success message
        this.showSuccessMessage(existingGoals ? 'Mục tiêu đã được cập nhật!' : 'Mục tiêu hàng ngày đã được lưu!');

        // Continue to psychology assessment
        setTimeout(() => this.showPsychologyAssessment(), 1500);
      } else {
        let errorText;
        try {
          errorText = await response.text();
        } catch (e) {
          errorText = `HTTP ${response.status} ${response.statusText}`;
        }
        console.error('❌ API Error Response:', {
          status: response.status,
          statusText: response.statusText,
          errorText: errorText
        });
        throw new Error(`Failed to save goals: ${errorText}`);
      }
    } catch (error) {
      console.error('❌ Error saving goals:', error);

      // Show detailed error message
      let errorMessage = 'Không thể lưu mục tiêu!\n\n';

      if ((error as Error).message?.includes('Failed to fetch') || (error as Error).message?.includes('API connection failed')) {
        errorMessage += '🔌 Lỗi kết nối:\n';
        errorMessage += '• JSON Server không chạy hoặc không thể truy cập\n';
        errorMessage += '• Hãy chạy: npm run server\n';
        errorMessage += '• Kiểm tra port 3001 có bị chiếm không\n\n';
      } else if ((error as Error).message?.includes('duplicate')) {
        errorMessage += '🔄 Lỗi trùng lặp:\n';
        errorMessage += '• Mục tiêu hôm nay đã tồn tại\n';
        errorMessage += '• Hãy refresh trang và thử lại\n\n';
      } else {
        errorMessage += `📋 Chi tiết lỗi: ${(error as Error).message || 'Unknown error'}\n\n`;
      }

      errorMessage += '💡 Hướng dẫn khắc phục:\n';
      errorMessage += '1. Kiểm tra JSON Server: npm run server\n';
      errorMessage += '2. Refresh trang binomo1.com/trading\n';
      errorMessage += '3. Reload extension nếu cần';

      alert(errorMessage);
    }
  }

  // Start the trading flow
  public async startTradingFlow() {
    try {
      console.log('🚀 Starting trading flow...');
      
      // Check if goals are set for today
      const todayGoals = await this.getTodayGoals();
      
      if (!todayGoals) {
        this.showDailyGoals();
      } else {
        this.showPsychologyAssessment();
      }
    } catch (error) {
      console.error('Error starting trading flow:', error);
      alert('Không thể kết nối với JSON Server. Hãy chạy: npm run server');
    }
  }

  private async getTodayGoals() {
    try {
      const today = new Date().toISOString().split('T')[0];
      const response = await this.fetchWithTimeout(`${this.apiBase}/dailyGoals`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const goals = await response.json();
      return goals.find((g: any) => g.date === today);
    } catch (error) {
      console.error('Error fetching today goals:', error);

      // Show user-friendly error message
      if ((error as Error).message.includes('timeout')) {
        console.error('💡 Hint: Make sure JSON Server is running with: npm run server');
      }

      return null;
    }
  }

  // Psychology Assessment - Full Implementation
  public showPsychologyAssessment() {
    console.log('🧠 Showing psychology assessment...');
    this.currentFlow = 'psychology';

    const content = `
      <div style="margin-bottom: 20px;">
        <p style="color: #666; margin-bottom: 16px;">Đánh giá trạng thái tâm lý hiện tại để đưa ra quyết định giao dịch phù hợp và an toàn.</p>
        <div style="background: #e3f2fd; padding: 12px; border-radius: 6px; margin-bottom: 16px;">
          <small style="color: #1976d2;">💡 <strong>Lưu ý:</strong> Hãy trung thực với cảm xúc hiện tại để nhận được khuyến nghị chính xác nhất.</small>
        </div>
      </div>

      <div style="display: grid; gap: 16px;">
        <div>
          <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">🎭 Trạng thái tâm lý hiện tại:</label>
          <select id="psychology-state" style="
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            box-sizing: border-box;
            transition: border-color 0.2s;
          " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'">
            <option value="">Chọn trạng thái tâm lý...</option>
            <option value="balanced">😌 Cân bằng - Tâm trạng ổn định, tự tin nhưng không chủ quan</option>
            <option value="greedy">🤑 Tham lam - Muốn kiếm tiền nhanh, không kiên nhẫn</option>
            <option value="fearful">😰 Sợ hãi - Lo lắng mất tiền, ngần ngại vào lệnh</option>
            <option value="impatient">⚡ Vội vàng - Không kiên nhẫn chờ setup, muốn vào lệnh ngay</option>
            <option value="overconfident">😎 Tự hào - Quá tự tin sau chuỗi thắng, cảm thấy "bất bại"</option>
            <option value="angry">😡 Tức giận - Muốn "revenge" sau khi thua, cảm xúc tiêu cực</option>
            <option value="stressed">😵 Căng thẳng - Áp lực tài chính, lo lắng về kết quả</option>
            <option value="euphoric">🤩 Hưng phấn - Quá phấn khích sau thắng lớn, mất tỉnh táo</option>
          </select>
        </div>

        <div>
          <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">📝 Mô tả chi tiết cảm xúc và suy nghĩ:</label>
          <textarea id="emotion-description" placeholder="Ví dụ: Hôm nay tôi cảm thấy khá tự tin sau khi đọc phân tích thị trường, nhưng cũng hơi lo lắng vì tuần trước thua một ít..." style="
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            min-height: 100px;
            resize: vertical;
            transition: border-color 0.2s;
            box-sizing: border-box;
          " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'"></textarea>
          <small style="color: #666;">Tối thiểu 20 ký tự để có đánh giá chính xác</small>
        </div>

        <div>
          <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">💰 Tình hình tài chính gần đây:</label>
          <select id="financial-situation" style="
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            box-sizing: border-box;
            transition: border-color 0.2s;
          " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'">
            <option value="">Chọn tình hình tài chính...</option>
            <option value="stable">✅ Ổn định - Không áp lực tài chính, giao dịch với tiền dư</option>
            <option value="slight_pressure">⚠️ Áp lực nhẹ - Có một chút áp lực nhưng vẫn kiểm soát được</option>
            <option value="high_pressure">🚨 Áp lực cao - Cần tiền gấp, giao dịch với tiền quan trọng</option>
            <option value="desperate">💸 Tuyệt vọng - Giao dịch để "cứu vãn" tình hình tài chính</option>
          </select>
        </div>

        <div>
          <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">📊 Kết quả giao dịch gần đây:</label>
          <select id="recent-performance" style="
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            box-sizing: border-box;
            transition: border-color 0.2s;
          " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'">
            <option value="">Chọn kết quả gần đây...</option>
            <option value="winning_streak">🔥 Chuỗi thắng - Thắng liên tiếp trong vài phiên gần đây</option>
            <option value="mixed_results">⚖️ Lẫn lộn - Có thắng có thua, kết quả không ổn định</option>
            <option value="losing_streak">📉 Chuỗi thua - Thua liên tiếp, đang trong giai đoạn khó khăn</option>
            <option value="break_even">➡️ Hòa vốn - Không lãi không lỗ trong thời gian gần đây</option>
            <option value="new_trader">🆕 Trader mới - Chưa có nhiều kinh nghiệm giao dịch</option>
          </select>
        </div>
      </div>
    `;

    const actions = [
      {
        text: 'Quay lại',
        onClick: () => {
          this.removeModal('psychology-assessment');
          this.showDailyGoals();
        }
      },
      {
        text: 'Đánh giá',
        primary: true,
        onClick: () => this.processPsychologyAssessment()
      }
    ];

    this.createModal('psychology-assessment', '🧠 Đánh giá Tâm lý', content, actions);
  }

  private async processPsychologyAssessment() {
    const state = (document.getElementById('psychology-state') as HTMLSelectElement)?.value;
    const description = (document.getElementById('emotion-description') as HTMLTextAreaElement)?.value;
    const financialSituation = (document.getElementById('financial-situation') as HTMLSelectElement)?.value;
    const recentPerformance = (document.getElementById('recent-performance') as HTMLSelectElement)?.value;

    // Validation
    if (!state) {
      alert('Vui lòng chọn trạng thái tâm lý hiện tại!');
      return;
    }

    if (!description || description.length < 20) {
      alert('Vui lòng mô tả cảm xúc chi tiết ít nhất 20 ký tự!');
      return;
    }

    if (!financialSituation) {
      alert('Vui lòng chọn tình hình tài chính!');
      return;
    }

    if (!recentPerformance) {
      alert('Vui lòng chọn kết quả giao dịch gần đây!');
      return;
    }

    const assessment = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      state: state,
      description: description,
      financialSituation: financialSituation,
      recentPerformance: recentPerformance,
      recommendation: this.generatePsychologyRecommendation(state, financialSituation, recentPerformance)
    };

    try {
      const response = await fetch(`${this.apiBase}/psychologyStates`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(assessment)
      });

      if (response.ok) {
        console.log('✅ Psychology assessment saved:', assessment);
        this.removeModal('psychology-assessment');

        // Show recommendation
        this.showPsychologyRecommendation(assessment);
      } else {
        throw new Error('Failed to save assessment');
      }
    } catch (error) {
      console.error('❌ Error saving assessment:', error);
      alert('Không thể lưu đánh giá. Hãy kiểm tra JSON Server đang chạy!');
    }
  }

  private generatePsychologyRecommendation(state: string, financialSituation: string, recentPerformance: string) {
    // Base recommendations for each psychological state
    const stateRecommendations = {
      balanced: {
        safe: true,
        riskLevel: 'low',
        message: 'Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.',
        advice: 'Hãy duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.'
      },
      greedy: {
        safe: false,
        riskLevel: 'high',
        message: 'Cảnh báo: Tham lam có thể dẫn đến quyết định sai lầm.',
        advice: 'Hãy bình tĩnh, giảm size lệnh và tập trung vào chất lượng thay vì số lượng.'
      },
      fearful: {
        safe: false,
        riskLevel: 'medium',
        message: 'Sợ hãi có thể khiến bạn bỏ lỡ cơ hội hoặc cắt lỗ quá sớm.',
        advice: 'Hãy xem xét nghỉ ngơi hoặc giao dịch với size nhỏ để lấy lại tự tin.'
      },
      impatient: {
        safe: false,
        riskLevel: 'high',
        message: 'Vội vàng là kẻ thù lớn nhất của trader.',
        advice: 'Không nên giao dịch khi thiếu kiên nhẫn. Hãy nghỉ ngơi và quay lại sau.'
      },
      overconfident: {
        safe: false,
        riskLevel: 'high',
        message: 'Quá tự tin có thể dẫn đến chủ quan và rủi ro cao.',
        advice: 'Hãy cẩn thận hơn, giảm size lệnh và tuân thủ nghiêm ngặt risk management.'
      },
      angry: {
        safe: false,
        riskLevel: 'extreme',
        message: 'Tuyệt đối không giao dịch khi tức giận.',
        advice: 'Hãy nghỉ ngơi hoàn toàn và chỉ quay lại khi tâm trạng đã ổn định.'
      },
      stressed: {
        safe: false,
        riskLevel: 'high',
        message: 'Căng thẳng làm giảm khả năng ra quyết định đúng đắn.',
        advice: 'Không nên giao dịch khi có áp lực. Hãy giải quyết vấn đề gốc rễ trước.'
      },
      euphoric: {
        safe: false,
        riskLevel: 'extreme',
        message: 'Hưng phấn quá mức có thể dẫn đến mất kiểm soát.',
        advice: 'Hãy bình tĩnh lại, nghỉ ngơi và không tăng size lệnh.'
      }
    };

    // Financial situation modifiers
    const financialModifiers = {
      stable: { multiplier: 1, warning: '' },
      slight_pressure: {
        multiplier: 1.2,
        warning: 'Áp lực tài chính nhẹ có thể ảnh hưởng đến quyết định.'
      },
      high_pressure: {
        multiplier: 1.5,
        warning: 'Áp lực tài chính cao - rất nguy hiểm cho giao dịch!'
      },
      desperate: {
        multiplier: 2,
        warning: 'Tuyệt đối không nên giao dịch khi tuyệt vọng!'
      }
    };

    // Recent performance modifiers
    const performanceModifiers = {
      winning_streak: {
        multiplier: 1.3,
        warning: 'Chuỗi thắng có thể gây ra quá tự tin.'
      },
      mixed_results: { multiplier: 1, warning: '' },
      losing_streak: {
        multiplier: 1.4,
        warning: 'Chuỗi thua có thể gây ra tâm lý revenge.'
      },
      break_even: { multiplier: 0.9, warning: '' },
      new_trader: {
        multiplier: 1.1,
        warning: 'Trader mới cần đặc biệt cẩn thận.'
      }
    };

    const baseRec = (stateRecommendations as any)[state] || stateRecommendations.balanced;
    const financialMod = (financialModifiers as any)[financialSituation] || financialModifiers.stable;
    const performanceMod = (performanceModifiers as any)[recentPerformance] || performanceModifiers.mixed_results;

    // Calculate final risk score
    const riskMultiplier = financialMod.multiplier * performanceMod.multiplier;

    let finalSafe = baseRec.safe;
    let finalRiskLevel = baseRec.riskLevel;

    // Adjust safety based on modifiers
    if (riskMultiplier > 1.5) {
      finalSafe = false;
      finalRiskLevel = 'extreme';
    } else if (riskMultiplier > 1.2) {
      finalSafe = false;
      if (finalRiskLevel === 'low') finalRiskLevel = 'medium';
      else if (finalRiskLevel === 'medium') finalRiskLevel = 'high';
    }

    // Compile warnings
    const warnings = [financialMod.warning, performanceMod.warning].filter(w => w);

    return {
      safe: finalSafe,
      riskLevel: finalRiskLevel,
      message: baseRec.message,
      advice: baseRec.advice,
      warnings: warnings,
      riskScore: Math.round(riskMultiplier * 100) / 100,
      shouldTrade: finalSafe && riskMultiplier < 1.3,
      maxRiskPerTrade: finalSafe ? (riskMultiplier < 1.1 ? '2%' : '1%') : '0%'
    };
  }

  private showPsychologyRecommendation(assessment: any) {
    const rec = assessment.recommendation;

    // Determine colors and icons based on risk level
    const riskConfig = {
      low: { color: '#4caf50', bgColor: '#e8f5e8', icon: '✅', title: 'An toàn để giao dịch' },
      medium: { color: '#ff9800', bgColor: '#fff3e0', icon: '⚠️', title: 'Cần cẩn thận' },
      high: { color: '#f44336', bgColor: '#ffebee', icon: '🚨', title: 'Rủi ro cao' },
      extreme: { color: '#d32f2f', bgColor: '#ffcdd2', icon: '🛑', title: 'Không nên giao dịch' }
    };

    const config = (riskConfig as any)[rec.riskLevel] || riskConfig.medium;

    const content = `
      <div style="text-align: center; margin-bottom: 24px;">
        <div style="
          display: inline-block;
          padding: 20px;
          border-radius: 50%;
          background: ${config.bgColor};
          margin-bottom: 16px;
        ">
          <span style="font-size: 48px;">${config.icon}</span>
        </div>
        <h3 style="margin: 0; color: ${config.color}; font-size: 20px;">
          ${config.title}
        </h3>
        <div style="margin-top: 8px;">
          <span style="
            background: ${config.color};
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
          ">
            Risk Score: ${rec.riskScore}
          </span>
        </div>
      </div>

      <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h4 style="margin: 0 0 12px 0; color: #333; font-size: 16px;">📋 Đánh giá chi tiết:</h4>
        <div style="margin-bottom: 12px; color: #333;">
          <strong style="color: #333;">Trạng thái:</strong> <span style="color: #333;">${this.getStateLabel(assessment.state)}</span>
        </div>
        <div style="margin-bottom: 12px; color: #333;">
          <strong style="color: #333;">Tình hình tài chính:</strong> <span style="color: #333;">${this.getFinancialLabel(assessment.financialSituation)}</span>
        </div>
        <div style="margin-bottom: 12px; color: #333;">
          <strong style="color: #333;">Kết quả gần đây:</strong> <span style="color: #333;">${this.getPerformanceLabel(assessment.recentPerformance)}</span>
        </div>
        <div style="color: #333;">
          <strong style="color: #333;">Mô tả:</strong> <em style="color: #666;">"${assessment.description}"</em>
        </div>
      </div>

      <div style="background: ${config.bgColor}; padding: 16px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid ${config.color};">
        <h4 style="margin: 0 0 8px 0; color: ${config.color};">💡 Khuyến nghị:</h4>
        <p style="margin: 0 0 8px 0; color: #333; line-height: 1.5;">${rec.message}</p>
        <p style="margin: 0; color: #666; line-height: 1.5;"><strong>Lời khuyên:</strong> ${rec.advice}</p>
      </div>

      ${rec.warnings.length > 0 ? `
        <div style="background: #fff3cd; padding: 16px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #ffc107;">
          <h4 style="margin: 0 0 8px 0; color: #856404;">⚠️ Cảnh báo:</h4>
          ${rec.warnings.map((warning: string) => `<p style="margin: 0 0 4px 0; color: #856404;">• ${warning}</p>`).join('')}
        </div>
      ` : ''}

      <div style="background: #e3f2fd; padding: 16px; border-radius: 8px; margin-bottom: 20px;">
        <h4 style="margin: 0 0 12px 0; color: #1976d2;">📊 Thông số giao dịch khuyến nghị:</h4>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; color: #333;">
          <div>
            <strong style="color: #333;">Nên giao dịch:</strong> <span style="color: #333;">${rec.shouldTrade ? '✅ Có' : '❌ Không'}</span>
          </div>
          <div>
            <strong style="color: #333;">Risk tối đa/lệnh:</strong> <span style="color: #333;">${rec.maxRiskPerTrade}</span>
          </div>
        </div>
      </div>

      <div style="font-size: 14px; color: #666; text-align: center;">
        <p style="margin: 0;">Đánh giá này dựa trên thông tin bạn cung cấp và chỉ mang tính tham khảo.</p>
      </div>
    `;

    const actions = [
      {
        text: 'Đánh giá lại',
        onClick: () => {
          this.removeModal('psychology-recommendation');
          this.showPsychologyAssessment();
        }
      }
    ];

    // Add appropriate action based on recommendation
    if (rec.shouldTrade) {
      actions.push({
        text: 'Tiếp tục giao dịch',
        primary: true,
        onClick: () => {
          this.removeModal('psychology-recommendation');

          // Start manual trading monitoring
          this.startManualTradingMode();

          // Show success message
          this.showSuccessMessage('✅ Bắt đầu giao dịch thủ công! Extension sẽ theo dõi và hỗ trợ bạn.');
        }
      });
    } else {
      actions.push({
        text: 'Nghỉ ngơi',
        primary: true,
        onClick: async () => {
          this.removeModal('psychology-recommendation');

          // Block trading until next day
          await this.blockTradingUntilTomorrow();

          // Show blocking message and redirect
          this.showSuccessMessage('Quyết định khôn ngoan! Đang chuyển đến trang thiền...');

          setTimeout(() => {
            // Open options page with meditation tab
            this.sendMessageSafely({ action: 'openOptions', tab: 'meditation' });

            // Note: Cannot close tab from content script
            // User will need to close manually or navigate away
          }, 2000);
        }
      });
    }

    this.createModal('psychology-recommendation', '🧠 Kết quả Đánh giá Tâm lý', content, actions);
  }

  private getStateLabel(state: string): string {
    const labels = {
      balanced: '😌 Cân bằng',
      greedy: '🤑 Tham lam',
      fearful: '😰 Sợ hãi',
      impatient: '⚡ Vội vàng',
      overconfident: '😎 Tự hào',
      angry: '😡 Tức giận',
      stressed: '😵 Căng thẳng',
      euphoric: '🤩 Hưng phấn'
    };
    return (labels as any)[state] || state;
  }

  private getFinancialLabel(situation: string): string {
    const labels = {
      stable: '✅ Ổn định',
      slight_pressure: '⚠️ Áp lực nhẹ',
      high_pressure: '🚨 Áp lực cao',
      desperate: '💸 Tuyệt vọng'
    };
    return (labels as any)[situation] || situation;
  }

  private getPerformanceLabel(performance: string): string {
    const labels = {
      winning_streak: '🔥 Chuỗi thắng',
      mixed_results: '⚖️ Lẫn lộn',
      losing_streak: '📉 Chuỗi thua',
      break_even: '➡️ Hòa vốn',
      new_trader: '🆕 Trader mới'
    };
    return (labels as any)[performance] || performance;
  }

  // Trading Method Selector - Full Implementation
  private async showTradingMethodSelector() {
    console.log('📊 Showing trading method selector...');
    this.currentFlow = 'method';

    // Load custom methods
    const customMethods = await this.loadCustomMethods();

    // Filter enabled methods
    const enabledCustomMethods = customMethods.filter((method: any) => this.methodSettings[method.id] !== false);

    const content = `
      <div style="margin-bottom: 24px;">
        <p style="color: #666; margin-bottom: 16px;">Chọn phương pháp phân tích kỹ thuật phù hợp với điều kiện thị trường hiện tại.</p>
        <div style="background: #e3f2fd; padding: 12px; border-radius: 6px; margin-bottom: 16px;">
          <small style="color: #1976d2;">💡 <strong>Lưu ý:</strong> Mỗi phương pháp có bộ câu hỏi phân tích riêng để đảm bảo setup chất lượng.</small>
        </div>
      </div>

      <div style="display: grid; gap: 16px;">
        ${this.methodSettings.bollinger_bands !== false ? `
        <div style="border: 2px solid #e0e0e0; border-radius: 8px; padding: 16px; cursor: pointer; transition: all 0.2s; background: white;"
             onclick="this.style.borderColor='#1976d2'; this.style.backgroundColor='#f3f8ff'; document.querySelectorAll('[data-method]').forEach(el => {if(el !== this) {el.style.borderColor='#e0e0e0'; el.style.backgroundColor='white';}}) ; document.getElementById('selected-method').value='bollinger_bands';"
             data-method="bollinger_bands">
          <div style="display: flex; align-items: center; margin-bottom: 8px;">
            <span style="font-size: 24px; margin-right: 12px;">📈</span>
            <h4 style="margin: 0; color: #333;">Bollinger Bands Breakout</h4>
          </div>
          <p style="margin: 0 0 8px 0; color: #666; font-size: 14px;">Giao dịch dựa trên việc giá breakout khỏi dải Bollinger Bands với volume xác nhận.</p>
          <div style="font-size: 12px; color: #888;">
            <strong>Phù hợp:</strong> Thị trường trending, volatility cao<br>
            <strong>Timeframe:</strong> 5m, 15m, 30m
          </div>
        </div>
        ` : ''}

        ${this.methodSettings.rsi_divergence !== false ? `
        <div style="border: 2px solid #e0e0e0; border-radius: 8px; padding: 16px; cursor: pointer; transition: all 0.2s; background: white;"
             onclick="this.style.borderColor='#1976d2'; this.style.backgroundColor='#f3f8ff'; document.querySelectorAll('[data-method]').forEach(el => {if(el !== this) {el.style.borderColor='#e0e0e0'; el.style.backgroundColor='white';}}) ; document.getElementById('selected-method').value='rsi_divergence';"
             data-method="rsi_divergence">
          <div style="display: flex; align-items: center; margin-bottom: 8px;">
            <span style="font-size: 24px; margin-right: 12px;">📊</span>
            <h4 style="margin: 0; color: #333;">RSI Divergence</h4>
          </div>
          <p style="margin: 0 0 8px 0; color: #666; font-size: 14px;">Tìm kiếm sự phân kỳ giữa giá và RSI để dự đoán sự đảo chiều xu hướng.</p>
          <div style="font-size: 12px; color: #888;">
            <strong>Phù hợp:</strong> Đỉnh/đáy thị trường, overbought/oversold<br>
            <strong>Timeframe:</strong> 15m, 30m, 1h
          </div>
        </div>
        ` : ''}

        ${this.methodSettings.support_resistance !== false ? `
        <div style="border: 2px solid #e0e0e0; border-radius: 8px; padding: 16px; cursor: pointer; transition: all 0.2s; background: white;"
             onclick="this.style.borderColor='#1976d2'; this.style.backgroundColor='#f3f8ff'; document.querySelectorAll('[data-method]').forEach(el => {if(el !== this) {el.style.borderColor='#e0e0e0'; el.style.backgroundColor='white';}}) ; document.getElementById('selected-method').value='support_resistance';"
             data-method="support_resistance">
          <div style="display: flex; align-items: center; margin-bottom: 8px;">
            <span style="font-size: 24px; margin-right: 12px;">🔄</span>
            <h4 style="margin: 0; color: #333;">Support & Resistance</h4>
          </div>
          <p style="margin: 0 0 8px 0; color: #666; font-size: 14px;">Giao dịch tại các vùng support/resistance quan trọng với xác nhận price action.</p>
          <div style="font-size: 12px; color: #888;">
            <strong>Phù hợp:</strong> Thị trường sideway, range-bound<br>
            <strong>Timeframe:</strong> 5m, 15m, 30m, 1h
          </div>
        </div>
        ` : ''}

        ${this.methodSettings.moving_average !== false ? `
        <div style="border: 2px solid #e0e0e0; border-radius: 8px; padding: 16px; cursor: pointer; transition: all 0.2s; background: white;"
             onclick="this.style.borderColor='#1976d2'; this.style.backgroundColor='#f3f8ff'; document.querySelectorAll('[data-method]').forEach(el => {if(el !== this) {el.style.borderColor='#e0e0e0'; el.style.backgroundColor='white';}}) ; document.getElementById('selected-method').value='moving_average';"
             data-method="moving_average">
          <div style="display: flex; align-items: center; margin-bottom: 8px;">
            <span style="font-size: 24px; margin-right: 12px;">📉</span>
            <h4 style="margin: 0; color: #333;">Moving Average Crossover</h4>
          </div>
          <p style="margin: 0 0 8px 0; color: #666; font-size: 14px;">Sử dụng sự cắt nhau của các đường MA để xác định xu hướng và điểm vào lệnh.</p>
          <div style="font-size: 12px; color: #888;">
            <strong>Phù hợp:</strong> Thị trường trending mạnh<br>
            <strong>Timeframe:</strong> 15m, 30m, 1h
          </div>
        </div>
        ` : ''}

        ${this.methodSettings.price_action !== false ? `
        <div style="border: 2px solid #e0e0e0; border-radius: 8px; padding: 16px; cursor: pointer; transition: all 0.2s; background: white;"
             onclick="this.style.borderColor='#1976d2'; this.style.backgroundColor='#f3f8ff'; document.querySelectorAll('[data-method]').forEach(el => {if(el !== this) {el.style.borderColor='#e0e0e0'; el.style.backgroundColor='white';}}) ; document.getElementById('selected-method').value='price_action';"
             data-method="price_action">
          <div style="display: flex; align-items: center; margin-bottom: 8px;">
            <span style="font-size: 24px; margin-right: 12px;">🕯️</span>
            <h4 style="margin: 0; color: #333;">Price Action Patterns</h4>
          </div>
          <p style="margin: 0 0 8px 0; color: #666; font-size: 14px;">Phân tích các mô hình nến Nhật và price action để dự đoán hướng di chuyển.</p>
          <div style="font-size: 12px; color: #888;">
            <strong>Phù hợp:</strong> Mọi điều kiện thị trường<br>
            <strong>Timeframe:</strong> 1m, 5m, 15m, 30m
          </div>
        </div>
        ` : ''}

        ${enabledCustomMethods.length > 0 ? `
          <div style="margin: 20px 0; padding: 16px; background: #e8f5e8; border-radius: 8px;">
            <h4 style="margin: 0 0 12px 0; color: #2e7d32;">🎯 Phương pháp tùy chỉnh của bạn:</h4>
            <div style="display: grid; gap: 12px;">
              ${enabledCustomMethods.map((method: any) => `
                <div style="border: 2px solid #4caf50; border-radius: 8px; padding: 12px; cursor: pointer; transition: all 0.2s; background: white;"
                     onclick="this.style.borderColor='#1976d2'; this.style.backgroundColor='#f3f8ff'; document.querySelectorAll('[data-method]').forEach(el => {el.style.borderColor='#e0e0e0'; el.style.backgroundColor='white';}); document.getElementById('selected-method').value='${method.id}';"
                     data-method="${method.id}">
                  <div style="display: flex; align-items: center; margin-bottom: 6px;">
                    <span style="font-size: 20px; margin-right: 8px;">${method.icon}</span>
                    <h5 style="margin: 0; color: #333;">${method.name}</h5>
                    <span style="margin-left: auto; background: #4caf50; color: white; padding: 2px 6px; border-radius: 10px; font-size: 10px;">CUSTOM</span>
                  </div>
                  <p style="margin: 0; color: #666; font-size: 13px;">${method.description}</p>
                  <div style="font-size: 11px; color: #888; margin-top: 4px;">
                    <strong>Câu hỏi:</strong> ${method.questions.length} | <strong>Điểm tối đa:</strong> ${method.totalMaxScore}
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
        ` : ''}
      </div>

      <input type="hidden" id="selected-method" value="">

      <div style="margin-top: 20px; padding: 16px; background: #fff3cd; border-radius: 6px; border-left: 4px solid #ffc107;">
        <small style="color: #856404;">
          <strong>⚠️ Quan trọng:</strong> Hãy chọn phương pháp mà bạn đã học và hiểu rõ.
          Mỗi phương pháp sẽ có bộ câu hỏi phân tích để đảm bảo setup chất lượng trước khi vào lệnh.
        </small>
      </div>
    `;

    const actions = [
      {
        text: 'Quay lại',
        onClick: () => {
          this.removeModal('trading-method-selector');
          this.showPsychologyAssessment();
        }
      },
      {
        text: 'Tiếp tục phân tích',
        primary: true,
        onClick: () => this.processTradingMethodSelection()
      }
    ];

    this.createModal('trading-method-selector', '📊 Chọn Phương pháp Giao dịch', content, actions);
  }

  private async processTradingMethodSelection() {
    const selectedMethod = (document.getElementById('selected-method') as HTMLInputElement)?.value;

    if (!selectedMethod) {
      alert('Vui lòng chọn một phương pháp giao dịch!');
      return;
    }

    console.log('✅ Trading method selected:', selectedMethod);
    this.removeModal('trading-method-selector');

    if (selectedMethod === 'custom') {
      this.removeModal('trading-method-selector');
      this.showSuccessMessage('Vui lòng vào Options để tạo phương pháp tùy chỉnh!');
    } else {
      await this.showTradingAnalysis(selectedMethod);
    }
  }

  private async showTradingAnalysis(method: string, customMethodData?: any) {
    console.log('🔍 Showing trading analysis for method:', method);
    this.currentFlow = 'analysis';

    const methodData = customMethodData || await this.getTradingMethodData(method);

    const content = `
      <div style="margin-bottom: 24px;">
        <div style="display: flex; align-items: center; margin-bottom: 16px;">
          <span style="font-size: 32px; margin-right: 12px;">${methodData.icon}</span>
          <div>
            <h3 style="margin: 0; color: #333;">${methodData.name}</h3>
            <p style="margin: 4px 0 0 0; color: #666; font-size: 14px;">${methodData.description}</p>
          </div>
        </div>

        <div style="background: #e8f5e8; padding: 12px; border-radius: 6px; margin-bottom: 16px;">
          <small style="color: #2e7d32;">
            <strong>📋 Hướng dẫn:</strong> Trả lời tất cả câu hỏi dựa trên chart hiện tại.
            Hệ thống sẽ tính điểm và đưa ra khuyến nghị có nên vào lệnh hay không.
          </small>
        </div>
      </div>

      <div style="display: grid; gap: 16px;">
        ${methodData.questions.map((question: any, index: number) => `
          <div style="border: 1px solid #e0e0e0; border-radius: 6px; padding: 16px; background: white;">
            <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">
              ${index + 1}. ${question.text}
            </label>

            ${question.type === 'select' ? `
              <select id="question-${index}" style="
                width: 100%;
                padding: 10px;
                border: 2px solid #e0e0e0;
                border-radius: 4px;
                font-size: 14px;
                background: white;
                color: #333;
                box-sizing: border-box;
              ">
                <option value="" style="color: #999;">Chọn đáp án...</option>
                ${question.options.map((option: any) => `
                  <option value="${option.value}" style="color: #333;">${option.label}</option>
                `).join('')}
              </select>
            ` : question.type === 'radio' ? `
              <div style="display: grid; gap: 8px;">
                ${question.options.map((option: any) => `
                  <label style="display: flex; align-items: center; cursor: pointer; padding: 8px; border-radius: 4px; transition: background-color 0.2s;"
                         onmouseover="this.style.backgroundColor='#f5f5f5'"
                         onmouseout="this.style.backgroundColor='transparent'">
                    <input type="radio" name="question-${index}" value="${option.value}" style="margin-right: 8px;">
                    <span style="font-size: 14px; color: #333;">${option.label}</span>
                  </label>
                `).join('')}
              </div>
            ` : `
              <textarea id="question-${index}" placeholder="${question.placeholder || 'Nhập mô tả chi tiết...'}" style="
                width: 100%;
                padding: 10px;
                border: 2px solid #e0e0e0;
                border-radius: 4px;
                font-size: 14px;
                color: #333;
                background: white;
                min-height: 80px;
                resize: vertical;
                box-sizing: border-box;
              "></textarea>
            `}

            ${question.hint ? `
              <small style="color: #666; margin-top: 4px; display: block;">
                💡 ${question.hint}
              </small>
            ` : ''}
          </div>
        `).join('')}
      </div>

      <div style="margin-top: 20px; padding: 16px; background: #e3f2fd; border-radius: 6px;">
        <h4 style="margin: 0 0 8px 0; color: #1976d2;">📊 Hệ thống chấm điểm:</h4>
        <div style="font-size: 14px; color: #1976d2;">
          • <strong>80-100%:</strong> Setup xuất sắc - Nên vào lệnh<br>
          • <strong>60-79%:</strong> Setup tốt - Có thể cân nhắc<br>
          • <strong>40-59%:</strong> Setup trung bình - Cần cẩn thận<br>
          • <strong>0-39%:</strong> Setup yếu - Không nên vào lệnh
        </div>
      </div>
    `;

    const actions = [
      {
        text: 'Chọn lại phương pháp',
        onClick: () => {
          this.removeModal('trading-analysis');
          this.showTradingMethodSelector();
        }
      },
      {
        text: 'Phân tích kết quả',
        primary: true,
        onClick: () => this.processTradingAnalysis(method, methodData)
      }
    ];

    this.createModal('trading-analysis', '🔍 Phân tích Thị trường', content, actions);
  }

  private async getTradingMethodData(method: string) {
    // Check if it's a custom method
    if (method.startsWith('custom_')) {
      const customMethod = await this.getCustomMethod(method);
      if (customMethod) {
        return customMethod;
      }
    }

    const methods = {
      bollinger_bands: {
        name: 'Bollinger Bands Breakout',
        icon: '📈',
        description: 'Giao dịch dựa trên việc giá breakout khỏi dải Bollinger Bands với volume xác nhận',
        questions: [
          {
            text: 'Giá có breakout rõ ràng khỏi Upper hoặc Lower Bollinger Band không?',
            type: 'radio',
            options: [
              { value: 'strong_breakout', label: '✅ Có - Breakout mạnh với nến đóng ngoài band', weight: 25 },
              { value: 'weak_breakout', label: '⚠️ Có - Nhưng breakout yếu, chỉ chạm band', weight: 10 },
              { value: 'no_breakout', label: '❌ Không - Giá vẫn trong dải bands', weight: 0 }
            ]
          },
          {
            text: 'Volume tại thời điểm breakout như thế nào?',
            type: 'radio',
            options: [
              { value: 'high_volume', label: '🔥 Volume cao hơn trung bình rõ rệt', weight: 20 },
              { value: 'normal_volume', label: '📊 Volume bình thường', weight: 10 },
              { value: 'low_volume', label: '📉 Volume thấp hơn trung bình', weight: 0 }
            ]
          },
          {
            text: 'Bollinger Bands đang trong trạng thái nào?',
            type: 'radio',
            options: [
              { value: 'expanding', label: '📈 Đang mở rộng (volatility tăng)', weight: 20 },
              { value: 'stable', label: '➡️ Ổn định', weight: 10 },
              { value: 'contracting', label: '📉 Đang thu hẹp (volatility giảm)', weight: 5 }
            ]
          },
          {
            text: 'RSI hiện tại đang ở vùng nào?',
            type: 'select',
            options: [
              { value: 'overbought', label: 'Overbought (>70) - Phù hợp cho short', weight: 15 },
              { value: 'oversold', label: 'Oversold (<30) - Phù hợp cho long', weight: 15 },
              { value: 'neutral', label: 'Neutral (30-70) - Trung tính', weight: 10 },
              { value: 'extreme', label: 'Extreme (>80 hoặc <20) - Cần cẩn thận', weight: 5 }
            ]
          },
          {
            text: 'Mô tả setup hiện tại và lý do vào lệnh:',
            type: 'textarea',
            placeholder: 'Ví dụ: Giá breakout mạnh khỏi upper band với volume cao, RSI chưa overbought, xu hướng tăng rõ ràng...',
            hint: 'Mô tả chi tiết giúp đánh giá chất lượng setup'
          }
        ]
      },
      rsi_divergence: {
        name: 'RSI Divergence',
        icon: '📊',
        description: 'Tìm kiếm sự phân kỳ giữa giá và RSI để dự đoán sự đảo chiều xu hướng',
        questions: [
          {
            text: 'Có xuất hiện divergence rõ ràng giữa giá và RSI không?',
            type: 'radio',
            options: [
              { value: 'strong_divergence', label: '✅ Có - Divergence rõ ràng qua nhiều đỉnh/đáy', weight: 30 },
              { value: 'weak_divergence', label: '⚠️ Có - Nhưng divergence yếu', weight: 15 },
              { value: 'no_divergence', label: '❌ Không có divergence', weight: 0 }
            ]
          },
          {
            text: 'Loại divergence nào đang xuất hiện?',
            type: 'radio',
            options: [
              { value: 'bearish', label: '🔴 Bearish - Giá cao hơn, RSI thấp hơn', weight: 20 },
              { value: 'bullish', label: '🟢 Bullish - Giá thấp hơn, RSI cao hơn', weight: 20 },
              { value: 'hidden_bearish', label: '🟠 Hidden Bearish - Trong downtrend', weight: 15 },
              { value: 'hidden_bullish', label: '🟡 Hidden Bullish - Trong uptrend', weight: 15 }
            ]
          },
          {
            text: 'RSI hiện tại ở vùng nào?',
            type: 'select',
            options: [
              { value: 'extreme_overbought', label: 'Extreme Overbought (>80)', weight: 20 },
              { value: 'overbought', label: 'Overbought (70-80)', weight: 15 },
              { value: 'oversold', label: 'Oversold (20-30)', weight: 15 },
              { value: 'extreme_oversold', label: 'Extreme Oversold (<20)', weight: 20 },
              { value: 'neutral', label: 'Neutral (30-70)', weight: 5 }
            ]
          },
          {
            text: 'Có tín hiệu xác nhận nào khác không?',
            type: 'radio',
            options: [
              { value: 'multiple_confirmations', label: '✅ Có nhiều tín hiệu xác nhận (MA, support/resistance, etc.)', weight: 15 },
              { value: 'some_confirmations', label: '⚠️ Có một vài tín hiệu xác nhận', weight: 10 },
              { value: 'no_confirmations', label: '❌ Không có tín hiệu xác nhận khác', weight: 0 }
            ]
          },
          {
            text: 'Mô tả chi tiết về divergence và setup:',
            type: 'textarea',
            placeholder: 'Ví dụ: Bearish divergence rõ ràng qua 3 đỉnh, RSI ở vùng overbought, có resistance mạnh...',
            hint: 'Mô tả cụ thể về các đỉnh/đáy tạo divergence'
          }
        ]
      },
      support_resistance: {
        name: 'Support & Resistance',
        icon: '🔄',
        description: 'Giao dịch tại các vùng support/resistance quan trọng với xác nhận price action',
        questions: [
          {
            text: 'Vùng support/resistance hiện tại có mạnh không?',
            type: 'radio',
            options: [
              { value: 'very_strong', label: '🔥 Rất mạnh - Đã test nhiều lần, có volume cao', weight: 25 },
              { value: 'strong', label: '✅ Mạnh - Đã test vài lần, rõ ràng', weight: 20 },
              { value: 'moderate', label: '⚠️ Trung bình - Chưa test nhiều', weight: 10 },
              { value: 'weak', label: '❌ Yếu - Mới hình thành hoặc không rõ ràng', weight: 0 }
            ]
          },
          {
            text: 'Giá đang ở vị trí nào so với vùng S/R?',
            type: 'radio',
            options: [
              { value: 'at_support', label: '🟢 Tại support - Chuẩn bị bounce', weight: 20 },
              { value: 'at_resistance', label: '🔴 Tại resistance - Chuẩn bị reject', weight: 20 },
              { value: 'near_sr', label: '⚠️ Gần vùng S/R - Chưa chạm', weight: 10 },
              { value: 'far_from_sr', label: '❌ Xa vùng S/R - Setup không hợp lệ', weight: 0 }
            ]
          },
          {
            text: 'Có tín hiệu price action xác nhận không?',
            type: 'radio',
            options: [
              { value: 'strong_rejection', label: '✅ Có - Rejection mạnh với nến đảo chiều rõ ràng', weight: 20 },
              { value: 'weak_rejection', label: '⚠️ Có - Rejection yếu', weight: 10 },
              { value: 'breakout', label: '🚀 Breakout - Giá vượt qua S/R', weight: 15 },
              { value: 'no_signal', label: '❌ Không có tín hiệu rõ ràng', weight: 0 }
            ]
          },
          {
            text: 'Volume tại vùng S/R như thế nào?',
            type: 'select',
            options: [
              { value: 'high_volume', label: 'Volume cao - Xác nhận mạnh', weight: 15 },
              { value: 'normal_volume', label: 'Volume bình thường', weight: 10 },
              { value: 'low_volume', label: 'Volume thấp - Thiếu conviction', weight: 5 }
            ]
          },
          {
            text: 'Mô tả vùng S/R và setup hiện tại:',
            type: 'textarea',
            placeholder: 'Ví dụ: Resistance mạnh tại 1.2500 đã test 3 lần, xuất hiện doji rejection với volume cao...',
            hint: 'Mô tả cụ thể về vùng S/R và các tín hiệu price action'
          }
        ]
      },
      moving_average: {
        name: 'Moving Average Crossover',
        icon: '📉',
        description: 'Sử dụng sự cắt nhau của các đường MA để xác định xu hướng và điểm vào lệnh',
        questions: [
          {
            text: 'Có xuất hiện crossover giữa các đường MA không?',
            type: 'radio',
            options: [
              { value: 'golden_cross', label: '🟡 Golden Cross - MA ngắn cắt lên MA dài', weight: 25 },
              { value: 'death_cross', label: '💀 Death Cross - MA ngắn cắt xuống MA dài', weight: 25 },
              { value: 'approaching', label: '⚠️ Đang tiến gần crossover', weight: 10 },
              { value: 'no_cross', label: '❌ Không có crossover', weight: 0 }
            ]
          },
          {
            text: 'Xu hướng tổng thể như thế nào?',
            type: 'radio',
            options: [
              { value: 'strong_uptrend', label: '📈 Uptrend mạnh - Tất cả MA hướng lên', weight: 20 },
              { value: 'strong_downtrend', label: '📉 Downtrend mạnh - Tất cả MA hướng xuống', weight: 20 },
              { value: 'weak_trend', label: '⚠️ Xu hướng yếu - MA không rõ ràng', weight: 10 },
              { value: 'sideways', label: '➡️ Sideway - MA nằm ngang', weight: 5 }
            ]
          },
          {
            text: 'Giá hiện tại so với các đường MA?',
            type: 'radio',
            options: [
              { value: 'above_all', label: '⬆️ Trên tất cả MA - Bullish', weight: 15 },
              { value: 'below_all', label: '⬇️ Dưới tất cả MA - Bearish', weight: 15 },
              { value: 'between_ma', label: '🔄 Giữa các MA - Không rõ ràng', weight: 5 },
              { value: 'at_ma', label: '📍 Tại đường MA - Test support/resistance', weight: 10 }
            ]
          },
          {
            text: 'Slope (độ dốc) của các đường MA?',
            type: 'select',
            options: [
              { value: 'steep_up', label: 'Dốc lên mạnh - Momentum tốt', weight: 15 },
              { value: 'gentle_up', label: 'Dốc lên nhẹ - Momentum yếu', weight: 10 },
              { value: 'flat', label: 'Nằm ngang - Không có momentum', weight: 5 },
              { value: 'gentle_down', label: 'Dốc xuống nhẹ - Momentum yếu', weight: 10 },
              { value: 'steep_down', label: 'Dốc xuống mạnh - Momentum tốt', weight: 15 }
            ]
          },
          {
            text: 'Mô tả setup MA và lý do vào lệnh:',
            type: 'textarea',
            placeholder: 'Ví dụ: Golden cross vừa xảy ra, giá trên tất cả MA, slope dốc lên mạnh, xu hướng tăng rõ ràng...',
            hint: 'Mô tả cụ thể về crossover và xu hướng'
          }
        ]
      },
      price_action: {
        name: 'Price Action Patterns',
        icon: '🕯️',
        description: 'Phân tích các mô hình nến Nhật và price action để dự đoán hướng di chuyển',
        questions: [
          {
            text: 'Có xuất hiện pattern nến Nhật nào không?',
            type: 'radio',
            options: [
              { value: 'strong_reversal', label: '🔄 Reversal pattern mạnh (Hammer, Doji, Engulfing)', weight: 25 },
              { value: 'continuation', label: '➡️ Continuation pattern (Flag, Pennant)', weight: 20 },
              { value: 'weak_pattern', label: '⚠️ Pattern yếu hoặc không rõ ràng', weight: 10 },
              { value: 'no_pattern', label: '❌ Không có pattern đặc biệt', weight: 0 }
            ]
          },
          {
            text: 'Cấu trúc thị trường hiện tại?',
            type: 'radio',
            options: [
              { value: 'higher_highs_lows', label: '📈 Higher Highs & Higher Lows - Uptrend', weight: 20 },
              { value: 'lower_highs_lows', label: '📉 Lower Highs & Lower Lows - Downtrend', weight: 20 },
              { value: 'range_bound', label: '🔄 Range-bound - Sideway', weight: 10 },
              { value: 'unclear', label: '❓ Cấu trúc không rõ ràng', weight: 0 }
            ]
          },
          {
            text: 'Có breakout khỏi cấu trúc quan trọng không?',
            type: 'radio',
            options: [
              { value: 'strong_breakout', label: '🚀 Breakout mạnh với volume cao', weight: 20 },
              { value: 'weak_breakout', label: '⚠️ Breakout yếu hoặc false breakout', weight: 5 },
              { value: 'no_breakout', label: '❌ Không có breakout', weight: 10 }
            ]
          },
          {
            text: 'Momentum hiện tại như thế nào?',
            type: 'select',
            options: [
              { value: 'strong_momentum', label: 'Momentum mạnh - Nến liên tiếp cùng hướng', weight: 15 },
              { value: 'weak_momentum', label: 'Momentum yếu - Nến nhỏ, indecision', weight: 5 },
              { value: 'no_momentum', label: 'Không có momentum rõ ràng', weight: 0 }
            ]
          },
          {
            text: 'Mô tả pattern và setup price action:',
            type: 'textarea',
            placeholder: 'Ví dụ: Hammer xuất hiện tại support, breakout khỏi range với momentum mạnh, cấu trúc uptrend...',
            hint: 'Mô tả cụ thể về pattern nến và cấu trúc thị trường'
          }
        ]
      }
    };

    return (methods as any)[method] || methods.bollinger_bands;
  }

  private async processTradingAnalysis(method: string, methodData: any) {
    // Collect answers
    const answers = [];
    let totalScore = 0;
    let maxScore = 0;
    let description = '';

    for (let i = 0; i < methodData.questions.length; i++) {
      const question = methodData.questions[i];
      let answer = '';
      let score = 0;

      if (question.type === 'radio') {
        const selectedRadio = document.querySelector(`input[name="question-${i}"]:checked`) as HTMLInputElement;
        if (selectedRadio) {
          answer = selectedRadio.value;
          const selectedOption = question.options.find((opt: any) => opt.value === answer);
          score = selectedOption ? selectedOption.weight : 0;
        }
      } else if (question.type === 'select') {
        const selectElement = document.getElementById(`question-${i}`) as HTMLSelectElement;
        if (selectElement && selectElement.value) {
          answer = selectElement.value;
          const selectedOption = question.options.find((opt: any) => opt.value === answer);
          score = selectedOption ? selectedOption.weight : 0;
        }
      } else if (question.type === 'textarea') {
        const textareaElement = document.getElementById(`question-${i}`) as HTMLTextAreaElement;
        if (textareaElement) {
          answer = textareaElement.value;
          description = answer; // Use last textarea as description
          score = answer.length > 20 ? 10 : 0; // Basic scoring for description
        }
      }

      answers.push({
        question: question.text,
        answer: answer,
        score: score
      });

      totalScore += score;
      maxScore += Math.max(...question.options?.map((opt: any) => opt.weight) || [10]);
    }

    // Validation
    const unansweredQuestions = answers.filter(a => !a.answer).length;
    if (unansweredQuestions > 0) {
      alert(`Vui lòng trả lời tất cả ${unansweredQuestions} câu hỏi còn lại!`);
      return;
    }

    // Calculate percentage
    const percentage = Math.round((totalScore / maxScore) * 100);

    // Generate recommendation
    const recommendation = this.generateTradingRecommendation(percentage, method);

    // Create analysis result
    const analysisResult = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      method: method,
      methodName: methodData.name,
      answers: answers,
      totalScore: totalScore,
      maxScore: maxScore,
      percentage: percentage,
      recommendation: recommendation,
      description: description
    };

    try {
      // Save to JSON Server
      const response = await fetch(`${this.apiBase}/tradingSessions`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(analysisResult)
      });

      if (response.ok) {
        console.log('✅ Trading analysis saved:', analysisResult);
        this.removeModal('trading-analysis');

        // Show results
        this.showAnalysisResults(analysisResult);
      } else {
        throw new Error('Failed to save analysis');
      }
    } catch (error) {
      console.error('❌ Error saving analysis:', error);
      // Still show results even if save fails
      this.removeModal('trading-analysis');
      this.showAnalysisResults(analysisResult);
    }
  }

  private generateTradingRecommendation(percentage: number, method: string) {
    let recommendation = {
      shouldTrade: false,
      riskLevel: 'high',
      confidence: 'low',
      message: '',
      advice: '',
      maxRisk: '0%'
    };

    if (percentage >= 80) {
      recommendation = {
        shouldTrade: true,
        riskLevel: 'low',
        confidence: 'high',
        message: 'Setup xuất sắc! Tất cả điều kiện đều thuận lợi.',
        advice: 'Đây là cơ hội tốt để vào lệnh. Hãy tuân thủ risk management và theo dõi chặt chẽ.',
        maxRisk: '2%'
      };
    } else if (percentage >= 60) {
      recommendation = {
        shouldTrade: true,
        riskLevel: 'medium',
        confidence: 'medium',
        message: 'Setup tốt với một số điều kiện thuận lợi.',
        advice: 'Có thể cân nhắc vào lệnh nhưng cần giảm size và cẩn thận hơn.',
        maxRisk: '1%'
      };
    } else if (percentage >= 40) {
      recommendation = {
        shouldTrade: false,
        riskLevel: 'high',
        confidence: 'low',
        message: 'Setup trung bình, nhiều yếu tố chưa thuận lợi.',
        advice: 'Nên chờ setup tốt hơn. Nếu vẫn muốn vào lệnh, hãy giảm size xuống mức tối thiểu.',
        maxRisk: '0.5%'
      };
    } else {
      recommendation = {
        shouldTrade: false,
        riskLevel: 'extreme',
        confidence: 'very_low',
        message: 'Setup yếu, không đủ điều kiện để giao dịch.',
        advice: 'Tuyệt đối không nên vào lệnh. Hãy chờ cơ hội tốt hơn hoặc học thêm về phương pháp này.',
        maxRisk: '0%'
      };
    }

    return recommendation;
  }

  private showAnalysisResults(analysisResult: any) {
    const rec = analysisResult.recommendation;

    // Determine colors based on percentage
    const getScoreColor = (percentage: number) => {
      if (percentage >= 80) return { color: '#4caf50', bgColor: '#e8f5e8', icon: '🎯' };
      if (percentage >= 60) return { color: '#ff9800', bgColor: '#fff3e0', icon: '⚠️' };
      if (percentage >= 40) return { color: '#f44336', bgColor: '#ffebee', icon: '🚨' };
      return { color: '#d32f2f', bgColor: '#ffcdd2', icon: '🛑' };
    };

    const scoreConfig = getScoreColor(analysisResult.percentage);

    const content = `
      <div style="text-align: center; margin-bottom: 24px;">
        <div style="
          display: inline-block;
          padding: 20px;
          border-radius: 50%;
          background: ${scoreConfig.bgColor};
          margin-bottom: 16px;
        ">
          <span style="font-size: 48px;">${scoreConfig.icon}</span>
        </div>
        <h3 style="margin: 0; color: ${scoreConfig.color}; font-size: 24px;">
          ${analysisResult.percentage}%
        </h3>
        <p style="margin: 8px 0 0 0; color: #666;">
          ${analysisResult.totalScore}/${analysisResult.maxScore} điểm
        </p>
      </div>

      <div style="background: ${scoreConfig.bgColor}; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid ${scoreConfig.color};">
        <h4 style="margin: 0 0 8px 0; color: ${scoreConfig.color};">📋 Đánh giá Setup:</h4>
        <p style="margin: 0 0 8px 0; color: #333; line-height: 1.5;">${rec.message}</p>
        <p style="margin: 0; color: #666; line-height: 1.5;"><strong>Khuyến nghị:</strong> ${rec.advice}</p>
      </div>

      <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h4 style="margin: 0 0 12px 0; color: #333;">📊 Chi tiết phân tích:</h4>
        <div style="margin-bottom: 12px; color: #333;">
          <strong style="color: #333;">Phương pháp:</strong> <span style="color: #333;">${analysisResult.methodName}</span>
        </div>
        <div style="margin-bottom: 12px; color: #333;">
          <strong style="color: #333;">Thời gian:</strong> <span style="color: #333;">${new Date(analysisResult.timestamp).toLocaleString('vi-VN')}</span>
        </div>
        ${analysisResult.description ? `
          <div style="margin-bottom: 12px; color: #333;">
            <strong style="color: #333;">Mô tả setup:</strong><br>
            <em style="color: #666;">"${analysisResult.description}"</em>
          </div>
        ` : ''}
      </div>

      <div style="background: #e3f2fd; padding: 16px; border-radius: 8px; margin-bottom: 20px;">
        <h4 style="margin: 0 0 12px 0; color: #1976d2;">🎯 Thông số giao dịch:</h4>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; font-size: 14px; color: #333;">
          <div>
            <strong style="color: #333;">Nên giao dịch:</strong> <span style="color: #333;">${rec.shouldTrade ? '✅ Có' : '❌ Không'}</span>
          </div>
          <div>
            <strong style="color: #333;">Risk tối đa:</strong> <span style="color: #333;">${rec.maxRisk}</span>
          </div>
          <div>
            <strong style="color: #333;">Confidence:</strong> <span style="color: #333;">${rec.confidence}</span>
          </div>
          <div>
            <strong style="color: #333;">Risk Level:</strong> <span style="color: #333;">${rec.riskLevel}</span>
          </div>
        </div>
      </div>

      <div style="background: #fff3cd; padding: 16px; border-radius: 8px; margin-bottom: 20px;">
        <h4 style="margin: 0 0 8px 0; color: #856404;">💡 Lưu ý quan trọng:</h4>
        <ul style="margin: 0; padding-left: 20px; color: #856404; font-size: 14px;">
          <li>Kết quả này chỉ mang tính tham khảo</li>
          <li>Luôn tuân thủ risk management</li>
          <li>Theo dõi thị trường liên tục</li>
          <li>Có kế hoạch exit rõ ràng</li>
        </ul>
      </div>
    `;

    const actions = [
      {
        text: 'Phân tích lại',
        onClick: () => {
          this.removeModal('analysis-results');
          this.showTradingMethodSelector();
        }
      }
    ];

    if (rec.shouldTrade) {
      actions.push({
        text: 'Tiếp tục giao dịch',
        primary: true,
        onClick: () => {
          this.removeModal('analysis-results');
          this.showTradingInterface(analysisResult);
        }
      });
    } else {
      actions.push({
        text: 'Chờ cơ hội khác',
        primary: true,
        onClick: () => {
          this.removeModal('analysis-results');
          this.showSuccessMessage('Quyết định khôn ngoan! Hãy chờ setup tốt hơn.');
        }
      });
    }

    this.createModal('analysis-results', '📊 Kết quả Phân tích', content, actions);
  }

  private showTradingInterface(analysisResult: any) {
    console.log('💹 Showing trading interface...');
    this.currentFlow = 'trading';

    const content = `
      <div style="text-align: center; padding: 40px 20px;">
        <div style="font-size: 48px; margin-bottom: 16px;">💹</div>
        <h3 style="margin: 0 0 16px 0; color: #333;">Giao diện Giao dịch</h3>
        <p style="color: #666; margin-bottom: 20px;">Tính năng này đang được phát triển...</p>
        <p style="color: #666; font-size: 14px;">Sẽ có giao diện đặt lệnh, quản lý risk và theo dõi thống kê real-time.</p>
        <div style="background: #e8f5e8; padding: 12px; border-radius: 6px; margin-top: 16px;">
          <small style="color: #2e7d32;">✅ <strong>Setup đã được phê duyệt:</strong> ${analysisResult.percentage}% - ${analysisResult.recommendation.message}</small>
        </div>
      </div>
    `;

    const actions = [
      {
        text: 'Quay lại',
        onClick: () => {
          this.removeModal('trading-interface');
          this.showAnalysisResults(analysisResult);
        }
      },
      {
        text: 'Hoàn thành',
        primary: true,
        onClick: () => {
          this.removeModal('trading-interface');
          this.showSuccessMessage('Cảm ơn bạn đã test Trading Method Selector! Tính năng Trading Interface sẽ sớm được phát triển.');
        }
      }
    ];

    this.createModal('trading-interface', '💹 Giao diện Giao dịch', content, actions);
  }

  private showCustomMethodCreator() {
    console.log('⚙️ Showing custom method creator...');

    const content = `
      <div style="margin-bottom: 24px;">
        <p style="color: #666; margin-bottom: 16px;">Tạo phương pháp giao dịch tùy chỉnh với bộ câu hỏi phân tích riêng phù hợp với strategy của bạn.</p>
        <div style="background: #e8f5e8; padding: 12px; border-radius: 6px; margin-bottom: 16px;">
          <small style="color: #2e7d32;">💡 <strong>Hướng dẫn:</strong> Tạo 3-7 câu hỏi để đánh giá setup giao dịch. Mỗi câu hỏi sẽ có trọng số điểm để tính toán khuyến nghị.</small>
        </div>
      </div>

      <div style="display: grid; gap: 16px;">
        <div>
          <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #333;">📊 Tên phương pháp</label>
          <input type="text" id="method-name" placeholder="Ví dụ: Fibonacci Retracement Strategy" style="
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            color: #333;
            background: white;
            box-sizing: border-box;
            transition: border-color 0.2s;
          " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'">
        </div>

        <div>
          <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #333;">📝 Mô tả phương pháp</label>
          <textarea id="method-description" placeholder="Mô tả ngắn gọn về phương pháp giao dịch này..." style="
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            color: #333;
            background: white;
            min-height: 80px;
            resize: vertical;
            box-sizing: border-box;
            transition: border-color 0.2s;
          " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'"></textarea>
        </div>

        <div>
          <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #333;">🎯 Icon (emoji)</label>
          <input type="text" id="method-icon" placeholder="📈" maxlength="2" style="
            width: 100px;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 20px;
            color: #333;
            background: white;
            text-align: center;
            box-sizing: border-box;
            transition: border-color 0.2s;
          " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'">
          <small style="color: #666; margin-left: 8px;">Chọn 1 emoji đại diện</small>
        </div>
      </div>

      <div style="margin: 24px 0;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
          <h4 style="margin: 0; color: #333;">❓ Câu hỏi phân tích (tối thiểu 3 câu)</h4>
          <button onclick="window.addCustomQuestion()" style="
            background: #1976d2;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
          " onmouseover="this.style.backgroundColor='#1565c0'" onmouseout="this.style.backgroundColor='#1976d2'">
            + Thêm câu hỏi
          </button>
        </div>

        <div id="custom-questions-container">
          <!-- Questions will be added here dynamically -->
        </div>
      </div>

      <div style="background: #fff3cd; padding: 16px; border-radius: 6px; border-left: 4px solid #ffc107;">
        <h4 style="margin: 0 0 8px 0; color: #856404;">💡 Lưu ý quan trọng:</h4>
        <ul style="margin: 0; padding-left: 20px; color: #856404; font-size: 14px;">
          <li>Mỗi câu hỏi nên tập trung vào 1 khía cạnh cụ thể của setup</li>
          <li>Trọng số cao (20-30) cho yếu tố quan trọng nhất</li>
          <li>Trọng số thấp (5-10) cho yếu tố phụ trợ</li>
          <li>Tổng trọng số nên từ 80-120 điểm</li>
        </ul>
      </div>
    `;

    const actions = [
      {
        text: 'Quay lại',
        onClick: () => {
          this.removeModal('custom-method-creator');
          this.showTradingMethodSelector();
        }
      },
      {
        text: 'Tạo phương pháp',
        primary: true,
        onClick: () => this.saveCustomMethod()
      }
    ];

    this.createModal('custom-method-creator', '⚙️ Tạo Phương pháp Tùy chỉnh', content, actions);

    // Initialize with 3 default questions
    setTimeout(() => {
      this.initializeCustomQuestions();
    }, 100);
  }

  private initializeCustomQuestions() {
    // Add global functions for question management
    (window as any).addCustomQuestion = () => this.addCustomQuestion();
    (window as any).removeCustomQuestion = (index: number) => this.removeCustomQuestion(index);

    // Add 3 default questions
    for (let i = 0; i < 3; i++) {
      this.addCustomQuestion();
    }
  }

  private addCustomQuestion() {
    const container = document.getElementById('custom-questions-container');
    if (!container) return;

    const questionIndex = container.children.length;
    const questionDiv = document.createElement('div');
    questionDiv.style.cssText = `
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      padding: 16px;
      margin-bottom: 16px;
      background: white;
    `;

    questionDiv.innerHTML = `
      <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 12px;">
        <h5 style="margin: 0; color: #333;">Câu hỏi ${questionIndex + 1}</h5>
        ${questionIndex >= 3 ? `
          <button onclick="window.removeCustomQuestion(${questionIndex})" style="
            background: #f44336;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-left: auto;
          ">Xóa</button>
        ` : ''}
      </div>

      <div style="display: grid; gap: 12px;">
        <div>
          <label style="display: block; margin-bottom: 4px; font-weight: 500; color: #333; font-size: 14px;">Nội dung câu hỏi:</label>
          <input type="text" id="question-text-${questionIndex}" placeholder="Ví dụ: Có tín hiệu divergence rõ ràng không?" style="
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            color: #333;
            background: white;
            box-sizing: border-box;
          ">
        </div>

        <div style="display: grid; grid-template-columns: 1fr auto; gap: 12px; align-items: end;">
          <div>
            <label style="display: block; margin-bottom: 4px; font-weight: 500; color: #333; font-size: 14px;">Loại câu hỏi:</label>
            <select id="question-type-${questionIndex}" onchange="window.updateQuestionOptions(${questionIndex})" style="
              width: 100%;
              padding: 10px;
              border: 1px solid #ddd;
              border-radius: 4px;
              font-size: 14px;
              color: #333;
              background: white;
            ">
              <option value="radio">Multiple Choice (Radio)</option>
              <option value="select">Dropdown (Select)</option>
              <option value="textarea">Mô tả chi tiết (Textarea)</option>
            </select>
          </div>

          <button onclick="window.addQuestionOption(${questionIndex})" id="add-option-btn-${questionIndex}" style="
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            white-space: nowrap;
          ">+ Thêm đáp án</button>
        </div>

        <div id="question-options-${questionIndex}">
          <!-- Options will be added here -->
        </div>
      </div>
    `;

    container.appendChild(questionDiv);

    // Add global functions for this question
    (window as any).updateQuestionOptions = (index: number) => this.updateQuestionOptions(index);
    (window as any).addQuestionOption = (index: number) => this.addQuestionOption(index);
    (window as any).removeQuestionOption = (questionIndex: number, optionIndex: number) => this.removeQuestionOption(questionIndex, optionIndex);

    // Initialize with 2 default options for radio/select
    this.addQuestionOption(questionIndex);
    this.addQuestionOption(questionIndex);
  }

  private removeCustomQuestion(index: number) {
    const container = document.getElementById('custom-questions-container');
    if (!container || container.children.length <= 3) return; // Keep minimum 3 questions

    if (container.children[index]) {
      container.removeChild(container.children[index]);

      // Update question numbers
      Array.from(container.children).forEach((child, i) => {
        const title = child.querySelector('h5');
        if (title) {
          title.textContent = `Câu hỏi ${i + 1}`;
        }
      });
    }
  }

  private updateQuestionOptions(questionIndex: number) {
    const typeSelect = document.getElementById(`question-type-${questionIndex}`) as HTMLSelectElement;
    const optionsContainer = document.getElementById(`question-options-${questionIndex}`);
    const addButton = document.getElementById(`add-option-btn-${questionIndex}`);

    if (!typeSelect || !optionsContainer || !addButton) return;

    if (typeSelect.value === 'textarea') {
      optionsContainer.innerHTML = '';
      addButton.style.display = 'none';
    } else {
      addButton.style.display = 'block';
      if (optionsContainer.children.length === 0) {
        // Add default options
        this.addQuestionOption(questionIndex);
        this.addQuestionOption(questionIndex);
      }
    }
  }

  private addQuestionOption(questionIndex: number) {
    const optionsContainer = document.getElementById(`question-options-${questionIndex}`);
    if (!optionsContainer) return;

    const optionIndex = optionsContainer.children.length;
    const optionDiv = document.createElement('div');
    optionDiv.style.cssText = `
      display: grid;
      grid-template-columns: 1fr auto auto;
      gap: 8px;
      align-items: center;
      margin-bottom: 8px;
      padding: 8px;
      background: #f8f9fa;
      border-radius: 4px;
    `;

    optionDiv.innerHTML = `
      <input type="text" id="option-text-${questionIndex}-${optionIndex}" placeholder="Nội dung đáp án..." style="
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        color: #333;
        background: white;
      ">
      <input type="number" id="option-weight-${questionIndex}-${optionIndex}" placeholder="Điểm" min="0" max="50" value="10" style="
        width: 60px;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        color: #333;
        background: white;
        text-align: center;
      ">
      ${optionIndex >= 2 ? `
        <button onclick="window.removeQuestionOption(${questionIndex}, ${optionIndex})" style="
          background: #f44336;
          color: white;
          border: none;
          padding: 6px 8px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
        ">×</button>
      ` : '<div></div>'}
    `;

    optionsContainer.appendChild(optionDiv);
  }

  private removeQuestionOption(questionIndex: number, optionIndex: number) {
    const optionsContainer = document.getElementById(`question-options-${questionIndex}`);
    if (!optionsContainer || optionsContainer.children.length <= 2) return; // Keep minimum 2 options

    if (optionsContainer.children[optionIndex]) {
      optionsContainer.removeChild(optionsContainer.children[optionIndex]);
    }
  }

  private async saveCustomMethod() {
    // Collect basic info
    const name = (document.getElementById('method-name') as HTMLInputElement)?.value;
    const description = (document.getElementById('method-description') as HTMLTextAreaElement)?.value;
    const icon = (document.getElementById('method-icon') as HTMLInputElement)?.value;

    // Validation
    if (!name || name.length < 3) {
      alert('Vui lòng nhập tên phương pháp (ít nhất 3 ký tự)!');
      return;
    }

    if (!description || description.length < 10) {
      alert('Vui lòng nhập mô tả phương pháp (ít nhất 10 ký tự)!');
      return;
    }

    if (!icon || icon.length === 0) {
      alert('Vui lòng chọn icon cho phương pháp!');
      return;
    }

    // Collect questions
    const container = document.getElementById('custom-questions-container');
    if (!container || container.children.length < 3) {
      alert('Cần ít nhất 3 câu hỏi để tạo phương pháp!');
      return;
    }

    const questions = [];
    let totalMaxScore = 0;

    for (let i = 0; i < container.children.length; i++) {
      const questionText = (document.getElementById(`question-text-${i}`) as HTMLInputElement)?.value;
      const questionType = (document.getElementById(`question-type-${i}`) as HTMLSelectElement)?.value;

      if (!questionText || questionText.length < 5) {
        alert(`Câu hỏi ${i + 1}: Vui lòng nhập nội dung câu hỏi (ít nhất 5 ký tự)!`);
        return;
      }

      const question: any = {
        text: questionText,
        type: questionType
      };

      if (questionType === 'textarea') {
        question.placeholder = 'Mô tả chi tiết setup...';
        totalMaxScore += 10; // Default score for textarea
      } else {
        // Collect options
        const optionsContainer = document.getElementById(`question-options-${i}`);
        if (!optionsContainer || optionsContainer.children.length < 2) {
          alert(`Câu hỏi ${i + 1}: Cần ít nhất 2 đáp án!`);
          return;
        }

        const options = [];
        let maxWeight = 0;

        for (let j = 0; j < optionsContainer.children.length; j++) {
          const optionText = (document.getElementById(`option-text-${i}-${j}`) as HTMLInputElement)?.value;
          const optionWeight = parseInt((document.getElementById(`option-weight-${i}-${j}`) as HTMLInputElement)?.value || '0');

          if (!optionText || optionText.length < 2) {
            alert(`Câu hỏi ${i + 1}, Đáp án ${j + 1}: Vui lòng nhập nội dung đáp án!`);
            return;
          }

          if (isNaN(optionWeight) || optionWeight < 0 || optionWeight > 50) {
            alert(`Câu hỏi ${i + 1}, Đáp án ${j + 1}: Điểm số phải từ 0-50!`);
            return;
          }

          options.push({
            value: `option_${j}`,
            label: optionText,
            weight: optionWeight
          });

          maxWeight = Math.max(maxWeight, optionWeight);
        }

        question.options = options;
        totalMaxScore += maxWeight;
      }

      questions.push(question);
    }

    // Validate total score
    if (totalMaxScore < 50) {
      alert('Tổng điểm tối đa quá thấp! Hãy tăng trọng số các đáp án để đạt ít nhất 50 điểm.');
      return;
    }

    if (totalMaxScore > 200) {
      alert('Tổng điểm tối đa quá cao! Hãy giảm trọng số các đáp án để không vượt quá 200 điểm.');
      return;
    }

    // Create custom method object
    const customMethod = {
      id: `custom_${Date.now()}`,
      name: name,
      icon: icon,
      description: description,
      questions: questions,
      totalMaxScore: totalMaxScore,
      createdAt: new Date().toISOString(),
      isCustom: true
    };

    try {
      // Save to JSON Server
      const response = await fetch(`${this.apiBase}/customMethods`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(customMethod)
      });

      if (response.ok) {
        console.log('✅ Custom method saved:', customMethod);
        this.removeModal('custom-method-creator');

        // Show success and option to test
        this.showCustomMethodSuccess(customMethod);
      } else {
        throw new Error('Failed to save custom method');
      }
    } catch (error) {
      console.error('❌ Error saving custom method:', error);
      alert('Không thể lưu phương pháp tùy chỉnh. Hãy kiểm tra JSON Server đang chạy!');
    }
  }

  private showCustomMethodSuccess(customMethod: any) {
    const content = `
      <div style="text-align: center; margin-bottom: 24px;">
        <div style="
          display: inline-block;
          padding: 20px;
          border-radius: 50%;
          background: #e8f5e8;
          margin-bottom: 16px;
        ">
          <span style="font-size: 48px;">${customMethod.icon}</span>
        </div>
        <h3 style="margin: 0; color: #4caf50; font-size: 20px;">
          Phương pháp đã được tạo thành công!
        </h3>
      </div>

      <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h4 style="margin: 0 0 12px 0; color: #333;">📊 Thông tin phương pháp:</h4>
        <div style="margin-bottom: 12px; color: #333;">
          <strong style="color: #333;">Tên:</strong> <span style="color: #333;">${customMethod.name}</span>
        </div>
        <div style="margin-bottom: 12px; color: #333;">
          <strong style="color: #333;">Mô tả:</strong> <span style="color: #333;">${customMethod.description}</span>
        </div>
        <div style="margin-bottom: 12px; color: #333;">
          <strong style="color: #333;">Số câu hỏi:</strong> <span style="color: #333;">${customMethod.questions.length}</span>
        </div>
        <div style="color: #333;">
          <strong style="color: #333;">Điểm tối đa:</strong> <span style="color: #333;">${customMethod.totalMaxScore}</span>
        </div>
      </div>

      <div style="background: #e8f5e8; padding: 16px; border-radius: 8px; margin-bottom: 20px;">
        <h4 style="margin: 0 0 8px 0; color: #2e7d32;">✅ Phương pháp đã sẵn sàng sử dụng!</h4>
        <p style="margin: 0; color: #2e7d32; font-size: 14px;">
          Bạn có thể test ngay phương pháp này hoặc quay lại để chọn phương pháp khác.
        </p>
      </div>
    `;

    const actions = [
      {
        text: 'Quay lại danh sách',
        onClick: () => {
          this.removeModal('custom-method-success');
          this.showTradingMethodSelector();
        }
      },
      {
        text: 'Test phương pháp này',
        primary: true,
        onClick: () => {
          this.removeModal('custom-method-success');
          this.showTradingAnalysis(customMethod.id, customMethod);
        }
      }
    ];

    this.createModal('custom-method-success', '✅ Tạo thành công', content, actions);
  }

  // Update getTradingMethodData to support custom methods
  private async getCustomMethod(methodId: string) {
    try {
      const response = await fetch(`${this.apiBase}/customMethods`);
      const customMethods = await response.json();
      return customMethods.find((method: any) => method.id === methodId);
    } catch (error) {
      console.error('Error fetching custom method:', error);
      return null;
    }
  }

  private async loadCustomMethods() {
    try {
      const response = await fetch(`${this.apiBase}/customMethods`);
      if (response.ok) {
        const methods = await response.json();
        console.log('✅ Loaded custom methods:', methods.length);
        return methods;
      }
    } catch (error) {
      console.log('No custom methods found or server not available');
    }
    return [];
  }

  // Restart flow
  public restartFlow() {
    // Remove all existing modals
    const modals = ['daily-goals-modal', 'psychology-assessment', 'trading-method-selector', 'trading-analysis', 'trading-interface', 'stats-dashboard'];
    modals.forEach(id => this.removeModal(id));
    
    this.currentFlow = null;
    
    // Start fresh
    this.startTradingFlow();
  }

  // Setup message listener for popup commands
  private setupMessageListener() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      console.log('Content script received message:', request);
      
      switch (request.action) {
        case 'showStats':
          this.showSuccessMessage('Stats dashboard đang được phát triển...');
          break;
        case 'restartFlow':
          this.restartFlow();
          break;
        case 'startFlow':
          this.startTradingFlow();
          break;
        case 'updateMethodSettings':
          this.methodSettings = request.methodSettings;
          console.log('✅ Method settings updated:', this.methodSettings);
          break;
        case 'tradeResult':
          this.handleTradeResult(request.result, request.profit);
          break;
        default:
          console.log('Unknown action:', request.action);
      }
      
      sendResponse({ success: true });
    });
  }
}

// Initialize when page is ready
document.addEventListener('DOMContentLoaded', () => {
  const assistant = new BinomoTradingAssistant();
  
  // Add global functions for testing
  (window as any).BinomoTradingAssistant = assistant;
});

// Also initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    const assistant = new BinomoTradingAssistant();
    (window as any).BinomoTradingAssistant = assistant;
  });
} else {
  const assistant = new BinomoTradingAssistant();
  (window as any).BinomoTradingAssistant = assistant;
}
