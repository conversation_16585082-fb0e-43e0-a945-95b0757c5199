# 🔧 File Not Found Error Fix Guide

## ❌ **Lỗi gặp phải:**
```
GET chrome-extension://jpbnogggkllkjpmfccehfafodfcpmjch/content.css net::ERR_FILE_NOT_FOUND
Failed to load resource: net::ERR_FILE_NOT_FOUND (popup)
```

## ✅ **Nguyên nhân:**
1. **content.css** không được copy vào `dist/` folder
2. **popup.html** và **options.html** reference sai đường dẫn JS files
3. **Build process** không copy static assets đúng cách
4. **Web accessible resources** không include đúng files

## 🛠️ **Solutions Applied:**

### 1. **Copy content.css vào dist:**
```bash
# Manual copy CSS file
copy "src\content\content.css" "dist\content.css"

# Verify file exists
dir dist\content.css  ✅ File exists
```

### 2. **Sửa popup.html reference:**
```html
<!-- ❌ Before (wrong path) -->
<script type="module" src="/src/popup/index.tsx"></script>

<!-- ✅ After (correct path) -->
<script type="module" src="popup/popup.js"></script>
```

### 3. **Sửa options.html reference:**
```html
<!-- ❌ Before (wrong path) -->
<script type="module" src="src/options/index.tsx"></script>

<!-- ✅ After (correct path) -->
<script type="module" src="options/options.js"></script>
```

### 4. **Cập nhật copy-assets script:**
```json
{
  "scripts": {
    "copy-assets": "xcopy public\\* dist\\ /E /Y && copy popup.html dist\\ && copy options.html dist\\ && copy src\\content\\content.css dist\\"
  }
}
```

## 📁 **File Structure sau khi fix:**
```
dist/
├── content.css              ✅ CSS file available
├── content/content.js       ✅ Content script
├── popup.html               ✅ Fixed script reference
├── popup/popup.js           ✅ Popup JS bundle
├── options.html             ✅ Fixed script reference  
├── options/options.js       ✅ Options JS bundle
├── background/background.js ✅ Service worker
├── assets/TrendingUp-*.js   ✅ Material UI bundle
├── manifest.json            ✅ Correct configuration
└── icons/                   ✅ Extension icons
```

## 🔧 **Manifest.json Configuration:**

### **Web Accessible Resources:**
```json
{
  "web_accessible_resources": [
    {
      "resources": ["assets/*", "content/*", "content.css"], // ✅ CSS accessible
      "matches": ["https://binomo1.com/*"]
    }
  ]
}
```

### **Content Scripts:**
```json
{
  "content_scripts": [
    {
      "matches": ["https://binomo1.com/trading*"],
      "js": ["content/content.js"], // ✅ JS only, CSS loaded dynamically
      "run_at": "document_end"
    }
  ]
}
```

## 🎯 **Content Script CSS Loading:**

### **Dynamic CSS Loading:**
```typescript
// content-wrapper.ts
const cssLink = document.createElement('link');
cssLink.rel = 'stylesheet';
cssLink.href = chrome.runtime.getURL('content.css'); // ✅ Correct path
document.head.appendChild(cssLink);
```

### **Verification:**
```javascript
// Check if CSS is loaded
console.log('CSS URL:', chrome.runtime.getURL('content.css'));
// Should output: chrome-extension://[id]/content.css
```

## 🚀 **Build Process Fix:**

### **Step-by-step Build:**
```bash
# 1. Build with Vite
npx vite build

# 2. Copy static assets
copy "src\content\content.css" "dist\content.css"
copy "popup.html" "dist\popup.html" 
copy "options.html" "dist\options.html"
copy "public\manifest.json" "dist\manifest.json"

# 3. Verify files exist
dir dist\content.css     ✅
dir dist\popup.html      ✅  
dir dist\options.html    ✅
dir dist\manifest.json   ✅
```

### **Automated Script:**
```json
{
  "scripts": {
    "build": "vite build && npm run copy-assets",
    "copy-assets": "xcopy public\\* dist\\ /E /Y && copy popup.html dist\\ && copy options.html dist\\ && copy src\\content\\content.css dist\\",
    "build-extension": "npm run build && echo Extension built successfully!"
  }
}
```

## 🔍 **Debugging Steps:**

### **1. Check Extension Console:**
```javascript
// In extension popup/options DevTools
console.log('Script loaded:', window.location.href);
// Should show: chrome-extension://[id]/popup.html
```

### **2. Check Content Script Console:**
```javascript
// In binomo1.com page DevTools  
console.log('CSS loaded:', document.querySelector('link[href*="content.css"]'));
// Should show: <link rel="stylesheet" href="chrome-extension://[id]/content.css">
```

### **3. Check Network Tab:**
- ✅ **popup/popup.js** should load successfully (200 OK)
- ✅ **options/options.js** should load successfully (200 OK)  
- ✅ **content.css** should load successfully (200 OK)
- ❌ **No 404 errors** for missing files

### **4. Check Extension Errors:**
```
Chrome Extensions → Developer mode → Errors
# Should show no errors related to file loading
```

## ⚠️ **Common Issues:**

### **Issue 1: CSS still not found**
```bash
# Solution: Verify web_accessible_resources in manifest.json
"resources": ["assets/*", "content/*", "content.css"]
```

### **Issue 2: Popup/Options blank**
```bash
# Solution: Check script src paths in HTML files
# Must be relative to extension root: "popup/popup.js"
```

### **Issue 3: Build overwrites HTML files**
```bash
# Solution: Edit HTML files in dist/ after build
# Or configure Vite to not process HTML files
```

## 🎯 **Success Indicators:**

### ✅ **Extension Loading:**
- **Popup** opens with Material UI interface
- **Options page** opens with settings tabs
- **Content script** loads on binomo1.com/trading
- **No console errors** related to file loading

### ✅ **File Verification:**
```bash
# All files exist in dist/
dist\content.css         ✅ 
dist\popup.html          ✅ (correct script src)
dist\options.html        ✅ (correct script src)  
dist\popup\popup.js      ✅
dist\options\options.js  ✅
dist\content\content.js  ✅
```

### ✅ **Network Requests:**
- **All extension resources** load with 200 OK status
- **No ERR_FILE_NOT_FOUND** errors
- **CSS applied** correctly to content script

## 🚀 **Final Testing:**

1. **Load extension** từ `dist/` folder
2. **Click popup** → Should show Material UI interface
3. **Open options** → Should show settings page
4. **Visit binomo1.com/trading** → Content script should load
5. **Check DevTools** → No file loading errors

---

**🎉 Extension bây giờ load tất cả files thành công và không còn lỗi ERR_FILE_NOT_FOUND!**
