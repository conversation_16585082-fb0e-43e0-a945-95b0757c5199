import React, { useState, useEffect } from "react";
import {
  Box,
  Button,
  ButtonGroup,
  Typography,
  Alert,
  Grid,
  Card,
  CardContent,
  CardActions,
  IconButton,
  Chip,
  Divider,
  ThemeProvider,
  createTheme,
  CssBaseline,
  Paper,
  Stack
} from '@mui/material';
import {
  TrendingUp as TradingIcon,
  Assessment as StatsIcon,
  Refresh as RefreshIcon,
  ArrowBack as BackIcon,
  Settings as SettingsIcon,
  Launch as LaunchIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  ViewSidebar as SidebarIcon
} from '@mui/icons-material';

// Material UI Theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#007bff',
    },
    secondary: {
      main: '#6c757d',
    },
    success: {
      main: '#28a745',
    },
    error: {
      main: '#dc3545',
    },
    warning: {
      main: '#ffc107',
    },
  },
  typography: {
    h6: {
      fontWeight: 600,
    },
    body2: {
      fontSize: '0.875rem',
    },
  },
});

const Popup = () => {
  const [apiStatus, setApiStatus] = useState<'checking' | 'online' | 'offline'>('checking');
  const [currentPage, setCurrentPage] = useState<'main' | 'settings'>('main');

  useEffect(() => {
    checkApiStatus();
  }, []);

  const checkApiStatus = async () => {
    try {
      const response = await fetch('http://localhost:3001/dailyGoals?_limit=1');
      setApiStatus(response.ok ? 'online' : 'offline');
    } catch (error) {
      setApiStatus('offline');
    }
  };

  const openStatsPage = () => {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]?.id) {
        chrome.tabs.sendMessage(tabs[0].id, { action: 'showStats' });
      }
    });
  };

  const restartFlow = () => {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]?.id) {
        chrome.tabs.sendMessage(tabs[0].id, { action: 'restartFlow' });
      }
    });
  };

  const openTradingPage = () => {
    chrome.tabs.create({ url: 'https://binomo1.com/trading' });
  };

  const openSidebar = async () => {
    try {
      // Get current tab to get window ID
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tab?.windowId) {
        await chrome.sidePanel.open({ windowId: tab.windowId });
      } else {
        // Alternative approach: get current window
        const windows = await chrome.windows.getAll({ populate: false });
        const currentWindow = windows.find(w => w.focused) || windows[0];
        if (currentWindow?.id) {
          await chrome.sidePanel.open({ windowId: currentWindow.id });
        } else {
          throw new Error('No valid window found');
        }
      }
    } catch (error) {
      console.error('Error opening sidebar:', error);
      alert('Không thể mở sidebar. Hãy thử right-click vào extension icon và chọn "Open side panel".');
    }
  };



  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {currentPage === 'main' ? (
        <MainPage
          apiStatus={apiStatus}
          onOpenTradingPage={openTradingPage}
          onOpenStatsPage={openStatsPage}
          onRestartFlow={restartFlow}
          onOpenSettings={() => setCurrentPage('settings')}
          onOpenSidebar={openSidebar}
          onRefreshApi={checkApiStatus}
        />
      ) : (
        <SettingsPage onBack={() => setCurrentPage('main')} />
      )}
    </ThemeProvider>
  );
};

// Main Page Component
interface MainPageProps {
  apiStatus: 'checking' | 'online' | 'offline';
  onOpenTradingPage: () => void;
  onOpenStatsPage: () => void;
  onRestartFlow: () => void;
  onOpenSettings: () => void;
  onOpenSidebar: () => void;
  onRefreshApi: () => void;
}

const MainPage: React.FC<MainPageProps> = ({
  apiStatus,
  onOpenTradingPage,
  onOpenStatsPage,
  onRestartFlow,
  onOpenSettings,
  onOpenSidebar,
  onRefreshApi
}) => {
  return (
    <Box sx={{ width: 380, p: 2 }}>
      {/* Header */}
      <Grid container spacing={1} sx={{ mb: 2 }}>
        <Grid size={8}>
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
              🎯 Binomo Trading Assistant
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
              Công cụ hỗ trợ trading có kỷ luật
            </Typography>
          </Box>
        </Grid>
        <Grid size={2}>
          <IconButton onClick={onOpenSidebar} size="small" title="Mở Sidebar">
            <SidebarIcon />
          </IconButton>
        </Grid>
        <Grid size={2}>
          <IconButton onClick={onOpenSettings} size="small" title="Cài đặt">
            <SettingsIcon />
          </IconButton>
        </Grid>
      </Grid>

      {/* API Status */}
      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid size={12}>
          <Alert
            severity={apiStatus === 'online' ? 'success' : apiStatus === 'offline' ? 'error' : 'info'}
            icon={apiStatus === 'online' ? <SuccessIcon /> : apiStatus === 'offline' ? <ErrorIcon /> : <InfoIcon />}
            sx={{ fontSize: '0.75rem' }}
            action={
              <IconButton size="small" onClick={onRefreshApi}>
                <RefreshIcon fontSize="small" />
              </IconButton>
            }
          >
            {apiStatus === 'checking' && 'Đang kiểm tra kết nối...'}
            {apiStatus === 'online' && 'JSON Server đang hoạt động'}
            {apiStatus === 'offline' && 'JSON Server không hoạt động - Hãy chạy npm run server'}
          </Alert>
        </Grid>
      </Grid>

      {/* Main Actions */}
      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid size={12}>
          <Card>
            <CardContent sx={{ pb: 1 }}>
              <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                <TradingIcon color="primary" />
                Giao dịch
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Mở trang Binomo để bắt đầu giao dịch
              </Typography>
            </CardContent>
            <CardActions>
              <Button
                fullWidth
                variant="contained"
                onClick={onOpenTradingPage}
                startIcon={<LaunchIcon />}
                disabled={apiStatus === 'offline'}
              >
                Mở trang giao dịch
              </Button>
            </CardActions>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid size={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ pb: 1 }}>
              <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                <StatsIcon color="success" />
                Thống kê
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                Xem báo cáo chi tiết
              </Typography>
            </CardContent>
            <CardActions>
              <Button
                fullWidth
                variant="outlined"
                color="success"
                onClick={onOpenStatsPage}
                size="small"
                disabled={apiStatus === 'offline'}
              >
                Xem thống kê
              </Button>
            </CardActions>
          </Card>
        </Grid>
        <Grid size={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ pb: 1 }}>
              <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                <RefreshIcon color="secondary" />
                Khởi động lại
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                Reset flow giao dịch
              </Typography>
            </CardContent>
            <CardActions>
              <Button
                fullWidth
                variant="outlined"
                color="secondary"
                onClick={onRestartFlow}
                size="small"
                disabled={apiStatus === 'offline'}
              >
                Khởi động lại
              </Button>
            </CardActions>
          </Card>
        </Grid>
      </Grid>

      {/* Footer Info */}
      <Grid container spacing={2}>
        <Grid size={12}>
          <Paper sx={{ p: 1.5, backgroundColor: '#f8f9fa' }}>
            <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', fontSize: '0.75rem' }}>
              <strong>Lưu ý:</strong> Extension chỉ hoạt động trên trang binomo1.com/trading
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mt: 1 }}>
              <Chip label="Material UI v7+" size="small" color="primary" variant="outlined" />
              <Chip label="JSON Server" size="small" color="secondary" variant="outlined" />
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

// Settings Page Component
interface SettingsPageProps {
  onBack: () => void;
}

const SettingsPage: React.FC<SettingsPageProps> = ({ onBack }) => {
  const [settings, setSettings] = useState({
    theme: 'light',
    language: 'vi',
    notifications: true,
    autoBackup: true,
    riskWarnings: true,
    defaultTradeAmount: 10,
    defaultTradeDuration: 5,
    apiUrl: 'http://localhost:3001'
  });

  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const saveSettings = async () => {
    setLoading(true);
    try {
      // Save to chrome storage
      await chrome.storage.local.set({ userSettings: settings });
      setSuccess(true);
      setTimeout(() => setSuccess(false), 2000);
    } catch (error) {
      console.error('Error saving settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const resetSettings = () => {
    const defaultSettings = {
      theme: 'light',
      language: 'vi',
      notifications: true,
      autoBackup: true,
      riskWarnings: true,
      defaultTradeAmount: 10,
      defaultTradeDuration: 5,
      apiUrl: 'http://localhost:3001'
    };
    setSettings(defaultSettings);
  };

  return (
    <Box sx={{ width: 380, p: 2 }}>
      {/* Header */}
      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid size={2}>
          <IconButton onClick={onBack} size="small">
            <BackIcon />
          </IconButton>
        </Grid>
        <Grid size={10}>
          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <SettingsIcon color="primary" />
            Cài đặt Extension
          </Typography>
        </Grid>
      </Grid>

      {success && (
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid size={12}>
            <Alert severity="success" sx={{ fontSize: '0.75rem' }}>
              Cài đặt đã được lưu thành công!
            </Alert>
          </Grid>
        </Grid>
      )}

      {/* Settings Form */}
      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid size={12}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" sx={{ mb: 2 }}>
                🎨 Giao diện
              </Typography>

              <Stack spacing={2}>
                <Box>
                  <Typography variant="body2" sx={{ mb: 1 }}>Theme:</Typography>
                  <ButtonGroup size="small" fullWidth>
                    <Button
                      variant={settings.theme === 'light' ? 'contained' : 'outlined'}
                      onClick={() => handleSettingChange('theme', 'light')}
                    >
                      Light
                    </Button>
                    <Button
                      variant={settings.theme === 'dark' ? 'contained' : 'outlined'}
                      onClick={() => handleSettingChange('theme', 'dark')}
                    >
                      Dark
                    </Button>
                  </ButtonGroup>
                </Box>

                <Box>
                  <Typography variant="body2" sx={{ mb: 1 }}>Ngôn ngữ:</Typography>
                  <ButtonGroup size="small" fullWidth>
                    <Button
                      variant={settings.language === 'vi' ? 'contained' : 'outlined'}
                      onClick={() => handleSettingChange('language', 'vi')}
                    >
                      Tiếng Việt
                    </Button>
                    <Button
                      variant={settings.language === 'en' ? 'contained' : 'outlined'}
                      onClick={() => handleSettingChange('language', 'en')}
                    >
                      English
                    </Button>
                  </ButtonGroup>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid size={12}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" sx={{ mb: 2 }}>
                ⚙️ Chức năng
              </Typography>

              <Stack spacing={2}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2">Thông báo</Typography>
                  <Button
                    size="small"
                    variant={settings.notifications ? 'contained' : 'outlined'}
                    color={settings.notifications ? 'success' : 'secondary'}
                    onClick={() => handleSettingChange('notifications', !settings.notifications)}
                  >
                    {settings.notifications ? 'BẬT' : 'TẮT'}
                  </Button>
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2">Tự động backup</Typography>
                  <Button
                    size="small"
                    variant={settings.autoBackup ? 'contained' : 'outlined'}
                    color={settings.autoBackup ? 'success' : 'secondary'}
                    onClick={() => handleSettingChange('autoBackup', !settings.autoBackup)}
                  >
                    {settings.autoBackup ? 'BẬT' : 'TẮT'}
                  </Button>
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2">Cảnh báo rủi ro</Typography>
                  <Button
                    size="small"
                    variant={settings.riskWarnings ? 'contained' : 'outlined'}
                    color={settings.riskWarnings ? 'success' : 'secondary'}
                    onClick={() => handleSettingChange('riskWarnings', !settings.riskWarnings)}
                  >
                    {settings.riskWarnings ? 'BẬT' : 'TẮT'}
                  </Button>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid size={12}>
          <Card>
            <CardContent>
              <Typography variant="subtitle2" sx={{ mb: 2 }}>
                💰 Giao dịch mặc định
              </Typography>

              <Stack spacing={2}>
                <Box>
                  <Typography variant="body2" sx={{ mb: 1 }}>Số tiền mặc định ($):</Typography>
                  <ButtonGroup size="small" fullWidth>
                    {[1, 5, 10, 25].map(amount => (
                      <Button
                        key={amount}
                        variant={settings.defaultTradeAmount === amount ? 'contained' : 'outlined'}
                        onClick={() => handleSettingChange('defaultTradeAmount', amount)}
                      >
                        ${amount}
                      </Button>
                    ))}
                  </ButtonGroup>
                </Box>

                <Box>
                  <Typography variant="body2" sx={{ mb: 1 }}>Thời gian mặc định:</Typography>
                  <ButtonGroup size="small" fullWidth>
                    {[1, 5, 15, 30].map(duration => (
                      <Button
                        key={duration}
                        variant={settings.defaultTradeDuration === duration ? 'contained' : 'outlined'}
                        onClick={() => handleSettingChange('defaultTradeDuration', duration)}
                      >
                        {duration}m
                      </Button>
                    ))}
                  </ButtonGroup>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Actions */}
      <Grid container spacing={2}>
        <Grid size={6}>
          <Button
            fullWidth
            variant="outlined"
            color="secondary"
            onClick={resetSettings}
            size="small"
          >
            Reset
          </Button>
        </Grid>
        <Grid size={6}>
          <Button
            fullWidth
            variant="contained"
            onClick={saveSettings}
            disabled={loading}
            size="small"
          >
            {loading ? 'Đang lưu...' : 'Lưu cài đặt'}
          </Button>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Popup;
