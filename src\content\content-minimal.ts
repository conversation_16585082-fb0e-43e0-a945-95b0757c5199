// Binomo Trading Assistant - Minimal Content Script
// Only essential features: trading block detection and psychology confirmation

console.log('🎯 Binomo Trading Assistant - Minimal Content Script Loaded');

class BinomoTradingAssistant {
  private isBlocked = false;

  constructor() {
    this.init();
  }

  private async init() {
    // Check if trading is blocked
    await this.checkTradingBlock();
    
    if (this.isBlocked) {
      this.showBlockedMessage();
    } else {
      // Check if user needs psychology confirmation
      this.checkPsychologyConfirmation();
    }
  }

  private async checkTradingBlock(): Promise<void> {
    try {
      const result = await chrome.storage.local.get(['tradingBlocked', 'blockDate']);
      if (result.tradingBlocked && result.blockDate) {
        const blockDate = new Date(result.blockDate);
        const now = new Date();
        const diffHours = (now.getTime() - blockDate.getTime()) / (1000 * 60 * 60);
        
        if (diffHours < 24) {
          this.isBlocked = true;
        } else {
          // Unblock after 24 hours
          await chrome.storage.local.remove(['tradingBlocked', 'blockDate']);
          this.isBlocked = false;
        }
      }
    } catch (error) {
      console.error('Error checking trading block:', error);
      this.isBlocked = false;
    }
  }

  private showBlockedMessage() {
    // Create blocking overlay
    const overlay = document.createElement('div');
    overlay.id = 'trading-block-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.9);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    overlay.innerHTML = `
      <div style="
        background: white;
        padding: 40px;
        border-radius: 16px;
        text-align: center;
        max-width: 500px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="font-size: 64px; margin-bottom: 20px;">🧘‍♂️</div>
        <h2 style="color: #d32f2f; margin: 0 0 16px 0;">Giao dịch bị khóa</h2>
        <p style="color: #666; margin: 0 0 24px 0; line-height: 1.5;">
          Hệ thống đã khóa giao dịch đến ngày mai để bảo vệ tâm lý của bạn.<br>
          Hãy sử dụng thời gian này để nghỉ ngơi và rèn luyện tâm.
        </p>
        <div style="display: flex; gap: 12px; justify-content: center;">
          <button id="meditation-btn" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🧘‍♂️ Thiền định</button>
          <button id="close-tab-btn" style="
            background: #666;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">❌ Đóng tab</button>
        </div>
        <p style="color: #999; font-size: 12px; margin: 16px 0 0 0;">
          Khóa sẽ tự động mở sau 24 giờ
        </p>
      </div>
    `;

    document.body.appendChild(overlay);

    // Add event listeners
    document.getElementById('meditation-btn')?.addEventListener('click', () => {
      this.openMeditationOptions();
    });

    document.getElementById('close-tab-btn')?.addEventListener('click', () => {
      window.close();
    });
  }

  private async checkPsychologyConfirmation() {
    try {
      const result = await chrome.storage.local.get(['needsPsychologyConfirmation', 'lastConfirmationTime']);
      
      if (result.needsPsychologyConfirmation) {
        this.showPsychologyConfirmation();
      }
    } catch (error) {
      console.error('Error checking psychology confirmation:', error);
    }
  }

  private showPsychologyConfirmation() {
    // Create confirmation modal
    const modal = document.createElement('div');
    modal.id = 'psychology-confirmation-modal';
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    modal.innerHTML = `
      <div style="
        background: white;
        padding: 32px;
        border-radius: 16px;
        text-align: center;
        max-width: 450px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="font-size: 48px; margin-bottom: 16px;">🧠</div>
        <h3 style="color: #1976d2; margin: 0 0 16px 0;">Xác nhận tâm lý</h3>
        <p style="color: #666; margin: 0 0 24px 0; line-height: 1.5;">
          Bạn đã hoàn thành thiền định hoặc nghỉ ngơi?<br>
          Tâm lý hiện tại có ổn định và sẵn sàng giao dịch không?
        </p>
        
        <div style="margin-bottom: 24px;">
          <p style="color: #333; font-weight: bold; margin: 0 0 12px 0;">
            Trạng thái tâm lý hiện tại:
          </p>
          <div style="display: flex; flex-direction: column; gap: 8px;">
            <button class="psychology-option" data-state="balanced" style="
              background: #e8f5e8;
              color: #2e7d32;
              border: 2px solid #e8f5e8;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😌 Cân bằng và tỉnh táo</button>
            <button class="psychology-option" data-state="confident" style="
              background: #e3f2fd;
              color: #1976d2;
              border: 2px solid #e3f2fd;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😊 Tự tin và kiên nhẫn</button>
            <button class="psychology-option" data-state="not_ready" style="
              background: #fff3e0;
              color: #f57c00;
              border: 2px solid #fff3e0;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😐 Chưa sẵn sàng, cần thêm thời gian</button>
          </div>
        </div>

        <div style="display: flex; gap: 12px; justify-content: center;">
          <button id="continue-trading-btn" style="
            background: #4caf50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            opacity: 0.5;
          " disabled>✅ Tiếp tục giao dịch</button>
          <button id="more-meditation-btn" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🧘‍♂️ Thiền thêm</button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    let selectedState = '';

    // Add psychology option listeners
    modal.querySelectorAll('.psychology-option').forEach(button => {
      button.addEventListener('click', (e) => {
        const target = e.target as HTMLElement;
        selectedState = target.getAttribute('data-state') || '';
        
        // Reset all buttons
        modal.querySelectorAll('.psychology-option').forEach(btn => {
          (btn as HTMLElement).style.borderColor = (btn as HTMLElement).style.backgroundColor;
        });
        
        // Highlight selected
        target.style.borderColor = '#1976d2';
        
        // Enable/disable continue button
        const continueBtn = document.getElementById('continue-trading-btn') as HTMLButtonElement;
        if (selectedState === 'balanced' || selectedState === 'confident') {
          continueBtn.disabled = false;
          continueBtn.style.opacity = '1';
        } else {
          continueBtn.disabled = true;
          continueBtn.style.opacity = '0.5';
        }
      });
    });

    // Add action listeners
    document.getElementById('continue-trading-btn')?.addEventListener('click', () => {
      if (selectedState === 'balanced' || selectedState === 'confident') {
        this.confirmPsychologyAndContinue(selectedState);
      }
    });

    document.getElementById('more-meditation-btn')?.addEventListener('click', () => {
      this.openMeditationOptions();
    });
  }

  private async confirmPsychologyAndContinue(state: string) {
    try {
      // Save psychology confirmation
      await chrome.storage.local.set({
        needsPsychologyConfirmation: false,
        lastConfirmationTime: Date.now(),
        confirmedPsychologyState: state
      });

      // Remove modal
      const modal = document.getElementById('psychology-confirmation-modal');
      if (modal) {
        modal.remove();
      }

      // Show success message
      this.showSuccessMessage('✅ Tâm lý đã được xác nhận! Bạn có thể giao dịch an toàn.');

    } catch (error) {
      console.error('Error confirming psychology:', error);
    }
  }

  private openMeditationOptions() {
    try {
      // Send message to background to open options page
      chrome.runtime.sendMessage({ 
        action: 'openOptions', 
        tab: 'meditation' 
      });
    } catch (error) {
      console.error('Error opening meditation options:', error);
      // Fallback: try to open directly
      window.open(chrome.runtime.getURL('options.html#meditation'), '_blank');
    }
  }

  private showSuccessMessage(message: string) {
    const toast = document.createElement('div');
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4caf50;
      color: white;
      padding: 16px 24px;
      border-radius: 8px;
      z-index: 999999;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      animation: slideIn 0.3s ease-out;
    `;

    toast.textContent = message;

    // Add animation
    const style = document.createElement('style');
    style.textContent = `
      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    `;
    document.head.appendChild(style);

    document.body.appendChild(toast);

    // Auto remove after 3 seconds
    setTimeout(() => {
      toast.remove();
      style.remove();
    }, 3000);
  }
}

// Initialize when page loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => new BinomoTradingAssistant());
} else {
  new BinomoTradingAssistant();
}
