chrome.runtime.onInstalled.addListener(()=>{console.log("Extension installed")});chrome.runtime.onMessage.addListener((e,s,o)=>{if(console.log("Background received message:",e),e.action==="openOptions")return chrome.tabs.create({url:chrome.runtime.getURL("options.html")}).then(r=>{e.tab?chrome.storage.local.set({requestedTab:e.tab}).then(()=>{console.log("Requested tab stored:",e.tab),o({success:!0})}).catch(t=>{console.error("Error storing requested tab:",t),o({success:!1,error:t.message})}):o({success:!0})}).catch(r=>{console.error("Error opening options page:",r),o({success:!1,error:r.message})}),!0;o({success:!1,error:"Unknown action"})});
