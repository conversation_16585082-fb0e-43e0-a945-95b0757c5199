{"dailyGoals": [{"id": "2024-01-15", "date": "2024-01-15", "tradingGoal": "<PERSON>h<PERSON><PERSON> hành phương pháp quá mua/quá bán với kỷ luật", "profitTarget": 100, "lossLimit": 50, "lessons": ["<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> giao dịch khi tâm lý tham lam", "<PERSON><PERSON><PERSON> tuân thủ nghiêm ngặt kế hoạch quản lý vốn", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> revenge trading khi thua liên tiếp"], "completed": true, "createdAt": "2024-01-15T08:00:00.000Z", "updatedAt": "2024-01-15T08:00:00.000Z"}, {"id": "2025-06-22", "date": "2025-06-22", "profitTarget": 50, "lossLimit": 50, "maxTrades": 2, "tradingGoal": "<PERSON><PERSON><PERSON><PERSON> hành ph<PERSON><PERSON><PERSON> pháp quá mua/ quá bán", "lessons": [], "createdAt": "2025-06-22T07:38:24.150Z"}, {"id": "2025-06-23-1750651835097", "date": "2025-06-23", "spiritualPurpose": "<PERSON><PERSON><PERSON> giao dịch để rèn luyện tâm, g<PERSON><PERSON><PERSON> bớt tham lam, rèn luyệ<PERSON> chán<PERSON> ni<PERSON>, gi<PERSON><PERSON> tham sân si sợ, biết đủ, đủ ăn đủ uống đủ mặc, biết vô thường, ....", "profitTarget": 50, "lossLimit": 20, "maxTrades": 20, "tradingGoal": "<PERSON>h<PERSON><PERSON> hàng ph<PERSON><PERSON><PERSON> pháp lực đẩy thị trường", "lessons": [], "createdAt": "2025-06-23T04:10:35.097Z"}, {"id": "2025-06-27", "date": "2025-06-27", "tradingGoal": "8 n<PERSON>m rồ<PERSON>, tô<PERSON> mệt mỏi với sự thoải mái, ném tiền ra cửa sổ. Tôi cần phải nghiêm túc, kỷ luật với nguyên tắc của phương pháp", "profitTarget": 500, "lossLimit": 2000, "maxTrades": 2, "completed": false, "createdAt": "2025-06-27T15:13:24.190Z", "updatedAt": "2025-06-27T15:13:24.190Z"}], "psychologyStates": [{"id": "1", "timestamp": 1705305600000, "state": "balanced", "description": "<PERSON><PERSON><PERSON> cảm thấy bình tĩnh và sẵn sàng phân tích khách quan", "canTrade": true, "notes": "Đ<PERSON> nghỉ ng<PERSON>i đầy đủ, tâm trạng <PERSON>n định", "createdAt": "2024-01-15T08:00:00.000Z"}, {"id": "1750577301210", "timestamp": "2025-06-22T07:28:21.210Z", "state": "greedy", "description": "Hôm nay tôi cảm thấy khá tự tin sau một chuỗi lệnh thắng", "financialSituation": "stable", "recentPerformance": "winning_streak", "recommendation": {"safe": false, "riskLevel": "high", "message": "Cảnh báo: <PERSON><PERSON> lam có thể dẫn đến quyết định sai lầm.", "advice": "<PERSON><PERSON><PERSON> bình tĩnh, gi<PERSON><PERSON> size lệnh và tập trung vào chất lượng thay vì số lượng.", "warnings": ["Chuỗi thắng có thể gây ra quá tự tin."], "riskScore": 1.3, "shouldTrade": false, "maxRiskPerTrade": "0%"}}, {"id": "1750577808796", "timestamp": "2025-06-22T07:36:48.796Z", "state": "greedy", "description": "Tôi đang khá thoải mái khi vào l<PERSON>nh, tôi tự tin <PERSON>.", "financialSituation": "stable", "recentPerformance": "winning_streak", "recommendation": {"safe": false, "riskLevel": "high", "message": "Cảnh báo: <PERSON><PERSON> lam có thể dẫn đến quyết định sai lầm.", "advice": "<PERSON><PERSON><PERSON> bình tĩnh, gi<PERSON><PERSON> size lệnh và tập trung vào chất lượng thay vì số lượng.", "warnings": ["Chuỗi thắng có thể gây ra quá tự tin."], "riskScore": 1.3, "shouldTrade": false, "maxRiskPerTrade": "0%"}}, {"id": "1750577893618", "timestamp": "2025-06-22T07:38:13.618Z", "state": "fearful", "description": "T<PERSON>i đang sợ hãi khi vào lệnh", "financialSituation": "high_pressure", "recentPerformance": "losing_streak", "recommendation": {"safe": false, "riskLevel": "extreme", "message": "<PERSON>ợ hãi có thể khiến bạn bỏ lỡ cơ hội hoặc cắt lỗ quá sớm.", "advice": "Hãy xem xét nghỉ ngơi hoặc giao dịch với size nhỏ để lấy lại tự tin.", "warnings": ["<PERSON><PERSON> l<PERSON>c tài ch<PERSON>h cao - rất nguy hiểm cho giao dịch!", "Chuỗi thua có thể gây ra tâm lý revenge."], "riskScore": 2.1, "shouldTrade": false, "maxRiskPerTrade": "0%"}}, {"id": "1750577947913", "timestamp": "2025-06-22T07:39:07.913Z", "state": "balanced", "description": "<PERSON><PERSON><PERSON> đang quản lý cảm xúc tốt", "financialSituation": "stable", "recentPerformance": "winning_streak", "recommendation": {"safe": false, "riskLevel": "medium", "message": "Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.", "advice": "<PERSON><PERSON><PERSON> duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.", "warnings": ["Chuỗi thắng có thể gây ra quá tự tin."], "riskScore": 1.3, "shouldTrade": false, "maxRiskPerTrade": "0%"}}, {"id": "1750578010024", "timestamp": "2025-06-22T07:40:10.024Z", "state": "balanced", "description": "T<PERSON>i đã sẵn sàng để giao dịch", "financialSituation": "stable", "recentPerformance": "mixed_results", "recommendation": {"safe": true, "riskLevel": "low", "message": "Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.", "advice": "<PERSON><PERSON><PERSON> duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.", "warnings": [], "riskScore": 1, "shouldTrade": true, "maxRiskPerTrade": "2%"}}, {"id": "1750578811202", "timestamp": "2025-06-22T07:53:31.202Z", "state": "balanced", "description": "<PERSON><PERSON><PERSON> đang khá bình tĩnh", "financialSituation": "stable", "recentPerformance": "mixed_results", "recommendation": {"safe": true, "riskLevel": "low", "message": "Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.", "advice": "<PERSON><PERSON><PERSON> duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.", "warnings": [], "riskScore": 1, "shouldTrade": true, "maxRiskPerTrade": "2%"}}, {"id": "1750579400078", "timestamp": "2025-06-22T08:03:20.078Z", "state": "balanced", "description": "<PERSON><PERSON><PERSON> đang khá bình tĩnh", "financialSituation": "stable", "recentPerformance": "mixed_results", "recommendation": {"safe": true, "riskLevel": "low", "message": "Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.", "advice": "<PERSON><PERSON><PERSON> duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.", "warnings": [], "riskScore": 1, "shouldTrade": true, "maxRiskPerTrade": "2%"}}, {"id": "1750579469046", "timestamp": "2025-06-22T08:04:29.046Z", "state": "balanced", "description": "<PERSON><PERSON><PERSON> đang bình tĩnh rất rất là bình tĩnh", "financialSituation": "stable", "recentPerformance": "mixed_results", "recommendation": {"safe": true, "riskLevel": "low", "message": "Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.", "advice": "<PERSON><PERSON><PERSON> duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.", "warnings": [], "riskScore": 1, "shouldTrade": true, "maxRiskPerTrade": "2%"}}, {"id": "1750579998949", "timestamp": "2025-06-22T08:13:18.949Z", "state": "balanced", "description": "<PERSON><PERSON><PERSON> đang rất rất rất bình tĩnh", "financialSituation": "stable", "recentPerformance": "mixed_results", "recommendation": {"safe": true, "riskLevel": "low", "message": "Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.", "advice": "<PERSON><PERSON><PERSON> duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.", "warnings": [], "riskScore": 1, "shouldTrade": true, "maxRiskPerTrade": "2%"}}, {"id": "1750580711425", "timestamp": "2025-06-22T08:25:11.425Z", "state": "balanced", "description": "<PERSON>ôm nay tôi buồn một mình trên phố đông", "financialSituation": "stable", "recentPerformance": "mixed_results", "recommendation": {"safe": true, "riskLevel": "low", "message": "Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.", "advice": "<PERSON><PERSON><PERSON> duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.", "warnings": [], "riskScore": 1, "shouldTrade": true, "maxRiskPerTrade": "2%"}}, {"id": "1750582163714", "timestamp": "2025-06-22T08:49:23.714Z", "state": "balanced", "description": "<PERSON>ôm nay tôi khá bình tĩnh để giao dịch", "financialSituation": "stable", "recentPerformance": "mixed_results", "recommendation": {"safe": true, "riskLevel": "low", "message": "Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.", "advice": "<PERSON><PERSON><PERSON> duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.", "warnings": [], "riskScore": 1, "shouldTrade": true, "maxRiskPerTrade": "2%"}}, {"id": "1750582692667", "timestamp": "2025-06-22T08:58:12.667Z", "state": "balanced", "description": "<PERSON>nay tôi khá là ổn định trong giao dịch", "financialSituation": "stable", "recentPerformance": "mixed_results", "recommendation": {"safe": true, "riskLevel": "low", "message": "Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.", "advice": "<PERSON><PERSON><PERSON> duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.", "warnings": [], "riskScore": 1, "shouldTrade": true, "maxRiskPerTrade": "2%"}}, {"id": "1750583830587", "timestamp": "2025-06-22T09:17:10.587Z", "state": "balanced", "description": "<PERSON>nay tôi đang rất là bức xúc", "financialSituation": "stable", "recentPerformance": "mixed_results", "recommendation": {"safe": true, "riskLevel": "low", "message": "Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.", "advice": "<PERSON><PERSON><PERSON> duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.", "warnings": [], "riskScore": 1, "shouldTrade": true, "maxRiskPerTrade": "2%"}}, {"id": "1750583907467", "timestamp": "2025-06-22T09:18:27.467Z", "state": "balanced", "description": "<PERSON><PERSON><PERSON> nay tôi đang rất là bứ<PERSON> xúc, ban<PERSON> cúc", "financialSituation": "stable", "recentPerformance": "mixed_results", "recommendation": {"safe": true, "riskLevel": "low", "message": "Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.", "advice": "<PERSON><PERSON><PERSON> duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.", "warnings": [], "riskScore": 1, "shouldTrade": true, "maxRiskPerTrade": "2%"}}, {"id": "1750586051343", "timestamp": "2025-06-22T09:54:11.343Z", "state": "balanced", "description": "<PERSON><PERSON> tả chi tiết cảm xúc và suy nghĩ:", "financialSituation": "stable", "recentPerformance": "mixed_results", "recommendation": {"safe": true, "riskLevel": "low", "message": "Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.", "advice": "<PERSON><PERSON><PERSON> duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.", "warnings": [], "riskScore": 1, "shouldTrade": true, "maxRiskPerTrade": "2%"}}, {"id": "1750593096466", "timestamp": "2025-06-22T11:51:36.466Z", "state": "greedy", "description": "<PERSON>ôm nay tôi tham lam", "financialSituation": "slight_pressure", "recentPerformance": "winning_streak", "recommendation": {"safe": false, "riskLevel": "extreme", "message": "Cảnh báo: <PERSON><PERSON> lam có thể dẫn đến quyết định sai lầm.", "advice": "<PERSON><PERSON><PERSON> bình tĩnh, gi<PERSON><PERSON> size lệnh và tập trung vào chất lượng thay vì số lượng.", "warnings": ["<PERSON><PERSON> lự<PERSON> tài ch<PERSON>h nhẹ có thể ảnh hưởng đến quyết định.", "Chuỗi thắng có thể gây ra quá tự tin."], "riskScore": 1.56, "shouldTrade": false, "maxRiskPerTrade": "0%"}}, {"id": "1750593187144", "timestamp": "2025-06-22T11:53:07.144Z", "state": "balanced", "description": "<PERSON><PERSON><PERSON> tho<PERSON>i mái lam luôn r<PERSON>, cho tôi chiến đi", "financialSituation": "stable", "recentPerformance": "mixed_results", "recommendation": {"safe": true, "riskLevel": "low", "message": "Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.", "advice": "<PERSON><PERSON><PERSON> duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.", "warnings": [], "riskScore": 1, "shouldTrade": true, "maxRiskPerTrade": "2%"}}, {"id": "1750597380263", "timestamp": "2025-06-22T13:03:00.263Z", "state": "greedy", "description": "h<PERSON> tôi đang rất tham lam", "financialSituation": "slight_pressure", "recentPerformance": "winning_streak", "recommendation": {"safe": false, "riskLevel": "extreme", "message": "Cảnh báo: <PERSON><PERSON> lam có thể dẫn đến quyết định sai lầm.", "advice": "<PERSON><PERSON><PERSON> bình tĩnh, gi<PERSON><PERSON> size lệnh và tập trung vào chất lượng thay vì số lượng.", "warnings": ["<PERSON><PERSON> lự<PERSON> tài ch<PERSON>h nhẹ có thể ảnh hưởng đến quyết định.", "Chuỗi thắng có thể gây ra quá tự tin."], "riskScore": 1.56, "shouldTrade": false, "maxRiskPerTrade": "0%"}}, {"id": "1750598135580", "timestamp": "2025-06-22T13:15:35.580Z", "state": "balanced", "description": "<PERSON><PERSON><PERSON> đang rất là bình thường nha", "financialSituation": "stable", "recentPerformance": "mixed_results", "recommendation": {"safe": true, "riskLevel": "low", "message": "Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.", "advice": "<PERSON><PERSON><PERSON> duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.", "warnings": [], "riskScore": 1, "shouldTrade": true, "maxRiskPerTrade": "2%"}}, {"id": "1750598176154", "timestamp": "2025-06-22T13:16:16.154Z", "state": "balanced", "description": "<PERSON><PERSON><PERSON> đang rất là bình thường", "financialSituation": "stable", "recentPerformance": "mixed_results", "recommendation": {"safe": true, "riskLevel": "low", "message": "Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.", "advice": "<PERSON><PERSON><PERSON> duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.", "warnings": [], "riskScore": 1, "shouldTrade": true, "maxRiskPerTrade": "2%"}}, {"id": "1750598749048", "timestamp": "2025-06-22T13:25:49.048Z", "state": "balanced", "description": "T<PERSON><PERSON> khá là bình tĩnh rồi", "financialSituation": "stable", "recentPerformance": "mixed_results", "recommendation": {"safe": true, "riskLevel": "low", "message": "Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.", "advice": "<PERSON><PERSON><PERSON> duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.", "warnings": [], "riskScore": 1, "shouldTrade": true, "maxRiskPerTrade": "2%"}}, {"id": "1750600446462", "timestamp": "2025-06-22T13:54:06.462Z", "state": "balanced", "description": "<PERSON><PERSON><PERSON> kha la thoai mai roi, binh tinh", "financialSituation": "stable", "recentPerformance": "mixed_results", "recommendation": {"safe": true, "riskLevel": "low", "message": "Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.", "advice": "<PERSON><PERSON><PERSON> duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.", "warnings": [], "riskScore": 1, "shouldTrade": true, "maxRiskPerTrade": "2%"}}, {"id": "1750600481310", "timestamp": "2025-06-22T13:54:41.310Z", "state": "balanced", "description": "<PERSON><PERSON><PERSON> kha la thoai mai roi, binh tinh", "financialSituation": "stable", "recentPerformance": "mixed_results", "recommendation": {"safe": true, "riskLevel": "low", "message": "Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.", "advice": "<PERSON><PERSON><PERSON> duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.", "warnings": [], "riskScore": 1, "shouldTrade": true, "maxRiskPerTrade": "2%"}}, {"id": "1750600507589", "timestamp": "2025-06-22T13:55:07.589Z", "state": "balanced", "description": "<PERSON><PERSON><PERSON> kha la thoai mai roi, binh tinh", "financialSituation": "stable", "recentPerformance": "mixed_results", "recommendation": {"safe": true, "riskLevel": "low", "message": "Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.", "advice": "<PERSON><PERSON><PERSON> duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.", "warnings": [], "riskScore": 1, "shouldTrade": true, "maxRiskPerTrade": "2%"}}, {"id": "1750600958733", "timestamp": "2025-06-22T14:02:38.733Z", "state": "balanced", "description": "Trading Assistant Trading Assistant Trading Assistant", "financialSituation": "stable", "recentPerformance": "mixed_results", "recommendation": {"safe": true, "riskLevel": "low", "message": "Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.", "advice": "<PERSON><PERSON><PERSON> duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.", "warnings": [], "riskScore": 1, "shouldTrade": true, "maxRiskPerTrade": "2%"}}, {"id": "1750651858424", "timestamp": "2025-06-23T04:10:58.424Z", "state": "euphoric", "description": "<PERSON><PERSON><PERSON> đang hưng phân, th<PERSON> ph<PERSON>i làm sao", "financialSituation": "stable", "recentPerformance": "mixed_results", "recommendation": {"safe": false, "riskLevel": "extreme", "message": "Hưng phấn quá mức có thể dẫn đến mất kiểm soát.", "advice": "<PERSON><PERSON><PERSON> bình tĩnh lại, nghỉ ngơi và không tăng size lệnh.", "warnings": [], "riskScore": 1, "shouldTrade": false, "maxRiskPerTrade": "0%"}}, {"id": "1750653540044", "timestamp": "2025-06-23T04:39:00.044Z", "state": "balanced", "description": "<PERSON>ôm nay tôi đã bình tĩnh lãi rồi", "financialSituation": "stable", "recentPerformance": "mixed_results", "recommendation": {"safe": true, "riskLevel": "low", "message": "Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.", "advice": "<PERSON><PERSON><PERSON> duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch.", "warnings": [], "riskScore": 1, "shouldTrade": true, "maxRiskPerTrade": "2%"}}], "tradingMethods": [{"id": "overbought_oversold", "name": "Quá mua / Quá bán", "description": "Phương pháp giao dịch dựa trên việc xác định vùng quá mua và quá bán", "theory": "<PERSON><PERSON> thuyết Quá mua / <PERSON>u<PERSON> bán:\n- <PERSON><PERSON><PERSON> mua: Gi<PERSON> đã tăng quá mức so với giá trị thực, có khả năng điều chỉnh giảm\n- <PERSON><PERSON><PERSON> bán: Giá đã giảm quá mức so với giá trị thực, c<PERSON> khả năng phục hồi tăng\n- <PERSON><PERSON> dụng các chỉ báo như RSI, Stochastic, Bollinger Bands để xác định\n- C<PERSON>n kết hợp với phân tích kỹ thuật khác để xác nhận tín hiệu", "questions": [{"id": "previous_momentum", "question": "Trước đó đã có lực đẩy không? Và nó đang như thế nào?", "type": "text", "required": true, "weight": 2}, {"id": "price_divergence", "question": "Giá có phân kỳ không?", "type": "yes_no", "required": true, "weight": 3}, {"id": "bb_breakout", "question": "Giá đã thoát ra ngoài vùng BB chưa?", "type": "yes_no", "required": true, "weight": 2}, {"id": "overbought_oversold", "question": "Giá đã quá mua/ quá bán không?", "type": "select", "options": ["<PERSON><PERSON><PERSON> mua", "<PERSON><PERSON><PERSON> b<PERSON>", "<PERSON><PERSON>ng trung tính"], "required": true, "weight": 3}, {"id": "price_acceptance", "question": "G<PERSON>á đã chấp nhận dừng chưa?", "type": "yes_no", "required": true, "weight": 2}, {"id": "snr_resistance", "question": "Có cản SnR phía trước không?", "type": "text", "required": true, "weight": 2}, {"id": "opposite_pressure", "question": "<PERSON><PERSON> <PERSON><PERSON> lực bên phe đối diện không?", "type": "text", "required": true, "weight": 2}, {"id": "notes", "question": "<PERSON><PERSON> chú thêm", "type": "text", "required": false, "weight": 0}], "createdAt": "2024-01-15T08:00:00.000Z", "updatedAt": "2024-01-15T08:00:00.000Z"}], "tradingSessions": [{"id": "session_1705305600000_abc123", "date": "2024-01-15", "timestamp": 1705305600000, "methodId": "overbought_oversold", "psychologyStateId": "1", "answers": {"previous_momentum": "<PERSON><PERSON> l<PERSON>c đ<PERSON>y tăng mạnh trong 30 phút qua", "price_divergence": "yes", "bb_breakout": "yes", "overbought_oversold": "<PERSON><PERSON><PERSON> mua", "price_acceptance": "yes", "snr_resistance": "<PERSON><PERSON> cản mạnh tại vùng 1.2000", "opposite_pressure": "<PERSON><PERSON> b<PERSON>", "notes": "<PERSON><PERSON> hiệu rất rõ ràng"}, "status": "completed", "createdAt": "2024-01-15T08:00:00.000Z", "updatedAt": "2024-01-15T10:00:00.000Z"}, {"id": "1750578875048", "timestamp": "2025-06-22T07:54:35.048Z", "method": "bollinger_bands", "methodName": "Bollinger Bands Breakout", "answers": [{"question": "G<PERSON><PERSON> có breakout rõ ràng khỏi Upper hoặc Lower Bollinger Band không?", "answer": "strong_breakout", "score": 25}, {"question": "Volume tại thời điểm breakout như thế nào?", "answer": "high_volume", "score": 20}, {"question": "Bollinger Bands đang trong trạng thái nào?", "answer": "expanding", "score": 20}, {"question": "RSI hiện tại đang ở vùng nào?", "answer": "overbought", "score": 15}, {"question": "<PERSON><PERSON> tả setup hiện tại và lý do vào lệnh:", "answer": "Giá breakout và có tín hiệu lực yếu", "score": 10}], "totalScore": 90, "maxScore": 90, "percentage": 100, "recommendation": {"shouldTrade": true, "riskLevel": "low", "confidence": "high", "message": "Setup xuất sắc! Tất cả điều kiện đều thuận lợi.", "advice": "Đ<PERSON>y là cơ hội tốt để vào lệnh. Hãy tuân thủ risk management và theo dõi chặt chẽ.", "maxRisk": "2%"}, "description": "Giá breakout và có tín hiệu lực yếu"}, {"id": "1750578999169", "timestamp": "2025-06-22T07:56:39.169Z", "method": "price_action", "methodName": "Price Action Patterns", "answers": [{"question": "Có xuất hiện pattern nến Nhật nào không?", "answer": "strong_reversal", "score": 25}, {"question": "<PERSON>ấu trúc thị trường hiện tại?", "answer": "higher_highs_lows", "score": 20}, {"question": "Có breakout khỏi cấu trúc quan trọng không?", "answer": "strong_breakout", "score": 20}, {"question": "Momentum hiện tại như thế nào?", "answer": "strong_momentum", "score": 15}, {"question": "Mô tả pattern và setup price action:", "answer": "<PERSON><PERSON><PERSON>", "score": 0}], "totalScore": 80, "maxScore": 90, "percentage": 89, "recommendation": {"shouldTrade": true, "riskLevel": "low", "confidence": "high", "message": "Setup xuất sắc! Tất cả điều kiện đều thuận lợi.", "advice": "Đ<PERSON>y là cơ hội tốt để vào lệnh. Hãy tuân thủ risk management và theo dõi chặt chẽ.", "maxRisk": "2%"}, "description": "<PERSON><PERSON><PERSON>"}, {"id": "1750579432423", "timestamp": "2025-06-22T08:03:52.423Z", "method": "price_action", "methodName": "Price Action Patterns", "answers": [{"question": "Có xuất hiện pattern nến Nhật nào không?", "answer": "strong_reversal", "score": 25}, {"question": "<PERSON>ấu trúc thị trường hiện tại?", "answer": "higher_highs_lows", "score": 20}, {"question": "Có breakout khỏi cấu trúc quan trọng không?", "answer": "strong_breakout", "score": 20}, {"question": "Momentum hiện tại như thế nào?", "answer": "strong_momentum", "score": 15}, {"question": "Mô tả pattern và setup price action:", "answer": "<PERSON><PERSON><PERSON> đang mạnh", "score": 0}], "totalScore": 80, "maxScore": 90, "percentage": 89, "recommendation": {"shouldTrade": true, "riskLevel": "low", "confidence": "high", "message": "Setup xuất sắc! Tất cả điều kiện đều thuận lợi.", "advice": "Đ<PERSON>y là cơ hội tốt để vào lệnh. Hãy tuân thủ risk management và theo dõi chặt chẽ.", "maxRisk": "2%"}, "description": "<PERSON><PERSON><PERSON> đang mạnh"}, {"id": "1750579535741", "timestamp": "2025-06-22T08:05:35.741Z", "method": "price_action", "methodName": "Price Action Patterns", "answers": [{"question": "Có xuất hiện pattern nến Nhật nào không?", "answer": "strong_reversal", "score": 25}, {"question": "<PERSON>ấu trúc thị trường hiện tại?", "answer": "higher_highs_lows", "score": 20}, {"question": "Có breakout khỏi cấu trúc quan trọng không?", "answer": "strong_breakout", "score": 20}, {"question": "Momentum hiện tại như thế nào?", "answer": "strong_momentum", "score": 15}, {"question": "Mô tả pattern và setup price action:", "answer": "<PERSON><PERSON><PERSON>", "score": 0}], "totalScore": 80, "maxScore": 90, "percentage": 89, "recommendation": {"shouldTrade": true, "riskLevel": "low", "confidence": "high", "message": "Setup xuất sắc! Tất cả điều kiện đều thuận lợi.", "advice": "Đ<PERSON>y là cơ hội tốt để vào lệnh. Hãy tuân thủ risk management và theo dõi chặt chẽ.", "maxRisk": "2%"}, "description": "<PERSON><PERSON><PERSON>"}, {"id": "1750580025089", "timestamp": "2025-06-22T08:13:45.089Z", "method": "price_action", "methodName": "Price Action Patterns", "answers": [{"question": "Có xuất hiện pattern nến Nhật nào không?", "answer": "strong_reversal", "score": 25}, {"question": "<PERSON>ấu trúc thị trường hiện tại?", "answer": "higher_highs_lows", "score": 20}, {"question": "Có breakout khỏi cấu trúc quan trọng không?", "answer": "strong_breakout", "score": 20}, {"question": "Momentum hiện tại như thế nào?", "answer": "strong_momentum", "score": 15}, {"question": "Mô tả pattern và setup price action:", "answer": "<PERSON><PERSON><PERSON>", "score": 0}], "totalScore": 80, "maxScore": 90, "percentage": 89, "recommendation": {"shouldTrade": true, "riskLevel": "low", "confidence": "high", "message": "Setup xuất sắc! Tất cả điều kiện đều thuận lợi.", "advice": "Đ<PERSON>y là cơ hội tốt để vào lệnh. Hãy tuân thủ risk management và theo dõi chặt chẽ.", "maxRisk": "2%"}, "description": "<PERSON><PERSON><PERSON>"}, {"id": "1750580827744", "timestamp": "2025-06-22T08:27:07.744Z", "method": "price_action", "methodName": "Price Action Patterns", "answers": [{"question": "Có xuất hiện pattern nến Nhật nào không?", "answer": "strong_reversal", "score": 25}, {"question": "<PERSON>ấu trúc thị trường hiện tại?", "answer": "higher_highs_lows", "score": 20}, {"question": "Có breakout khỏi cấu trúc quan trọng không?", "answer": "strong_breakout", "score": 20}, {"question": "Momentum hiện tại như thế nào?", "answer": "strong_momentum", "score": 15}, {"question": "Mô tả pattern và setup price action:", "answer": "<PERSON><PERSON><PERSON> đ<PERSON>y thị trường đang mạnh", "score": 10}], "totalScore": 90, "maxScore": 90, "percentage": 100, "recommendation": {"shouldTrade": true, "riskLevel": "low", "confidence": "high", "message": "Setup xuất sắc! Tất cả điều kiện đều thuận lợi.", "advice": "Đ<PERSON>y là cơ hội tốt để vào lệnh. Hãy tuân thủ risk management và theo dõi chặt chẽ.", "maxRisk": "2%"}, "description": "<PERSON><PERSON><PERSON> đ<PERSON>y thị trường đang mạnh"}, {"id": "1750586071452", "timestamp": "2025-06-22T09:54:31.452Z", "method": "custom_1750586023132", "methodName": "Phương ph<PERSON>p giao d<PERSON> 1", "answers": [{"question": "Câu hỏi 1", "answer": "option_0", "score": 20}, {"question": "Câu hỏi phân tích 2", "answer": "option_0", "score": 20}, {"question": "Câu hỏi phân tích 2", "answer": "option_0", "score": 20}], "totalScore": 60, "maxScore": 60, "percentage": 100, "recommendation": {"shouldTrade": true, "riskLevel": "low", "confidence": "high", "message": "Setup xuất sắc! Tất cả điều kiện đều thuận lợi.", "advice": "Đ<PERSON>y là cơ hội tốt để vào lệnh. Hãy tuân thủ risk management và theo dõi chặt chẽ.", "maxRisk": "2%"}, "description": ""}], "trades": [{"id": "trade_1705305600000_def456", "sessionId": "session_1705305600000_abc123", "timestamp": 1705305600000, "amount": 10, "duration": 5, "direction": "down", "asset": "EUR/USD", "entryPrice": 1.205, "exitPrice": 1.203, "result": "win", "profit": 8.5, "notes": "<PERSON><PERSON><PERSON> thực hiện đúng kế hoạch", "createdAt": "2024-01-15T08:00:00.000Z", "updatedAt": "2024-01-15T08:05:00.000Z"}], "statistics": [{"id": "daily_2024-01-15", "type": "daily", "date": "2024-01-15", "totalTrades": 5, "winTrades": 3, "lossTrades": 2, "winRate": 60, "totalProfit": 25.5, "totalLoss": 20, "netProfit": 5.5, "methodsUsed": ["overbought_oversold"], "createdAt": "2024-01-15T08:00:00.000Z", "updatedAt": "2024-01-15T18:00:00.000Z"}], "customMethods": [{"name": "Phương ph<PERSON>p giao d<PERSON> 1", "icon": "📈", "description": "Phương ph<PERSON>p giao d<PERSON> 1", "questions": [{"text": "Câu hỏi 1", "type": "radio", "options": [{"value": "option_0", "label": "Nội dung đáp án 1", "weight": 20}, {"value": "option_1", "label": "Nội dung đáp <PERSON> 2", "weight": 10}]}, {"text": "Câu hỏi phân tích 2", "type": "radio", "options": [{"value": "option_0", "label": "Nội dung đáp án 1", "weight": 20}, {"value": "option_1", "label": "Nội dung đáp <PERSON> 2", "weight": 10}]}, {"text": "Câu hỏi phân tích 2", "type": "radio", "options": [{"value": "option_0", "label": "Nội dung đáp án 1", "weight": 20}, {"value": "option_1", "label": "Nội dung đáp <PERSON> 2", "weight": 10}]}], "totalMaxScore": 60, "isCustom": true, "id": "custom_1750586023132", "createdAt": "2025-06-22T09:53:43.132Z"}], "settings": [{"id": "user_settings", "theme": "light", "language": "vi", "notifications": true, "autoBackup": true, "riskWarnings": true, "defaultTradeAmount": 10, "defaultTradeDuration": 5, "createdAt": "2024-01-15T08:00:00.000Z", "updatedAt": "2024-01-15T08:00:00.000Z"}]}