// Binomo Trading Assistant - Options Page (Material UI)

import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  ButtonGroup,
  Typography,
  Alert,
  Grid,
  Card,
  CardContent,
  CardActions,
  Switch,
  FormControlLabel,
  TextField,
  Divider,
  ThemeProvider,
  createTheme,
  CssBaseline,
  Paper,
  Stack,
  Tabs,
  Tab,
  Slider,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  CircularProgress,
  Tooltip
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Palette as ThemeIcon,
  Notifications as NotificationIcon,
  Security as SecurityIcon,
  Storage as StorageIcon,
  TrendingUp as TradingIcon,
  Save as SaveIcon,
  Restore as RestoreIcon,
  Download as ExportIcon,
  Upload as ImportIcon,
  Analytics as MethodsIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  SelfImprovement as MeditationIcon,
  Favorite as GratitudeIcon,
  Psychology as CompassionIcon,
  SmartToy as AIIcon
} from '@mui/icons-material';
import CustomMethodCreator from './CustomMethodCreator';

// Material UI Theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#007bff',
    },
    secondary: {
      main: '#6c757d',
    },
  },
});

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`options-tabpanel-${index}`}
      aria-labelledby={`options-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const Options = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [tradingMethods, setTradingMethods] = useState([]);
  const [customMethods, setCustomMethods] = useState([]);
  const [methodSettings, setMethodSettings] = useState({});
  const [showCreateMethod, setShowCreateMethod] = useState(false);
  const [editingMethod, setEditingMethod] = useState(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [methodToDelete, setMethodToDelete] = useState(null);
  const [showMeditation, setShowMeditation] = useState(false);
  const [meditationType, setMeditationType] = useState('mindfulness');
  const [settings, setSettings] = useState({
    // Theme settings
    theme: 'light',
    language: 'vi',
    fontSize: 14,
    
    // Notification settings
    notifications: true,
    soundAlerts: true,
    desktopNotifications: true,
    emailNotifications: false,
    
    // Trading settings
    defaultTradeAmount: 10,
    defaultTradeDuration: 5,
    maxDailyTrades: 50,
    riskWarnings: true,
    autoStopLoss: true,
    
    // Security settings
    requireConfirmation: true,
    sessionTimeout: 30,
    autoLock: false,
    
    // Storage settings
    autoBackup: true,
    backupFrequency: 'daily',
    maxBackups: 10,
    apiUrl: 'http://localhost:3001',

    // AI settings
    openaiApiKey: '',
    useAIPsychology: false
  });

  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadSettings();
    loadTradingMethods();

    // Check if we should open meditation tab
    checkRequestedTab();
  }, []);

  const checkRequestedTab = async () => {
    try {
      const result = await chrome.storage.local.get(['requestedTab']);
      if (result.requestedTab === 'meditation') {
        setCurrentTab(4); // Meditation tab index
        await chrome.storage.local.remove(['requestedTab']);
      } else if (result.requestedTab === 'ai') {
        setCurrentTab(5); // AI tab index
        await chrome.storage.local.remove(['requestedTab']);
      }
    } catch (error) {
      console.log('Could not check requested tab');
    }
  };

  // Built-in trading methods
  const getBuiltInMethods = () => [
    {
      id: 'bollinger_bands',
      name: 'Bollinger Bands Breakout',
      icon: '📈',
      description: 'Giao dịch dựa trên việc giá breakout khỏi dải Bollinger Bands với volume xác nhận',
      questions: 5
    },
    {
      id: 'rsi_divergence',
      name: 'RSI Divergence',
      icon: '📊',
      description: 'Tìm kiếm sự phân kỳ giữa giá và RSI để dự đoán sự đảo chiều xu hướng',
      questions: 5
    },
    {
      id: 'support_resistance',
      name: 'Support & Resistance',
      icon: '🔄',
      description: 'Giao dịch tại các vùng support/resistance quan trọng với xác nhận price action',
      questions: 5
    },
    {
      id: 'moving_average',
      name: 'Moving Average Crossover',
      icon: '📉',
      description: 'Sử dụng sự cắt nhau của các đường MA để xác định xu hướng và điểm vào lệnh',
      questions: 5
    },
    {
      id: 'price_action',
      name: 'Price Action Patterns',
      icon: '🕯️',
      description: 'Phân tích các mô hình nến Nhật và price action để dự đoán hướng di chuyển',
      questions: 5
    }
  ];

  // Load trading methods and settings
  const loadTradingMethods = async () => {
    try {
      // Load custom methods from JSON server
      const response = await fetch('http://localhost:3001/customMethods');
      if (response.ok) {
        const methods = await response.json();
        setCustomMethods(methods);
      }
    } catch (error) {
      console.log('No custom methods found or server not available');
    }

    // Load method settings from storage
    const result = await chrome.storage.local.get(['methodSettings']);
    if (result.methodSettings) {
      setMethodSettings(result.methodSettings);
    } else {
      // Default: all built-in methods enabled
      const defaultSettings = {};
      getBuiltInMethods().forEach(method => {
        defaultSettings[method.id] = true;
      });
      setMethodSettings(defaultSettings);
    }
  };

  // Check if method is enabled
  const getMethodEnabled = (methodId: string) => {
    return methodSettings[methodId] !== false; // Default to true if not set
  };

  // Toggle method enable/disable
  const toggleMethod = async (methodId: string, enabled: boolean) => {
    const newSettings = { ...methodSettings, [methodId]: enabled };
    setMethodSettings(newSettings);
    await chrome.storage.local.set({ methodSettings: newSettings });

    // Send message to content script to update available methods
    try {
      const tabs = await chrome.tabs.query({ url: '*://binomo1.com/trading*' });
      tabs.forEach(tab => {
        if (tab.id) {
          chrome.tabs.sendMessage(tab.id, {
            action: 'updateMethodSettings',
            methodSettings: newSettings
          }).catch(error => {
            console.log(`Could not update content script in tab ${tab.id}:`, error);
          });
        }
      });
    } catch (error) {
      console.log('Could not query tabs or update content script:', error);
    }
  };

  // Edit custom method
  const editCustomMethod = (method: any) => {
    console.log('Editing method:', method.name);
    setEditingMethod(method);
    setShowCreateMethod(true);
  };

  // Show delete confirmation
  const showDeleteConfirmation = (method: any) => {
    setMethodToDelete(method);
    setDeleteConfirmOpen(true);
  };

  // Delete custom method
  const deleteCustomMethod = async () => {
    if (!methodToDelete) return;

    const methodId = methodToDelete.id;

    setLoading(true);
    setDeleteConfirmOpen(false);

    try {
      const response = await fetch(`http://localhost:3001/customMethods/${methodId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        // Update local state
        setCustomMethods(prev => prev.filter(m => m.id !== methodId));

        // Remove from method settings
        const newSettings = { ...methodSettings };
        delete newSettings[methodId];
        setMethodSettings(newSettings);
        await chrome.storage.local.set({ methodSettings: newSettings });

        // Update content scripts
        try {
          const tabs = await chrome.tabs.query({ url: '*://binomo1.com/trading*' });
          tabs.forEach(tab => {
            if (tab.id) {
              chrome.tabs.sendMessage(tab.id, {
                action: 'updateMethodSettings',
                methodSettings: newSettings
              }).catch(error => {
                console.log(`Could not update content script in tab ${tab.id}:`, error);
              });
            }
          });
        } catch (error) {
          console.log('Could not query tabs or update content script:', error);
        }

        setSuccess(true);
        setTimeout(() => setSuccess(false), 3000);
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      console.error('Error deleting method:', error);
      setError('Không thể xóa phương pháp. Hãy kiểm tra JSON Server đang chạy!');
      setTimeout(() => setError(null), 5000);
    } finally {
      setLoading(false);
      setMethodToDelete(null);
    }
  };

  // Save custom method
  const saveCustomMethod = async (method: any) => {
    try {
      let response;
      if (method.id && editingMethod) {
        // Update existing method
        response = await fetch(`http://localhost:3001/customMethods/${method.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(method)
        });
      } else {
        // Create new method
        response = await fetch('http://localhost:3001/customMethods', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(method)
        });
      }

      if (response.ok) {
        // Reload custom methods
        await loadTradingMethods();

        // Enable the new method by default (only for new methods)
        if (!editingMethod) {
          const newSettings = { ...methodSettings, [method.id]: true };
          setMethodSettings(newSettings);
          await chrome.storage.local.set({ methodSettings: newSettings });
        }

        // Update content scripts
        try {
          const tabs = await chrome.tabs.query({ url: '*://binomo1.com/trading*' });
          tabs.forEach(tab => {
            if (tab.id) {
              chrome.tabs.sendMessage(tab.id, {
                action: 'updateMethodSettings',
                methodSettings: methodSettings
              }).catch(error => {
                console.log(`Could not update content script in tab ${tab.id}:`, error);
              });
            }
          });
        } catch (error) {
          console.log('Could not query tabs or update content script:', error);
        }

        setSuccess(true);
        setTimeout(() => setSuccess(false), 3000);
      } else {
        throw new Error('Failed to save method');
      }
    } catch (error) {
      setError('Không thể lưu phương pháp. Hãy kiểm tra JSON Server đang chạy!');
      setTimeout(() => setError(null), 5000);
    }
  };

  const loadSettings = async () => {
    try {
      const result = await chrome.storage.local.get(['userSettings', 'openaiApiKey']);
      if (result.userSettings) {
        setSettings(prev => ({ ...prev, ...result.userSettings }));
      }
      // Load OpenAI API key separately for security
      if (result.openaiApiKey) {
        setSettings(prev => ({ ...prev, openaiApiKey: result.openaiApiKey }));
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const saveSettings = async () => {
    setLoading(true);
    setError(null);

    try {
      // Save general settings
      const { openaiApiKey, ...otherSettings } = settings;
      await chrome.storage.local.set({ userSettings: otherSettings });

      // Save OpenAI API key separately for security
      if (openaiApiKey) {
        await chrome.storage.local.set({ openaiApiKey });
      } else {
        await chrome.storage.local.remove(['openaiApiKey']);
      }

      setSuccess(true);
      setTimeout(() => setSuccess(false), 3000);
    } catch (error) {
      setError('Có lỗi xảy ra khi lưu cài đặt');
      console.error('Error saving settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const resetSettings = async () => {
    const confirmReset = confirm('Bạn có chắc chắn muốn reset tất cả cài đặt về mặc định không?');
    if (!confirmReset) return;

    const defaultSettings = {
      theme: 'light',
      language: 'vi',
      fontSize: 14,
      notifications: true,
      soundAlerts: true,
      desktopNotifications: true,
      emailNotifications: false,
      defaultTradeAmount: 10,
      defaultTradeDuration: 5,
      maxDailyTrades: 50,
      riskWarnings: true,
      autoStopLoss: true,
      requireConfirmation: true,
      sessionTimeout: 30,
      autoLock: false,
      autoBackup: true,
      backupFrequency: 'daily',
      maxBackups: 10,
      apiUrl: 'http://localhost:3001'
    };

    setSettings(defaultSettings);
    await saveSettings();
  };

  const exportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `binomo-settings-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  };

  const importSettings = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const importedSettings = JSON.parse(e.target?.result as string);
            setSettings(prev => ({ ...prev, ...importedSettings }));
            alert('Cài đặt đã được import thành công!');
          } catch (error) {
            alert('File không hợp lệ!');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  const confirmPsychologyAndOpenBinomo = async () => {
    try {
      // Clear psychology confirmation flag and set confirmed state
      await chrome.storage.local.set({
        needsPsychologyConfirmation: false,
        lastConfirmationTime: Date.now(),
        confirmedPsychologyState: 'meditation_completed'
      });

      // Close meditation dialog
      setShowMeditation(false);

      // Open Binomo in new tab
      chrome.tabs.create({ url: 'https://binomo1.com/trading' });

      // Show success notification
      console.log('✅ Psychology confirmed after meditation - Opening Binomo');

    } catch (error) {
      console.error('Error confirming psychology:', error);
      // Fallback: open Binomo directly
      window.open('https://binomo1.com/trading', '_blank');
    }
  };

  const testOpenAIConnection = async () => {
    if (!settings.openaiApiKey) {
      setError('Vui lòng nhập OpenAI API key trước');
      setTimeout(() => setError(null), 3000);
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('https://api.openai.com/v1/models', {
        headers: {
          'Authorization': `Bearer ${settings.openaiApiKey}`
        }
      });

      if (response.ok) {
        setSuccess(true);
        setTimeout(() => setSuccess(false), 3000);
        alert('✅ Kết nối OpenAI thành công!');
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      setError('Không thể kết nối OpenAI. Kiểm tra lại API key.');
      setTimeout(() => setError(null), 5000);
      console.error('OpenAI connection test failed:', error);
    }
    setLoading(false);
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
        {/* Header */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid size={12}>
            <Paper sx={{ p: 3, textAlign: 'center', background: 'linear-gradient(45deg, #007bff 30%, #0056b3 90%)' }}>
              <Typography variant="h4" sx={{ color: 'white', mb: 1, display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 2 }}>
                <SettingsIcon fontSize="large" />
                🎯 Binomo Trading Assistant - Cài đặt
              </Typography>
              <Typography variant="body1" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                Tùy chỉnh extension theo nhu cầu của bạn
              </Typography>
            </Paper>
          </Grid>
        </Grid>

        {/* Success/Error Messages */}
        {success && (
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid size={12}>
              <Alert severity="success">
                Cài đặt đã được lưu thành công!
              </Alert>
            </Grid>
          </Grid>
        )}

        {error && (
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid size={12}>
              <Alert severity="error">
                {error}
              </Alert>
            </Grid>
          </Grid>
        )}

        {/* Tabs */}
        <Grid container spacing={3}>
          <Grid size={12}>
            <Paper sx={{ mb: 3 }}>
              <Tabs
                value={currentTab}
                onChange={(_, newValue) => setCurrentTab(newValue)}
                variant="scrollable"
                scrollButtons="auto"
              >
                <Tab icon={<ThemeIcon />} label="Giao diện" />
                <Tab icon={<NotificationIcon />} label="Thông báo" />
                <Tab icon={<TradingIcon />} label="Giao dịch" />
                <Tab icon={<MethodsIcon />} label="Phương pháp" />
                <Tab icon={<MeditationIcon />} label="Thiền" />
                <Tab icon={<AIIcon />} label="AI" />
                <Tab icon={<SecurityIcon />} label="Bảo mật" />
                <Tab icon={<StorageIcon />} label="Dữ liệu" />
              </Tabs>
            </Paper>
          </Grid>
        </Grid>

        {/* Tab Content */}
        <Grid container spacing={3}>
          <Grid size={12}>
            {/* Theme Settings */}
            <TabPanel value={currentTab} index={0}>
              <Grid container spacing={3}>
                <Grid size={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>🎨 Theme</Typography>
                      <ButtonGroup fullWidth sx={{ mb: 2 }}>
                        <Button 
                          variant={settings.theme === 'light' ? 'contained' : 'outlined'}
                          onClick={() => handleSettingChange('theme', 'light')}
                        >
                          Light
                        </Button>
                        <Button 
                          variant={settings.theme === 'dark' ? 'contained' : 'outlined'}
                          onClick={() => handleSettingChange('theme', 'dark')}
                        >
                          Dark
                        </Button>
                      </ButtonGroup>
                      
                      <Typography variant="body2" sx={{ mb: 1 }}>Font Size: {settings.fontSize}px</Typography>
                      <Slider
                        value={settings.fontSize}
                        onChange={(_, value) => handleSettingChange('fontSize', value)}
                        min={12}
                        max={20}
                        step={1}
                        marks
                        valueLabelDisplay="auto"
                      />
                    </CardContent>
                  </Card>
                </Grid>
                
                <Grid size={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>🌐 Ngôn ngữ</Typography>
                      <ButtonGroup fullWidth>
                        <Button 
                          variant={settings.language === 'vi' ? 'contained' : 'outlined'}
                          onClick={() => handleSettingChange('language', 'vi')}
                        >
                          Tiếng Việt
                        </Button>
                        <Button 
                          variant={settings.language === 'en' ? 'contained' : 'outlined'}
                          onClick={() => handleSettingChange('language', 'en')}
                        >
                          English
                        </Button>
                      </ButtonGroup>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Notification Settings */}
            <TabPanel value={currentTab} index={1}>
              <Grid container spacing={3}>
                <Grid size={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>🔔 Thông báo</Typography>
                      <Stack spacing={2}>
                        <FormControlLabel
                          control={
                            <Switch 
                              checked={settings.notifications}
                              onChange={(e) => handleSettingChange('notifications', e.target.checked)}
                            />
                          }
                          label="Bật thông báo"
                        />
                        <FormControlLabel
                          control={
                            <Switch 
                              checked={settings.soundAlerts}
                              onChange={(e) => handleSettingChange('soundAlerts', e.target.checked)}
                            />
                          }
                          label="Âm thanh cảnh báo"
                        />
                        <FormControlLabel
                          control={
                            <Switch 
                              checked={settings.desktopNotifications}
                              onChange={(e) => handleSettingChange('desktopNotifications', e.target.checked)}
                            />
                          }
                          label="Thông báo desktop"
                        />
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Trading Settings */}
            <TabPanel value={currentTab} index={2}>
              <Grid container spacing={3}>
                <Grid size={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>💰 Giao dịch mặc định</Typography>
                      <Stack spacing={3}>
                        <Box>
                          <Typography variant="body2" sx={{ mb: 1 }}>Số tiền mặc định ($):</Typography>
                          <ButtonGroup fullWidth>
                            {[1, 5, 10, 25, 50].map(amount => (
                              <Button 
                                key={amount}
                                variant={settings.defaultTradeAmount === amount ? 'contained' : 'outlined'}
                                onClick={() => handleSettingChange('defaultTradeAmount', amount)}
                              >
                                ${amount}
                              </Button>
                            ))}
                          </ButtonGroup>
                        </Box>

                        <Box>
                          <Typography variant="body2" sx={{ mb: 1 }}>Thời gian mặc định:</Typography>
                          <ButtonGroup fullWidth>
                            {[1, 5, 15, 30, 60].map(duration => (
                              <Button 
                                key={duration}
                                variant={settings.defaultTradeDuration === duration ? 'contained' : 'outlined'}
                                onClick={() => handleSettingChange('defaultTradeDuration', duration)}
                              >
                                {duration}m
                              </Button>
                            ))}
                          </ButtonGroup>
                        </Box>
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid size={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>⚠️ Quản lý rủi ro</Typography>
                      <Stack spacing={2}>
                        <FormControlLabel
                          control={
                            <Switch 
                              checked={settings.riskWarnings}
                              onChange={(e) => handleSettingChange('riskWarnings', e.target.checked)}
                            />
                          }
                          label="Cảnh báo rủi ro"
                        />
                        <FormControlLabel
                          control={
                            <Switch 
                              checked={settings.autoStopLoss}
                              onChange={(e) => handleSettingChange('autoStopLoss', e.target.checked)}
                            />
                          }
                          label="Tự động dừng khi thua lỗ"
                        />
                        <Box>
                          <Typography variant="body2" sx={{ mb: 1 }}>
                            Giới hạn lệnh/ngày: {settings.maxDailyTrades}
                          </Typography>
                          <Slider
                            value={settings.maxDailyTrades}
                            onChange={(_, value) => handleSettingChange('maxDailyTrades', value)}
                            min={10}
                            max={100}
                            step={5}
                            marks
                            valueLabelDisplay="auto"
                          />
                        </Box>
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Trading Methods Management */}
            <TabPanel value={currentTab} index={3}>
              <Grid container spacing={3}>
                {/* Built-in Methods */}
                <Grid size={12}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Typography variant="h6">📊 Phương pháp có sẵn</Typography>
                        <Chip label={`${getBuiltInMethods().length} phương pháp`} color="primary" />
                      </Box>
                      <Grid container spacing={2}>
                        {getBuiltInMethods().map((method) => (
                          <Grid size={12} md={6} lg={4} key={method.id}>
                            <Card variant="outlined" sx={{ height: '100%' }}>
                              <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                  <Typography variant="h6" sx={{ mr: 1 }}>{method.icon}</Typography>
                                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                                    {method.name}
                                  </Typography>
                                </Box>
                                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                  {method.description}
                                </Typography>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                  <Chip
                                    label={`${method.questions?.length || 5} câu hỏi`}
                                    size="small"
                                    variant="outlined"
                                  />
                                  <FormControlLabel
                                    control={
                                      <Switch
                                        checked={getMethodEnabled(method.id)}
                                        onChange={(e) => toggleMethod(method.id, e.target.checked)}
                                        size="small"
                                      />
                                    }
                                    label=""
                                    sx={{ m: 0 }}
                                  />
                                </Box>
                              </CardContent>
                            </Card>
                          </Grid>
                        ))}
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Custom Methods */}
                <Grid size={12}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Typography variant="h6">⚙️ Phương pháp tùy chỉnh</Typography>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Chip label={`${customMethods.length} phương pháp`} color="secondary" />
                          <Button
                            variant="contained"
                            size="small"
                            startIcon={<AddIcon />}
                            onClick={() => setShowCreateMethod(true)}
                          >
                            Tạo mới
                          </Button>
                        </Box>
                      </Box>

                      {customMethods.length === 0 ? (
                        <Box sx={{ textAlign: 'center', py: 4 }}>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            Chưa có phương pháp tùy chỉnh nào
                          </Typography>
                          <Button
                            variant="outlined"
                            startIcon={<AddIcon />}
                            onClick={() => setShowCreateMethod(true)}
                          >
                            Tạo phương pháp đầu tiên
                          </Button>
                        </Box>
                      ) : (
                        <Grid container spacing={2}>
                          {customMethods.map((method) => (
                            <Grid size={12} md={6} lg={4} key={method.id}>
                              <Card variant="outlined" sx={{ height: '100%', border: '2px solid #4caf50' }}>
                                <CardContent>
                                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                    <Typography variant="h6" sx={{ mr: 1 }}>{method.icon}</Typography>
                                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold', flex: 1 }}>
                                      {method.name}
                                    </Typography>
                                    <Chip label="CUSTOM" size="small" color="success" />
                                  </Box>
                                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                    {method.description}
                                  </Typography>
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                    <Chip
                                      label={`${method.questions?.length || 0} câu hỏi`}
                                      size="small"
                                      variant="outlined"
                                    />
                                    <Chip
                                      label={`${method.totalMaxScore || 0} điểm`}
                                      size="small"
                                      variant="outlined"
                                    />
                                  </Box>
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                    <Box sx={{ display: 'flex', gap: 1 }}>
                                      <Tooltip title="Chỉnh sửa phương pháp này">
                                        <span>
                                          <Button
                                            size="small"
                                            startIcon={<EditIcon />}
                                            onClick={() => editCustomMethod(method)}
                                            disabled={loading}
                                          >
                                            Sửa
                                          </Button>
                                        </span>
                                      </Tooltip>
                                      <Tooltip title="Xóa phương pháp này vĩnh viễn">
                                        <span>
                                          <Button
                                            size="small"
                                            color="error"
                                            startIcon={<DeleteIcon />}
                                            onClick={() => showDeleteConfirmation(method)}
                                            disabled={loading}
                                          >
                                            Xóa
                                          </Button>
                                        </span>
                                      </Tooltip>
                                    </Box>
                                    <FormControlLabel
                                      control={
                                        <Switch
                                          checked={getMethodEnabled(method.id)}
                                          onChange={(e) => toggleMethod(method.id, e.target.checked)}
                                          size="small"
                                        />
                                      }
                                      label=""
                                      sx={{ m: 0 }}
                                    />
                                  </Box>
                                </CardContent>
                              </Card>
                            </Grid>
                          ))}
                        </Grid>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Meditation Tab */}
            <TabPanel value={currentTab} index={4}>
              <Grid container spacing={3}>
                <Grid size={12}>
                  <Card>
                    <CardContent>
                      <Box sx={{ textAlign: 'center', mb: 3 }}>
                        <MeditationIcon sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
                        <Typography variant="h4" gutterBottom>
                          🧘‍♂️ Thiền Chánh Niệm
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          Thời gian để tâm hồn nghỉ ngơi và tái tạo năng lượng tích cực
                        </Typography>
                      </Box>

                      <Grid container spacing={3}>
                        {/* OpenAI API Configuration */}
                        <Grid size={12}>
                          <Card variant="outlined">
                            <CardContent>
                              <Typography variant="h6" gutterBottom>
                                🔑 OpenAI API Configuration
                              </Typography>
                              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                Nhập OpenAI API key để sử dụng tính năng đánh giá tâm lý AI
                              </Typography>

                              <TextField
                                fullWidth
                                label="OpenAI API Key"
                                type="password"
                                value={settings.openaiApiKey}
                                onChange={(e) => handleSettingChange('openaiApiKey', e.target.value)}
                                placeholder="sk-..."
                                helperText="API key sẽ được lưu trữ an toàn trong Chrome storage"
                                sx={{ mb: 2 }}
                              />

                              <FormControlLabel
                                control={
                                  <Switch
                                    checked={settings.useAIPsychology}
                                    onChange={(e) => handleSettingChange('useAIPsychology', e.target.checked)}
                                  />
                                }
                                label="Sử dụng AI để đánh giá tâm lý"
                              />

                              {settings.openaiApiKey && (
                                <Box sx={{ mt: 2 }}>
                                  <Alert severity="info" sx={{ mb: 2 }}>
                                    <Typography variant="body2">
                                      ✅ API key đã được cấu hình. AI psychology assessment sẽ hoạt động trong sidebar.
                                    </Typography>
                                  </Alert>
                                  <Button
                                    variant="outlined"
                                    size="small"
                                    onClick={testOpenAIConnection}
                                    disabled={loading}
                                  >
                                    🧪 Test kết nối
                                  </Button>
                                </Box>
                              )}
                            </CardContent>
                          </Card>
                        </Grid>

                        {/* AI Features */}
                        <Grid size={12} md={6}>
                          <Card variant="outlined" sx={{ height: '100%' }}>
                            <CardContent>
                              <Typography variant="h6" gutterBottom>
                                🧠 AI Psychology Features
                              </Typography>
                              <Box component="ul" sx={{ pl: 2, m: 0 }}>
                                <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                                  Phân tích đa yếu tố: cảm xúc, tài chính, thể chất, tinh thần
                                </Typography>
                                <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                                  Tính toán thời gian khóa linh hoạt (15 phút - 24 giờ)
                                </Typography>
                                <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                                  Khuyến nghị cá nhân hóa dựa trên tình trạng hiện tại
                                </Typography>
                                <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                                  Phân tích rủi ro và yếu tố tích cực
                                </Typography>
                              </Box>
                            </CardContent>
                          </Card>
                        </Grid>

                        {/* Usage Guide */}
                        <Grid size={12} md={6}>
                          <Card variant="outlined" sx={{ height: '100%' }}>
                            <CardContent>
                              <Typography variant="h6" gutterBottom>
                                📖 Hướng dẫn sử dụng
                              </Typography>
                              <Box component="ol" sx={{ pl: 2, m: 0 }}>
                                <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                                  Đăng ký tài khoản OpenAI tại <strong>platform.openai.com</strong>
                                </Typography>
                                <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                                  Tạo API key trong phần API keys
                                </Typography>
                                <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                                  Copy và paste API key vào ô trên
                                </Typography>
                                <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                                  Bật tính năng AI psychology assessment
                                </Typography>
                                <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                                  Sử dụng trong sidebar khi đánh giá tâm lý
                                </Typography>
                              </Box>
                            </CardContent>
                          </Card>
                        </Grid>

                        {/* Cost Information */}
                        <Grid size={12}>
                          <Alert severity="warning">
                            <Typography variant="body2">
                              <strong>💰 Chi phí:</strong> Mỗi lần đánh giá AI sẽ tốn khoảng $0.001-0.002 USD.
                              Với 100 lần đánh giá/tháng, chi phí khoảng $0.10-0.20 USD.
                            </Typography>
                          </Alert>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>

            {/* AI Tab */}
            <TabPanel value={currentTab} index={5}>
              <Grid container spacing={3}>
                <Grid size={12}>
                  <Card>
                    <CardContent>
                      <Box sx={{ textAlign: 'center', mb: 3 }}>
                        <AIIcon sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
                        <Typography variant="h4" gutterBottom>
                          🤖 AI Psychology Assessment
                        </Typography>
                        <Typography variant="body1" color="text.secondary">
                          Cấu hình OpenAI để đánh giá tâm lý giao dịch thông minh
                        </Typography>
                      </Box>

                      <Grid container spacing={3}>
                        {/* Mindfulness Meditation */}
                        <Grid size={12} md={4}>
                          <Card variant="outlined" sx={{ height: '100%', cursor: 'pointer' }}
                                onClick={() => { setMeditationType('mindfulness'); setShowMeditation(true); }}>
                            <CardContent sx={{ textAlign: 'center' }}>
                              <MeditationIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                              <Typography variant="h6" gutterBottom>
                                Thiền Chánh Niệm
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Quan sát hơi thở, nhận biết cảm xúc và suy nghĩ mà không phán xét
                              </Typography>
                            </CardContent>
                          </Card>
                        </Grid>

                        {/* Gratitude Meditation */}
                        <Grid size={12} md={4}>
                          <Card variant="outlined" sx={{ height: '100%', cursor: 'pointer' }}
                                onClick={() => { setMeditationType('gratitude'); setShowMeditation(true); }}>
                            <CardContent sx={{ textAlign: 'center' }}>
                              <GratitudeIcon sx={{ fontSize: 48, color: 'secondary.main', mb: 2 }} />
                              <Typography variant="h6" gutterBottom>
                                Thiền Biết Ơn
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Cảm ơn những gì đã có, nuôi dưỡng lòng biết ơn và sự hài lòng
                              </Typography>
                            </CardContent>
                          </Card>
                        </Grid>

                        {/* Compassion Meditation */}
                        <Grid size={12} md={4}>
                          <Card variant="outlined" sx={{ height: '100%', cursor: 'pointer' }}
                                onClick={() => { setMeditationType('compassion'); setShowMeditation(true); }}>
                            <CardContent sx={{ textAlign: 'center' }}>
                              <CompassionIcon sx={{ fontSize: 48, color: 'success.main', mb: 2 }} />
                              <Typography variant="h6" gutterBottom>
                                Thiền Từ Bi
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Gửi tình yêu thương đến bản thân và mọi người xung quanh
                              </Typography>
                            </CardContent>
                          </Card>
                        </Grid>
                      </Grid>

                      <Box sx={{ mt: 3, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
                        <Typography variant="body2" color="info.contrastText">
                          💡 <strong>Lưu ý:</strong> Khi tâm lý không phù hợp để giao dịch, hãy dành thời gian thiền để tái tạo năng lượng tích cực.
                          Giao dịch với tâm hồn bình an sẽ mang lại kết quả tốt hơn.
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Security Settings */}
            <TabPanel value={currentTab} index={6}>
              <Grid container spacing={3}>
                <Grid size={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>🔒 Bảo mật</Typography>
                      <Stack spacing={2}>
                        <FormControlLabel
                          control={
                            <Switch 
                              checked={settings.requireConfirmation}
                              onChange={(e) => handleSettingChange('requireConfirmation', e.target.checked)}
                            />
                          }
                          label="Yêu cầu xác nhận trước khi giao dịch"
                        />
                        <Box>
                          <Typography variant="body2" sx={{ mb: 1 }}>
                            Session timeout: {settings.sessionTimeout} phút
                          </Typography>
                          <Slider
                            value={settings.sessionTimeout}
                            onChange={(_, value) => handleSettingChange('sessionTimeout', value)}
                            min={5}
                            max={120}
                            step={5}
                            marks
                            valueLabelDisplay="auto"
                          />
                        </Box>
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Data Management */}
            <TabPanel value={currentTab} index={7}>
              <Grid container spacing={3}>
                <Grid size={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>💾 Backup & Storage</Typography>
                      <Stack spacing={2}>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={settings.autoBackup}
                              onChange={(e) => handleSettingChange('autoBackup', e.target.checked)}
                            />
                          }
                          label="Tự động backup"
                        />

                        <TextField
                          label="API URL"
                          value={settings.apiUrl}
                          onChange={(e) => handleSettingChange('apiUrl', e.target.value)}
                          fullWidth
                          size="small"
                        />
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid size={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" sx={{ mb: 2 }}>📤 Import/Export</Typography>
                      <Stack spacing={2}>
                        <Button
                          variant="outlined"
                          startIcon={<ExportIcon />}
                          onClick={exportSettings}
                          fullWidth
                        >
                          Xuất cài đặt
                        </Button>
                        <Button
                          variant="outlined"
                          startIcon={<ImportIcon />}
                          onClick={importSettings}
                          fullWidth
                        >
                          Nhập cài đặt
                        </Button>
                      </Stack>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </TabPanel>
          </Grid>
        </Grid>

        {/* Action Buttons */}
        <Grid container spacing={3} sx={{ mt: 3 }}>
          <Grid size={12}>
            <Paper sx={{ p: 3 }}>
              <Stack direction="row" spacing={2} justifyContent="center">
                <Button 
                  variant="outlined"
                  color="secondary"
                  startIcon={<RestoreIcon />}
                  onClick={resetSettings}
                >
                  Reset về mặc định
                </Button>
                <Button 
                  variant="contained"
                  startIcon={<SaveIcon />}
                  onClick={saveSettings}
                  disabled={loading}
                  size="large"
                >
                  {loading ? 'Đang lưu...' : 'Lưu cài đặt'}
                </Button>
              </Stack>
            </Paper>
          </Grid>
        </Grid>

        {/* Custom Method Creator Dialog */}
        <CustomMethodCreator
          open={showCreateMethod}
          onClose={() => {
            setShowCreateMethod(false);
            setEditingMethod(null);
          }}
          onSave={saveCustomMethod}
          editingMethod={editingMethod}
        />

        {/* Meditation Dialog */}
        <Dialog
          open={showMeditation}
          onClose={() => setShowMeditation(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {meditationType === 'mindfulness' && <MeditationIcon color="primary" />}
              {meditationType === 'gratitude' && <GratitudeIcon color="secondary" />}
              {meditationType === 'compassion' && <CompassionIcon color="success" />}
              <Typography variant="h6">
                {meditationType === 'mindfulness' && '🧘‍♂️ Thiền Chánh Niệm'}
                {meditationType === 'gratitude' && '🙏 Thiền Biết Ơn'}
                {meditationType === 'compassion' && '💝 Thiền Từ Bi'}
              </Typography>
            </Box>
          </DialogTitle>
          <DialogContent>
            {meditationType === 'mindfulness' && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Hướng dẫn Thiền Chánh Niệm
                </Typography>
                <Typography paragraph>
                  1. <strong>Tìm tư thế thoải mái:</strong> Ngồi thẳng lưng, thả lỏng vai, đặt tay trên đùi.
                </Typography>
                <Typography paragraph>
                  2. <strong>Quan sát hơi thở:</strong> Tập trung vào cảm giác hơi thở vào ra tự nhiên.
                </Typography>
                <Typography paragraph>
                  3. <strong>Nhận biết suy nghĩ:</strong> Khi tâm trí lang thang, nhẹ nhàng đưa về hơi thở.
                </Typography>
                <Typography paragraph>
                  4. <strong>Không phán xét:</strong> Quan sát mọi cảm xúc, suy nghĩ mà không đánh giá.
                </Typography>
                <Typography paragraph>
                  5. <strong>Thực hành 10-15 phút:</strong> Bắt đầu với thời gian ngắn, tăng dần.
                </Typography>
                <Box sx={{ mt: 3, p: 2, bgcolor: 'primary.light', borderRadius: 1 }}>
                  <Typography variant="body2" color="primary.contrastText">
                    💡 <strong>Lợi ích:</strong> Giảm stress, tăng khả năng tập trung, cải thiện khả năng ra quyết định trong giao dịch.
                  </Typography>
                </Box>
              </Box>
            )}

            {meditationType === 'gratitude' && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Hướng dẫn Thiền Biết Ơn
                </Typography>
                <Typography paragraph>
                  1. <strong>Ngồi yên tĩnh:</strong> Tìm không gian thoải mái, thở sâu 3 lần.
                </Typography>
                <Typography paragraph>
                  2. <strong>Nghĩ về những điều tốt đẹp:</strong> Gia đình, sức khỏe, cơ hội học hỏi từ thị trường.
                </Typography>
                <Typography paragraph>
                  3. <strong>Cảm ơn thị trường:</strong> "Tôi cảm ơn thị trường đã tạo ra nơi để tôi rèn luyện tâm và kiếm lợi nhuận đủ sống."
                </Typography>
                <Typography paragraph>
                  4. <strong>Cảm ơn bản thân:</strong> "Tôi cảm ơn bản thân đã kiên nhẫn học hỏi và rèn luyện."
                </Typography>
                <Typography paragraph>
                  5. <strong>Gửi lòng biết ơn:</strong> Đến mọi người đã hỗ trợ hành trình của bạn.
                </Typography>
                <Box sx={{ mt: 3, p: 2, bgcolor: 'secondary.light', borderRadius: 1 }}>
                  <Typography variant="body2" color="secondary.contrastText">
                    💝 <strong>Lợi ích:</strong> Tăng cảm giác hạnh phúc, giảm tham lam, tạo tâm thái tích cực trong giao dịch.
                  </Typography>
                </Box>
              </Box>
            )}

            {meditationType === 'compassion' && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Hướng dẫn Thiền Từ Bi
                </Typography>
                <Typography paragraph>
                  1. <strong>Bắt đầu với bản thân:</strong> "Mong tôi được bình an, hạnh phúc và thành công."
                </Typography>
                <Typography paragraph>
                  2. <strong>Gửi đến người thân:</strong> "Mong gia đình tôi được khỏe mạnh và hạnh phúc."
                </Typography>
                <Typography paragraph>
                  3. <strong>Gửi đến trader khác:</strong> "Mong tất cả trader đều học hỏi và phát triển."
                </Typography>
                <Typography paragraph>
                  4. <strong>Tha thứ cho bản thân:</strong> "Tôi tha thứ cho những sai lầm trong giao dịch và sẽ học hỏi."
                </Typography>
                <Typography paragraph>
                  5. <strong>Tình yêu thương rộng lớn:</strong> Gửi tình yêu thương đến tất cả chúng sinh.
                </Typography>
                <Box sx={{ mt: 3, p: 2, bgcolor: 'success.light', borderRadius: 1 }}>
                  <Typography variant="body2" color="success.contrastText">
                    🌟 <strong>Lợi ích:</strong> Giảm tức giận khi thua lỗ, tăng khả năng tha thứ, tạo tâm thái bình an.
                  </Typography>
                </Box>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowMeditation(false)}>
              Đóng
            </Button>
            <Button
              variant="contained"
              color="success"
              onClick={confirmPsychologyAndOpenBinomo}
            >
              ✅ Tâm đã ổn định - Mở Binomo
            </Button>
          </DialogActions>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog
          open={deleteConfirmOpen}
          onClose={() => {
            setDeleteConfirmOpen(false);
            setMethodToDelete(null);
          }}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <DeleteIcon color="error" />
              <Typography variant="h6">Xác nhận xóa phương pháp</Typography>
            </Box>
          </DialogTitle>
          <DialogContent>
            <DialogContentText>
              Bạn có chắc chắn muốn xóa phương pháp{' '}
              <strong>"{methodToDelete?.name}"</strong>?
            </DialogContentText>
            <DialogContentText sx={{ mt: 2, color: 'error.main' }}>
              ⚠️ Hành động này không thể hoàn tác. Tất cả dữ liệu liên quan đến phương pháp này sẽ bị xóa vĩnh viễn.
            </DialogContentText>
            {methodToDelete && (
              <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  <strong>Thông tin phương pháp:</strong>
                </Typography>
                <Typography variant="body2">
                  • Tên: {methodToDelete.name}
                </Typography>
                <Typography variant="body2">
                  • Số câu hỏi: {methodToDelete.questions?.length || 0}
                </Typography>
                <Typography variant="body2">
                  • Điểm tối đa: {methodToDelete.totalMaxScore || 0}
                </Typography>
                <Typography variant="body2">
                  • Ngày tạo: {methodToDelete.createdAt ? new Date(methodToDelete.createdAt).toLocaleDateString('vi-VN') : 'N/A'}
                </Typography>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => {
                setDeleteConfirmOpen(false);
                setMethodToDelete(null);
              }}
              disabled={loading}
            >
              Hủy
            </Button>
            <Button
              onClick={deleteCustomMethod}
              color="error"
              variant="contained"
              disabled={loading}
              startIcon={loading ? <CircularProgress size={16} /> : <DeleteIcon />}
            >
              {loading ? 'Đang xóa...' : 'Xóa phương pháp'}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ThemeProvider>
  );
};

export default Options;
