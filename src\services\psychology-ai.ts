// Psychology Assessment with OpenAI Integration

export interface PsychologyAssessment {
  score: number; // 0-100 scale
  level: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
  shouldTrade: boolean;
  blockDuration: number; // minutes
  recommendation: string;
  aiAnalysis: string;
  factors: {
    emotional: number;
    financial: number;
    physical: number;
    mental: number;
  };
}

export interface AssessmentInput {
  emotionalState: string;
  financialSituation: string;
  recentPerformance: string;
  sleepQuality: string;
  stressLevel: string;
  motivation: string;
  additionalNotes?: string;
}

// Block duration mapping based on psychology score
const BLOCK_DURATIONS = {
  excellent: 0,      // 90-100: No block
  good: 0,          // 80-89: No block  
  fair: 15,         // 60-79: 15 minutes
  poor: 240,        // 30-59: 4 hours
  critical: 1440    // 0-29: 24 hours (1 day)
};

// Detailed block duration options
const DETAILED_BLOCK_DURATIONS = {
  0: 0,           // No block
  15: 15,         // 15 minutes
  30: 30,         // 30 minutes
  60: 60,         // 1 hour
  240: 240,       // 4 hours
  480: 480,       // 8 hours
  720: 720,       // 12 hours
  1440: 1440      // 24 hours
};

export class PsychologyAI {
  private apiKey: string;
  private baseURL = 'https://api.openai.com/v1/chat/completions';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async assessPsychology(input: AssessmentInput): Promise<PsychologyAssessment> {
    try {
      // Create prompt for OpenAI
      const prompt = this.createAssessmentPrompt(input);
      
      // Get AI analysis
      const aiResponse = await this.callOpenAI(prompt);
      
      // Parse AI response and calculate scores
      const assessment = this.parseAIResponse(aiResponse, input);
      
      return assessment;
    } catch (error) {
      console.error('Error in AI psychology assessment:', error);
      // Fallback to rule-based assessment
      return this.fallbackAssessment(input);
    }
  }

  private createAssessmentPrompt(input: AssessmentInput): string {
    return `
Bạn là một chuyên gia tâm lý giao dịch với 20 năm kinh nghiệm. Hãy đánh giá tâm lý giao dịch của người này:

THÔNG TIN ĐÁNH GIÁ:
- Trạng thái cảm xúc: ${input.emotionalState}
- Tình hình tài chính: ${input.financialSituation}
- Kết quả giao dịch gần đây: ${input.recentPerformance}
- Chất lượng giấc ngủ: ${input.sleepQuality}
- Mức độ căng thẳng: ${input.stressLevel}
- Động lực giao dịch: ${input.motivation}
${input.additionalNotes ? `- Ghi chú thêm: ${input.additionalNotes}` : ''}

Hãy trả về đánh giá theo format JSON chính xác sau:
{
  "score": [số điểm từ 0-100],
  "emotional_factor": [điểm cảm xúc 0-100],
  "financial_factor": [điểm tài chính 0-100], 
  "physical_factor": [điểm thể chất 0-100],
  "mental_factor": [điểm tinh thần 0-100],
  "should_trade": [true/false],
  "recommendation": "[khuyến nghị ngắn gọn]",
  "analysis": "[phân tích chi tiết 2-3 câu]",
  "risk_factors": ["[yếu tố rủi ro 1]", "[yếu tố rủi ro 2]"],
  "positive_factors": ["[yếu tố tích cực 1]", "[yếu tố tích cực 2]"]
}

TIÊU CHÍ ĐÁNH GIÁ:
- 90-100: Tâm lý xuất sắc, sẵn sàng giao dịch
- 80-89: Tâm lý tốt, có thể giao dịch cẩn thận
- 60-79: Tâm lý trung bình, nên nghỉ 15-30 phút
- 30-59: Tâm lý kém, nên nghỉ 4-8 tiếng
- 0-29: Tâm lý rất kém, nên nghỉ 12-24 tiếng

Chỉ trả về JSON, không có text khác.
`;
  }

  private async callOpenAI(prompt: string): Promise<string> {
    const response = await fetch(this.baseURL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'Bạn là chuyên gia tâm lý giao dịch. Luôn trả về JSON hợp lệ.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  private parseAIResponse(aiResponse: string, input: AssessmentInput): PsychologyAssessment {
    try {
      // Clean and parse JSON response
      const cleanResponse = aiResponse.replace(/```json\n?|\n?```/g, '').trim();
      const parsed = JSON.parse(cleanResponse);

      // Calculate block duration based on score
      const level = this.getScoreLevel(parsed.score);
      const blockDuration = this.calculateBlockDuration(parsed.score, parsed);

      return {
        score: parsed.score,
        level,
        shouldTrade: parsed.should_trade && parsed.score >= 60,
        blockDuration,
        recommendation: parsed.recommendation,
        aiAnalysis: parsed.analysis,
        factors: {
          emotional: parsed.emotional_factor || 50,
          financial: parsed.financial_factor || 50,
          physical: parsed.physical_factor || 50,
          mental: parsed.mental_factor || 50
        }
      };
    } catch (error) {
      console.error('Error parsing AI response:', error);
      return this.fallbackAssessment(input);
    }
  }

  private getScoreLevel(score: number): PsychologyAssessment['level'] {
    if (score >= 90) return 'excellent';
    if (score >= 80) return 'good';
    if (score >= 60) return 'fair';
    if (score >= 30) return 'poor';
    return 'critical';
  }

  private calculateBlockDuration(score: number, aiData: any): number {
    // Base duration from score
    let duration = 0;
    
    if (score >= 80) {
      duration = 0; // No block
    } else if (score >= 60) {
      duration = 15; // 15 minutes
    } else if (score >= 45) {
      duration = 60; // 1 hour
    } else if (score >= 30) {
      duration = 240; // 4 hours
    } else if (score >= 15) {
      duration = 720; // 12 hours
    } else {
      duration = 1440; // 24 hours
    }

    // Adjust based on risk factors
    if (aiData.risk_factors && aiData.risk_factors.length > 2) {
      duration = Math.min(duration * 1.5, 1440);
    }

    // Reduce based on positive factors
    if (aiData.positive_factors && aiData.positive_factors.length > 2) {
      duration = Math.max(duration * 0.7, 0);
    }

    return Math.round(duration);
  }

  private fallbackAssessment(input: AssessmentInput): PsychologyAssessment {
    // Simple rule-based fallback
    let score = 50;
    
    // Emotional state scoring
    if (input.emotionalState.includes('cân bằng') || input.emotionalState.includes('tích cực')) {
      score += 20;
    } else if (input.emotionalState.includes('căng thẳng') || input.emotionalState.includes('lo âu')) {
      score -= 20;
    }

    // Financial situation scoring
    if (input.financialSituation.includes('ổn định')) {
      score += 15;
    } else if (input.financialSituation.includes('khó khăn')) {
      score -= 15;
    }

    // Recent performance scoring
    if (input.recentPerformance.includes('tốt') || input.recentPerformance.includes('lãi')) {
      score += 10;
    } else if (input.recentPerformance.includes('thua') || input.recentPerformance.includes('lỗ')) {
      score -= 15;
    }

    score = Math.max(0, Math.min(100, score));
    const level = this.getScoreLevel(score);

    return {
      score,
      level,
      shouldTrade: score >= 60,
      blockDuration: BLOCK_DURATIONS[level],
      recommendation: score >= 60 ? 'Có thể giao dịch cẩn thận' : 'Nên nghỉ ngơi và thiền định',
      aiAnalysis: 'Đánh giá dựa trên quy tắc cơ bản (AI không khả dụng)',
      factors: {
        emotional: score,
        financial: score,
        physical: score,
        mental: score
      }
    };
  }

  // Get available block durations for manual selection
  static getBlockDurationOptions() {
    return [
      { value: 0, label: 'Không khóa', description: 'Tiếp tục giao dịch ngay' },
      { value: 15, label: '15 phút', description: 'Nghỉ ngắn để tĩnh tâm' },
      { value: 30, label: '30 phút', description: 'Nghỉ ngơi và suy ngẫm' },
      { value: 60, label: '1 tiếng', description: 'Thời gian thiền định' },
      { value: 240, label: '4 tiếng', description: 'Nghỉ ngơi dài hạn' },
      { value: 480, label: '8 tiếng', description: 'Nghỉ ngơi qua đêm' },
      { value: 720, label: '12 tiếng', description: 'Nghỉ ngơi nửa ngày' },
      { value: 1440, label: '24 tiếng', description: 'Nghỉ ngơi cả ngày' }
    ];
  }
}

export default PsychologyAI;
