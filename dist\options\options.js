import{g as We,a as Ie,r as g,j as o,n as Ut,o as qt,b as Fe,p as Gt,u as qe,s as re,q as ke,t as me,v as mo,w as Eo,x as Ao,P as Wt,y as yn,c as ve,z as Te,f as R,D as st,E as Et,F as ft,G as bo,H as po,J as go,K as ko,L as To,M as wn,N as Qt,O as Dn,d as Ee,B as le,I as yo,A as Po,h as Re,i as $e,m as ht,l as Ce,e as Nn,T as Wn,C as Fn,k as Vn,Q as Hn,S as Un}from"../assets/TrendingUp-BITtWk55.js";import{o as kt,P as qn,u as Ke,a as zo,b as Sn,F as Xo,M as Xn,B as Yn,f as Gn,L as Yo,i as lo,g as Kn,G as _n,d as jn,c as Cn,T as Ct,e as Qn,I as Jn,S as Zn,A as er,h as Go}from"../assets/Psychology-zjuSHFns.js";import{G as F,S as tr,B as Jt,a as wt}from"../assets/Settings-C6j4aHPK.js";var He="top",tt="bottom",ot="right",Ue="left",Do="auto",Kt=[He,tt,ot,Ue],Rt="start",Xt="end",or="clippingParents",kn="viewport",At="popper",nr="reference",Ko=Kt.reduce(function(e,t){return e.concat([t+"-"+Rt,t+"-"+Xt])},[]),Tn=[].concat(Kt,[Do]).reduce(function(e,t){return e.concat([t,t+"-"+Rt,t+"-"+Xt])},[]),rr="beforeRead",sr="read",ar="afterRead",ir="beforeMain",lr="main",cr="afterMain",pr="beforeWrite",dr="write",ur="afterWrite",hr=[rr,sr,ar,ir,lr,cr,pr,dr,ur];function it(e){return e?(e.nodeName||"").toLowerCase():null}function _e(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Pt(e){var t=_e(e).Element;return e instanceof t||e instanceof Element}function et(e){var t=_e(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function No(e){if(typeof ShadowRoot>"u")return!1;var t=_e(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function fr(e){var t=e.state;Object.keys(t.elements).forEach(function(n){var r=t.styles[n]||{},s=t.attributes[n]||{},a=t.elements[n];!et(a)||!it(a)||(Object.assign(a.style,r),Object.keys(s).forEach(function(c){var i=s[c];i===!1?a.removeAttribute(c):a.setAttribute(c,i===!0?"":i)}))})}function mr(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(r){var s=t.elements[r],a=t.attributes[r]||{},c=Object.keys(t.styles.hasOwnProperty(r)?t.styles[r]:n[r]),i=c.reduce(function(l,m){return l[m]="",l},{});!et(s)||!it(s)||(Object.assign(s.style,i),Object.keys(a).forEach(function(l){s.removeAttribute(l)}))})}}const gr={name:"applyStyles",enabled:!0,phase:"write",fn:fr,effect:mr,requires:["computeStyles"]};function at(e){return e.split("-")[0]}var Tt=Math.max,uo=Math.min,$t=Math.round;function Mo(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function Pn(){return!/^((?!chrome|android).)*safari/i.test(Mo())}function Bt(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!1);var r=e.getBoundingClientRect(),s=1,a=1;t&&et(e)&&(s=e.offsetWidth>0&&$t(r.width)/e.offsetWidth||1,a=e.offsetHeight>0&&$t(r.height)/e.offsetHeight||1);var c=Pt(e)?_e(e):window,i=c.visualViewport,l=!Pn()&&n,m=(r.left+(l&&i?i.offsetLeft:0))/s,p=(r.top+(l&&i?i.offsetTop:0))/a,T=r.width/s,j=r.height/a;return{width:T,height:j,top:p,right:m+T,bottom:p+j,left:m,x:m,y:p}}function Wo(e){var t=Bt(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function Mn(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&No(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function dt(e){return _e(e).getComputedStyle(e)}function vr(e){return["table","td","th"].indexOf(it(e))>=0}function mt(e){return((Pt(e)?e.ownerDocument:e.document)||window.document).documentElement}function vo(e){return it(e)==="html"?e:e.assignedSlot||e.parentNode||(No(e)?e.host:null)||mt(e)}function _o(e){return!et(e)||dt(e).position==="fixed"?null:e.offsetParent}function xr(e){var t=/firefox/i.test(Mo()),n=/Trident/i.test(Mo());if(n&&et(e)){var r=dt(e);if(r.position==="fixed")return null}var s=vo(e);for(No(s)&&(s=s.host);et(s)&&["html","body"].indexOf(it(s))<0;){var a=dt(s);if(a.transform!=="none"||a.perspective!=="none"||a.contain==="paint"||["transform","perspective"].indexOf(a.willChange)!==-1||t&&a.willChange==="filter"||t&&a.filter&&a.filter!=="none")return s;s=s.parentNode}return null}function _t(e){for(var t=_e(e),n=_o(e);n&&vr(n)&&dt(n).position==="static";)n=_o(n);return n&&(it(n)==="html"||it(n)==="body"&&dt(n).position==="static")?t:n||xr(e)||t}function Fo(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Ft(e,t,n){return Tt(e,uo(t,n))}function br(e,t,n){var r=Ft(e,t,n);return r>n?n:r}function Rn(){return{top:0,right:0,bottom:0,left:0}}function $n(e){return Object.assign({},Rn(),e)}function Bn(e,t){return t.reduce(function(n,r){return n[r]=e,n},{})}var yr=function(t,n){return t=typeof t=="function"?t(Object.assign({},n.rects,{placement:n.placement})):t,$n(typeof t!="number"?t:Bn(t,Kt))};function wr(e){var t,n=e.state,r=e.name,s=e.options,a=n.elements.arrow,c=n.modifiersData.popperOffsets,i=at(n.placement),l=Fo(i),m=[Ue,ot].indexOf(i)>=0,p=m?"height":"width";if(!(!a||!c)){var T=yr(s.padding,n),j=Wo(a),b=l==="y"?He:Ue,P=l==="y"?tt:ot,y=n.rects.reference[p]+n.rects.reference[l]-c[l]-n.rects.popper[p],k=c[l]-n.rects.reference[l],$=_t(a),u=$?l==="y"?$.clientHeight||0:$.clientWidth||0:0,f=y/2-k/2,d=T[b],v=u-j[p]-T[P],S=u/2-j[p]/2+f,O=Ft(d,S,v),X=l;n.modifiersData[r]=(t={},t[X]=O,t.centerOffset=O-S,t)}}function Sr(e){var t=e.state,n=e.options,r=n.element,s=r===void 0?"[data-popper-arrow]":r;s!=null&&(typeof s=="string"&&(s=t.elements.popper.querySelector(s),!s)||Mn(t.elements.popper,s)&&(t.elements.arrow=s))}const jr={name:"arrow",enabled:!0,phase:"main",fn:wr,effect:Sr,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Ot(e){return e.split("-")[1]}var Cr={top:"auto",right:"auto",bottom:"auto",left:"auto"};function kr(e,t){var n=e.x,r=e.y,s=t.devicePixelRatio||1;return{x:$t(n*s)/s||0,y:$t(r*s)/s||0}}function Qo(e){var t,n=e.popper,r=e.popperRect,s=e.placement,a=e.variation,c=e.offsets,i=e.position,l=e.gpuAcceleration,m=e.adaptive,p=e.roundOffsets,T=e.isFixed,j=c.x,b=j===void 0?0:j,P=c.y,y=P===void 0?0:P,k=typeof p=="function"?p({x:b,y}):{x:b,y};b=k.x,y=k.y;var $=c.hasOwnProperty("x"),u=c.hasOwnProperty("y"),f=Ue,d=He,v=window;if(m){var S=_t(n),O="clientHeight",X="clientWidth";if(S===_e(n)&&(S=mt(n),dt(S).position!=="static"&&i==="absolute"&&(O="scrollHeight",X="scrollWidth")),S=S,s===He||(s===Ue||s===ot)&&a===Xt){d=tt;var A=T&&S===v&&v.visualViewport?v.visualViewport.height:S[O];y-=A-r.height,y*=l?1:-1}if(s===Ue||(s===He||s===tt)&&a===Xt){f=ot;var I=T&&S===v&&v.visualViewport?v.visualViewport.width:S[X];b-=I-r.width,b*=l?1:-1}}var W=Object.assign({position:i},m&&Cr),Y=p===!0?kr({x:b,y},_e(n)):{x:b,y};if(b=Y.x,y=Y.y,l){var w;return Object.assign({},W,(w={},w[d]=u?"0":"",w[f]=$?"0":"",w.transform=(v.devicePixelRatio||1)<=1?"translate("+b+"px, "+y+"px)":"translate3d("+b+"px, "+y+"px, 0)",w))}return Object.assign({},W,(t={},t[d]=u?y+"px":"",t[f]=$?b+"px":"",t.transform="",t))}function Tr(e){var t=e.state,n=e.options,r=n.gpuAcceleration,s=r===void 0?!0:r,a=n.adaptive,c=a===void 0?!0:a,i=n.roundOffsets,l=i===void 0?!0:i,m={placement:at(t.placement),variation:Ot(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:s,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,Qo(Object.assign({},m,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:c,roundOffsets:l})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,Qo(Object.assign({},m,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}const Pr={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Tr,data:{}};var Zt={passive:!0};function Mr(e){var t=e.state,n=e.instance,r=e.options,s=r.scroll,a=s===void 0?!0:s,c=r.resize,i=c===void 0?!0:c,l=_e(t.elements.popper),m=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&m.forEach(function(p){p.addEventListener("scroll",n.update,Zt)}),i&&l.addEventListener("resize",n.update,Zt),function(){a&&m.forEach(function(p){p.removeEventListener("scroll",n.update,Zt)}),i&&l.removeEventListener("resize",n.update,Zt)}}const Rr={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Mr,data:{}};var $r={left:"right",right:"left",bottom:"top",top:"bottom"};function co(e){return e.replace(/left|right|bottom|top/g,function(t){return $r[t]})}var Br={start:"end",end:"start"};function Jo(e){return e.replace(/start|end/g,function(t){return Br[t]})}function Vo(e){var t=_e(e),n=t.pageXOffset,r=t.pageYOffset;return{scrollLeft:n,scrollTop:r}}function Ho(e){return Bt(mt(e)).left+Vo(e).scrollLeft}function Or(e,t){var n=_e(e),r=mt(e),s=n.visualViewport,a=r.clientWidth,c=r.clientHeight,i=0,l=0;if(s){a=s.width,c=s.height;var m=Pn();(m||!m&&t==="fixed")&&(i=s.offsetLeft,l=s.offsetTop)}return{width:a,height:c,x:i+Ho(e),y:l}}function Ir(e){var t,n=mt(e),r=Vo(e),s=(t=e.ownerDocument)==null?void 0:t.body,a=Tt(n.scrollWidth,n.clientWidth,s?s.scrollWidth:0,s?s.clientWidth:0),c=Tt(n.scrollHeight,n.clientHeight,s?s.scrollHeight:0,s?s.clientHeight:0),i=-r.scrollLeft+Ho(e),l=-r.scrollTop;return dt(s||n).direction==="rtl"&&(i+=Tt(n.clientWidth,s?s.clientWidth:0)-a),{width:a,height:c,x:i,y:l}}function Uo(e){var t=dt(e),n=t.overflow,r=t.overflowX,s=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+s+r)}function On(e){return["html","body","#document"].indexOf(it(e))>=0?e.ownerDocument.body:et(e)&&Uo(e)?e:On(vo(e))}function Vt(e,t){var n;t===void 0&&(t=[]);var r=On(e),s=r===((n=e.ownerDocument)==null?void 0:n.body),a=_e(r),c=s?[a].concat(a.visualViewport||[],Uo(r)?r:[]):r,i=t.concat(c);return s?i:i.concat(Vt(vo(c)))}function Ro(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Lr(e,t){var n=Bt(e,!1,t==="fixed");return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}function Zo(e,t,n){return t===kn?Ro(Or(e,n)):Pt(t)?Lr(t,n):Ro(Ir(mt(e)))}function Er(e){var t=Vt(vo(e)),n=["absolute","fixed"].indexOf(dt(e).position)>=0,r=n&&et(e)?_t(e):e;return Pt(r)?t.filter(function(s){return Pt(s)&&Mn(s,r)&&it(s)!=="body"}):[]}function Ar(e,t,n,r){var s=t==="clippingParents"?Er(e):[].concat(t),a=[].concat(s,[n]),c=a[0],i=a.reduce(function(l,m){var p=Zo(e,m,r);return l.top=Tt(p.top,l.top),l.right=uo(p.right,l.right),l.bottom=uo(p.bottom,l.bottom),l.left=Tt(p.left,l.left),l},Zo(e,c,r));return i.width=i.right-i.left,i.height=i.bottom-i.top,i.x=i.left,i.y=i.top,i}function In(e){var t=e.reference,n=e.element,r=e.placement,s=r?at(r):null,a=r?Ot(r):null,c=t.x+t.width/2-n.width/2,i=t.y+t.height/2-n.height/2,l;switch(s){case He:l={x:c,y:t.y-n.height};break;case tt:l={x:c,y:t.y+t.height};break;case ot:l={x:t.x+t.width,y:i};break;case Ue:l={x:t.x-n.width,y:i};break;default:l={x:t.x,y:t.y}}var m=s?Fo(s):null;if(m!=null){var p=m==="y"?"height":"width";switch(a){case Rt:l[m]=l[m]-(t[p]/2-n[p]/2);break;case Xt:l[m]=l[m]+(t[p]/2-n[p]/2);break}}return l}function Yt(e,t){t===void 0&&(t={});var n=t,r=n.placement,s=r===void 0?e.placement:r,a=n.strategy,c=a===void 0?e.strategy:a,i=n.boundary,l=i===void 0?or:i,m=n.rootBoundary,p=m===void 0?kn:m,T=n.elementContext,j=T===void 0?At:T,b=n.altBoundary,P=b===void 0?!1:b,y=n.padding,k=y===void 0?0:y,$=$n(typeof k!="number"?k:Bn(k,Kt)),u=j===At?nr:At,f=e.rects.popper,d=e.elements[P?u:j],v=Ar(Pt(d)?d:d.contextElement||mt(e.elements.popper),l,p,c),S=Bt(e.elements.reference),O=In({reference:S,element:f,placement:s}),X=Ro(Object.assign({},f,O)),A=j===At?X:S,I={top:v.top-A.top+$.top,bottom:A.bottom-v.bottom+$.bottom,left:v.left-A.left+$.left,right:A.right-v.right+$.right},W=e.modifiersData.offset;if(j===At&&W){var Y=W[s];Object.keys(I).forEach(function(w){var M=[ot,tt].indexOf(w)>=0?1:-1,ee=[He,tt].indexOf(w)>=0?"y":"x";I[w]+=Y[ee]*M})}return I}function zr(e,t){t===void 0&&(t={});var n=t,r=n.placement,s=n.boundary,a=n.rootBoundary,c=n.padding,i=n.flipVariations,l=n.allowedAutoPlacements,m=l===void 0?Tn:l,p=Ot(r),T=p?i?Ko:Ko.filter(function(P){return Ot(P)===p}):Kt,j=T.filter(function(P){return m.indexOf(P)>=0});j.length===0&&(j=T);var b=j.reduce(function(P,y){return P[y]=Yt(e,{placement:y,boundary:s,rootBoundary:a,padding:c})[at(y)],P},{});return Object.keys(b).sort(function(P,y){return b[P]-b[y]})}function Dr(e){if(at(e)===Do)return[];var t=co(e);return[Jo(e),t,Jo(t)]}function Nr(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var s=n.mainAxis,a=s===void 0?!0:s,c=n.altAxis,i=c===void 0?!0:c,l=n.fallbackPlacements,m=n.padding,p=n.boundary,T=n.rootBoundary,j=n.altBoundary,b=n.flipVariations,P=b===void 0?!0:b,y=n.allowedAutoPlacements,k=t.options.placement,$=at(k),u=$===k,f=l||(u||!P?[co(k)]:Dr(k)),d=[k].concat(f).reduce(function(xe,q){return xe.concat(at(q)===Do?zr(t,{placement:q,boundary:p,rootBoundary:T,padding:m,flipVariations:P,allowedAutoPlacements:y}):q)},[]),v=t.rects.reference,S=t.rects.popper,O=new Map,X=!0,A=d[0],I=0;I<d.length;I++){var W=d[I],Y=at(W),w=Ot(W)===Rt,M=[He,tt].indexOf(Y)>=0,ee=M?"width":"height",E=Yt(t,{placement:W,boundary:p,rootBoundary:T,altBoundary:j,padding:m}),J=M?w?ot:Ue:w?tt:He;v[ee]>S[ee]&&(J=co(J));var V=co(J),pe=[];if(a&&pe.push(E[Y]<=0),i&&pe.push(E[J]<=0,E[V]<=0),pe.every(function(xe){return xe})){A=W,X=!1;break}O.set(W,pe)}if(X)for(var se=P?3:1,H=function(q){var te=d.find(function(ue){var h=O.get(ue);if(h)return h.slice(0,q).every(function(L){return L})});if(te)return A=te,"break"},G=se;G>0;G--){var Me=H(G);if(Me==="break")break}t.placement!==A&&(t.modifiersData[r]._skip=!0,t.placement=A,t.reset=!0)}}const Wr={name:"flip",enabled:!0,phase:"main",fn:Nr,requiresIfExists:["offset"],data:{_skip:!1}};function en(e,t,n){return n===void 0&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function tn(e){return[He,ot,tt,Ue].some(function(t){return e[t]>=0})}function Fr(e){var t=e.state,n=e.name,r=t.rects.reference,s=t.rects.popper,a=t.modifiersData.preventOverflow,c=Yt(t,{elementContext:"reference"}),i=Yt(t,{altBoundary:!0}),l=en(c,r),m=en(i,s,a),p=tn(l),T=tn(m);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:m,isReferenceHidden:p,hasPopperEscaped:T},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":p,"data-popper-escaped":T})}const Vr={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Fr};function Hr(e,t,n){var r=at(e),s=[Ue,He].indexOf(r)>=0?-1:1,a=typeof n=="function"?n(Object.assign({},t,{placement:e})):n,c=a[0],i=a[1];return c=c||0,i=(i||0)*s,[Ue,ot].indexOf(r)>=0?{x:i,y:c}:{x:c,y:i}}function Ur(e){var t=e.state,n=e.options,r=e.name,s=n.offset,a=s===void 0?[0,0]:s,c=Tn.reduce(function(p,T){return p[T]=Hr(T,t.rects,a),p},{}),i=c[t.placement],l=i.x,m=i.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=m),t.modifiersData[r]=c}const qr={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Ur};function Xr(e){var t=e.state,n=e.name;t.modifiersData[n]=In({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})}const Yr={name:"popperOffsets",enabled:!0,phase:"read",fn:Xr,data:{}};function Gr(e){return e==="x"?"y":"x"}function Kr(e){var t=e.state,n=e.options,r=e.name,s=n.mainAxis,a=s===void 0?!0:s,c=n.altAxis,i=c===void 0?!1:c,l=n.boundary,m=n.rootBoundary,p=n.altBoundary,T=n.padding,j=n.tether,b=j===void 0?!0:j,P=n.tetherOffset,y=P===void 0?0:P,k=Yt(t,{boundary:l,rootBoundary:m,padding:T,altBoundary:p}),$=at(t.placement),u=Ot(t.placement),f=!u,d=Fo($),v=Gr(d),S=t.modifiersData.popperOffsets,O=t.rects.reference,X=t.rects.popper,A=typeof y=="function"?y(Object.assign({},t.rects,{placement:t.placement})):y,I=typeof A=="number"?{mainAxis:A,altAxis:A}:Object.assign({mainAxis:0,altAxis:0},A),W=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,Y={x:0,y:0};if(S){if(a){var w,M=d==="y"?He:Ue,ee=d==="y"?tt:ot,E=d==="y"?"height":"width",J=S[d],V=J+k[M],pe=J-k[ee],se=b?-X[E]/2:0,H=u===Rt?O[E]:X[E],G=u===Rt?-X[E]:-O[E],Me=t.elements.arrow,xe=b&&Me?Wo(Me):{width:0,height:0},q=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:Rn(),te=q[M],ue=q[ee],h=Ft(0,O[E],xe[E]),L=f?O[E]/2-se-h-te-I.mainAxis:H-h-te-I.mainAxis,U=f?-O[E]/2+se+h+ue+I.mainAxis:G+h+ue+I.mainAxis,Z=t.elements.arrow&&_t(t.elements.arrow),_=Z?d==="y"?Z.clientTop||0:Z.clientLeft||0:0,oe=(w=W==null?void 0:W[d])!=null?w:0,Se=J+L-oe-_,Ae=J+U-oe,ze=Ft(b?uo(V,Se):V,J,b?Tt(pe,Ae):pe);S[d]=ze,Y[d]=ze-J}if(i){var ae,Pe=d==="x"?He:Ue,Le=d==="x"?tt:ot,Be=S[v],Oe=v==="y"?"height":"width",Ve=Be+k[Pe],De=Be-k[Le],Xe=[He,Ue].indexOf($)!==-1,x=(ae=W==null?void 0:W[v])!=null?ae:0,C=Xe?Ve:Be-O[Oe]-X[Oe]-x+I.altAxis,z=Xe?Be+O[Oe]+X[Oe]-x-I.altAxis:De,K=b&&Xe?br(C,Be,z):Ft(b?C:Ve,Be,b?z:De);S[v]=K,Y[v]=K-Be}t.modifiersData[r]=Y}}const _r={name:"preventOverflow",enabled:!0,phase:"main",fn:Kr,requiresIfExists:["offset"]};function Qr(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function Jr(e){return e===_e(e)||!et(e)?Vo(e):Qr(e)}function Zr(e){var t=e.getBoundingClientRect(),n=$t(t.width)/e.offsetWidth||1,r=$t(t.height)/e.offsetHeight||1;return n!==1||r!==1}function es(e,t,n){n===void 0&&(n=!1);var r=et(t),s=et(t)&&Zr(t),a=mt(t),c=Bt(e,s,n),i={scrollLeft:0,scrollTop:0},l={x:0,y:0};return(r||!r&&!n)&&((it(t)!=="body"||Uo(a))&&(i=Jr(t)),et(t)?(l=Bt(t,!0),l.x+=t.clientLeft,l.y+=t.clientTop):a&&(l.x=Ho(a))),{x:c.left+i.scrollLeft-l.x,y:c.top+i.scrollTop-l.y,width:c.width,height:c.height}}function ts(e){var t=new Map,n=new Set,r=[];e.forEach(function(a){t.set(a.name,a)});function s(a){n.add(a.name);var c=[].concat(a.requires||[],a.requiresIfExists||[]);c.forEach(function(i){if(!n.has(i)){var l=t.get(i);l&&s(l)}}),r.push(a)}return e.forEach(function(a){n.has(a.name)||s(a)}),r}function os(e){var t=ts(e);return hr.reduce(function(n,r){return n.concat(t.filter(function(s){return s.phase===r}))},[])}function ns(e){var t;return function(){return t||(t=new Promise(function(n){Promise.resolve().then(function(){t=void 0,n(e())})})),t}}function rs(e){var t=e.reduce(function(n,r){var s=n[r.name];return n[r.name]=s?Object.assign({},s,r,{options:Object.assign({},s.options,r.options),data:Object.assign({},s.data,r.data)}):r,n},{});return Object.keys(t).map(function(n){return t[n]})}var on={placement:"bottom",modifiers:[],strategy:"absolute"};function nn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(r){return!(r&&typeof r.getBoundingClientRect=="function")})}function ss(e){e===void 0&&(e={});var t=e,n=t.defaultModifiers,r=n===void 0?[]:n,s=t.defaultOptions,a=s===void 0?on:s;return function(i,l,m){m===void 0&&(m=a);var p={placement:"bottom",orderedModifiers:[],options:Object.assign({},on,a),modifiersData:{},elements:{reference:i,popper:l},attributes:{},styles:{}},T=[],j=!1,b={state:p,setOptions:function($){var u=typeof $=="function"?$(p.options):$;y(),p.options=Object.assign({},a,p.options,u),p.scrollParents={reference:Pt(i)?Vt(i):i.contextElement?Vt(i.contextElement):[],popper:Vt(l)};var f=os(rs([].concat(r,p.options.modifiers)));return p.orderedModifiers=f.filter(function(d){return d.enabled}),P(),b.update()},forceUpdate:function(){if(!j){var $=p.elements,u=$.reference,f=$.popper;if(nn(u,f)){p.rects={reference:es(u,_t(f),p.options.strategy==="fixed"),popper:Wo(f)},p.reset=!1,p.placement=p.options.placement,p.orderedModifiers.forEach(function(I){return p.modifiersData[I.name]=Object.assign({},I.data)});for(var d=0;d<p.orderedModifiers.length;d++){if(p.reset===!0){p.reset=!1,d=-1;continue}var v=p.orderedModifiers[d],S=v.fn,O=v.options,X=O===void 0?{}:O,A=v.name;typeof S=="function"&&(p=S({state:p,options:X,name:A,instance:b})||p)}}}},update:ns(function(){return new Promise(function(k){b.forceUpdate(),k(p)})}),destroy:function(){y(),j=!0}};if(!nn(i,l))return b;b.setOptions(m).then(function(k){!j&&m.onFirstUpdate&&m.onFirstUpdate(k)});function P(){p.orderedModifiers.forEach(function(k){var $=k.name,u=k.options,f=u===void 0?{}:u,d=k.effect;if(typeof d=="function"){var v=d({state:p,name:$,instance:b,options:f}),S=function(){};T.push(v||S)}})}function y(){T.forEach(function(k){return k()}),T=[]}return b}}var as=[Rr,Yr,Pr,gr,qr,Wr,_r,jr,Vr],is=ss({defaultModifiers:as});function ls(e){return We("MuiPopper",e)}Ie("MuiPopper",["root"]);function cs(e,t){if(t==="ltr")return e;switch(e){case"bottom-end":return"bottom-start";case"bottom-start":return"bottom-end";case"top-end":return"top-start";case"top-start":return"top-end";default:return e}}function $o(e){return typeof e=="function"?e():e}function ps(e){return e.nodeType!==void 0}const ds=e=>{const{classes:t}=e;return Fe({root:["root"]},ls,t)},us={},hs=g.forwardRef(function(t,n){const{anchorEl:r,children:s,direction:a,disablePortal:c,modifiers:i,open:l,placement:m,popperOptions:p,popperRef:T,slotProps:j={},slots:b={},TransitionProps:P,ownerState:y,...k}=t,$=g.useRef(null),u=Ut($,n),f=g.useRef(null),d=Ut(f,T),v=g.useRef(d);qt(()=>{v.current=d},[d]),g.useImperativeHandle(T,()=>f.current,[]);const S=cs(m,a),[O,X]=g.useState(S),[A,I]=g.useState($o(r));g.useEffect(()=>{f.current&&f.current.forceUpdate()}),g.useEffect(()=>{r&&I($o(r))},[r]),qt(()=>{if(!A||!l)return;const ee=V=>{X(V.placement)};let E=[{name:"preventOverflow",options:{altBoundary:c}},{name:"flip",options:{altBoundary:c}},{name:"onUpdate",enabled:!0,phase:"afterWrite",fn:({state:V})=>{ee(V)}}];i!=null&&(E=E.concat(i)),p&&p.modifiers!=null&&(E=E.concat(p.modifiers));const J=is(A,$.current,{placement:S,...p,modifiers:E});return v.current(J),()=>{J.destroy(),v.current(null)}},[A,c,i,l,p,S]);const W={placement:O};P!==null&&(W.TransitionProps=P);const Y=ds(t),w=b.root??"div",M=Ke({elementType:w,externalSlotProps:j.root,externalForwardedProps:k,additionalProps:{role:"tooltip",ref:u},ownerState:t,className:Y.root});return o.jsx(w,{...M,children:typeof s=="function"?s(W):s})}),fs=g.forwardRef(function(t,n){const{anchorEl:r,children:s,container:a,direction:c="ltr",disablePortal:i=!1,keepMounted:l=!1,modifiers:m,open:p,placement:T="bottom",popperOptions:j=us,popperRef:b,style:P,transition:y=!1,slotProps:k={},slots:$={},...u}=t,[f,d]=g.useState(!0),v=()=>{d(!1)},S=()=>{d(!0)};if(!l&&!p&&(!y||f))return null;let O;if(a)O=a;else if(r){const I=$o(r);O=I&&ps(I)?kt(I).body:kt(null).body}const X=!p&&l&&(!y||f)?"none":void 0,A=y?{in:p,onEnter:v,onExited:S}:void 0;return o.jsx(qn,{disablePortal:i,container:O,children:o.jsx(hs,{anchorEl:r,direction:c,disablePortal:i,modifiers:m,ref:n,open:y?!f:p,placement:T,popperOptions:j,popperRef:b,slotProps:k,slots:$,...u,style:{position:"fixed",top:0,left:0,display:X,...P},TransitionProps:A,children:s})})}),ms=re(fs,{name:"MuiPopper",slot:"Root"})({}),Ln=g.forwardRef(function(t,n){const r=Gt(),s=qe({props:t,name:"MuiPopper"}),{anchorEl:a,component:c,components:i,componentsProps:l,container:m,disablePortal:p,keepMounted:T,modifiers:j,open:b,placement:P,popperOptions:y,popperRef:k,transition:$,slots:u,slotProps:f,...d}=s,v=(u==null?void 0:u.root)??(i==null?void 0:i.Root),S={anchorEl:a,container:m,disablePortal:p,keepMounted:T,modifiers:j,open:b,placement:P,popperOptions:y,popperRef:k,transition:$,...d};return o.jsx(ms,{as:c,direction:r?"rtl":"ltr",slots:{root:v},slotProps:f??l,...S,ref:n})});function gs(e){return We("PrivateSwitchBase",e)}Ie("PrivateSwitchBase",["root","checked","disabled","input","edgeStart","edgeEnd"]);const vs=e=>{const{classes:t,checked:n,disabled:r,edge:s}=e,a={root:["root",n&&"checked",r&&"disabled",s&&`edge${me(s)}`],input:["input"]};return Fe(a,gs,t)},xs=re(mo)({padding:9,borderRadius:"50%",variants:[{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:({edge:e,ownerState:t})=>e==="start"&&t.size!=="small",style:{marginLeft:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}},{props:({edge:e,ownerState:t})=>e==="end"&&t.size!=="small",style:{marginRight:-12}}]}),bs=re("input",{shouldForwardProp:Eo})({cursor:"inherit",position:"absolute",opacity:0,width:"100%",height:"100%",top:0,left:0,margin:0,padding:0,zIndex:1}),ys=g.forwardRef(function(t,n){const{autoFocus:r,checked:s,checkedIcon:a,defaultChecked:c,disabled:i,disableFocusRipple:l=!1,edge:m=!1,icon:p,id:T,inputProps:j,inputRef:b,name:P,onBlur:y,onChange:k,onFocus:$,readOnly:u,required:f=!1,tabIndex:d,type:v,value:S,slots:O={},slotProps:X={},...A}=t,[I,W]=zo({controlled:s,default:!!c,name:"SwitchBase",state:"checked"}),Y=Sn(),w=q=>{$&&$(q),Y&&Y.onFocus&&Y.onFocus(q)},M=q=>{y&&y(q),Y&&Y.onBlur&&Y.onBlur(q)},ee=q=>{if(q.nativeEvent.defaultPrevented)return;const te=q.target.checked;W(te),k&&k(q,te)};let E=i;Y&&typeof E>"u"&&(E=Y.disabled);const J=v==="checkbox"||v==="radio",V={...t,checked:I,disabled:E,disableFocusRipple:l,edge:m},pe=vs(V),se={slots:O,slotProps:{input:j,...X}},[H,G]=ke("root",{ref:n,elementType:xs,className:pe.root,shouldForwardComponentProp:!0,externalForwardedProps:{...se,component:"span",...A},getSlotProps:q=>({...q,onFocus:te=>{var ue;(ue=q.onFocus)==null||ue.call(q,te),w(te)},onBlur:te=>{var ue;(ue=q.onBlur)==null||ue.call(q,te),M(te)}}),ownerState:V,additionalProps:{centerRipple:!0,focusRipple:!l,disabled:E,role:void 0,tabIndex:null}}),[Me,xe]=ke("input",{ref:b,elementType:bs,className:pe.input,externalForwardedProps:se,getSlotProps:q=>({...q,onChange:te=>{var ue;(ue=q.onChange)==null||ue.call(q,te),ee(te)}}),ownerState:V,additionalProps:{autoFocus:r,checked:s,defaultChecked:c,disabled:E,id:J?T:void 0,name:P,readOnly:u,required:f,tabIndex:d,type:v,...v==="checkbox"&&S===void 0?{}:{value:S}}});return o.jsxs(H,{...G,children:[o.jsx(Me,{...xe}),I?a:p]})});function ws(e){return We("MuiDialog",e)}const wo=Ie("MuiDialog",["root","scrollPaper","scrollBody","container","paper","paperScrollPaper","paperScrollBody","paperWidthFalse","paperWidthXs","paperWidthSm","paperWidthMd","paperWidthLg","paperWidthXl","paperFullWidth","paperFullScreen"]),En=g.createContext({}),Ss=re(Yn,{name:"MuiDialog",slot:"Backdrop",overrides:(e,t)=>t.backdrop})({zIndex:-1}),js=e=>{const{classes:t,scroll:n,maxWidth:r,fullWidth:s,fullScreen:a}=e,c={root:["root"],container:["container",`scroll${me(n)}`],paper:["paper",`paperScroll${me(n)}`,`paperWidth${me(String(r))}`,s&&"paperFullWidth",a&&"paperFullScreen"]};return Fe(c,ws,t)},Cs=re(Xn,{name:"MuiDialog",slot:"Root"})({"@media print":{position:"absolute !important"}}),ks=re("div",{name:"MuiDialog",slot:"Container",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.container,t[`scroll${me(n.scroll)}`]]}})({height:"100%","@media print":{height:"auto"},outline:0,variants:[{props:{scroll:"paper"},style:{display:"flex",justifyContent:"center",alignItems:"center"}},{props:{scroll:"body"},style:{overflowY:"auto",overflowX:"hidden",textAlign:"center","&::after":{content:'""',display:"inline-block",verticalAlign:"middle",height:"100%",width:"0"}}}]}),Ts=re(Wt,{name:"MuiDialog",slot:"Paper",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.paper,t[`scrollPaper${me(n.scroll)}`],t[`paperWidth${me(String(n.maxWidth))}`],n.fullWidth&&t.paperFullWidth,n.fullScreen&&t.paperFullScreen]}})(Te(({theme:e})=>({margin:32,position:"relative",overflowY:"auto","@media print":{overflowY:"visible",boxShadow:"none"},variants:[{props:{scroll:"paper"},style:{display:"flex",flexDirection:"column",maxHeight:"calc(100% - 64px)"}},{props:{scroll:"body"},style:{display:"inline-block",verticalAlign:"middle",textAlign:"initial"}},{props:({ownerState:t})=>!t.maxWidth,style:{maxWidth:"calc(100% - 64px)"}},{props:{maxWidth:"xs"},style:{maxWidth:e.breakpoints.unit==="px"?Math.max(e.breakpoints.values.xs,444):`max(${e.breakpoints.values.xs}${e.breakpoints.unit}, 444px)`,[`&.${wo.paperScrollBody}`]:{[e.breakpoints.down(Math.max(e.breakpoints.values.xs,444)+32*2)]:{maxWidth:"calc(100% - 64px)"}}}},...Object.keys(e.breakpoints.values).filter(t=>t!=="xs").map(t=>({props:{maxWidth:t},style:{maxWidth:`${e.breakpoints.values[t]}${e.breakpoints.unit}`,[`&.${wo.paperScrollBody}`]:{[e.breakpoints.down(e.breakpoints.values[t]+32*2)]:{maxWidth:"calc(100% - 64px)"}}}})),{props:({ownerState:t})=>t.fullWidth,style:{width:"calc(100% - 64px)"}},{props:({ownerState:t})=>t.fullScreen,style:{margin:0,width:"100%",maxWidth:"100%",height:"100%",maxHeight:"none",borderRadius:0,[`&.${wo.paperScrollBody}`]:{margin:0,maxWidth:"100%"}}}]}))),Bo=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiDialog"}),s=Ao(),a={enter:s.transitions.duration.enteringScreen,exit:s.transitions.duration.leavingScreen},{"aria-describedby":c,"aria-labelledby":i,"aria-modal":l=!0,BackdropComponent:m,BackdropProps:p,children:T,className:j,disableEscapeKeyDown:b=!1,fullScreen:P=!1,fullWidth:y=!1,maxWidth:k="sm",onClick:$,onClose:u,open:f,PaperComponent:d=Wt,PaperProps:v={},scroll:S="paper",slots:O={},slotProps:X={},TransitionComponent:A=Xo,transitionDuration:I=a,TransitionProps:W,...Y}=r,w={...r,disableEscapeKeyDown:b,fullScreen:P,fullWidth:y,maxWidth:k,scroll:S},M=js(w),ee=g.useRef(),E=oe=>{ee.current=oe.target===oe.currentTarget},J=oe=>{$&&$(oe),ee.current&&(ee.current=null,u&&u(oe,"backdropClick"))},V=yn(i),pe=g.useMemo(()=>({titleId:V}),[V]),se={transition:A,...O},H={transition:W,paper:v,backdrop:p,...X},G={slots:se,slotProps:H},[Me,xe]=ke("root",{elementType:Cs,shouldForwardComponentProp:!0,externalForwardedProps:G,ownerState:w,className:ve(M.root,j),ref:n}),[q,te]=ke("backdrop",{elementType:Ss,shouldForwardComponentProp:!0,externalForwardedProps:G,ownerState:w}),[ue,h]=ke("paper",{elementType:Ts,shouldForwardComponentProp:!0,externalForwardedProps:G,ownerState:w,className:ve(M.paper,v.className)}),[L,U]=ke("container",{elementType:ks,externalForwardedProps:G,ownerState:w,className:M.container}),[Z,_]=ke("transition",{elementType:Xo,externalForwardedProps:G,ownerState:w,additionalProps:{appear:!0,in:f,timeout:I,role:"presentation"}});return o.jsx(Me,{closeAfterTransition:!0,slots:{backdrop:q},slotProps:{backdrop:{transitionDuration:I,as:m,...te}},disableEscapeKeyDown:b,onClose:u,open:f,onClick:J,...xe,...Y,children:o.jsx(Z,{..._,children:o.jsx(L,{onMouseDown:E,...U,children:o.jsx(ue,{as:d,elevation:24,role:"dialog","aria-describedby":c,"aria-labelledby":V,"aria-modal":l,...h,children:o.jsx(En.Provider,{value:pe,children:T})})})})})});function Ps(e){return We("MuiDialogActions",e)}Ie("MuiDialogActions",["root","spacing"]);const Ms=e=>{const{classes:t,disableSpacing:n}=e;return Fe({root:["root",!n&&"spacing"]},Ps,t)},Rs=re("div",{name:"MuiDialogActions",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,!n.disableSpacing&&t.spacing]}})({display:"flex",alignItems:"center",padding:8,justifyContent:"flex-end",flex:"0 0 auto",variants:[{props:({ownerState:e})=>!e.disableSpacing,style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),Oo=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiDialogActions"}),{className:s,disableSpacing:a=!1,...c}=r,i={...r,disableSpacing:a},l=Ms(i);return o.jsx(Rs,{className:ve(l.root,s),ownerState:i,ref:n,...c})});function $s(e){return We("MuiDialogContent",e)}Ie("MuiDialogContent",["root","dividers"]);function Bs(e){return We("MuiDialogTitle",e)}const Os=Ie("MuiDialogTitle",["root"]),Is=e=>{const{classes:t,dividers:n}=e;return Fe({root:["root",n&&"dividers"]},$s,t)},Ls=re("div",{name:"MuiDialogContent",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.dividers&&t.dividers]}})(Te(({theme:e})=>({flex:"1 1 auto",WebkitOverflowScrolling:"touch",overflowY:"auto",padding:"20px 24px",variants:[{props:({ownerState:t})=>t.dividers,style:{padding:"16px 24px",borderTop:`1px solid ${(e.vars||e).palette.divider}`,borderBottom:`1px solid ${(e.vars||e).palette.divider}`}},{props:({ownerState:t})=>!t.dividers,style:{[`.${Os.root} + &`]:{paddingTop:0}}}]}))),Io=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiDialogContent"}),{className:s,dividers:a=!1,...c}=r,i={...r,dividers:a},l=Is(i);return o.jsx(Ls,{className:ve(l.root,s),ownerState:i,ref:n,...c})});function Es(e){return We("MuiDialogContentText",e)}Ie("MuiDialogContentText",["root"]);const As=e=>{const{classes:t}=e,r=Fe({root:["root"]},Es,t);return{...t,...r}},zs=re(R,{shouldForwardProp:e=>Eo(e)||e==="classes",name:"MuiDialogContentText",slot:"Root"})({}),rn=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiDialogContentText"}),{children:s,className:a,...c}=r,i=As(c);return o.jsx(zs,{component:"p",variant:"body1",color:"textSecondary",ref:n,ownerState:c,className:ve(i.root,a),...r,classes:i})}),Ds=e=>{const{classes:t}=e;return Fe({root:["root"]},Bs,t)},Ns=re(R,{name:"MuiDialogTitle",slot:"Root"})({padding:"16px 24px",flex:"0 0 auto"}),Lo=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiDialogTitle"}),{className:s,id:a,...c}=r,i=r,l=Ds(i),{titleId:m=a}=g.useContext(En);return o.jsx(Ns,{component:"h2",className:ve(l.root,s),ownerState:i,ref:n,variant:"h6",id:a??m,...c})}),sn=Ie("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"]);function Ws(e){return We("MuiFormControlLabel",e)}const Nt=Ie("MuiFormControlLabel",["root","labelPlacementStart","labelPlacementTop","labelPlacementBottom","disabled","label","error","required","asterisk"]),Fs=e=>{const{classes:t,disabled:n,labelPlacement:r,error:s,required:a}=e,c={root:["root",n&&"disabled",`labelPlacement${me(r)}`,s&&"error",a&&"required"],label:["label",n&&"disabled"],asterisk:["asterisk",s&&"error"]};return Fe(c,Ws,t)},Vs=re("label",{name:"MuiFormControlLabel",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[{[`& .${Nt.label}`]:t.label},t.root,t[`labelPlacement${me(n.labelPlacement)}`]]}})(Te(({theme:e})=>({display:"inline-flex",alignItems:"center",cursor:"pointer",verticalAlign:"middle",WebkitTapHighlightColor:"transparent",marginLeft:-11,marginRight:16,[`&.${Nt.disabled}`]:{cursor:"default"},[`& .${Nt.label}`]:{[`&.${Nt.disabled}`]:{color:(e.vars||e).palette.text.disabled}},variants:[{props:{labelPlacement:"start"},style:{flexDirection:"row-reverse",marginRight:-11}},{props:{labelPlacement:"top"},style:{flexDirection:"column-reverse"}},{props:{labelPlacement:"bottom"},style:{flexDirection:"column"}},{props:({labelPlacement:t})=>t==="start"||t==="top"||t==="bottom",style:{marginLeft:16}}]}))),Hs=re("span",{name:"MuiFormControlLabel",slot:"Asterisk"})(Te(({theme:e})=>({[`&.${Nt.error}`]:{color:(e.vars||e).palette.error.main}}))),ct=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiFormControlLabel"}),{checked:s,className:a,componentsProps:c={},control:i,disabled:l,disableTypography:m,inputRef:p,label:T,labelPlacement:j="end",name:b,onChange:P,required:y,slots:k={},slotProps:$={},value:u,...f}=r,d=Sn(),v=l??i.props.disabled??(d==null?void 0:d.disabled),S=y??i.props.required,O={disabled:v,required:S};["checked","name","onChange","value","inputRef"].forEach(ee=>{typeof i.props[ee]>"u"&&typeof r[ee]<"u"&&(O[ee]=r[ee])});const X=Gn({props:r,muiFormControl:d,states:["error"]}),A={...r,disabled:v,labelPlacement:j,required:S,error:X.error},I=Fs(A),W={slots:k,slotProps:{...c,...$}},[Y,w]=ke("typography",{elementType:R,externalForwardedProps:W,ownerState:A});let M=T;return M!=null&&M.type!==R&&!m&&(M=o.jsx(Y,{component:"span",...w,className:ve(I.label,w==null?void 0:w.className),children:M})),o.jsxs(Vs,{className:ve(I.root,a),ownerState:A,ref:n,...f,children:[g.cloneElement(i,O),S?o.jsxs("div",{children:[M,o.jsxs(Hs,{ownerState:A,"aria-hidden":!0,className:I.asterisk,children:[" ","*"]})]}):M]})}),an=Ie("MuiListItemIcon",["root","alignItemsFlexStart"]),ln=Ie("MuiListItemText",["root","multiline","dense","inset","primary","secondary"]);function Us(e){return We("MuiMenuItem",e)}const zt=Ie("MuiMenuItem",["root","focusVisible","dense","disabled","divider","gutters","selected"]),qs=(e,t)=>{const{ownerState:n}=e;return[t.root,n.dense&&t.dense,n.divider&&t.divider,!n.disableGutters&&t.gutters]},Xs=e=>{const{disabled:t,dense:n,divider:r,disableGutters:s,selected:a,classes:c}=e,l=Fe({root:["root",n&&"dense",t&&"disabled",!s&&"gutters",r&&"divider",a&&"selected"]},Us,c);return{...c,...l}},Ys=re(mo,{shouldForwardProp:e=>Eo(e)||e==="classes",name:"MuiMenuItem",slot:"Root",overridesResolver:qs})(Te(({theme:e})=>({...e.typography.body1,display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minHeight:48,paddingTop:6,paddingBottom:6,boxSizing:"border-box",whiteSpace:"nowrap","&:hover":{textDecoration:"none",backgroundColor:(e.vars||e).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${zt.selected}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${e.vars.palette.action.selectedOpacity})`:st(e.palette.primary.main,e.palette.action.selectedOpacity),[`&.${zt.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.focusOpacity}))`:st(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.focusOpacity)}},[`&.${zt.selected}:hover`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.hoverOpacity}))`:st(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${e.vars.palette.action.selectedOpacity})`:st(e.palette.primary.main,e.palette.action.selectedOpacity)}},[`&.${zt.focusVisible}`]:{backgroundColor:(e.vars||e).palette.action.focus},[`&.${zt.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity},[`& + .${sn.root}`]:{marginTop:e.spacing(1),marginBottom:e.spacing(1)},[`& + .${sn.inset}`]:{marginLeft:52},[`& .${ln.root}`]:{marginTop:0,marginBottom:0},[`& .${ln.inset}`]:{paddingLeft:36},[`& .${an.root}`]:{minWidth:36},variants:[{props:({ownerState:t})=>!t.disableGutters,style:{paddingLeft:16,paddingRight:16}},{props:({ownerState:t})=>t.divider,style:{borderBottom:`1px solid ${(e.vars||e).palette.divider}`,backgroundClip:"padding-box"}},{props:({ownerState:t})=>!t.dense,style:{[e.breakpoints.up("sm")]:{minHeight:"auto"}}},{props:({ownerState:t})=>t.dense,style:{minHeight:32,paddingTop:4,paddingBottom:4,...e.typography.body2,[`& .${an.root} svg`]:{fontSize:"1.25rem"}}}]}))),So=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiMenuItem"}),{autoFocus:s=!1,component:a="li",dense:c=!1,divider:i=!1,disableGutters:l=!1,focusVisibleClassName:m,role:p="menuitem",tabIndex:T,className:j,...b}=r,P=g.useContext(Yo),y=g.useMemo(()=>({dense:c||P.dense||!1,disableGutters:l}),[P.dense,c,l]),k=g.useRef(null);qt(()=>{s&&k.current&&k.current.focus()},[s]);const $={...r,dense:y.dense,divider:i,disableGutters:l},u=Xs(r),f=Ut(k,n);let d;return r.disabled||(d=T!==void 0?T:-1),o.jsx(Yo.Provider,{value:y,children:o.jsx(Ys,{ref:f,role:p,tabIndex:d,component:a,focusVisibleClassName:ve(u.focusVisible,m),className:ve(u.root,j),...b,ownerState:$,classes:u})})}),Gs={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",whiteSpace:"nowrap",width:"1px"};function Ks(e,t,n=(r,s)=>r===s){return e.length===t.length&&e.every((r,s)=>n(r,t[s]))}const _s=2;function Mt(e,t,n,r,s){return n===1?Math.min(e+t,s):Math.max(e-t,r)}function An(e,t){return e-t}function cn(e,t){const{index:n}=e.reduce((r,s,a)=>{const c=Math.abs(t-s);return r===null||c<r.distance||c===r.distance?{distance:c,index:a}:r},null)??{};return n}function eo(e,t){if(t.current!==void 0&&e.changedTouches){const n=e;for(let r=0;r<n.changedTouches.length;r+=1){const s=n.changedTouches[r];if(s.identifier===t.current)return{x:s.clientX,y:s.clientY}}return!1}return{x:e.clientX,y:e.clientY}}function ho(e,t,n){return(e-t)*100/(n-t)}function Qs(e,t,n){return(n-t)*e+t}function Js(e){if(Math.abs(e)<1){const n=e.toExponential().split("e-"),r=n[0].split(".")[1];return(r?r.length:0)+parseInt(n[1],10)}const t=e.toString().split(".")[1];return t?t.length:0}function Zs(e,t,n){const r=Math.round((e-n)/t)*t+n;return Number(r.toFixed(Js(t)))}function pn({values:e,newValue:t,index:n}){const r=e.slice();return r[n]=t,r.sort(An)}function to({sliderRef:e,activeIndex:t,setActive:n}){var s,a,c;const r=kt(e.current);(!((s=e.current)!=null&&s.contains(r.activeElement))||Number((a=r==null?void 0:r.activeElement)==null?void 0:a.getAttribute("data-index"))!==t)&&((c=e.current)==null||c.querySelector(`[type="range"][data-index="${t}"]`).focus()),n&&n(t)}function oo(e,t){return typeof e=="number"&&typeof t=="number"?e===t:typeof e=="object"&&typeof t=="object"?Ks(e,t):!1}const ea={horizontal:{offset:e=>({left:`${e}%`}),leap:e=>({width:`${e}%`})},"horizontal-reverse":{offset:e=>({right:`${e}%`}),leap:e=>({width:`${e}%`})},vertical:{offset:e=>({bottom:`${e}%`}),leap:e=>({height:`${e}%`})}},ta=e=>e;let no;function dn(){return no===void 0&&(typeof CSS<"u"&&typeof CSS.supports=="function"?no=CSS.supports("touch-action","none"):no=!0),no}function oa(e){const{"aria-labelledby":t,defaultValue:n,disabled:r=!1,disableSwap:s=!1,isRtl:a=!1,marks:c=!1,max:i=100,min:l=0,name:m,onChange:p,onChangeCommitted:T,orientation:j="horizontal",rootRef:b,scale:P=ta,step:y=1,shiftStep:k=10,tabIndex:$,value:u}=e,f=g.useRef(void 0),[d,v]=g.useState(-1),[S,O]=g.useState(-1),[X,A]=g.useState(!1),I=g.useRef(0),W=g.useRef(null),[Y,w]=zo({controlled:u,default:n??l,name:"Slider"}),M=p&&((x,C,z)=>{const K=x.nativeEvent||x,ne=new K.constructor(K.type,K);Object.defineProperty(ne,"target",{writable:!0,value:{value:C,name:m}}),W.current=C,p(ne,C,z)}),ee=Array.isArray(Y);let E=ee?Y.slice().sort(An):[Y];E=E.map(x=>x==null?l:Et(x,l,i));const J=c===!0&&y!==null?[...Array(Math.floor((i-l)/y)+1)].map((x,C)=>({value:l+y*C})):c||[],V=J.map(x=>x.value),[pe,se]=g.useState(-1),H=g.useRef(null),G=Ut(b,H),Me=x=>C=>{var K;const z=Number(C.currentTarget.getAttribute("data-index"));po(C.target)&&se(z),O(z),(K=x==null?void 0:x.onFocus)==null||K.call(x,C)},xe=x=>C=>{var z;po(C.target)||se(-1),O(-1),(z=x==null?void 0:x.onBlur)==null||z.call(x,C)},q=(x,C)=>{const z=Number(x.currentTarget.getAttribute("data-index")),K=E[z],ne=V.indexOf(K);let N=C;if(J&&y==null){const ie=V[V.length-1];N>=ie?N=ie:N<=V[0]?N=V[0]:N=N<K?V[ne-1]:V[ne+1]}if(N=Et(N,l,i),ee){s&&(N=Et(N,E[z-1]||-1/0,E[z+1]||1/0));const ie=N;N=pn({values:E,newValue:N,index:z});let be=z;s||(be=N.indexOf(ie)),to({sliderRef:H,activeIndex:be})}w(N),se(z),M&&!oo(N,Y)&&M(x,N,z),T&&T(x,W.current??N)},te=x=>C=>{var z;if(["ArrowUp","ArrowDown","ArrowLeft","ArrowRight","PageUp","PageDown","Home","End"].includes(C.key)){C.preventDefault();const K=Number(C.currentTarget.getAttribute("data-index")),ne=E[K];let N=null;if(y!=null){const ie=C.shiftKey?k:y;switch(C.key){case"ArrowUp":N=Mt(ne,ie,1,l,i);break;case"ArrowRight":N=Mt(ne,ie,a?-1:1,l,i);break;case"ArrowDown":N=Mt(ne,ie,-1,l,i);break;case"ArrowLeft":N=Mt(ne,ie,a?1:-1,l,i);break;case"PageUp":N=Mt(ne,k,1,l,i);break;case"PageDown":N=Mt(ne,k,-1,l,i);break;case"Home":N=l;break;case"End":N=i;break}}else if(J){const ie=V[V.length-1],be=V.indexOf(ne),he=[a?"ArrowRight":"ArrowLeft","ArrowDown","PageDown","Home"],fe=[a?"ArrowLeft":"ArrowRight","ArrowUp","PageUp","End"];he.includes(C.key)?be===0?N=V[0]:N=V[be-1]:fe.includes(C.key)&&(be===V.length-1?N=ie:N=V[be+1])}N!=null&&q(C,N)}(z=x==null?void 0:x.onKeyDown)==null||z.call(x,C)};qt(()=>{var x;r&&H.current.contains(document.activeElement)&&((x=document.activeElement)==null||x.blur())},[r]),r&&d!==-1&&v(-1),r&&pe!==-1&&se(-1);const ue=x=>C=>{var z;(z=x.onChange)==null||z.call(x,C),q(C,C.target.valueAsNumber)},h=g.useRef(void 0);let L=j;a&&j==="horizontal"&&(L+="-reverse");const U=({finger:x,move:C=!1})=>{const{current:z}=H,{width:K,height:ne,bottom:N,left:ie}=z.getBoundingClientRect();let be;L.startsWith("vertical")?be=(N-x.y)/ne:be=(x.x-ie)/K,L.includes("-reverse")&&(be=1-be);let he;if(he=Qs(be,l,i),y)he=Zs(he,y,l);else{const Qe=cn(V,he);he=V[Qe]}he=Et(he,l,i);let fe=0;if(ee){C?fe=h.current:fe=cn(E,he),s&&(he=Et(he,E[fe-1]||-1/0,E[fe+1]||1/0));const Qe=he;he=pn({values:E,newValue:he,index:fe}),s&&C||(fe=he.indexOf(Qe),h.current=fe)}return{newValue:he,activeIndex:fe}},Z=ft(x=>{const C=eo(x,f);if(!C)return;if(I.current+=1,x.type==="mousemove"&&x.buttons===0){_(x);return}const{newValue:z,activeIndex:K}=U({finger:C,move:!0});to({sliderRef:H,activeIndex:K,setActive:v}),w(z),!X&&I.current>_s&&A(!0),M&&!oo(z,Y)&&M(x,z,K)}),_=ft(x=>{const C=eo(x,f);if(A(!1),!C)return;const{newValue:z}=U({finger:C,move:!0});v(-1),x.type==="touchend"&&O(-1),T&&T(x,W.current??z),f.current=void 0,Se()}),oe=ft(x=>{if(r)return;dn()||x.preventDefault();const C=x.changedTouches[0];C!=null&&(f.current=C.identifier);const z=eo(x,f);if(z!==!1){const{newValue:ne,activeIndex:N}=U({finger:z});to({sliderRef:H,activeIndex:N,setActive:v}),w(ne),M&&!oo(ne,Y)&&M(x,ne,N)}I.current=0;const K=kt(H.current);K.addEventListener("touchmove",Z,{passive:!0}),K.addEventListener("touchend",_,{passive:!0})}),Se=g.useCallback(()=>{const x=kt(H.current);x.removeEventListener("mousemove",Z),x.removeEventListener("mouseup",_),x.removeEventListener("touchmove",Z),x.removeEventListener("touchend",_)},[_,Z]);g.useEffect(()=>{const{current:x}=H;return x.addEventListener("touchstart",oe,{passive:dn()}),()=>{x.removeEventListener("touchstart",oe),Se()}},[Se,oe]),g.useEffect(()=>{r&&Se()},[r,Se]);const Ae=x=>C=>{var ne;if((ne=x.onMouseDown)==null||ne.call(x,C),r||C.defaultPrevented||C.button!==0)return;C.preventDefault();const z=eo(C,f);if(z!==!1){const{newValue:N,activeIndex:ie}=U({finger:z});to({sliderRef:H,activeIndex:ie,setActive:v}),w(N),M&&!oo(N,Y)&&M(C,N,ie)}I.current=0;const K=kt(H.current);K.addEventListener("mousemove",Z,{passive:!0}),K.addEventListener("mouseup",_)},ze=ho(ee?E[0]:l,l,i),ae=ho(E[E.length-1],l,i)-ze,Pe=(x={})=>{const C=bo(x),z={onMouseDown:Ae(C||{})},K={...C,...z};return{...x,ref:G,...K}},Le=x=>C=>{var K;(K=x.onMouseOver)==null||K.call(x,C);const z=Number(C.currentTarget.getAttribute("data-index"));O(z)},Be=x=>C=>{var z;(z=x.onMouseLeave)==null||z.call(x,C),O(-1)},Oe=(x={})=>{const C=bo(x),z={onMouseOver:Le(C||{}),onMouseLeave:Be(C||{})};return{...x,...C,...z}},Ve=x=>({pointerEvents:d!==-1&&d!==x?"none":void 0});let De;return j==="vertical"&&(De=a?"vertical-rl":"vertical-lr"),{active:d,axis:L,axisProps:ea,dragging:X,focusedThumbIndex:pe,getHiddenInputProps:(x={})=>{const C=bo(x),z={onChange:ue(C||{}),onFocus:Me(C||{}),onBlur:xe(C||{}),onKeyDown:te(C||{})},K={...C,...z};return{tabIndex:$,"aria-labelledby":t,"aria-orientation":j,"aria-valuemax":P(i),"aria-valuemin":P(l),name:m,type:"range",min:e.min,max:e.max,step:e.step===null&&e.marks?"any":e.step??void 0,disabled:r,...x,...K,style:{...Gs,direction:a?"rtl":"ltr",width:"100%",height:"100%",writingMode:De}}},getRootProps:Pe,getThumbProps:Oe,marks:J,open:S,range:ee,rootRef:G,trackLeap:ae,trackOffset:ze,values:E,getThumbStyle:Ve}}const na=e=>!e||!lo(e);function ra(e){return We("MuiSlider",e)}const Ze=Ie("MuiSlider",["root","active","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","disabled","dragging","focusVisible","mark","markActive","marked","markLabel","markLabelActive","rail","sizeSmall","thumb","thumbColorPrimary","thumbColorSecondary","thumbColorError","thumbColorSuccess","thumbColorInfo","thumbColorWarning","track","trackInverted","trackFalse","thumbSizeSmall","valueLabel","valueLabelOpen","valueLabelCircle","valueLabelLabel","vertical"]),sa=e=>{const{open:t}=e;return{offset:ve(t&&Ze.valueLabelOpen),circle:Ze.valueLabelCircle,label:Ze.valueLabelLabel}};function aa(e){const{children:t,className:n,value:r}=e,s=sa(e);return t?g.cloneElement(t,{className:t.props.className},o.jsxs(g.Fragment,{children:[t.props.children,o.jsx("span",{className:ve(s.offset,n),"aria-hidden":!0,children:o.jsx("span",{className:s.circle,children:o.jsx("span",{className:s.label,children:r})})})]})):null}function un(e){return e}const ia=re("span",{name:"MuiSlider",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t[`color${me(n.color)}`],n.size!=="medium"&&t[`size${me(n.size)}`],n.marked&&t.marked,n.orientation==="vertical"&&t.vertical,n.track==="inverted"&&t.trackInverted,n.track===!1&&t.trackFalse]}})(Te(({theme:e})=>({borderRadius:12,boxSizing:"content-box",display:"inline-block",position:"relative",cursor:"pointer",touchAction:"none",WebkitTapHighlightColor:"transparent","@media print":{colorAdjust:"exact"},[`&.${Ze.disabled}`]:{pointerEvents:"none",cursor:"default",color:(e.vars||e).palette.grey[400]},[`&.${Ze.dragging}`]:{[`& .${Ze.thumb}, & .${Ze.track}`]:{transition:"none"}},variants:[...Object.entries(e.palette).filter(go()).map(([t])=>({props:{color:t},style:{color:(e.vars||e).palette[t].main}})),{props:{orientation:"horizontal"},style:{height:4,width:"100%",padding:"13px 0","@media (pointer: coarse)":{padding:"20px 0"}}},{props:{orientation:"horizontal",size:"small"},style:{height:2}},{props:{orientation:"horizontal",marked:!0},style:{marginBottom:20}},{props:{orientation:"vertical"},style:{height:"100%",width:4,padding:"0 13px","@media (pointer: coarse)":{padding:"0 20px"}}},{props:{orientation:"vertical",size:"small"},style:{width:2}},{props:{orientation:"vertical",marked:!0},style:{marginRight:44}}]}))),la=re("span",{name:"MuiSlider",slot:"Rail"})({display:"block",position:"absolute",borderRadius:"inherit",backgroundColor:"currentColor",opacity:.38,variants:[{props:{orientation:"horizontal"},style:{width:"100%",height:"inherit",top:"50%",transform:"translateY(-50%)"}},{props:{orientation:"vertical"},style:{height:"100%",width:"inherit",left:"50%",transform:"translateX(-50%)"}},{props:{track:"inverted"},style:{opacity:1}}]}),ca=re("span",{name:"MuiSlider",slot:"Track"})(Te(({theme:e})=>({display:"block",position:"absolute",borderRadius:"inherit",border:"1px solid currentColor",backgroundColor:"currentColor",transition:e.transitions.create(["left","width","bottom","height"],{duration:e.transitions.duration.shortest}),variants:[{props:{size:"small"},style:{border:"none"}},{props:{orientation:"horizontal"},style:{height:"inherit",top:"50%",transform:"translateY(-50%)"}},{props:{orientation:"vertical"},style:{width:"inherit",left:"50%",transform:"translateX(-50%)"}},{props:{track:!1},style:{display:"none"}},...Object.entries(e.palette).filter(go()).map(([t])=>({props:{color:t,track:"inverted"},style:{...e.vars?{backgroundColor:e.vars.palette.Slider[`${t}Track`],borderColor:e.vars.palette.Slider[`${t}Track`]}:{backgroundColor:ko(e.palette[t].main,.62),borderColor:ko(e.palette[t].main,.62),...e.applyStyles("dark",{backgroundColor:To(e.palette[t].main,.5)}),...e.applyStyles("dark",{borderColor:To(e.palette[t].main,.5)})}}}))]}))),pa=re("span",{name:"MuiSlider",slot:"Thumb",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.thumb,t[`thumbColor${me(n.color)}`],n.size!=="medium"&&t[`thumbSize${me(n.size)}`]]}})(Te(({theme:e})=>({position:"absolute",width:20,height:20,boxSizing:"border-box",borderRadius:"50%",outline:0,backgroundColor:"currentColor",display:"flex",alignItems:"center",justifyContent:"center",transition:e.transitions.create(["box-shadow","left","bottom"],{duration:e.transitions.duration.shortest}),"&::before":{position:"absolute",content:'""',borderRadius:"inherit",width:"100%",height:"100%",boxShadow:(e.vars||e).shadows[2]},"&::after":{position:"absolute",content:'""',borderRadius:"50%",width:42,height:42,top:"50%",left:"50%",transform:"translate(-50%, -50%)"},[`&.${Ze.disabled}`]:{"&:hover":{boxShadow:"none"}},variants:[{props:{size:"small"},style:{width:12,height:12,"&::before":{boxShadow:"none"}}},{props:{orientation:"horizontal"},style:{top:"50%",transform:"translate(-50%, -50%)"}},{props:{orientation:"vertical"},style:{left:"50%",transform:"translate(-50%, 50%)"}},...Object.entries(e.palette).filter(go()).map(([t])=>({props:{color:t},style:{[`&:hover, &.${Ze.focusVisible}`]:{...e.vars?{boxShadow:`0px 0px 0px 8px rgba(${e.vars.palette[t].mainChannel} / 0.16)`}:{boxShadow:`0px 0px 0px 8px ${st(e.palette[t].main,.16)}`},"@media (hover: none)":{boxShadow:"none"}},[`&.${Ze.active}`]:{...e.vars?{boxShadow:`0px 0px 0px 14px rgba(${e.vars.palette[t].mainChannel} / 0.16)`}:{boxShadow:`0px 0px 0px 14px ${st(e.palette[t].main,.16)}`}}}}))]}))),da=re(aa,{name:"MuiSlider",slot:"ValueLabel"})(Te(({theme:e})=>({zIndex:1,whiteSpace:"nowrap",...e.typography.body2,fontWeight:500,transition:e.transitions.create(["transform"],{duration:e.transitions.duration.shortest}),position:"absolute",backgroundColor:(e.vars||e).palette.grey[600],borderRadius:2,color:(e.vars||e).palette.common.white,display:"flex",alignItems:"center",justifyContent:"center",padding:"0.25rem 0.75rem",variants:[{props:{orientation:"horizontal"},style:{transform:"translateY(-100%) scale(0)",top:"-10px",transformOrigin:"bottom center","&::before":{position:"absolute",content:'""',width:8,height:8,transform:"translate(-50%, 50%) rotate(45deg)",backgroundColor:"inherit",bottom:0,left:"50%"},[`&.${Ze.valueLabelOpen}`]:{transform:"translateY(-100%) scale(1)"}}},{props:{orientation:"vertical"},style:{transform:"translateY(-50%) scale(0)",right:"30px",top:"50%",transformOrigin:"right center","&::before":{position:"absolute",content:'""',width:8,height:8,transform:"translate(-50%, -50%) rotate(45deg)",backgroundColor:"inherit",right:-8,top:"50%"},[`&.${Ze.valueLabelOpen}`]:{transform:"translateY(-50%) scale(1)"}}},{props:{size:"small"},style:{fontSize:e.typography.pxToRem(12),padding:"0.25rem 0.5rem"}},{props:{orientation:"vertical",size:"small"},style:{right:"20px"}}]}))),ua=re("span",{name:"MuiSlider",slot:"Mark",shouldForwardProp:e=>wn(e)&&e!=="markActive",overridesResolver:(e,t)=>{const{markActive:n}=e;return[t.mark,n&&t.markActive]}})(Te(({theme:e})=>({position:"absolute",width:2,height:2,borderRadius:1,backgroundColor:"currentColor",variants:[{props:{orientation:"horizontal"},style:{top:"50%",transform:"translate(-1px, -50%)"}},{props:{orientation:"vertical"},style:{left:"50%",transform:"translate(-50%, 1px)"}},{props:{markActive:!0},style:{backgroundColor:(e.vars||e).palette.background.paper,opacity:.8}}]}))),ha=re("span",{name:"MuiSlider",slot:"MarkLabel",shouldForwardProp:e=>wn(e)&&e!=="markLabelActive"})(Te(({theme:e})=>({...e.typography.body2,color:(e.vars||e).palette.text.secondary,position:"absolute",whiteSpace:"nowrap",variants:[{props:{orientation:"horizontal"},style:{top:30,transform:"translateX(-50%)","@media (pointer: coarse)":{top:40}}},{props:{orientation:"vertical"},style:{left:36,transform:"translateY(50%)","@media (pointer: coarse)":{left:44}}},{props:{markLabelActive:!0},style:{color:(e.vars||e).palette.text.primary}}]}))),fa=e=>{const{disabled:t,dragging:n,marked:r,orientation:s,track:a,classes:c,color:i,size:l}=e,m={root:["root",t&&"disabled",n&&"dragging",r&&"marked",s==="vertical"&&"vertical",a==="inverted"&&"trackInverted",a===!1&&"trackFalse",i&&`color${me(i)}`,l&&`size${me(l)}`],rail:["rail"],track:["track"],mark:["mark"],markActive:["markActive"],markLabel:["markLabel"],markLabelActive:["markLabelActive"],valueLabel:["valueLabel"],thumb:["thumb",t&&"disabled",l&&`thumbSize${me(l)}`,i&&`thumbColor${me(i)}`],active:["active"],disabled:["disabled"],focusVisible:["focusVisible"]};return Fe(m,ra,c)},ma=({children:e})=>e,jo=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiSlider"}),s=Gt(),{"aria-label":a,"aria-valuetext":c,"aria-labelledby":i,component:l="span",components:m={},componentsProps:p={},color:T="primary",classes:j,className:b,disableSwap:P=!1,disabled:y=!1,getAriaLabel:k,getAriaValueText:$,marks:u=!1,max:f=100,min:d=0,name:v,onChange:S,onChangeCommitted:O,orientation:X="horizontal",shiftStep:A=10,size:I="medium",step:W=1,scale:Y=un,slotProps:w,slots:M,tabIndex:ee,track:E="normal",value:J,valueLabelDisplay:V="off",valueLabelFormat:pe=un,...se}=r,H={...r,isRtl:s,max:f,min:d,classes:j,disabled:y,disableSwap:P,orientation:X,marks:u,color:T,size:I,step:W,shiftStep:A,scale:Y,track:E,valueLabelDisplay:V,valueLabelFormat:pe},{axisProps:G,getRootProps:Me,getHiddenInputProps:xe,getThumbProps:q,open:te,active:ue,axis:h,focusedThumbIndex:L,range:U,dragging:Z,marks:_,values:oe,trackOffset:Se,trackLeap:Ae,getThumbStyle:ze}=oa({...H,rootRef:n});H.marked=_.length>0&&_.some(ge=>ge.label),H.dragging=Z,H.focusedThumbIndex=L;const ae=fa(H),Pe=(M==null?void 0:M.root)??m.Root??ia,Le=(M==null?void 0:M.rail)??m.Rail??la,Be=(M==null?void 0:M.track)??m.Track??ca,Oe=(M==null?void 0:M.thumb)??m.Thumb??pa,Ve=(M==null?void 0:M.valueLabel)??m.ValueLabel??da,De=(M==null?void 0:M.mark)??m.Mark??ua,Xe=(M==null?void 0:M.markLabel)??m.MarkLabel??ha,x=(M==null?void 0:M.input)??m.Input??"input",C=(w==null?void 0:w.root)??p.root,z=(w==null?void 0:w.rail)??p.rail,K=(w==null?void 0:w.track)??p.track,ne=(w==null?void 0:w.thumb)??p.thumb,N=(w==null?void 0:w.valueLabel)??p.valueLabel,ie=(w==null?void 0:w.mark)??p.mark,be=(w==null?void 0:w.markLabel)??p.markLabel,he=(w==null?void 0:w.input)??p.input,fe=Ke({elementType:Pe,getSlotProps:Me,externalSlotProps:C,externalForwardedProps:se,additionalProps:{...na(Pe)&&{as:l}},ownerState:{...H,...C==null?void 0:C.ownerState},className:[ae.root,b]}),Qe=Ke({elementType:Le,externalSlotProps:z,ownerState:H,className:ae.rail}),gt=Ke({elementType:Be,externalSlotProps:K,additionalProps:{style:{...G[h].offset(Se),...G[h].leap(Ae)}},ownerState:{...H,...K==null?void 0:K.ownerState},className:ae.track}),vt=Ke({elementType:Oe,getSlotProps:q,externalSlotProps:ne,ownerState:{...H,...ne==null?void 0:ne.ownerState},className:ae.thumb}),lt=Ke({elementType:Ve,externalSlotProps:N,ownerState:{...H,...N==null?void 0:N.ownerState},className:ae.valueLabel}),nt=Ke({elementType:De,externalSlotProps:ie,ownerState:H,className:ae.mark}),xt=Ke({elementType:Xe,externalSlotProps:be,ownerState:H,className:ae.markLabel}),It=Ke({elementType:x,getSlotProps:xe,externalSlotProps:he,ownerState:H});return o.jsxs(Pe,{...fe,children:[o.jsx(Le,{...Qe}),o.jsx(Be,{...gt}),_.filter(ge=>ge.value>=d&&ge.value<=f).map((ge,je)=>{const bt=ho(ge.value,d,f),ut=G[h].offset(bt);let Ye;return E===!1?Ye=oe.includes(ge.value):Ye=E==="normal"&&(U?ge.value>=oe[0]&&ge.value<=oe[oe.length-1]:ge.value<=oe[0])||E==="inverted"&&(U?ge.value<=oe[0]||ge.value>=oe[oe.length-1]:ge.value>=oe[0]),o.jsxs(g.Fragment,{children:[o.jsx(De,{"data-index":je,...nt,...!lo(De)&&{markActive:Ye},style:{...ut,...nt.style},className:ve(nt.className,Ye&&ae.markActive)}),ge.label!=null?o.jsx(Xe,{"aria-hidden":!0,"data-index":je,...xt,...!lo(Xe)&&{markLabelActive:Ye},style:{...ut,...xt.style},className:ve(ae.markLabel,xt.className,Ye&&ae.markLabelActive),children:ge.label}):null]},je)}),oe.map((ge,je)=>{const bt=ho(ge,d,f),ut=G[h].offset(bt),Ye=V==="off"?ma:Ve;return o.jsx(Ye,{...!lo(Ye)&&{valueLabelFormat:pe,valueLabelDisplay:V,value:typeof pe=="function"?pe(Y(ge),je):pe,index:je,open:te===je||ue===je||V==="on",disabled:y},...lt,children:o.jsx(Oe,{"data-index":je,...vt,className:ve(ae.thumb,vt.className,ue===je&&ae.active,L===je&&ae.focusVisible),style:{...ut,...ze(je),...vt.style},children:o.jsx(x,{"data-index":je,"aria-label":k?k(je):a,"aria-valuenow":Y(ge),"aria-labelledby":i,"aria-valuetext":$?$(Y(ge),je):c,value:oe[je],...It})})},je)})]})});function ga(e){return We("MuiTooltip",e)}const we=Ie("MuiTooltip",["popper","popperInteractive","popperArrow","popperClose","tooltip","tooltipArrow","touch","tooltipPlacementLeft","tooltipPlacementRight","tooltipPlacementTop","tooltipPlacementBottom","arrow"]);function va(e){return Math.round(e*1e5)/1e5}const xa=e=>{const{classes:t,disableInteractive:n,arrow:r,touch:s,placement:a}=e,c={popper:["popper",!n&&"popperInteractive",r&&"popperArrow"],tooltip:["tooltip",r&&"tooltipArrow",s&&"touch",`tooltipPlacement${me(a.split("-")[0])}`],arrow:["arrow"]};return Fe(c,ga,t)},ba=re(Ln,{name:"MuiTooltip",slot:"Popper",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.popper,!n.disableInteractive&&t.popperInteractive,n.arrow&&t.popperArrow,!n.open&&t.popperClose]}})(Te(({theme:e})=>({zIndex:(e.vars||e).zIndex.tooltip,pointerEvents:"none",variants:[{props:({ownerState:t})=>!t.disableInteractive,style:{pointerEvents:"auto"}},{props:({open:t})=>!t,style:{pointerEvents:"none"}},{props:({ownerState:t})=>t.arrow,style:{[`&[data-popper-placement*="bottom"] .${we.arrow}`]:{top:0,marginTop:"-0.71em","&::before":{transformOrigin:"0 100%"}},[`&[data-popper-placement*="top"] .${we.arrow}`]:{bottom:0,marginBottom:"-0.71em","&::before":{transformOrigin:"100% 0"}},[`&[data-popper-placement*="right"] .${we.arrow}`]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"100% 100%"}},[`&[data-popper-placement*="left"] .${we.arrow}`]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"0 0"}}}},{props:({ownerState:t})=>t.arrow&&!t.isRtl,style:{[`&[data-popper-placement*="right"] .${we.arrow}`]:{left:0,marginLeft:"-0.71em"}}},{props:({ownerState:t})=>t.arrow&&!!t.isRtl,style:{[`&[data-popper-placement*="right"] .${we.arrow}`]:{right:0,marginRight:"-0.71em"}}},{props:({ownerState:t})=>t.arrow&&!t.isRtl,style:{[`&[data-popper-placement*="left"] .${we.arrow}`]:{right:0,marginRight:"-0.71em"}}},{props:({ownerState:t})=>t.arrow&&!!t.isRtl,style:{[`&[data-popper-placement*="left"] .${we.arrow}`]:{left:0,marginLeft:"-0.71em"}}}]}))),ya=re("div",{name:"MuiTooltip",slot:"Tooltip",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.tooltip,n.touch&&t.touch,n.arrow&&t.tooltipArrow,t[`tooltipPlacement${me(n.placement.split("-")[0])}`]]}})(Te(({theme:e})=>({backgroundColor:e.vars?e.vars.palette.Tooltip.bg:st(e.palette.grey[700],.92),borderRadius:(e.vars||e).shape.borderRadius,color:(e.vars||e).palette.common.white,fontFamily:e.typography.fontFamily,padding:"4px 8px",fontSize:e.typography.pxToRem(11),maxWidth:300,margin:2,wordWrap:"break-word",fontWeight:e.typography.fontWeightMedium,[`.${we.popper}[data-popper-placement*="left"] &`]:{transformOrigin:"right center"},[`.${we.popper}[data-popper-placement*="right"] &`]:{transformOrigin:"left center"},[`.${we.popper}[data-popper-placement*="top"] &`]:{transformOrigin:"center bottom",marginBottom:"14px"},[`.${we.popper}[data-popper-placement*="bottom"] &`]:{transformOrigin:"center top",marginTop:"14px"},variants:[{props:({ownerState:t})=>t.arrow,style:{position:"relative",margin:0}},{props:({ownerState:t})=>t.touch,style:{padding:"8px 16px",fontSize:e.typography.pxToRem(14),lineHeight:`${va(16/14)}em`,fontWeight:e.typography.fontWeightRegular}},{props:({ownerState:t})=>!t.isRtl,style:{[`.${we.popper}[data-popper-placement*="left"] &`]:{marginRight:"14px"},[`.${we.popper}[data-popper-placement*="right"] &`]:{marginLeft:"14px"}}},{props:({ownerState:t})=>!t.isRtl&&t.touch,style:{[`.${we.popper}[data-popper-placement*="left"] &`]:{marginRight:"24px"},[`.${we.popper}[data-popper-placement*="right"] &`]:{marginLeft:"24px"}}},{props:({ownerState:t})=>!!t.isRtl,style:{[`.${we.popper}[data-popper-placement*="left"] &`]:{marginLeft:"14px"},[`.${we.popper}[data-popper-placement*="right"] &`]:{marginRight:"14px"}}},{props:({ownerState:t})=>!!t.isRtl&&t.touch,style:{[`.${we.popper}[data-popper-placement*="left"] &`]:{marginLeft:"24px"},[`.${we.popper}[data-popper-placement*="right"] &`]:{marginRight:"24px"}}},{props:({ownerState:t})=>t.touch,style:{[`.${we.popper}[data-popper-placement*="top"] &`]:{marginBottom:"24px"}}},{props:({ownerState:t})=>t.touch,style:{[`.${we.popper}[data-popper-placement*="bottom"] &`]:{marginTop:"24px"}}}]}))),wa=re("span",{name:"MuiTooltip",slot:"Arrow"})(Te(({theme:e})=>({overflow:"hidden",position:"absolute",width:"1em",height:"0.71em",boxSizing:"border-box",color:e.vars?e.vars.palette.Tooltip.bg:st(e.palette.grey[700],.9),"&::before":{content:'""',margin:"auto",display:"block",width:"100%",height:"100%",backgroundColor:"currentColor",transform:"rotate(45deg)"}})));let ro=!1;const hn=new Dn;let Dt={x:0,y:0};function so(e,t){return(n,...r)=>{t&&t(n,...r),e(n,...r)}}const fn=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiTooltip"}),{arrow:s=!1,children:a,classes:c,components:i={},componentsProps:l={},describeChild:m=!1,disableFocusListener:p=!1,disableHoverListener:T=!1,disableInteractive:j=!1,disableTouchListener:b=!1,enterDelay:P=100,enterNextDelay:y=0,enterTouchDelay:k=700,followCursor:$=!1,id:u,leaveDelay:f=0,leaveTouchDelay:d=1500,onClose:v,onOpen:S,open:O,placement:X="bottom",PopperComponent:A,PopperProps:I={},slotProps:W={},slots:Y={},title:w,TransitionComponent:M,TransitionProps:ee,...E}=r,J=g.isValidElement(a)?a:o.jsx("span",{children:a}),V=Ao(),pe=Gt(),[se,H]=g.useState(),[G,Me]=g.useState(null),xe=g.useRef(!1),q=j||$,te=Qt(),ue=Qt(),h=Qt(),L=Qt(),[U,Z]=zo({controlled:O,default:!1,name:"Tooltip",state:"open"});let _=U;const oe=yn(u),Se=g.useRef(),Ae=ft(()=>{Se.current!==void 0&&(document.body.style.WebkitUserSelect=Se.current,Se.current=void 0),L.clear()});g.useEffect(()=>Ae,[Ae]);const ze=Q=>{hn.clear(),ro=!0,Z(!0),S&&!_&&S(Q)},ae=ft(Q=>{hn.start(800+f,()=>{ro=!1}),Z(!1),v&&_&&v(Q),te.start(V.transitions.duration.shortest,()=>{xe.current=!1})}),Pe=Q=>{xe.current&&Q.type!=="touchstart"||(se&&se.removeAttribute("title"),ue.clear(),h.clear(),P||ro&&y?ue.start(ro?y:P,()=>{ze(Q)}):ze(Q))},Le=Q=>{ue.clear(),h.start(f,()=>{ae(Q)})},[,Be]=g.useState(!1),Oe=Q=>{po(Q.target)||(Be(!1),Le(Q))},Ve=Q=>{se||H(Q.currentTarget),po(Q.target)&&(Be(!0),Pe(Q))},De=Q=>{xe.current=!0;const Ge=J.props;Ge.onTouchStart&&Ge.onTouchStart(Q)},Xe=Q=>{De(Q),h.clear(),te.clear(),Ae(),Se.current=document.body.style.WebkitUserSelect,document.body.style.WebkitUserSelect="none",L.start(k,()=>{document.body.style.WebkitUserSelect=Se.current,Pe(Q)})},x=Q=>{J.props.onTouchEnd&&J.props.onTouchEnd(Q),Ae(),h.start(d,()=>{ae(Q)})};g.useEffect(()=>{if(!_)return;function Q(Ge){Ge.key==="Escape"&&ae(Ge)}return document.addEventListener("keydown",Q),()=>{document.removeEventListener("keydown",Q)}},[ae,_]);const C=Ut(Kn(J),H,n);!w&&w!==0&&(_=!1);const z=g.useRef(),K=Q=>{const Ge=J.props;Ge.onMouseMove&&Ge.onMouseMove(Q),Dt={x:Q.clientX,y:Q.clientY},z.current&&z.current.update()},ne={},N=typeof w=="string";m?(ne.title=!_&&N&&!T?w:null,ne["aria-describedby"]=_?oe:null):(ne["aria-label"]=N?w:null,ne["aria-labelledby"]=_&&!N?oe:null);const ie={...ne,...E,...J.props,className:ve(E.className,J.props.className),onTouchStart:De,ref:C,...$?{onMouseMove:K}:{}},be={};b||(ie.onTouchStart=Xe,ie.onTouchEnd=x),T||(ie.onMouseOver=so(Pe,ie.onMouseOver),ie.onMouseLeave=so(Le,ie.onMouseLeave),q||(be.onMouseOver=Pe,be.onMouseLeave=Le)),p||(ie.onFocus=so(Ve,ie.onFocus),ie.onBlur=so(Oe,ie.onBlur),q||(be.onFocus=Ve,be.onBlur=Oe));const he={...r,isRtl:pe,arrow:s,disableInteractive:q,placement:X,PopperComponentProp:A,touch:xe.current},fe=typeof W.popper=="function"?W.popper(he):W.popper,Qe=g.useMemo(()=>{var Ge,B;let Q=[{name:"arrow",enabled:!!G,options:{element:G,padding:4}}];return(Ge=I.popperOptions)!=null&&Ge.modifiers&&(Q=Q.concat(I.popperOptions.modifiers)),(B=fe==null?void 0:fe.popperOptions)!=null&&B.modifiers&&(Q=Q.concat(fe.popperOptions.modifiers)),{...I.popperOptions,...fe==null?void 0:fe.popperOptions,modifiers:Q}},[G,I.popperOptions,fe==null?void 0:fe.popperOptions]),gt=xa(he),vt=typeof W.transition=="function"?W.transition(he):W.transition,lt={slots:{popper:i.Popper,transition:i.Transition??M,tooltip:i.Tooltip,arrow:i.Arrow,...Y},slotProps:{arrow:W.arrow??l.arrow,popper:{...I,...fe??l.popper},tooltip:W.tooltip??l.tooltip,transition:{...ee,...vt??l.transition}}},[nt,xt]=ke("popper",{elementType:ba,externalForwardedProps:lt,ownerState:he,className:ve(gt.popper,I==null?void 0:I.className)}),[It,ge]=ke("transition",{elementType:_n,externalForwardedProps:lt,ownerState:he}),[je,bt]=ke("tooltip",{elementType:ya,className:gt.tooltip,externalForwardedProps:lt,ownerState:he}),[ut,Ye]=ke("arrow",{elementType:wa,className:gt.arrow,externalForwardedProps:lt,ownerState:he,ref:Me});return o.jsxs(g.Fragment,{children:[g.cloneElement(J,ie),o.jsx(nt,{as:A??Ln,placement:X,anchorEl:$?{getBoundingClientRect:()=>({top:Dt.y,left:Dt.x,right:Dt.x,bottom:Dt.y,width:0,height:0})}:se,popperRef:z,open:se?_:!1,id:oe,transition:!0,...be,...xt,popperOptions:Qe,children:({TransitionProps:Q})=>o.jsx(It,{timeout:V.transitions.duration.shorter,...Q,...ge,children:o.jsxs(je,{...bt,children:[w,s?o.jsx(ut,{...Ye}):null]})})})]})});function Sa(e){return We("MuiSwitch",e)}const Ne=Ie("MuiSwitch",["root","edgeStart","edgeEnd","switchBase","colorPrimary","colorSecondary","sizeSmall","sizeMedium","checked","disabled","input","thumb","track"]),ja=e=>{const{classes:t,edge:n,size:r,color:s,checked:a,disabled:c}=e,i={root:["root",n&&`edge${me(n)}`,`size${me(r)}`],switchBase:["switchBase",`color${me(s)}`,a&&"checked",c&&"disabled"],thumb:["thumb"],track:["track"],input:["input"]},l=Fe(i,Sa,t);return{...t,...l}},Ca=re("span",{name:"MuiSwitch",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.edge&&t[`edge${me(n.edge)}`],t[`size${me(n.size)}`]]}})({display:"inline-flex",width:34+12*2,height:14+12*2,overflow:"hidden",padding:12,boxSizing:"border-box",position:"relative",flexShrink:0,zIndex:0,verticalAlign:"middle","@media print":{colorAdjust:"exact"},variants:[{props:{edge:"start"},style:{marginLeft:-8}},{props:{edge:"end"},style:{marginRight:-8}},{props:{size:"small"},style:{width:40,height:24,padding:7,[`& .${Ne.thumb}`]:{width:16,height:16},[`& .${Ne.switchBase}`]:{padding:4,[`&.${Ne.checked}`]:{transform:"translateX(16px)"}}}}]}),ka=re(ys,{name:"MuiSwitch",slot:"SwitchBase",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.switchBase,{[`& .${Ne.input}`]:t.input},n.color!=="default"&&t[`color${me(n.color)}`]]}})(Te(({theme:e})=>({position:"absolute",top:0,left:0,zIndex:1,color:e.vars?e.vars.palette.Switch.defaultColor:`${e.palette.mode==="light"?e.palette.common.white:e.palette.grey[300]}`,transition:e.transitions.create(["left","transform"],{duration:e.transitions.duration.shortest}),[`&.${Ne.checked}`]:{transform:"translateX(20px)"},[`&.${Ne.disabled}`]:{color:e.vars?e.vars.palette.Switch.defaultDisabledColor:`${e.palette.mode==="light"?e.palette.grey[100]:e.palette.grey[600]}`},[`&.${Ne.checked} + .${Ne.track}`]:{opacity:.5},[`&.${Ne.disabled} + .${Ne.track}`]:{opacity:e.vars?e.vars.opacity.switchTrackDisabled:`${e.palette.mode==="light"?.12:.2}`},[`& .${Ne.input}`]:{left:"-100%",width:"300%"}})),Te(({theme:e})=>({"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette.action.activeChannel} / ${e.vars.palette.action.hoverOpacity})`:st(e.palette.action.active,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},variants:[...Object.entries(e.palette).filter(go(["light"])).map(([t])=>({props:{color:t},style:{[`&.${Ne.checked}`]:{color:(e.vars||e).palette[t].main,"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette[t].mainChannel} / ${e.vars.palette.action.hoverOpacity})`:st(e.palette[t].main,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${Ne.disabled}`]:{color:e.vars?e.vars.palette.Switch[`${t}DisabledColor`]:`${e.palette.mode==="light"?ko(e.palette[t].main,.62):To(e.palette[t].main,.55)}`}},[`&.${Ne.checked} + .${Ne.track}`]:{backgroundColor:(e.vars||e).palette[t].main}}}))]}))),Ta=re("span",{name:"MuiSwitch",slot:"Track"})(Te(({theme:e})=>({height:"100%",width:"100%",borderRadius:14/2,zIndex:-1,transition:e.transitions.create(["opacity","background-color"],{duration:e.transitions.duration.shortest}),backgroundColor:e.vars?e.vars.palette.common.onBackground:`${e.palette.mode==="light"?e.palette.common.black:e.palette.common.white}`,opacity:e.vars?e.vars.opacity.switchTrack:`${e.palette.mode==="light"?.38:.3}`}))),Pa=re("span",{name:"MuiSwitch",slot:"Thumb"})(Te(({theme:e})=>({boxShadow:(e.vars||e).shadows[1],backgroundColor:"currentColor",width:20,height:20,borderRadius:"50%"}))),pt=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiSwitch"}),{className:s,color:a="primary",edge:c=!1,size:i="medium",sx:l,slots:m={},slotProps:p={},...T}=r,j={...r,color:a,edge:c,size:i},b=ja(j),P={slots:m,slotProps:p},[y,k]=ke("root",{className:ve(b.root,s),elementType:Ca,externalForwardedProps:P,ownerState:j,additionalProps:{sx:l}}),[$,u]=ke("thumb",{className:b.thumb,elementType:Pa,externalForwardedProps:P,ownerState:j}),f=o.jsx($,{...u}),[d,v]=ke("track",{className:b.track,elementType:Ta,externalForwardedProps:P,ownerState:j});return o.jsxs(y,{...k,children:[o.jsx(ka,{type:"checkbox",icon:f,checkedIcon:f,ref:n,ownerState:j,...T,classes:{...b,root:b.switchBase},slots:{...m.switchBase&&{root:m.switchBase},...m.input&&{input:m.input}},slotProps:{...p.switchBase&&{root:typeof p.switchBase=="function"?p.switchBase(j):p.switchBase},...p.input&&{input:typeof p.input=="function"?p.input(j):p.input}}}),o.jsx(d,{...v})]})});function Ma(e){return We("MuiTab",e)}const Je=Ie("MuiTab",["root","labelIcon","textColorInherit","textColorPrimary","textColorSecondary","selected","disabled","fullWidth","wrapped","iconWrapper","icon"]),Ra=e=>{const{classes:t,textColor:n,fullWidth:r,wrapped:s,icon:a,label:c,selected:i,disabled:l}=e,m={root:["root",a&&c&&"labelIcon",`textColor${me(n)}`,r&&"fullWidth",s&&"wrapped",i&&"selected",l&&"disabled"],icon:["iconWrapper","icon"]};return Fe(m,Ma,t)},$a=re(mo,{name:"MuiTab",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.label&&n.icon&&t.labelIcon,t[`textColor${me(n.textColor)}`],n.fullWidth&&t.fullWidth,n.wrapped&&t.wrapped,{[`& .${Je.iconWrapper}`]:t.iconWrapper},{[`& .${Je.icon}`]:t.icon}]}})(Te(({theme:e})=>({...e.typography.button,maxWidth:360,minWidth:90,position:"relative",minHeight:48,flexShrink:0,padding:"12px 16px",overflow:"hidden",whiteSpace:"normal",textAlign:"center",lineHeight:1.25,variants:[{props:({ownerState:t})=>t.label&&(t.iconPosition==="top"||t.iconPosition==="bottom"),style:{flexDirection:"column"}},{props:({ownerState:t})=>t.label&&t.iconPosition!=="top"&&t.iconPosition!=="bottom",style:{flexDirection:"row"}},{props:({ownerState:t})=>t.icon&&t.label,style:{minHeight:72,paddingTop:9,paddingBottom:9}},{props:({ownerState:t,iconPosition:n})=>t.icon&&t.label&&n==="top",style:{[`& > .${Je.icon}`]:{marginBottom:6}}},{props:({ownerState:t,iconPosition:n})=>t.icon&&t.label&&n==="bottom",style:{[`& > .${Je.icon}`]:{marginTop:6}}},{props:({ownerState:t,iconPosition:n})=>t.icon&&t.label&&n==="start",style:{[`& > .${Je.icon}`]:{marginRight:e.spacing(1)}}},{props:({ownerState:t,iconPosition:n})=>t.icon&&t.label&&n==="end",style:{[`& > .${Je.icon}`]:{marginLeft:e.spacing(1)}}},{props:{textColor:"inherit"},style:{color:"inherit",opacity:.6,[`&.${Je.selected}`]:{opacity:1},[`&.${Je.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity}}},{props:{textColor:"primary"},style:{color:(e.vars||e).palette.text.secondary,[`&.${Je.selected}`]:{color:(e.vars||e).palette.primary.main},[`&.${Je.disabled}`]:{color:(e.vars||e).palette.text.disabled}}},{props:{textColor:"secondary"},style:{color:(e.vars||e).palette.text.secondary,[`&.${Je.selected}`]:{color:(e.vars||e).palette.secondary.main},[`&.${Je.disabled}`]:{color:(e.vars||e).palette.text.disabled}}},{props:({ownerState:t})=>t.fullWidth,style:{flexShrink:1,flexGrow:1,flexBasis:0,maxWidth:"none"}},{props:({ownerState:t})=>t.wrapped,style:{fontSize:e.typography.pxToRem(12)}}]}))),St=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiTab"}),{className:s,disabled:a=!1,disableFocusRipple:c=!1,fullWidth:i,icon:l,iconPosition:m="top",indicator:p,label:T,onChange:j,onClick:b,onFocus:P,selected:y,selectionFollowsFocus:k,textColor:$="inherit",value:u,wrapped:f=!1,...d}=r,v={...r,disabled:a,disableFocusRipple:c,selected:y,icon:!!l,iconPosition:m,label:!!T,fullWidth:i,textColor:$,wrapped:f},S=Ra(v),O=l&&T&&g.isValidElement(l)?g.cloneElement(l,{className:ve(S.icon,l.props.className)}):l,X=I=>{!y&&j&&j(I,u),b&&b(I)},A=I=>{k&&!y&&j&&j(I,u),P&&P(I)};return o.jsxs($a,{focusRipple:!c,className:ve(S.root,s),ref:n,role:"tab","aria-selected":y,disabled:a,onClick:X,onFocus:A,ownerState:v,tabIndex:y?0:-1,...d,children:[m==="top"||m==="start"?o.jsxs(g.Fragment,{children:[O,T]}):o.jsxs(g.Fragment,{children:[T,O]}),p]})}),Ba=Ee(o.jsx("path",{d:"M15.41 16.09l-4.58-4.59 4.58-4.59L14 5.5l-6 6 6 6z"})),Oa=Ee(o.jsx("path",{d:"M8.59 16.34l4.58-4.59-4.58-4.59L10 5.75l6 6-6 6z"}));function Ia(e){return(1+Math.sin(Math.PI*e-Math.PI/2))/2}function La(e,t,n,r={},s=()=>{}){const{ease:a=Ia,duration:c=300}=r;let i=null;const l=t[e];let m=!1;const p=()=>{m=!0},T=j=>{if(m){s(new Error("Animation cancelled"));return}i===null&&(i=j);const b=Math.min(1,(j-i)/c);if(t[e]=a(b)*(n-l)+l,b>=1){requestAnimationFrame(()=>{s(null)});return}requestAnimationFrame(T)};return l===n?(s(new Error("Element already at target position")),p):(requestAnimationFrame(T),p)}const Ea={width:99,height:99,position:"absolute",top:-9999,overflow:"scroll"};function Aa(e){const{onChange:t,...n}=e,r=g.useRef(),s=g.useRef(null),a=()=>{r.current=s.current.offsetHeight-s.current.clientHeight};return qt(()=>{const c=jn(()=>{const l=r.current;a(),l!==r.current&&t(r.current)}),i=Cn(s.current);return i.addEventListener("resize",c),()=>{c.clear(),i.removeEventListener("resize",c)}},[t]),g.useEffect(()=>{a(),t(r.current)},[t]),o.jsx("div",{style:Ea,...n,ref:s})}function za(e){return We("MuiTabScrollButton",e)}const Da=Ie("MuiTabScrollButton",["root","vertical","horizontal","disabled"]),Na=e=>{const{classes:t,orientation:n,disabled:r}=e;return Fe({root:["root",n,r&&"disabled"]},za,t)},Wa=re(mo,{name:"MuiTabScrollButton",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.orientation&&t[n.orientation]]}})({width:40,flexShrink:0,opacity:.8,[`&.${Da.disabled}`]:{opacity:0},variants:[{props:{orientation:"vertical"},style:{width:"100%",height:40,"& svg":{transform:"var(--TabScrollButton-svgRotate)"}}}]}),Fa=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiTabScrollButton"}),{className:s,slots:a={},slotProps:c={},direction:i,orientation:l,disabled:m,...p}=r,T=Gt(),j={isRtl:T,...r},b=Na(j),P=a.StartScrollButtonIcon??Ba,y=a.EndScrollButtonIcon??Oa,k=Ke({elementType:P,externalSlotProps:c.startScrollButtonIcon,additionalProps:{fontSize:"small"},ownerState:j}),$=Ke({elementType:y,externalSlotProps:c.endScrollButtonIcon,additionalProps:{fontSize:"small"},ownerState:j});return o.jsx(Wa,{component:"div",className:ve(b.root,s),ref:n,role:null,ownerState:j,tabIndex:null,...p,style:{...p.style,...l==="vertical"&&{"--TabScrollButton-svgRotate":`rotate(${T?-90:90}deg)`}},children:i==="left"?o.jsx(P,{...k}):o.jsx(y,{...$})})});function Va(e){return We("MuiTabs",e)}const Co=Ie("MuiTabs",["root","vertical","list","flexContainer","flexContainerVertical","centered","scroller","fixed","scrollableX","scrollableY","hideScrollbar","scrollButtons","scrollButtonsHideMobile","indicator"]),mn=(e,t)=>e===t?e.firstChild:t&&t.nextElementSibling?t.nextElementSibling:e.firstChild,gn=(e,t)=>e===t?e.lastChild:t&&t.previousElementSibling?t.previousElementSibling:e.lastChild,ao=(e,t,n)=>{let r=!1,s=n(e,t);for(;s;){if(s===e.firstChild){if(r)return;r=!0}const a=s.disabled||s.getAttribute("aria-disabled")==="true";if(!s.hasAttribute("tabindex")||a)s=n(e,s);else{s.focus();return}}},Ha=e=>{const{vertical:t,fixed:n,hideScrollbar:r,scrollableX:s,scrollableY:a,centered:c,scrollButtonsHideMobile:i,classes:l}=e;return Fe({root:["root",t&&"vertical"],scroller:["scroller",n&&"fixed",r&&"hideScrollbar",s&&"scrollableX",a&&"scrollableY"],list:["list","flexContainer",t&&"flexContainerVertical",t&&"vertical",c&&"centered"],indicator:["indicator"],scrollButtons:["scrollButtons",i&&"scrollButtonsHideMobile"],scrollableX:[s&&"scrollableX"],hideScrollbar:[r&&"hideScrollbar"]},Va,l)},Ua=re("div",{name:"MuiTabs",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[{[`& .${Co.scrollButtons}`]:t.scrollButtons},{[`& .${Co.scrollButtons}`]:n.scrollButtonsHideMobile&&t.scrollButtonsHideMobile},t.root,n.vertical&&t.vertical]}})(Te(({theme:e})=>({overflow:"hidden",minHeight:48,WebkitOverflowScrolling:"touch",display:"flex",variants:[{props:({ownerState:t})=>t.vertical,style:{flexDirection:"column"}},{props:({ownerState:t})=>t.scrollButtonsHideMobile,style:{[`& .${Co.scrollButtons}`]:{[e.breakpoints.down("sm")]:{display:"none"}}}}]}))),qa=re("div",{name:"MuiTabs",slot:"Scroller",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.scroller,n.fixed&&t.fixed,n.hideScrollbar&&t.hideScrollbar,n.scrollableX&&t.scrollableX,n.scrollableY&&t.scrollableY]}})({position:"relative",display:"inline-block",flex:"1 1 auto",whiteSpace:"nowrap",variants:[{props:({ownerState:e})=>e.fixed,style:{overflowX:"hidden",width:"100%"}},{props:({ownerState:e})=>e.hideScrollbar,style:{scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}}},{props:({ownerState:e})=>e.scrollableX,style:{overflowX:"auto",overflowY:"hidden"}},{props:({ownerState:e})=>e.scrollableY,style:{overflowY:"auto",overflowX:"hidden"}}]}),Xa=re("div",{name:"MuiTabs",slot:"List",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.list,t.flexContainer,n.vertical&&t.flexContainerVertical,n.centered&&t.centered]}})({display:"flex",variants:[{props:({ownerState:e})=>e.vertical,style:{flexDirection:"column"}},{props:({ownerState:e})=>e.centered,style:{justifyContent:"center"}}]}),Ya=re("span",{name:"MuiTabs",slot:"Indicator"})(Te(({theme:e})=>({position:"absolute",height:2,bottom:0,width:"100%",transition:e.transitions.create(),variants:[{props:{indicatorColor:"primary"},style:{backgroundColor:(e.vars||e).palette.primary.main}},{props:{indicatorColor:"secondary"},style:{backgroundColor:(e.vars||e).palette.secondary.main}},{props:({ownerState:t})=>t.vertical,style:{height:"100%",width:2,right:0}}]}))),Ga=re(Aa)({overflowX:"auto",overflowY:"hidden",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}}),vn={},Ka=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiTabs"}),s=Ao(),a=Gt(),{"aria-label":c,"aria-labelledby":i,action:l,centered:m=!1,children:p,className:T,component:j="div",allowScrollButtonsMobile:b=!1,indicatorColor:P="primary",onChange:y,orientation:k="horizontal",ScrollButtonComponent:$,scrollButtons:u="auto",selectionFollowsFocus:f,slots:d={},slotProps:v={},TabIndicatorProps:S={},TabScrollButtonProps:O={},textColor:X="primary",value:A,variant:I="standard",visibleScrollbar:W=!1,...Y}=r,w=I==="scrollable",M=k==="vertical",ee=M?"scrollTop":"scrollLeft",E=M?"top":"left",J=M?"bottom":"right",V=M?"clientHeight":"clientWidth",pe=M?"height":"width",se={...r,component:j,allowScrollButtonsMobile:b,indicatorColor:P,orientation:k,vertical:M,scrollButtons:u,textColor:X,variant:I,visibleScrollbar:W,fixed:!w,hideScrollbar:w&&!W,scrollableX:w&&!M,scrollableY:w&&M,centered:m&&!w,scrollButtonsHideMobile:!b},H=Ha(se),G=Ke({elementType:d.StartScrollButtonIcon,externalSlotProps:v.startScrollButtonIcon,ownerState:se}),Me=Ke({elementType:d.EndScrollButtonIcon,externalSlotProps:v.endScrollButtonIcon,ownerState:se}),[xe,q]=g.useState(!1),[te,ue]=g.useState(vn),[h,L]=g.useState(!1),[U,Z]=g.useState(!1),[_,oe]=g.useState(!1),[Se,Ae]=g.useState({overflow:"hidden",scrollbarWidth:0}),ze=new Map,ae=g.useRef(null),Pe=g.useRef(null),Le={slots:d,slotProps:{indicator:S,scrollButton:O,...v}},Be=()=>{const B=ae.current;let D;if(B){const de=B.getBoundingClientRect();D={clientWidth:B.clientWidth,scrollLeft:B.scrollLeft,scrollTop:B.scrollTop,scrollWidth:B.scrollWidth,top:de.top,bottom:de.bottom,left:de.left,right:de.right}}let ce;if(B&&A!==!1){const de=Pe.current.children;if(de.length>0){const ye=de[ze.get(A)];ce=ye?ye.getBoundingClientRect():null}}return{tabsMeta:D,tabMeta:ce}},Oe=ft(()=>{const{tabsMeta:B,tabMeta:D}=Be();let ce=0,de;M?(de="top",D&&B&&(ce=D.top-B.top+B.scrollTop)):(de=a?"right":"left",D&&B&&(ce=(a?-1:1)*(D[de]-B[de]+B.scrollLeft)));const ye={[de]:ce,[pe]:D?D[pe]:0};if(typeof te[de]!="number"||typeof te[pe]!="number")ue(ye);else{const rt=Math.abs(te[de]-ye[de]),yt=Math.abs(te[pe]-ye[pe]);(rt>=1||yt>=1)&&ue(ye)}}),Ve=(B,{animation:D=!0}={})=>{D?La(ee,ae.current,B,{duration:s.transitions.duration.standard}):ae.current[ee]=B},De=B=>{let D=ae.current[ee];M?D+=B:D+=B*(a?-1:1),Ve(D)},Xe=()=>{const B=ae.current[V];let D=0;const ce=Array.from(Pe.current.children);for(let de=0;de<ce.length;de+=1){const ye=ce[de];if(D+ye[V]>B){de===0&&(D=B);break}D+=ye[V]}return D},x=()=>{De(-1*Xe())},C=()=>{De(Xe())},[z,{onChange:K,...ne}]=ke("scrollbar",{className:ve(H.scrollableX,H.hideScrollbar),elementType:Ga,shouldForwardComponentProp:!0,externalForwardedProps:Le,ownerState:se}),N=g.useCallback(B=>{K==null||K(B),Ae({overflow:null,scrollbarWidth:B})},[K]),[ie,be]=ke("scrollButtons",{className:ve(H.scrollButtons,O.className),elementType:Fa,externalForwardedProps:Le,ownerState:se,additionalProps:{orientation:k,slots:{StartScrollButtonIcon:d.startScrollButtonIcon||d.StartScrollButtonIcon,EndScrollButtonIcon:d.endScrollButtonIcon||d.EndScrollButtonIcon},slotProps:{startScrollButtonIcon:G,endScrollButtonIcon:Me}}}),he=()=>{const B={};B.scrollbarSizeListener=w?o.jsx(z,{...ne,onChange:N}):null;const ce=w&&(u==="auto"&&(h||U)||u===!0);return B.scrollButtonStart=ce?o.jsx(ie,{direction:a?"right":"left",onClick:x,disabled:!h,...be}):null,B.scrollButtonEnd=ce?o.jsx(ie,{direction:a?"left":"right",onClick:C,disabled:!U,...be}):null,B},fe=ft(B=>{const{tabsMeta:D,tabMeta:ce}=Be();if(!(!ce||!D)){if(ce[E]<D[E]){const de=D[ee]+(ce[E]-D[E]);Ve(de,{animation:B})}else if(ce[J]>D[J]){const de=D[ee]+(ce[J]-D[J]);Ve(de,{animation:B})}}}),Qe=ft(()=>{w&&u!==!1&&oe(!_)});g.useEffect(()=>{const B=jn(()=>{ae.current&&Oe()});let D;const ce=rt=>{rt.forEach(yt=>{yt.removedNodes.forEach(Lt=>{D==null||D.unobserve(Lt)}),yt.addedNodes.forEach(Lt=>{D==null||D.observe(Lt)})}),B(),Qe()},de=Cn(ae.current);de.addEventListener("resize",B);let ye;return typeof ResizeObserver<"u"&&(D=new ResizeObserver(B),Array.from(Pe.current.children).forEach(rt=>{D.observe(rt)})),typeof MutationObserver<"u"&&(ye=new MutationObserver(ce),ye.observe(Pe.current,{childList:!0})),()=>{B.clear(),de.removeEventListener("resize",B),ye==null||ye.disconnect(),D==null||D.disconnect()}},[Oe,Qe]),g.useEffect(()=>{const B=Array.from(Pe.current.children),D=B.length;if(typeof IntersectionObserver<"u"&&D>0&&w&&u!==!1){const ce=B[0],de=B[D-1],ye={root:ae.current,threshold:.99},rt=xo=>{L(!xo[0].isIntersecting)},yt=new IntersectionObserver(rt,ye);yt.observe(ce);const Lt=xo=>{Z(!xo[0].isIntersecting)},qo=new IntersectionObserver(Lt,ye);return qo.observe(de),()=>{yt.disconnect(),qo.disconnect()}}},[w,u,_,p==null?void 0:p.length]),g.useEffect(()=>{q(!0)},[]),g.useEffect(()=>{Oe()}),g.useEffect(()=>{fe(vn!==te)},[fe,te]),g.useImperativeHandle(l,()=>({updateIndicator:Oe,updateScrollButtons:Qe}),[Oe,Qe]);const[gt,vt]=ke("indicator",{className:ve(H.indicator,S.className),elementType:Ya,externalForwardedProps:Le,ownerState:se,additionalProps:{style:te}}),lt=o.jsx(gt,{...vt});let nt=0;const xt=g.Children.map(p,B=>{if(!g.isValidElement(B))return null;const D=B.props.value===void 0?nt:B.props.value;ze.set(D,nt);const ce=D===A;return nt+=1,g.cloneElement(B,{fullWidth:I==="fullWidth",indicator:ce&&!xe&&lt,selected:ce,selectionFollowsFocus:f,onChange:y,textColor:X,value:D,...nt===1&&A===!1&&!B.props.tabIndex?{tabIndex:0}:{}})}),It=B=>{if(B.altKey||B.shiftKey||B.ctrlKey||B.metaKey)return;const D=Pe.current,ce=kt(D).activeElement;if(ce.getAttribute("role")!=="tab")return;let ye=k==="horizontal"?"ArrowLeft":"ArrowUp",rt=k==="horizontal"?"ArrowRight":"ArrowDown";switch(k==="horizontal"&&a&&(ye="ArrowRight",rt="ArrowLeft"),B.key){case ye:B.preventDefault(),ao(D,ce,gn);break;case rt:B.preventDefault(),ao(D,ce,mn);break;case"Home":B.preventDefault(),ao(D,null,mn);break;case"End":B.preventDefault(),ao(D,null,gn);break}},ge=he(),[je,bt]=ke("root",{ref:n,className:ve(H.root,T),elementType:Ua,externalForwardedProps:{...Le,...Y,component:j},ownerState:se}),[ut,Ye]=ke("scroller",{ref:ae,className:H.scroller,elementType:qa,externalForwardedProps:Le,ownerState:se,additionalProps:{style:{overflow:Se.overflow,[M?`margin${a?"Left":"Right"}`:"marginBottom"]:W?void 0:-Se.scrollbarWidth}}}),[Q,Ge]=ke("list",{ref:Pe,className:ve(H.list,H.flexContainer),elementType:Xa,externalForwardedProps:Le,ownerState:se,getSlotProps:B=>({...B,onKeyDown:D=>{var ce;It(D),(ce=B.onKeyDown)==null||ce.call(B,D)}})});return o.jsxs(je,{...bt,children:[ge.scrollButtonStart,ge.scrollbarSizeListener,o.jsxs(ut,{...Ye,children:[o.jsx(Q,{"aria-label":c,"aria-labelledby":i,"aria-orientation":k==="vertical"?"vertical":null,role:"tablist",...Ge,children:xt}),xe&&lt]}),ge.scrollButtonEnd]})}),fo=Ee(o.jsx("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"})),_a=Ee(o.jsx("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})),Ht=Ee(o.jsx("path",{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6zM19 4h-3.5l-1-1h-5l-1 1H5v2h14z"})),Qa=Ee(o.jsx("path",{d:"M5 20h14v-2H5zM19 9h-4V3H9v6H5l7 7z"})),Ja=Ee(o.jsx("path",{d:"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75z"})),xn=Ee(o.jsx("path",{d:"m12 21.35-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54z"})),Za=Ee(o.jsx("path",{d:"M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2m6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1z"})),ei=Ee(o.jsx("path",{d:"M12 2C6.49 2 2 6.49 2 12s4.49 10 10 10c1.38 0 2.5-1.12 2.5-2.5 0-.61-.23-1.2-.64-1.67-.08-.1-.13-.21-.13-.33 0-.28.22-.5.5-.5H16c3.31 0 6-2.69 6-6 0-4.96-4.49-9-10-9m5.5 11c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5m-3-4c-.83 0-1.5-.67-1.5-1.5S13.67 6 14.5 6s1.5.67 1.5 1.5S15.33 9 14.5 9M5 11.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5S7.33 13 6.5 13 5 12.33 5 11.5m6-4c0 .83-.67 1.5-1.5 1.5S8 8.33 8 7.5 8.67 6 9.5 6s1.5.67 1.5 1.5"})),ti=Ee(o.jsx("path",{d:"M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9m-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8z"})),zn=Ee(o.jsx("path",{d:"M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3m3-10H5V5h10z"})),oi=Ee(o.jsx("path",{d:"M12 1 3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11z"})),io=Ee([o.jsx("circle",{cx:"12",cy:"6",r:"2"},"0"),o.jsx("path",{d:"M21 16v-2c-2.24 0-4.16-.96-5.6-2.68l-1.34-1.6c-.38-.46-.94-.72-1.53-.72h-1.05c-.59 0-1.15.26-1.53.72l-1.34 1.6C7.16 13.04 5.24 14 3 14v2c2.77 0 5.19-1.17 7-3.25V15l-3.88 1.55c-.67.27-1.12.93-1.12 1.66C5 19.2 5.8 20 6.79 20H9v-.5c0-1.38 1.12-2.5 2.5-2.5h3c.28 0 .5.22.5.5s-.22.5-.5.5h-3c-.83 0-1.5.67-1.5 1.5v.5h7.21c.99 0 1.79-.8 1.79-1.79 0-.73-.45-1.39-1.12-1.66L14 15v-2.25c1.81 2.08 4.23 3.25 7 3.25"},"1")]),ni=Ee(o.jsx("path",{d:"M2 20h20v-4H2zm2-3h2v2H4zM2 4v4h20V4zm4 3H4V5h2zm-4 7h20v-4H2zm2-3h2v2H4z"})),ri=Ee(o.jsx("path",{d:"M5 20h14v-2H5zm0-10h4v6h6v-6h4l-7-7z"})),si=({open:e,onClose:t,onSave:n,editingMethod:r})=>{const[s,a]=g.useState({name:"",icon:"📈",description:"",questions:[],totalMaxScore:0,isCustom:!0}),[c,i]=g.useState([]);g.useEffect(()=>{a(r||{name:"",icon:"📈",description:"",questions:[{text:"",type:"radio",options:[{value:"option_0",label:"",weight:10},{value:"option_1",label:"",weight:5}]},{text:"",type:"radio",options:[{value:"option_0",label:"",weight:10},{value:"option_1",label:"",weight:5}]},{text:"",type:"radio",options:[{value:"option_0",label:"",weight:10},{value:"option_1",label:"",weight:5}]}],totalMaxScore:0,isCustom:!0})},[r,e]);const l=()=>{s.questions.length<7&&a(u=>({...u,questions:[...u.questions,{text:"",type:"radio",options:[{value:"option_0",label:"",weight:10},{value:"option_1",label:"",weight:5}]}]}))},m=u=>{s.questions.length>3&&a(f=>({...f,questions:f.questions.filter((d,v)=>v!==u)}))},p=(u,f,d)=>{a(v=>({...v,questions:v.questions.map((S,O)=>O===u?{...S,[f]:d}:S)}))},T=u=>{const f=s.questions[u];if(f.options&&f.options.length<6){const d={value:`option_${f.options.length}`,label:"",weight:5};p(u,"options",[...f.options,d])}},j=(u,f)=>{const d=s.questions[u];if(d.options&&d.options.length>2){const v=d.options.filter((S,O)=>O!==f);p(u,"options",v)}},b=(u,f,d,v)=>{const S=s.questions[u];if(S.options){const O=S.options.map((X,A)=>A===f?{...X,[d]:v}:X);p(u,"options",O)}},P=()=>{let u=0;return s.questions.forEach(f=>{if(f.type==="textarea")u+=10;else if(f.options&&f.options.length>0){const d=Math.max(...f.options.map(v=>v.weight));u+=d}}),u},y=()=>{const u=[];(!s.name||s.name.length<3)&&u.push("Tên phương pháp phải có ít nhất 3 ký tự"),(!s.description||s.description.length<10)&&u.push("Mô tả phải có ít nhất 10 ký tự"),s.icon||u.push("Vui lòng chọn icon"),s.questions.length<3&&u.push("Cần ít nhất 3 câu hỏi"),s.questions.forEach((d,v)=>{(!d.text||d.text.length<5)&&u.push(`Câu hỏi ${v+1}: Nội dung phải có ít nhất 5 ký tự`),d.type!=="textarea"&&(!d.options||d.options.length<2?u.push(`Câu hỏi ${v+1}: Cần ít nhất 2 đáp án`):d.options.forEach((S,O)=>{(!S.label||S.label.length<2)&&u.push(`Câu hỏi ${v+1}, Đáp án ${O+1}: Nội dung quá ngắn`),(S.weight<0||S.weight>50)&&u.push(`Câu hỏi ${v+1}, Đáp án ${O+1}: Điểm phải từ 0-50`)}))});const f=P();return f<50&&u.push("Tổng điểm tối đa quá thấp (< 50)"),f>200&&u.push("Tổng điểm tối đa quá cao (> 200)"),u},k=()=>{const u=y();if(u.length>0){i(u);return}const f={...s,id:(r==null?void 0:r.id)||`custom_${Date.now()}`,totalMaxScore:P(),createdAt:(r==null?void 0:r.createdAt)||new Date().toISOString()};n(f),t()},$=()=>{i([]),t()};return o.jsxs(Bo,{open:e,onClose:$,maxWidth:"md",fullWidth:!0,children:[o.jsx(Lo,{children:o.jsxs(le,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[o.jsx(R,{variant:"h6",children:r?"✏️ Chỉnh sửa phương pháp":"⚙️ Tạo phương pháp tùy chỉnh"}),o.jsx(yo,{onClick:$,children:o.jsx(_a,{})})]})}),o.jsxs(Io,{children:[c.length>0&&o.jsx(Po,{severity:"error",sx:{mb:2},children:o.jsx("ul",{style:{margin:0,paddingLeft:20},children:c.map((u,f)=>o.jsx("li",{children:u},f))})}),o.jsxs(F,{container:!0,spacing:2,children:[o.jsx(F,{size:12,children:o.jsx(Re,{variant:"outlined",children:o.jsxs($e,{children:[o.jsx(R,{variant:"h6",sx:{mb:2},children:"📊 Thông tin cơ bản"}),o.jsxs(F,{container:!0,spacing:2,children:[o.jsx(F,{size:{xs:12,md:8},children:o.jsx(Ct,{fullWidth:!0,label:"Tên phương pháp",value:s.name,onChange:u=>a(f=>({...f,name:u.target.value})),placeholder:"Ví dụ: Fibonacci Retracement Strategy"})}),o.jsx(F,{size:{xs:12,md:4},children:o.jsx(Ct,{fullWidth:!0,label:"Icon (emoji)",value:s.icon,onChange:u=>a(f=>({...f,icon:u.target.value})),inputProps:{maxLength:2,style:{textAlign:"center",fontSize:"20px"}}})}),o.jsx(F,{size:12,children:o.jsx(Ct,{fullWidth:!0,multiline:!0,rows:3,label:"Mô tả phương pháp",value:s.description,onChange:u=>a(f=>({...f,description:u.target.value})),placeholder:"Mô tả ngắn gọn về phương pháp giao dịch này..."})})]})]})})}),o.jsx(F,{size:12,children:o.jsx(Re,{variant:"outlined",children:o.jsxs($e,{children:[o.jsxs(le,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[o.jsx(R,{variant:"h6",children:"❓ Câu hỏi phân tích"}),o.jsxs(le,{sx:{display:"flex",gap:1,alignItems:"center"},children:[o.jsx(ht,{label:`${s.questions.length} câu hỏi`,size:"small"}),o.jsx(ht,{label:`${P()} điểm`,size:"small",color:"primary"}),o.jsx(Ce,{size:"small",startIcon:o.jsx(fo,{}),onClick:l,disabled:s.questions.length>=7,children:"Thêm câu hỏi"})]})]}),s.questions.map((u,f)=>o.jsx(Re,{variant:"outlined",sx:{mb:2},children:o.jsxs($e,{children:[o.jsxs(le,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[o.jsxs(R,{variant:"subtitle1",sx:{fontWeight:"bold"},children:["Câu hỏi ",f+1]}),s.questions.length>3&&o.jsx(yo,{size:"small",color:"error",onClick:()=>m(f),children:o.jsx(Ht,{})})]}),o.jsxs(F,{container:!0,spacing:2,children:[o.jsx(F,{size:{xs:12,md:8},children:o.jsx(Ct,{fullWidth:!0,label:"Nội dung câu hỏi",value:u.text,onChange:d=>p(f,"text",d.target.value),placeholder:"Ví dụ: Có tín hiệu divergence rõ ràng không?"})}),o.jsx(F,{size:{xs:12,md:4},children:o.jsxs(Qn,{fullWidth:!0,children:[o.jsx(Jn,{children:"Loại câu hỏi"}),o.jsxs(Zn,{value:u.type,label:"Loại câu hỏi",onChange:d=>{const v=d.target.value;v==="textarea"?(p(f,"type",v),p(f,"options",void 0),p(f,"placeholder","Mô tả chi tiết setup...")):(p(f,"type",v),u.options||p(f,"options",[{value:"option_0",label:"",weight:10},{value:"option_1",label:"",weight:5}]))},children:[o.jsx(So,{value:"radio",children:"Multiple Choice (Radio)"}),o.jsx(So,{value:"select",children:"Dropdown (Select)"}),o.jsx(So,{value:"textarea",children:"Mô tả chi tiết (Textarea)"})]})]})}),u.type!=="textarea"&&u.options&&o.jsxs(F,{size:12,children:[o.jsxs(le,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[o.jsx(R,{variant:"body2",color:"text.secondary",children:"Đáp án:"}),o.jsx(Ce,{size:"small",startIcon:o.jsx(fo,{}),onClick:()=>T(f),disabled:!u.options||u.options.length>=6,children:"Thêm đáp án"})]}),u.options.map((d,v)=>o.jsxs(le,{sx:{display:"flex",gap:1,mb:1,alignItems:"center"},children:[o.jsx(Ct,{size:"small",placeholder:"Nội dung đáp án...",value:d.label,onChange:S=>b(f,v,"label",S.target.value),sx:{flex:1}}),o.jsx(Ct,{size:"small",type:"number",placeholder:"Điểm",value:d.weight,onChange:S=>b(f,v,"weight",parseInt(S.target.value)||0),inputProps:{min:0,max:50},sx:{width:80}}),u.options&&u.options.length>2&&o.jsx(yo,{size:"small",color:"error",onClick:()=>j(f,v),children:o.jsx(Ht,{})})]},v))]})]})]})},f))]})})})]})]}),o.jsxs(Oo,{children:[o.jsx(Ce,{onClick:$,children:"Hủy"}),o.jsx(Ce,{variant:"contained",startIcon:o.jsx(zn,{}),onClick:k,children:r?"Cập nhật":"Tạo phương pháp"})]})]})},ai=Nn({palette:{primary:{main:"#007bff"},secondary:{main:"#6c757d"}}});function jt(e){const{children:t,value:n,index:r,...s}=e;return o.jsx("div",{role:"tabpanel",hidden:n!==r,id:`options-tabpanel-${r}`,"aria-labelledby":`options-tab-${r}`,...s,children:n===r&&o.jsx(le,{sx:{p:3},children:t})})}const ii=()=>{var ue;const[e,t]=g.useState(0),[n,r]=g.useState([]),[s,a]=g.useState([]),[c,i]=g.useState({}),[l,m]=g.useState(!1),[p,T]=g.useState(null),[j,b]=g.useState(!1),[P,y]=g.useState(null),[k,$]=g.useState(!1),[u,f]=g.useState("mindfulness"),[d,v]=g.useState({theme:"light",language:"vi",fontSize:14,notifications:!0,soundAlerts:!0,desktopNotifications:!0,emailNotifications:!1,defaultTradeAmount:10,defaultTradeDuration:5,maxDailyTrades:50,riskWarnings:!0,autoStopLoss:!0,requireConfirmation:!0,sessionTimeout:30,autoLock:!1,autoBackup:!0,backupFrequency:"daily",maxBackups:10,apiUrl:"http://localhost:3001"}),[S,O]=g.useState(!1),[X,A]=g.useState(!1),[I,W]=g.useState(null);g.useEffect(()=>{H(),M(),Y()},[]);const Y=async()=>{try{(await chrome.storage.local.get(["requestedTab"])).requestedTab==="meditation"&&(t(4),await chrome.storage.local.remove(["requestedTab"]))}catch{console.log("Could not check requested tab")}},w=()=>[{id:"bollinger_bands",name:"Bollinger Bands Breakout",icon:"📈",description:"Giao dịch dựa trên việc giá breakout khỏi dải Bollinger Bands với volume xác nhận",questions:5},{id:"rsi_divergence",name:"RSI Divergence",icon:"📊",description:"Tìm kiếm sự phân kỳ giữa giá và RSI để dự đoán sự đảo chiều xu hướng",questions:5},{id:"support_resistance",name:"Support & Resistance",icon:"🔄",description:"Giao dịch tại các vùng support/resistance quan trọng với xác nhận price action",questions:5},{id:"moving_average",name:"Moving Average Crossover",icon:"📉",description:"Sử dụng sự cắt nhau của các đường MA để xác định xu hướng và điểm vào lệnh",questions:5},{id:"price_action",name:"Price Action Patterns",icon:"🕯️",description:"Phân tích các mô hình nến Nhật và price action để dự đoán hướng di chuyển",questions:5}],M=async()=>{try{const L=await fetch("http://localhost:3001/customMethods");if(L.ok){const U=await L.json();a(U)}}catch{console.log("No custom methods found or server not available")}const h=await chrome.storage.local.get(["methodSettings"]);if(h.methodSettings)i(h.methodSettings);else{const L={};w().forEach(U=>{L[U.id]=!0}),i(L)}},ee=h=>c[h]!==!1,E=async(h,L)=>{const U={...c,[h]:L};i(U),await chrome.storage.local.set({methodSettings:U});try{(await chrome.tabs.query({url:"*://binomo1.com/trading*"})).forEach(_=>{_.id&&chrome.tabs.sendMessage(_.id,{action:"updateMethodSettings",methodSettings:U}).catch(oe=>{console.log(`Could not update content script in tab ${_.id}:`,oe)})})}catch(Z){console.log("Could not query tabs or update content script:",Z)}},J=h=>{console.log("Editing method:",h.name),T(h),m(!0)},V=h=>{y(h),b(!0)},pe=async()=>{if(!P)return;const h=P.id;O(!0),b(!1);try{const L=await fetch(`http://localhost:3001/customMethods/${h}`,{method:"DELETE"});if(L.ok){a(Z=>Z.filter(_=>_.id!==h));const U={...c};delete U[h],i(U),await chrome.storage.local.set({methodSettings:U});try{(await chrome.tabs.query({url:"*://binomo1.com/trading*"})).forEach(_=>{_.id&&chrome.tabs.sendMessage(_.id,{action:"updateMethodSettings",methodSettings:U}).catch(oe=>{console.log(`Could not update content script in tab ${_.id}:`,oe)})})}catch(Z){console.log("Could not query tabs or update content script:",Z)}A(!0),setTimeout(()=>A(!1),3e3)}else throw new Error(`HTTP ${L.status}`)}catch(L){console.error("Error deleting method:",L),W("Không thể xóa phương pháp. Hãy kiểm tra JSON Server đang chạy!"),setTimeout(()=>W(null),5e3)}finally{O(!1),y(null)}},se=async h=>{try{let L;if(h.id&&p?L=await fetch(`http://localhost:3001/customMethods/${h.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(h)}):L=await fetch("http://localhost:3001/customMethods",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(h)}),L.ok){if(await M(),!p){const U={...c,[h.id]:!0};i(U),await chrome.storage.local.set({methodSettings:U})}try{(await chrome.tabs.query({url:"*://binomo1.com/trading*"})).forEach(Z=>{Z.id&&chrome.tabs.sendMessage(Z.id,{action:"updateMethodSettings",methodSettings:c}).catch(_=>{console.log(`Could not update content script in tab ${Z.id}:`,_)})})}catch(U){console.log("Could not query tabs or update content script:",U)}A(!0),setTimeout(()=>A(!1),3e3)}else throw new Error("Failed to save method")}catch{W("Không thể lưu phương pháp. Hãy kiểm tra JSON Server đang chạy!"),setTimeout(()=>W(null),5e3)}},H=async()=>{try{const h=await chrome.storage.local.get("userSettings");h.userSettings&&v(L=>({...L,...h.userSettings}))}catch(h){console.error("Error loading settings:",h)}},G=(h,L)=>{v(U=>({...U,[h]:L}))},Me=async()=>{O(!0),W(null);try{await chrome.storage.local.set({userSettings:d}),A(!0),setTimeout(()=>A(!1),3e3)}catch(h){W("Có lỗi xảy ra khi lưu cài đặt"),console.error("Error saving settings:",h)}finally{O(!1)}},xe=async()=>{if(!confirm("Bạn có chắc chắn muốn reset tất cả cài đặt về mặc định không?"))return;v({theme:"light",language:"vi",fontSize:14,notifications:!0,soundAlerts:!0,desktopNotifications:!0,emailNotifications:!1,defaultTradeAmount:10,defaultTradeDuration:5,maxDailyTrades:50,riskWarnings:!0,autoStopLoss:!0,requireConfirmation:!0,sessionTimeout:30,autoLock:!1,autoBackup:!0,backupFrequency:"daily",maxBackups:10,apiUrl:"http://localhost:3001"}),await Me()},q=()=>{const h=JSON.stringify(d,null,2),L=new Blob([h],{type:"application/json"}),U=URL.createObjectURL(L),Z=document.createElement("a");Z.href=U,Z.download=`binomo-settings-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(Z),Z.click(),document.body.removeChild(Z),URL.revokeObjectURL(U)},te=()=>{const h=document.createElement("input");h.type="file",h.accept=".json",h.onchange=L=>{var Z;const U=(Z=L.target.files)==null?void 0:Z[0];if(U){const _=new FileReader;_.onload=oe=>{var Se;try{const Ae=JSON.parse((Se=oe.target)==null?void 0:Se.result);v(ze=>({...ze,...Ae})),alert("Cài đặt đã được import thành công!")}catch{alert("File không hợp lệ!")}},_.readAsText(U)}},h.click()};return o.jsxs(Wn,{theme:ai,children:[o.jsx(Fn,{}),o.jsxs(le,{sx:{maxWidth:1200,mx:"auto",p:3},children:[o.jsx(F,{container:!0,spacing:3,sx:{mb:4},children:o.jsx(F,{size:12,children:o.jsxs(Wt,{sx:{p:3,textAlign:"center",background:"linear-gradient(45deg, #007bff 30%, #0056b3 90%)"},children:[o.jsxs(R,{variant:"h4",sx:{color:"white",mb:1,display:"flex",alignItems:"center",justifyContent:"center",gap:2},children:[o.jsx(tr,{fontSize:"large"}),"🎯 Binomo Trading Assistant - Cài đặt"]}),o.jsx(R,{variant:"body1",sx:{color:"rgba(255,255,255,0.8)"},children:"Tùy chỉnh extension theo nhu cầu của bạn"})]})})}),X&&o.jsx(F,{container:!0,spacing:3,sx:{mb:3},children:o.jsx(F,{size:12,children:o.jsx(Po,{severity:"success",children:"Cài đặt đã được lưu thành công!"})})}),I&&o.jsx(F,{container:!0,spacing:3,sx:{mb:3},children:o.jsx(F,{size:12,children:o.jsx(Po,{severity:"error",children:I})})}),o.jsx(F,{container:!0,spacing:3,children:o.jsx(F,{size:12,children:o.jsx(Wt,{sx:{mb:3},children:o.jsxs(Ka,{value:e,onChange:(h,L)=>t(L),variant:"scrollable",scrollButtons:"auto",children:[o.jsx(St,{icon:o.jsx(ei,{}),label:"Giao diện"}),o.jsx(St,{icon:o.jsx(Za,{}),label:"Thông báo"}),o.jsx(St,{icon:o.jsx(Vn,{}),label:"Giao dịch"}),o.jsx(St,{icon:o.jsx(er,{}),label:"Phương pháp"}),o.jsx(St,{icon:o.jsx(io,{}),label:"Thiền"}),o.jsx(St,{icon:o.jsx(oi,{}),label:"Bảo mật"}),o.jsx(St,{icon:o.jsx(ni,{}),label:"Dữ liệu"})]})})})}),o.jsx(F,{container:!0,spacing:3,children:o.jsxs(F,{size:12,children:[o.jsx(jt,{value:e,index:0,children:o.jsxs(F,{container:!0,spacing:3,children:[o.jsx(F,{size:12,md:6,children:o.jsx(Re,{children:o.jsxs($e,{children:[o.jsx(R,{variant:"h6",sx:{mb:2},children:"🎨 Theme"}),o.jsxs(Jt,{fullWidth:!0,sx:{mb:2},children:[o.jsx(Ce,{variant:d.theme==="light"?"contained":"outlined",onClick:()=>G("theme","light"),children:"Light"}),o.jsx(Ce,{variant:d.theme==="dark"?"contained":"outlined",onClick:()=>G("theme","dark"),children:"Dark"})]}),o.jsxs(R,{variant:"body2",sx:{mb:1},children:["Font Size: ",d.fontSize,"px"]}),o.jsx(jo,{value:d.fontSize,onChange:(h,L)=>G("fontSize",L),min:12,max:20,step:1,marks:!0,valueLabelDisplay:"auto"})]})})}),o.jsx(F,{size:12,md:6,children:o.jsx(Re,{children:o.jsxs($e,{children:[o.jsx(R,{variant:"h6",sx:{mb:2},children:"🌐 Ngôn ngữ"}),o.jsxs(Jt,{fullWidth:!0,children:[o.jsx(Ce,{variant:d.language==="vi"?"contained":"outlined",onClick:()=>G("language","vi"),children:"Tiếng Việt"}),o.jsx(Ce,{variant:d.language==="en"?"contained":"outlined",onClick:()=>G("language","en"),children:"English"})]})]})})})]})}),o.jsx(jt,{value:e,index:1,children:o.jsx(F,{container:!0,spacing:3,children:o.jsx(F,{size:12,md:6,children:o.jsx(Re,{children:o.jsxs($e,{children:[o.jsx(R,{variant:"h6",sx:{mb:2},children:"🔔 Thông báo"}),o.jsxs(wt,{spacing:2,children:[o.jsx(ct,{control:o.jsx(pt,{checked:d.notifications,onChange:h=>G("notifications",h.target.checked)}),label:"Bật thông báo"}),o.jsx(ct,{control:o.jsx(pt,{checked:d.soundAlerts,onChange:h=>G("soundAlerts",h.target.checked)}),label:"Âm thanh cảnh báo"}),o.jsx(ct,{control:o.jsx(pt,{checked:d.desktopNotifications,onChange:h=>G("desktopNotifications",h.target.checked)}),label:"Thông báo desktop"})]})]})})})})}),o.jsx(jt,{value:e,index:2,children:o.jsxs(F,{container:!0,spacing:3,children:[o.jsx(F,{size:12,md:6,children:o.jsx(Re,{children:o.jsxs($e,{children:[o.jsx(R,{variant:"h6",sx:{mb:2},children:"💰 Giao dịch mặc định"}),o.jsxs(wt,{spacing:3,children:[o.jsxs(le,{children:[o.jsx(R,{variant:"body2",sx:{mb:1},children:"Số tiền mặc định ($):"}),o.jsx(Jt,{fullWidth:!0,children:[1,5,10,25,50].map(h=>o.jsxs(Ce,{variant:d.defaultTradeAmount===h?"contained":"outlined",onClick:()=>G("defaultTradeAmount",h),children:["$",h]},h))})]}),o.jsxs(le,{children:[o.jsx(R,{variant:"body2",sx:{mb:1},children:"Thời gian mặc định:"}),o.jsx(Jt,{fullWidth:!0,children:[1,5,15,30,60].map(h=>o.jsxs(Ce,{variant:d.defaultTradeDuration===h?"contained":"outlined",onClick:()=>G("defaultTradeDuration",h),children:[h,"m"]},h))})]})]})]})})}),o.jsx(F,{size:12,md:6,children:o.jsx(Re,{children:o.jsxs($e,{children:[o.jsx(R,{variant:"h6",sx:{mb:2},children:"⚠️ Quản lý rủi ro"}),o.jsxs(wt,{spacing:2,children:[o.jsx(ct,{control:o.jsx(pt,{checked:d.riskWarnings,onChange:h=>G("riskWarnings",h.target.checked)}),label:"Cảnh báo rủi ro"}),o.jsx(ct,{control:o.jsx(pt,{checked:d.autoStopLoss,onChange:h=>G("autoStopLoss",h.target.checked)}),label:"Tự động dừng khi thua lỗ"}),o.jsxs(le,{children:[o.jsxs(R,{variant:"body2",sx:{mb:1},children:["Giới hạn lệnh/ngày: ",d.maxDailyTrades]}),o.jsx(jo,{value:d.maxDailyTrades,onChange:(h,L)=>G("maxDailyTrades",L),min:10,max:100,step:5,marks:!0,valueLabelDisplay:"auto"})]})]})]})})})]})}),o.jsx(jt,{value:e,index:3,children:o.jsxs(F,{container:!0,spacing:3,children:[o.jsx(F,{size:12,children:o.jsx(Re,{children:o.jsxs($e,{children:[o.jsxs(le,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[o.jsx(R,{variant:"h6",children:"📊 Phương pháp có sẵn"}),o.jsx(ht,{label:`${w().length} phương pháp`,color:"primary"})]}),o.jsx(F,{container:!0,spacing:2,children:w().map(h=>{var L;return o.jsx(F,{size:12,md:6,lg:4,children:o.jsx(Re,{variant:"outlined",sx:{height:"100%"},children:o.jsxs($e,{children:[o.jsxs(le,{sx:{display:"flex",alignItems:"center",mb:1},children:[o.jsx(R,{variant:"h6",sx:{mr:1},children:h.icon}),o.jsx(R,{variant:"subtitle1",sx:{fontWeight:"bold"},children:h.name})]}),o.jsx(R,{variant:"body2",color:"text.secondary",sx:{mb:2},children:h.description}),o.jsxs(le,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[o.jsx(ht,{label:`${((L=h.questions)==null?void 0:L.length)||5} câu hỏi`,size:"small",variant:"outlined"}),o.jsx(ct,{control:o.jsx(pt,{checked:ee(h.id),onChange:U=>E(h.id,U.target.checked),size:"small"}),label:"",sx:{m:0}})]})]})})},h.id)})})]})})}),o.jsx(F,{size:12,children:o.jsx(Re,{children:o.jsxs($e,{children:[o.jsxs(le,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[o.jsx(R,{variant:"h6",children:"⚙️ Phương pháp tùy chỉnh"}),o.jsxs(le,{sx:{display:"flex",gap:1},children:[o.jsx(ht,{label:`${s.length} phương pháp`,color:"secondary"}),o.jsx(Ce,{variant:"contained",size:"small",startIcon:o.jsx(fo,{}),onClick:()=>m(!0),children:"Tạo mới"})]})]}),s.length===0?o.jsxs(le,{sx:{textAlign:"center",py:4},children:[o.jsx(R,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"Chưa có phương pháp tùy chỉnh nào"}),o.jsx(Ce,{variant:"outlined",startIcon:o.jsx(fo,{}),onClick:()=>m(!0),children:"Tạo phương pháp đầu tiên"})]}):o.jsx(F,{container:!0,spacing:2,children:s.map(h=>{var L;return o.jsx(F,{size:12,md:6,lg:4,children:o.jsx(Re,{variant:"outlined",sx:{height:"100%",border:"2px solid #4caf50"},children:o.jsxs($e,{children:[o.jsxs(le,{sx:{display:"flex",alignItems:"center",mb:1},children:[o.jsx(R,{variant:"h6",sx:{mr:1},children:h.icon}),o.jsx(R,{variant:"subtitle1",sx:{fontWeight:"bold",flex:1},children:h.name}),o.jsx(ht,{label:"CUSTOM",size:"small",color:"success"})]}),o.jsx(R,{variant:"body2",color:"text.secondary",sx:{mb:2},children:h.description}),o.jsxs(le,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[o.jsx(ht,{label:`${((L=h.questions)==null?void 0:L.length)||0} câu hỏi`,size:"small",variant:"outlined"}),o.jsx(ht,{label:`${h.totalMaxScore||0} điểm`,size:"small",variant:"outlined"})]}),o.jsxs(le,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[o.jsxs(le,{sx:{display:"flex",gap:1},children:[o.jsx(fn,{title:"Chỉnh sửa phương pháp này",children:o.jsx("span",{children:o.jsx(Ce,{size:"small",startIcon:o.jsx(Ja,{}),onClick:()=>J(h),disabled:S,children:"Sửa"})})}),o.jsx(fn,{title:"Xóa phương pháp này vĩnh viễn",children:o.jsx("span",{children:o.jsx(Ce,{size:"small",color:"error",startIcon:o.jsx(Ht,{}),onClick:()=>V(h),disabled:S,children:"Xóa"})})})]}),o.jsx(ct,{control:o.jsx(pt,{checked:ee(h.id),onChange:U=>E(h.id,U.target.checked),size:"small"}),label:"",sx:{m:0}})]})]})})},h.id)})})]})})})]})}),o.jsx(jt,{value:e,index:4,children:o.jsx(F,{container:!0,spacing:3,children:o.jsx(F,{size:12,children:o.jsx(Re,{children:o.jsxs($e,{children:[o.jsxs(le,{sx:{textAlign:"center",mb:3},children:[o.jsx(io,{sx:{fontSize:64,color:"primary.main",mb:2}}),o.jsx(R,{variant:"h4",gutterBottom:!0,children:"🧘‍♂️ Thiền Chánh Niệm"}),o.jsx(R,{variant:"body1",color:"text.secondary",children:"Thời gian để tâm hồn nghỉ ngơi và tái tạo năng lượng tích cực"})]}),o.jsxs(F,{container:!0,spacing:3,children:[o.jsx(F,{size:12,md:4,children:o.jsx(Re,{variant:"outlined",sx:{height:"100%",cursor:"pointer"},onClick:()=>{f("mindfulness"),$(!0)},children:o.jsxs($e,{sx:{textAlign:"center"},children:[o.jsx(io,{sx:{fontSize:48,color:"primary.main",mb:2}}),o.jsx(R,{variant:"h6",gutterBottom:!0,children:"Thiền Chánh Niệm"}),o.jsx(R,{variant:"body2",color:"text.secondary",children:"Quan sát hơi thở, nhận biết cảm xúc và suy nghĩ mà không phán xét"})]})})}),o.jsx(F,{size:12,md:4,children:o.jsx(Re,{variant:"outlined",sx:{height:"100%",cursor:"pointer"},onClick:()=>{f("gratitude"),$(!0)},children:o.jsxs($e,{sx:{textAlign:"center"},children:[o.jsx(xn,{sx:{fontSize:48,color:"secondary.main",mb:2}}),o.jsx(R,{variant:"h6",gutterBottom:!0,children:"Thiền Biết Ơn"}),o.jsx(R,{variant:"body2",color:"text.secondary",children:"Cảm ơn những gì đã có, nuôi dưỡng lòng biết ơn và sự hài lòng"})]})})}),o.jsx(F,{size:12,md:4,children:o.jsx(Re,{variant:"outlined",sx:{height:"100%",cursor:"pointer"},onClick:()=>{f("compassion"),$(!0)},children:o.jsxs($e,{sx:{textAlign:"center"},children:[o.jsx(Go,{sx:{fontSize:48,color:"success.main",mb:2}}),o.jsx(R,{variant:"h6",gutterBottom:!0,children:"Thiền Từ Bi"}),o.jsx(R,{variant:"body2",color:"text.secondary",children:"Gửi tình yêu thương đến bản thân và mọi người xung quanh"})]})})})]}),o.jsx(le,{sx:{mt:3,p:2,bgcolor:"info.light",borderRadius:1},children:o.jsxs(R,{variant:"body2",color:"info.contrastText",children:["💡 ",o.jsx("strong",{children:"Lưu ý:"})," Khi tâm lý không phù hợp để giao dịch, hãy dành thời gian thiền để tái tạo năng lượng tích cực. Giao dịch với tâm hồn bình an sẽ mang lại kết quả tốt hơn."]})})]})})})})}),o.jsx(jt,{value:e,index:5,children:o.jsx(F,{container:!0,spacing:3,children:o.jsx(F,{size:12,md:6,children:o.jsx(Re,{children:o.jsxs($e,{children:[o.jsx(R,{variant:"h6",sx:{mb:2},children:"🔒 Bảo mật"}),o.jsxs(wt,{spacing:2,children:[o.jsx(ct,{control:o.jsx(pt,{checked:d.requireConfirmation,onChange:h=>G("requireConfirmation",h.target.checked)}),label:"Yêu cầu xác nhận trước khi giao dịch"}),o.jsxs(le,{children:[o.jsxs(R,{variant:"body2",sx:{mb:1},children:["Session timeout: ",d.sessionTimeout," phút"]}),o.jsx(jo,{value:d.sessionTimeout,onChange:(h,L)=>G("sessionTimeout",L),min:5,max:120,step:5,marks:!0,valueLabelDisplay:"auto"})]})]})]})})})})}),o.jsx(jt,{value:e,index:6,children:o.jsxs(F,{container:!0,spacing:3,children:[o.jsx(F,{size:12,md:6,children:o.jsx(Re,{children:o.jsxs($e,{children:[o.jsx(R,{variant:"h6",sx:{mb:2},children:"💾 Backup & Storage"}),o.jsxs(wt,{spacing:2,children:[o.jsx(ct,{control:o.jsx(pt,{checked:d.autoBackup,onChange:h=>G("autoBackup",h.target.checked)}),label:"Tự động backup"}),o.jsx(Ct,{label:"API URL",value:d.apiUrl,onChange:h=>G("apiUrl",h.target.value),fullWidth:!0,size:"small"})]})]})})}),o.jsx(F,{size:12,md:6,children:o.jsx(Re,{children:o.jsxs($e,{children:[o.jsx(R,{variant:"h6",sx:{mb:2},children:"📤 Import/Export"}),o.jsxs(wt,{spacing:2,children:[o.jsx(Ce,{variant:"outlined",startIcon:o.jsx(Qa,{}),onClick:q,fullWidth:!0,children:"Xuất cài đặt"}),o.jsx(Ce,{variant:"outlined",startIcon:o.jsx(ri,{}),onClick:te,fullWidth:!0,children:"Nhập cài đặt"})]})]})})})]})})]})}),o.jsx(F,{container:!0,spacing:3,sx:{mt:3},children:o.jsx(F,{size:12,children:o.jsx(Wt,{sx:{p:3},children:o.jsxs(wt,{direction:"row",spacing:2,justifyContent:"center",children:[o.jsx(Ce,{variant:"outlined",color:"secondary",startIcon:o.jsx(ti,{}),onClick:xe,children:"Reset về mặc định"}),o.jsx(Ce,{variant:"contained",startIcon:o.jsx(zn,{}),onClick:Me,disabled:S,size:"large",children:S?"Đang lưu...":"Lưu cài đặt"})]})})})}),o.jsx(si,{open:l,onClose:()=>{m(!1),T(null)},onSave:se,editingMethod:p}),o.jsxs(Bo,{open:k,onClose:()=>$(!1),maxWidth:"md",fullWidth:!0,children:[o.jsx(Lo,{children:o.jsxs(le,{sx:{display:"flex",alignItems:"center",gap:1},children:[u==="mindfulness"&&o.jsx(io,{color:"primary"}),u==="gratitude"&&o.jsx(xn,{color:"secondary"}),u==="compassion"&&o.jsx(Go,{color:"success"}),o.jsxs(R,{variant:"h6",children:[u==="mindfulness"&&"🧘‍♂️ Thiền Chánh Niệm",u==="gratitude"&&"🙏 Thiền Biết Ơn",u==="compassion"&&"💝 Thiền Từ Bi"]})]})}),o.jsxs(Io,{children:[u==="mindfulness"&&o.jsxs(le,{children:[o.jsx(R,{variant:"h6",gutterBottom:!0,children:"Hướng dẫn Thiền Chánh Niệm"}),o.jsxs(R,{paragraph:!0,children:["1. ",o.jsx("strong",{children:"Tìm tư thế thoải mái:"})," Ngồi thẳng lưng, thả lỏng vai, đặt tay trên đùi."]}),o.jsxs(R,{paragraph:!0,children:["2. ",o.jsx("strong",{children:"Quan sát hơi thở:"})," Tập trung vào cảm giác hơi thở vào ra tự nhiên."]}),o.jsxs(R,{paragraph:!0,children:["3. ",o.jsx("strong",{children:"Nhận biết suy nghĩ:"})," Khi tâm trí lang thang, nhẹ nhàng đưa về hơi thở."]}),o.jsxs(R,{paragraph:!0,children:["4. ",o.jsx("strong",{children:"Không phán xét:"})," Quan sát mọi cảm xúc, suy nghĩ mà không đánh giá."]}),o.jsxs(R,{paragraph:!0,children:["5. ",o.jsx("strong",{children:"Thực hành 10-15 phút:"})," Bắt đầu với thời gian ngắn, tăng dần."]}),o.jsx(le,{sx:{mt:3,p:2,bgcolor:"primary.light",borderRadius:1},children:o.jsxs(R,{variant:"body2",color:"primary.contrastText",children:["💡 ",o.jsx("strong",{children:"Lợi ích:"})," Giảm stress, tăng khả năng tập trung, cải thiện khả năng ra quyết định trong giao dịch."]})})]}),u==="gratitude"&&o.jsxs(le,{children:[o.jsx(R,{variant:"h6",gutterBottom:!0,children:"Hướng dẫn Thiền Biết Ơn"}),o.jsxs(R,{paragraph:!0,children:["1. ",o.jsx("strong",{children:"Ngồi yên tĩnh:"})," Tìm không gian thoải mái, thở sâu 3 lần."]}),o.jsxs(R,{paragraph:!0,children:["2. ",o.jsx("strong",{children:"Nghĩ về những điều tốt đẹp:"})," Gia đình, sức khỏe, cơ hội học hỏi từ thị trường."]}),o.jsxs(R,{paragraph:!0,children:["3. ",o.jsx("strong",{children:"Cảm ơn thị trường:"}),' "Tôi cảm ơn thị trường đã tạo ra nơi để tôi rèn luyện tâm và kiếm lợi nhuận đủ sống."']}),o.jsxs(R,{paragraph:!0,children:["4. ",o.jsx("strong",{children:"Cảm ơn bản thân:"}),' "Tôi cảm ơn bản thân đã kiên nhẫn học hỏi và rèn luyện."']}),o.jsxs(R,{paragraph:!0,children:["5. ",o.jsx("strong",{children:"Gửi lòng biết ơn:"})," Đến mọi người đã hỗ trợ hành trình của bạn."]}),o.jsx(le,{sx:{mt:3,p:2,bgcolor:"secondary.light",borderRadius:1},children:o.jsxs(R,{variant:"body2",color:"secondary.contrastText",children:["💝 ",o.jsx("strong",{children:"Lợi ích:"})," Tăng cảm giác hạnh phúc, giảm tham lam, tạo tâm thái tích cực trong giao dịch."]})})]}),u==="compassion"&&o.jsxs(le,{children:[o.jsx(R,{variant:"h6",gutterBottom:!0,children:"Hướng dẫn Thiền Từ Bi"}),o.jsxs(R,{paragraph:!0,children:["1. ",o.jsx("strong",{children:"Bắt đầu với bản thân:"}),' "Mong tôi được bình an, hạnh phúc và thành công."']}),o.jsxs(R,{paragraph:!0,children:["2. ",o.jsx("strong",{children:"Gửi đến người thân:"}),' "Mong gia đình tôi được khỏe mạnh và hạnh phúc."']}),o.jsxs(R,{paragraph:!0,children:["3. ",o.jsx("strong",{children:"Gửi đến trader khác:"}),' "Mong tất cả trader đều học hỏi và phát triển."']}),o.jsxs(R,{paragraph:!0,children:["4. ",o.jsx("strong",{children:"Tha thứ cho bản thân:"}),' "Tôi tha thứ cho những sai lầm trong giao dịch và sẽ học hỏi."']}),o.jsxs(R,{paragraph:!0,children:["5. ",o.jsx("strong",{children:"Tình yêu thương rộng lớn:"})," Gửi tình yêu thương đến tất cả chúng sinh."]}),o.jsx(le,{sx:{mt:3,p:2,bgcolor:"success.light",borderRadius:1},children:o.jsxs(R,{variant:"body2",color:"success.contrastText",children:["🌟 ",o.jsx("strong",{children:"Lợi ích:"})," Giảm tức giận khi thua lỗ, tăng khả năng tha thứ, tạo tâm thái bình an."]})})]})]}),o.jsx(Oo,{children:o.jsx(Ce,{onClick:()=>$(!1),children:"Đóng"})})]}),o.jsxs(Bo,{open:j,onClose:()=>{b(!1),y(null)},maxWidth:"sm",fullWidth:!0,children:[o.jsx(Lo,{children:o.jsxs(le,{sx:{display:"flex",alignItems:"center",gap:1},children:[o.jsx(Ht,{color:"error"}),o.jsx(R,{variant:"h6",children:"Xác nhận xóa phương pháp"})]})}),o.jsxs(Io,{children:[o.jsxs(rn,{children:["Bạn có chắc chắn muốn xóa phương pháp"," ",o.jsxs("strong",{children:['"',P==null?void 0:P.name,'"']}),"?"]}),o.jsx(rn,{sx:{mt:2,color:"error.main"},children:"⚠️ Hành động này không thể hoàn tác. Tất cả dữ liệu liên quan đến phương pháp này sẽ bị xóa vĩnh viễn."}),P&&o.jsxs(le,{sx:{mt:2,p:2,bgcolor:"grey.100",borderRadius:1},children:[o.jsx(R,{variant:"body2",color:"text.secondary",children:o.jsx("strong",{children:"Thông tin phương pháp:"})}),o.jsxs(R,{variant:"body2",children:["• Tên: ",P.name]}),o.jsxs(R,{variant:"body2",children:["• Số câu hỏi: ",((ue=P.questions)==null?void 0:ue.length)||0]}),o.jsxs(R,{variant:"body2",children:["• Điểm tối đa: ",P.totalMaxScore||0]}),o.jsxs(R,{variant:"body2",children:["• Ngày tạo: ",P.createdAt?new Date(P.createdAt).toLocaleDateString("vi-VN"):"N/A"]})]})]}),o.jsxs(Oo,{children:[o.jsx(Ce,{onClick:()=>{b(!1),y(null)},disabled:S,children:"Hủy"}),o.jsx(Ce,{onClick:pe,color:"error",variant:"contained",disabled:S,startIcon:S?o.jsx(Hn,{size:16}):o.jsx(Ht,{}),children:S?"Đang xóa...":"Xóa phương pháp"})]})]})]})]})},bn=document.getElementById("root");bn&&Un.createRoot(bn).render(o.jsx(ii,{}));
