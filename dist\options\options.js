import{g as We,a as Oe,r as g,j as o,n as qt,o as Kt,b as Fe,p as Gt,u as qe,s as re,q as Me,t as me,v as go,w as Lo,x as Eo,P as Ft,y as wn,c as xe,z as Re,f as b,D as it,E as Lt,F as xt,G as yo,H as uo,J as xo,K as To,L as Po,M as jn,N as Jt,O as Nn,d as Ee,B as ee,I as wo,A as Nt,h as ye,i as we,m as mt,l as je,e as Wn,T as Fn,C as Vn,k as Hn,Q as Un,S as qn}from"../assets/TrendingUp-BITtWk55.js";import{o as kt,P as Kn,u as Ge,a as zo,b as Sn,F as Ko,M as Xn,B as Yn,f as Gn,L as Xo,i as co,g as _n,G as Qn,d as Cn,c as kn,T as gt,e as Jn,I as Zn,S as er,A as tr,h as Yo}from"../assets/Psychology-zjuSHFns.js";import{G as W,S as or,B as Zt,a as Ct}from"../assets/Settings-C6j4aHPK.js";var He="top",tt="bottom",ot="right",Ue="left",Do="auto",_t=[He,tt,ot,Ue],Rt="start",Xt="end",nr="clippingParents",Tn="viewport",Et="popper",rr="reference",Go=_t.reduce(function(e,t){return e.concat([t+"-"+Rt,t+"-"+Xt])},[]),Pn=[].concat(_t,[Do]).reduce(function(e,t){return e.concat([t,t+"-"+Rt,t+"-"+Xt])},[]),sr="beforeRead",ar="read",ir="afterRead",lr="beforeMain",cr="main",pr="afterMain",dr="beforeWrite",ur="write",hr="afterWrite",fr=[sr,ar,ir,lr,cr,pr,dr,ur,hr];function ct(e){return e?(e.nodeName||"").toLowerCase():null}function _e(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Pt(e){var t=_e(e).Element;return e instanceof t||e instanceof Element}function et(e){var t=_e(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function No(e){if(typeof ShadowRoot>"u")return!1;var t=_e(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function mr(e){var t=e.state;Object.keys(t.elements).forEach(function(n){var r=t.styles[n]||{},s=t.attributes[n]||{},a=t.elements[n];!et(a)||!ct(a)||(Object.assign(a.style,r),Object.keys(s).forEach(function(c){var i=s[c];i===!1?a.removeAttribute(c):a.setAttribute(c,i===!0?"":i)}))})}function gr(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(r){var s=t.elements[r],a=t.attributes[r]||{},c=Object.keys(t.styles.hasOwnProperty(r)?t.styles[r]:n[r]),i=c.reduce(function(l,m){return l[m]="",l},{});!et(s)||!ct(s)||(Object.assign(s.style,i),Object.keys(a).forEach(function(l){s.removeAttribute(l)}))})}}const xr={name:"applyStyles",enabled:!0,phase:"write",fn:mr,effect:gr,requires:["computeStyles"]};function lt(e){return e.split("-")[0]}var Tt=Math.max,ho=Math.min,At=Math.round;function Mo(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function Mn(){return!/^((?!chrome|android).)*safari/i.test(Mo())}function $t(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!1);var r=e.getBoundingClientRect(),s=1,a=1;t&&et(e)&&(s=e.offsetWidth>0&&At(r.width)/e.offsetWidth||1,a=e.offsetHeight>0&&At(r.height)/e.offsetHeight||1);var c=Pt(e)?_e(e):window,i=c.visualViewport,l=!Mn()&&n,m=(r.left+(l&&i?i.offsetLeft:0))/s,p=(r.top+(l&&i?i.offsetTop:0))/a,P=r.width/s,C=r.height/a;return{width:P,height:C,top:p,right:m+P,bottom:p+C,left:m,x:m,y:p}}function Wo(e){var t=$t(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function Rn(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&No(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function dt(e){return _e(e).getComputedStyle(e)}function vr(e){return["table","td","th"].indexOf(ct(e))>=0}function vt(e){return((Pt(e)?e.ownerDocument:e.document)||window.document).documentElement}function vo(e){return ct(e)==="html"?e:e.assignedSlot||e.parentNode||(No(e)?e.host:null)||vt(e)}function _o(e){return!et(e)||dt(e).position==="fixed"?null:e.offsetParent}function br(e){var t=/firefox/i.test(Mo()),n=/Trident/i.test(Mo());if(n&&et(e)){var r=dt(e);if(r.position==="fixed")return null}var s=vo(e);for(No(s)&&(s=s.host);et(s)&&["html","body"].indexOf(ct(s))<0;){var a=dt(s);if(a.transform!=="none"||a.perspective!=="none"||a.contain==="paint"||["transform","perspective"].indexOf(a.willChange)!==-1||t&&a.willChange==="filter"||t&&a.filter&&a.filter!=="none")return s;s=s.parentNode}return null}function Qt(e){for(var t=_e(e),n=_o(e);n&&vr(n)&&dt(n).position==="static";)n=_o(n);return n&&(ct(n)==="html"||ct(n)==="body"&&dt(n).position==="static")?t:n||br(e)||t}function Fo(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Vt(e,t,n){return Tt(e,ho(t,n))}function yr(e,t,n){var r=Vt(e,t,n);return r>n?n:r}function An(){return{top:0,right:0,bottom:0,left:0}}function $n(e){return Object.assign({},An(),e)}function In(e,t){return t.reduce(function(n,r){return n[r]=e,n},{})}var wr=function(t,n){return t=typeof t=="function"?t(Object.assign({},n.rects,{placement:n.placement})):t,$n(typeof t!="number"?t:In(t,_t))};function jr(e){var t,n=e.state,r=e.name,s=e.options,a=n.elements.arrow,c=n.modifiersData.popperOffsets,i=lt(n.placement),l=Fo(i),m=[Ue,ot].indexOf(i)>=0,p=m?"height":"width";if(!(!a||!c)){var P=wr(s.padding,n),C=Wo(a),y=l==="y"?He:Ue,M=l==="y"?tt:ot,w=n.rects.reference[p]+n.rects.reference[l]-c[l]-n.rects.popper[p],T=c[l]-n.rects.reference[l],A=Qt(a),h=A?l==="y"?A.clientHeight||0:A.clientWidth||0:0,f=w/2-T/2,d=P[y],x=h-C[p]-P[M],S=h/2-C[p]/2+f,I=Vt(d,S,x),Y=l;n.modifiersData[r]=(t={},t[Y]=I,t.centerOffset=I-S,t)}}function Sr(e){var t=e.state,n=e.options,r=n.element,s=r===void 0?"[data-popper-arrow]":r;s!=null&&(typeof s=="string"&&(s=t.elements.popper.querySelector(s),!s)||Rn(t.elements.popper,s)&&(t.elements.arrow=s))}const Cr={name:"arrow",enabled:!0,phase:"main",fn:jr,effect:Sr,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function It(e){return e.split("-")[1]}var kr={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Tr(e,t){var n=e.x,r=e.y,s=t.devicePixelRatio||1;return{x:At(n*s)/s||0,y:At(r*s)/s||0}}function Qo(e){var t,n=e.popper,r=e.popperRect,s=e.placement,a=e.variation,c=e.offsets,i=e.position,l=e.gpuAcceleration,m=e.adaptive,p=e.roundOffsets,P=e.isFixed,C=c.x,y=C===void 0?0:C,M=c.y,w=M===void 0?0:M,T=typeof p=="function"?p({x:y,y:w}):{x:y,y:w};y=T.x,w=T.y;var A=c.hasOwnProperty("x"),h=c.hasOwnProperty("y"),f=Ue,d=He,x=window;if(m){var S=Qt(n),I="clientHeight",Y="clientWidth";if(S===_e(n)&&(S=vt(n),dt(S).position!=="static"&&i==="absolute"&&(I="scrollHeight",Y="scrollWidth")),S=S,s===He||(s===Ue||s===ot)&&a===Xt){d=tt;var L=P&&S===x&&x.visualViewport?x.visualViewport.height:S[I];w-=L-r.height,w*=l?1:-1}if(s===Ue||(s===He||s===tt)&&a===Xt){f=ot;var B=P&&S===x&&x.visualViewport?x.visualViewport.width:S[Y];y-=B-r.width,y*=l?1:-1}}var F=Object.assign({position:i},m&&kr),G=p===!0?Tr({x:y,y:w},_e(n)):{x:y,y:w};if(y=G.x,w=G.y,l){var j;return Object.assign({},F,(j={},j[d]=h?"0":"",j[f]=A?"0":"",j.transform=(x.devicePixelRatio||1)<=1?"translate("+y+"px, "+w+"px)":"translate3d("+y+"px, "+w+"px, 0)",j))}return Object.assign({},F,(t={},t[d]=h?w+"px":"",t[f]=A?y+"px":"",t.transform="",t))}function Pr(e){var t=e.state,n=e.options,r=n.gpuAcceleration,s=r===void 0?!0:r,a=n.adaptive,c=a===void 0?!0:a,i=n.roundOffsets,l=i===void 0?!0:i,m={placement:lt(t.placement),variation:It(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:s,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,Qo(Object.assign({},m,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:c,roundOffsets:l})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,Qo(Object.assign({},m,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}const Mr={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Pr,data:{}};var eo={passive:!0};function Rr(e){var t=e.state,n=e.instance,r=e.options,s=r.scroll,a=s===void 0?!0:s,c=r.resize,i=c===void 0?!0:c,l=_e(t.elements.popper),m=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&m.forEach(function(p){p.addEventListener("scroll",n.update,eo)}),i&&l.addEventListener("resize",n.update,eo),function(){a&&m.forEach(function(p){p.removeEventListener("scroll",n.update,eo)}),i&&l.removeEventListener("resize",n.update,eo)}}const Ar={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Rr,data:{}};var $r={left:"right",right:"left",bottom:"top",top:"bottom"};function po(e){return e.replace(/left|right|bottom|top/g,function(t){return $r[t]})}var Ir={start:"end",end:"start"};function Jo(e){return e.replace(/start|end/g,function(t){return Ir[t]})}function Vo(e){var t=_e(e),n=t.pageXOffset,r=t.pageYOffset;return{scrollLeft:n,scrollTop:r}}function Ho(e){return $t(vt(e)).left+Vo(e).scrollLeft}function Br(e,t){var n=_e(e),r=vt(e),s=n.visualViewport,a=r.clientWidth,c=r.clientHeight,i=0,l=0;if(s){a=s.width,c=s.height;var m=Mn();(m||!m&&t==="fixed")&&(i=s.offsetLeft,l=s.offsetTop)}return{width:a,height:c,x:i+Ho(e),y:l}}function Or(e){var t,n=vt(e),r=Vo(e),s=(t=e.ownerDocument)==null?void 0:t.body,a=Tt(n.scrollWidth,n.clientWidth,s?s.scrollWidth:0,s?s.clientWidth:0),c=Tt(n.scrollHeight,n.clientHeight,s?s.scrollHeight:0,s?s.clientHeight:0),i=-r.scrollLeft+Ho(e),l=-r.scrollTop;return dt(s||n).direction==="rtl"&&(i+=Tt(n.clientWidth,s?s.clientWidth:0)-a),{width:a,height:c,x:i,y:l}}function Uo(e){var t=dt(e),n=t.overflow,r=t.overflowX,s=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+s+r)}function Bn(e){return["html","body","#document"].indexOf(ct(e))>=0?e.ownerDocument.body:et(e)&&Uo(e)?e:Bn(vo(e))}function Ht(e,t){var n;t===void 0&&(t=[]);var r=Bn(e),s=r===((n=e.ownerDocument)==null?void 0:n.body),a=_e(r),c=s?[a].concat(a.visualViewport||[],Uo(r)?r:[]):r,i=t.concat(c);return s?i:i.concat(Ht(vo(c)))}function Ro(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Lr(e,t){var n=$t(e,!1,t==="fixed");return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}function Zo(e,t,n){return t===Tn?Ro(Br(e,n)):Pt(t)?Lr(t,n):Ro(Or(vt(e)))}function Er(e){var t=Ht(vo(e)),n=["absolute","fixed"].indexOf(dt(e).position)>=0,r=n&&et(e)?Qt(e):e;return Pt(r)?t.filter(function(s){return Pt(s)&&Rn(s,r)&&ct(s)!=="body"}):[]}function zr(e,t,n,r){var s=t==="clippingParents"?Er(e):[].concat(t),a=[].concat(s,[n]),c=a[0],i=a.reduce(function(l,m){var p=Zo(e,m,r);return l.top=Tt(p.top,l.top),l.right=ho(p.right,l.right),l.bottom=ho(p.bottom,l.bottom),l.left=Tt(p.left,l.left),l},Zo(e,c,r));return i.width=i.right-i.left,i.height=i.bottom-i.top,i.x=i.left,i.y=i.top,i}function On(e){var t=e.reference,n=e.element,r=e.placement,s=r?lt(r):null,a=r?It(r):null,c=t.x+t.width/2-n.width/2,i=t.y+t.height/2-n.height/2,l;switch(s){case He:l={x:c,y:t.y-n.height};break;case tt:l={x:c,y:t.y+t.height};break;case ot:l={x:t.x+t.width,y:i};break;case Ue:l={x:t.x-n.width,y:i};break;default:l={x:t.x,y:t.y}}var m=s?Fo(s):null;if(m!=null){var p=m==="y"?"height":"width";switch(a){case Rt:l[m]=l[m]-(t[p]/2-n[p]/2);break;case Xt:l[m]=l[m]+(t[p]/2-n[p]/2);break}}return l}function Yt(e,t){t===void 0&&(t={});var n=t,r=n.placement,s=r===void 0?e.placement:r,a=n.strategy,c=a===void 0?e.strategy:a,i=n.boundary,l=i===void 0?nr:i,m=n.rootBoundary,p=m===void 0?Tn:m,P=n.elementContext,C=P===void 0?Et:P,y=n.altBoundary,M=y===void 0?!1:y,w=n.padding,T=w===void 0?0:w,A=$n(typeof T!="number"?T:In(T,_t)),h=C===Et?rr:Et,f=e.rects.popper,d=e.elements[M?h:C],x=zr(Pt(d)?d:d.contextElement||vt(e.elements.popper),l,p,c),S=$t(e.elements.reference),I=On({reference:S,element:f,placement:s}),Y=Ro(Object.assign({},f,I)),L=C===Et?Y:S,B={top:x.top-L.top+A.top,bottom:L.bottom-x.bottom+A.bottom,left:x.left-L.left+A.left,right:L.right-x.right+A.right},F=e.modifiersData.offset;if(C===Et&&F){var G=F[s];Object.keys(B).forEach(function(j){var R=[ot,tt].indexOf(j)>=0?1:-1,Z=[He,tt].indexOf(j)>=0?"y":"x";B[j]+=G[Z]*R})}return B}function Dr(e,t){t===void 0&&(t={});var n=t,r=n.placement,s=n.boundary,a=n.rootBoundary,c=n.padding,i=n.flipVariations,l=n.allowedAutoPlacements,m=l===void 0?Pn:l,p=It(r),P=p?i?Go:Go.filter(function(M){return It(M)===p}):_t,C=P.filter(function(M){return m.indexOf(M)>=0});C.length===0&&(C=P);var y=C.reduce(function(M,w){return M[w]=Yt(e,{placement:w,boundary:s,rootBoundary:a,padding:c})[lt(w)],M},{});return Object.keys(y).sort(function(M,w){return y[M]-y[w]})}function Nr(e){if(lt(e)===Do)return[];var t=po(e);return[Jo(e),t,Jo(t)]}function Wr(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var s=n.mainAxis,a=s===void 0?!0:s,c=n.altAxis,i=c===void 0?!0:c,l=n.fallbackPlacements,m=n.padding,p=n.boundary,P=n.rootBoundary,C=n.altBoundary,y=n.flipVariations,M=y===void 0?!0:y,w=n.allowedAutoPlacements,T=t.options.placement,A=lt(T),h=A===T,f=l||(h||!M?[po(T)]:Nr(T)),d=[T].concat(f).reduce(function(ve,X){return ve.concat(lt(X)===Do?Dr(t,{placement:X,boundary:p,rootBoundary:P,padding:m,flipVariations:M,allowedAutoPlacements:w}):X)},[]),x=t.rects.reference,S=t.rects.popper,I=new Map,Y=!0,L=d[0],B=0;B<d.length;B++){var F=d[B],G=lt(F),j=It(F)===Rt,R=[He,tt].indexOf(G)>=0,Z=R?"width":"height",E=Yt(t,{placement:F,boundary:p,rootBoundary:P,altBoundary:C,padding:m}),J=R?j?ot:Ue:j?tt:He;x[Z]>S[Z]&&(J=po(J));var U=po(J),ce=[];if(a&&ce.push(E[G]<=0),i&&ce.push(E[J]<=0,E[U]<=0),ce.every(function(ve){return ve})){L=F,Y=!1;break}I.set(F,ce)}if(Y)for(var se=M?3:1,q=function(X){var te=d.find(function(de){var he=I.get(de);if(he)return he.slice(0,X).every(function(Te){return Te})});if(te)return L=te,"break"},K=se;K>0;K--){var Ae=q(K);if(Ae==="break")break}t.placement!==L&&(t.modifiersData[r]._skip=!0,t.placement=L,t.reset=!0)}}const Fr={name:"flip",enabled:!0,phase:"main",fn:Wr,requiresIfExists:["offset"],data:{_skip:!1}};function en(e,t,n){return n===void 0&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function tn(e){return[He,ot,tt,Ue].some(function(t){return e[t]>=0})}function Vr(e){var t=e.state,n=e.name,r=t.rects.reference,s=t.rects.popper,a=t.modifiersData.preventOverflow,c=Yt(t,{elementContext:"reference"}),i=Yt(t,{altBoundary:!0}),l=en(c,r),m=en(i,s,a),p=tn(l),P=tn(m);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:m,isReferenceHidden:p,hasPopperEscaped:P},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":p,"data-popper-escaped":P})}const Hr={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Vr};function Ur(e,t,n){var r=lt(e),s=[Ue,He].indexOf(r)>=0?-1:1,a=typeof n=="function"?n(Object.assign({},t,{placement:e})):n,c=a[0],i=a[1];return c=c||0,i=(i||0)*s,[Ue,ot].indexOf(r)>=0?{x:i,y:c}:{x:c,y:i}}function qr(e){var t=e.state,n=e.options,r=e.name,s=n.offset,a=s===void 0?[0,0]:s,c=Pn.reduce(function(p,P){return p[P]=Ur(P,t.rects,a),p},{}),i=c[t.placement],l=i.x,m=i.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=m),t.modifiersData[r]=c}const Kr={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:qr};function Xr(e){var t=e.state,n=e.name;t.modifiersData[n]=On({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})}const Yr={name:"popperOffsets",enabled:!0,phase:"read",fn:Xr,data:{}};function Gr(e){return e==="x"?"y":"x"}function _r(e){var t=e.state,n=e.options,r=e.name,s=n.mainAxis,a=s===void 0?!0:s,c=n.altAxis,i=c===void 0?!1:c,l=n.boundary,m=n.rootBoundary,p=n.altBoundary,P=n.padding,C=n.tether,y=C===void 0?!0:C,M=n.tetherOffset,w=M===void 0?0:M,T=Yt(t,{boundary:l,rootBoundary:m,padding:P,altBoundary:p}),A=lt(t.placement),h=It(t.placement),f=!h,d=Fo(A),x=Gr(d),S=t.modifiersData.popperOffsets,I=t.rects.reference,Y=t.rects.popper,L=typeof w=="function"?w(Object.assign({},t.rects,{placement:t.placement})):w,B=typeof L=="number"?{mainAxis:L,altAxis:L}:Object.assign({mainAxis:0,altAxis:0},L),F=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,G={x:0,y:0};if(S){if(a){var j,R=d==="y"?He:Ue,Z=d==="y"?tt:ot,E=d==="y"?"height":"width",J=S[d],U=J+T[R],ce=J-T[Z],se=y?-Y[E]/2:0,q=h===Rt?I[E]:Y[E],K=h===Rt?-Y[E]:-I[E],Ae=t.elements.arrow,ve=y&&Ae?Wo(Ae):{width:0,height:0},X=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:An(),te=X[R],de=X[Z],he=Vt(0,I[E],ve[E]),Te=f?I[E]/2-se-he-te-B.mainAxis:q-he-te-B.mainAxis,u=f?-I[E]/2+se+he+de+B.mainAxis:K+he+de+B.mainAxis,O=t.elements.arrow&&Qt(t.elements.arrow),z=O?d==="y"?O.clientTop||0:O.clientLeft||0:0,H=(j=F==null?void 0:F[d])!=null?j:0,ae=J+Te-H-z,$e=J+u-H,ze=Vt(y?ho(U,ae):U,J,y?Tt(ce,$e):ce);S[d]=ze,G[d]=ze-J}if(i){var oe,Se=d==="x"?He:Ue,Le=d==="x"?tt:ot,Ie=S[x],Be=x==="y"?"height":"width",Ve=Ie+T[Se],De=Ie-T[Le],Ke=[He,Ue].indexOf(A)!==-1,v=(oe=F==null?void 0:F[x])!=null?oe:0,k=Ke?Ve:Ie-I[Be]-Y[Be]-v+B.altAxis,D=Ke?Ie+I[Be]+Y[Be]-v-B.altAxis:De,_=y&&Ke?yr(k,Ie,D):Vt(y?k:Ve,Ie,y?D:De);S[x]=_,G[x]=_-Ie}t.modifiersData[r]=G}}const Qr={name:"preventOverflow",enabled:!0,phase:"main",fn:_r,requiresIfExists:["offset"]};function Jr(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function Zr(e){return e===_e(e)||!et(e)?Vo(e):Jr(e)}function es(e){var t=e.getBoundingClientRect(),n=At(t.width)/e.offsetWidth||1,r=At(t.height)/e.offsetHeight||1;return n!==1||r!==1}function ts(e,t,n){n===void 0&&(n=!1);var r=et(t),s=et(t)&&es(t),a=vt(t),c=$t(e,s,n),i={scrollLeft:0,scrollTop:0},l={x:0,y:0};return(r||!r&&!n)&&((ct(t)!=="body"||Uo(a))&&(i=Zr(t)),et(t)?(l=$t(t,!0),l.x+=t.clientLeft,l.y+=t.clientTop):a&&(l.x=Ho(a))),{x:c.left+i.scrollLeft-l.x,y:c.top+i.scrollTop-l.y,width:c.width,height:c.height}}function os(e){var t=new Map,n=new Set,r=[];e.forEach(function(a){t.set(a.name,a)});function s(a){n.add(a.name);var c=[].concat(a.requires||[],a.requiresIfExists||[]);c.forEach(function(i){if(!n.has(i)){var l=t.get(i);l&&s(l)}}),r.push(a)}return e.forEach(function(a){n.has(a.name)||s(a)}),r}function ns(e){var t=os(e);return fr.reduce(function(n,r){return n.concat(t.filter(function(s){return s.phase===r}))},[])}function rs(e){var t;return function(){return t||(t=new Promise(function(n){Promise.resolve().then(function(){t=void 0,n(e())})})),t}}function ss(e){var t=e.reduce(function(n,r){var s=n[r.name];return n[r.name]=s?Object.assign({},s,r,{options:Object.assign({},s.options,r.options),data:Object.assign({},s.data,r.data)}):r,n},{});return Object.keys(t).map(function(n){return t[n]})}var on={placement:"bottom",modifiers:[],strategy:"absolute"};function nn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(r){return!(r&&typeof r.getBoundingClientRect=="function")})}function as(e){e===void 0&&(e={});var t=e,n=t.defaultModifiers,r=n===void 0?[]:n,s=t.defaultOptions,a=s===void 0?on:s;return function(i,l,m){m===void 0&&(m=a);var p={placement:"bottom",orderedModifiers:[],options:Object.assign({},on,a),modifiersData:{},elements:{reference:i,popper:l},attributes:{},styles:{}},P=[],C=!1,y={state:p,setOptions:function(A){var h=typeof A=="function"?A(p.options):A;w(),p.options=Object.assign({},a,p.options,h),p.scrollParents={reference:Pt(i)?Ht(i):i.contextElement?Ht(i.contextElement):[],popper:Ht(l)};var f=ns(ss([].concat(r,p.options.modifiers)));return p.orderedModifiers=f.filter(function(d){return d.enabled}),M(),y.update()},forceUpdate:function(){if(!C){var A=p.elements,h=A.reference,f=A.popper;if(nn(h,f)){p.rects={reference:ts(h,Qt(f),p.options.strategy==="fixed"),popper:Wo(f)},p.reset=!1,p.placement=p.options.placement,p.orderedModifiers.forEach(function(B){return p.modifiersData[B.name]=Object.assign({},B.data)});for(var d=0;d<p.orderedModifiers.length;d++){if(p.reset===!0){p.reset=!1,d=-1;continue}var x=p.orderedModifiers[d],S=x.fn,I=x.options,Y=I===void 0?{}:I,L=x.name;typeof S=="function"&&(p=S({state:p,options:Y,name:L,instance:y})||p)}}}},update:rs(function(){return new Promise(function(T){y.forceUpdate(),T(p)})}),destroy:function(){w(),C=!0}};if(!nn(i,l))return y;y.setOptions(m).then(function(T){!C&&m.onFirstUpdate&&m.onFirstUpdate(T)});function M(){p.orderedModifiers.forEach(function(T){var A=T.name,h=T.options,f=h===void 0?{}:h,d=T.effect;if(typeof d=="function"){var x=d({state:p,name:A,instance:y,options:f}),S=function(){};P.push(x||S)}})}function w(){P.forEach(function(T){return T()}),P=[]}return y}}var is=[Ar,Yr,Mr,xr,Kr,Fr,Qr,Cr,Hr],ls=as({defaultModifiers:is});function cs(e){return We("MuiPopper",e)}Oe("MuiPopper",["root"]);function ps(e,t){if(t==="ltr")return e;switch(e){case"bottom-end":return"bottom-start";case"bottom-start":return"bottom-end";case"top-end":return"top-start";case"top-start":return"top-end";default:return e}}function Ao(e){return typeof e=="function"?e():e}function ds(e){return e.nodeType!==void 0}const us=e=>{const{classes:t}=e;return Fe({root:["root"]},cs,t)},hs={},fs=g.forwardRef(function(t,n){const{anchorEl:r,children:s,direction:a,disablePortal:c,modifiers:i,open:l,placement:m,popperOptions:p,popperRef:P,slotProps:C={},slots:y={},TransitionProps:M,ownerState:w,...T}=t,A=g.useRef(null),h=qt(A,n),f=g.useRef(null),d=qt(f,P),x=g.useRef(d);Kt(()=>{x.current=d},[d]),g.useImperativeHandle(P,()=>f.current,[]);const S=ps(m,a),[I,Y]=g.useState(S),[L,B]=g.useState(Ao(r));g.useEffect(()=>{f.current&&f.current.forceUpdate()}),g.useEffect(()=>{r&&B(Ao(r))},[r]),Kt(()=>{if(!L||!l)return;const Z=U=>{Y(U.placement)};let E=[{name:"preventOverflow",options:{altBoundary:c}},{name:"flip",options:{altBoundary:c}},{name:"onUpdate",enabled:!0,phase:"afterWrite",fn:({state:U})=>{Z(U)}}];i!=null&&(E=E.concat(i)),p&&p.modifiers!=null&&(E=E.concat(p.modifiers));const J=ls(L,A.current,{placement:S,...p,modifiers:E});return x.current(J),()=>{J.destroy(),x.current(null)}},[L,c,i,l,p,S]);const F={placement:I};M!==null&&(F.TransitionProps=M);const G=us(t),j=y.root??"div",R=Ge({elementType:j,externalSlotProps:C.root,externalForwardedProps:T,additionalProps:{role:"tooltip",ref:h},ownerState:t,className:G.root});return o.jsx(j,{...R,children:typeof s=="function"?s(F):s})}),ms=g.forwardRef(function(t,n){const{anchorEl:r,children:s,container:a,direction:c="ltr",disablePortal:i=!1,keepMounted:l=!1,modifiers:m,open:p,placement:P="bottom",popperOptions:C=hs,popperRef:y,style:M,transition:w=!1,slotProps:T={},slots:A={},...h}=t,[f,d]=g.useState(!0),x=()=>{d(!1)},S=()=>{d(!0)};if(!l&&!p&&(!w||f))return null;let I;if(a)I=a;else if(r){const B=Ao(r);I=B&&ds(B)?kt(B).body:kt(null).body}const Y=!p&&l&&(!w||f)?"none":void 0,L=w?{in:p,onEnter:x,onExited:S}:void 0;return o.jsx(Kn,{disablePortal:i,container:I,children:o.jsx(fs,{anchorEl:r,direction:c,disablePortal:i,modifiers:m,ref:n,open:w?!f:p,placement:P,popperOptions:C,popperRef:y,slotProps:T,slots:A,...h,style:{position:"fixed",top:0,left:0,display:Y,...M},TransitionProps:L,children:s})})}),gs=re(ms,{name:"MuiPopper",slot:"Root"})({}),Ln=g.forwardRef(function(t,n){const r=Gt(),s=qe({props:t,name:"MuiPopper"}),{anchorEl:a,component:c,components:i,componentsProps:l,container:m,disablePortal:p,keepMounted:P,modifiers:C,open:y,placement:M,popperOptions:w,popperRef:T,transition:A,slots:h,slotProps:f,...d}=s,x=(h==null?void 0:h.root)??(i==null?void 0:i.Root),S={anchorEl:a,container:m,disablePortal:p,keepMounted:P,modifiers:C,open:y,placement:M,popperOptions:w,popperRef:T,transition:A,...d};return o.jsx(gs,{as:c,direction:r?"rtl":"ltr",slots:{root:x},slotProps:f??l,...S,ref:n})});function xs(e){return We("PrivateSwitchBase",e)}Oe("PrivateSwitchBase",["root","checked","disabled","input","edgeStart","edgeEnd"]);const vs=e=>{const{classes:t,checked:n,disabled:r,edge:s}=e,a={root:["root",n&&"checked",r&&"disabled",s&&`edge${me(s)}`],input:["input"]};return Fe(a,xs,t)},bs=re(go)({padding:9,borderRadius:"50%",variants:[{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:({edge:e,ownerState:t})=>e==="start"&&t.size!=="small",style:{marginLeft:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}},{props:({edge:e,ownerState:t})=>e==="end"&&t.size!=="small",style:{marginRight:-12}}]}),ys=re("input",{shouldForwardProp:Lo})({cursor:"inherit",position:"absolute",opacity:0,width:"100%",height:"100%",top:0,left:0,margin:0,padding:0,zIndex:1}),ws=g.forwardRef(function(t,n){const{autoFocus:r,checked:s,checkedIcon:a,defaultChecked:c,disabled:i,disableFocusRipple:l=!1,edge:m=!1,icon:p,id:P,inputProps:C,inputRef:y,name:M,onBlur:w,onChange:T,onFocus:A,readOnly:h,required:f=!1,tabIndex:d,type:x,value:S,slots:I={},slotProps:Y={},...L}=t,[B,F]=zo({controlled:s,default:!!c,name:"SwitchBase",state:"checked"}),G=Sn(),j=X=>{A&&A(X),G&&G.onFocus&&G.onFocus(X)},R=X=>{w&&w(X),G&&G.onBlur&&G.onBlur(X)},Z=X=>{if(X.nativeEvent.defaultPrevented)return;const te=X.target.checked;F(te),T&&T(X,te)};let E=i;G&&typeof E>"u"&&(E=G.disabled);const J=x==="checkbox"||x==="radio",U={...t,checked:B,disabled:E,disableFocusRipple:l,edge:m},ce=vs(U),se={slots:I,slotProps:{input:C,...Y}},[q,K]=Me("root",{ref:n,elementType:bs,className:ce.root,shouldForwardComponentProp:!0,externalForwardedProps:{...se,component:"span",...L},getSlotProps:X=>({...X,onFocus:te=>{var de;(de=X.onFocus)==null||de.call(X,te),j(te)},onBlur:te=>{var de;(de=X.onBlur)==null||de.call(X,te),R(te)}}),ownerState:U,additionalProps:{centerRipple:!0,focusRipple:!l,disabled:E,role:void 0,tabIndex:null}}),[Ae,ve]=Me("input",{ref:y,elementType:ys,className:ce.input,externalForwardedProps:se,getSlotProps:X=>({...X,onChange:te=>{var de;(de=X.onChange)==null||de.call(X,te),Z(te)}}),ownerState:U,additionalProps:{autoFocus:r,checked:s,defaultChecked:c,disabled:E,id:J?P:void 0,name:M,readOnly:h,required:f,tabIndex:d,type:x,...x==="checkbox"&&S===void 0?{}:{value:S}}});return o.jsxs(q,{...K,children:[o.jsx(Ae,{...ve}),B?a:p]})});function js(e){return We("MuiDialog",e)}const jo=Oe("MuiDialog",["root","scrollPaper","scrollBody","container","paper","paperScrollPaper","paperScrollBody","paperWidthFalse","paperWidthXs","paperWidthSm","paperWidthMd","paperWidthLg","paperWidthXl","paperFullWidth","paperFullScreen"]),En=g.createContext({}),Ss=re(Yn,{name:"MuiDialog",slot:"Backdrop",overrides:(e,t)=>t.backdrop})({zIndex:-1}),Cs=e=>{const{classes:t,scroll:n,maxWidth:r,fullWidth:s,fullScreen:a}=e,c={root:["root"],container:["container",`scroll${me(n)}`],paper:["paper",`paperScroll${me(n)}`,`paperWidth${me(String(r))}`,s&&"paperFullWidth",a&&"paperFullScreen"]};return Fe(c,js,t)},ks=re(Xn,{name:"MuiDialog",slot:"Root"})({"@media print":{position:"absolute !important"}}),Ts=re("div",{name:"MuiDialog",slot:"Container",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.container,t[`scroll${me(n.scroll)}`]]}})({height:"100%","@media print":{height:"auto"},outline:0,variants:[{props:{scroll:"paper"},style:{display:"flex",justifyContent:"center",alignItems:"center"}},{props:{scroll:"body"},style:{overflowY:"auto",overflowX:"hidden",textAlign:"center","&::after":{content:'""',display:"inline-block",verticalAlign:"middle",height:"100%",width:"0"}}}]}),Ps=re(Ft,{name:"MuiDialog",slot:"Paper",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.paper,t[`scrollPaper${me(n.scroll)}`],t[`paperWidth${me(String(n.maxWidth))}`],n.fullWidth&&t.paperFullWidth,n.fullScreen&&t.paperFullScreen]}})(Re(({theme:e})=>({margin:32,position:"relative",overflowY:"auto","@media print":{overflowY:"visible",boxShadow:"none"},variants:[{props:{scroll:"paper"},style:{display:"flex",flexDirection:"column",maxHeight:"calc(100% - 64px)"}},{props:{scroll:"body"},style:{display:"inline-block",verticalAlign:"middle",textAlign:"initial"}},{props:({ownerState:t})=>!t.maxWidth,style:{maxWidth:"calc(100% - 64px)"}},{props:{maxWidth:"xs"},style:{maxWidth:e.breakpoints.unit==="px"?Math.max(e.breakpoints.values.xs,444):`max(${e.breakpoints.values.xs}${e.breakpoints.unit}, 444px)`,[`&.${jo.paperScrollBody}`]:{[e.breakpoints.down(Math.max(e.breakpoints.values.xs,444)+32*2)]:{maxWidth:"calc(100% - 64px)"}}}},...Object.keys(e.breakpoints.values).filter(t=>t!=="xs").map(t=>({props:{maxWidth:t},style:{maxWidth:`${e.breakpoints.values[t]}${e.breakpoints.unit}`,[`&.${jo.paperScrollBody}`]:{[e.breakpoints.down(e.breakpoints.values[t]+32*2)]:{maxWidth:"calc(100% - 64px)"}}}})),{props:({ownerState:t})=>t.fullWidth,style:{width:"calc(100% - 64px)"}},{props:({ownerState:t})=>t.fullScreen,style:{margin:0,width:"100%",maxWidth:"100%",height:"100%",maxHeight:"none",borderRadius:0,[`&.${jo.paperScrollBody}`]:{margin:0,maxWidth:"100%"}}}]}))),$o=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiDialog"}),s=Eo(),a={enter:s.transitions.duration.enteringScreen,exit:s.transitions.duration.leavingScreen},{"aria-describedby":c,"aria-labelledby":i,"aria-modal":l=!0,BackdropComponent:m,BackdropProps:p,children:P,className:C,disableEscapeKeyDown:y=!1,fullScreen:M=!1,fullWidth:w=!1,maxWidth:T="sm",onClick:A,onClose:h,open:f,PaperComponent:d=Ft,PaperProps:x={},scroll:S="paper",slots:I={},slotProps:Y={},TransitionComponent:L=Ko,transitionDuration:B=a,TransitionProps:F,...G}=r,j={...r,disableEscapeKeyDown:y,fullScreen:M,fullWidth:w,maxWidth:T,scroll:S},R=Cs(j),Z=g.useRef(),E=H=>{Z.current=H.target===H.currentTarget},J=H=>{A&&A(H),Z.current&&(Z.current=null,h&&h(H,"backdropClick"))},U=wn(i),ce=g.useMemo(()=>({titleId:U}),[U]),se={transition:L,...I},q={transition:F,paper:x,backdrop:p,...Y},K={slots:se,slotProps:q},[Ae,ve]=Me("root",{elementType:ks,shouldForwardComponentProp:!0,externalForwardedProps:K,ownerState:j,className:xe(R.root,C),ref:n}),[X,te]=Me("backdrop",{elementType:Ss,shouldForwardComponentProp:!0,externalForwardedProps:K,ownerState:j}),[de,he]=Me("paper",{elementType:Ps,shouldForwardComponentProp:!0,externalForwardedProps:K,ownerState:j,className:xe(R.paper,x.className)}),[Te,u]=Me("container",{elementType:Ts,externalForwardedProps:K,ownerState:j,className:R.container}),[O,z]=Me("transition",{elementType:Ko,externalForwardedProps:K,ownerState:j,additionalProps:{appear:!0,in:f,timeout:B,role:"presentation"}});return o.jsx(Ae,{closeAfterTransition:!0,slots:{backdrop:X},slotProps:{backdrop:{transitionDuration:B,as:m,...te}},disableEscapeKeyDown:y,onClose:h,open:f,onClick:J,...ve,...G,children:o.jsx(O,{...z,children:o.jsx(Te,{onMouseDown:E,...u,children:o.jsx(de,{as:d,elevation:24,role:"dialog","aria-describedby":c,"aria-labelledby":U,"aria-modal":l,...he,children:o.jsx(En.Provider,{value:ce,children:P})})})})})});function Ms(e){return We("MuiDialogActions",e)}Oe("MuiDialogActions",["root","spacing"]);const Rs=e=>{const{classes:t,disableSpacing:n}=e;return Fe({root:["root",!n&&"spacing"]},Ms,t)},As=re("div",{name:"MuiDialogActions",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,!n.disableSpacing&&t.spacing]}})({display:"flex",alignItems:"center",padding:8,justifyContent:"flex-end",flex:"0 0 auto",variants:[{props:({ownerState:e})=>!e.disableSpacing,style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),Io=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiDialogActions"}),{className:s,disableSpacing:a=!1,...c}=r,i={...r,disableSpacing:a},l=Rs(i);return o.jsx(As,{className:xe(l.root,s),ownerState:i,ref:n,...c})});function $s(e){return We("MuiDialogContent",e)}Oe("MuiDialogContent",["root","dividers"]);function Is(e){return We("MuiDialogTitle",e)}const Bs=Oe("MuiDialogTitle",["root"]),Os=e=>{const{classes:t,dividers:n}=e;return Fe({root:["root",n&&"dividers"]},$s,t)},Ls=re("div",{name:"MuiDialogContent",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.dividers&&t.dividers]}})(Re(({theme:e})=>({flex:"1 1 auto",WebkitOverflowScrolling:"touch",overflowY:"auto",padding:"20px 24px",variants:[{props:({ownerState:t})=>t.dividers,style:{padding:"16px 24px",borderTop:`1px solid ${(e.vars||e).palette.divider}`,borderBottom:`1px solid ${(e.vars||e).palette.divider}`}},{props:({ownerState:t})=>!t.dividers,style:{[`.${Bs.root} + &`]:{paddingTop:0}}}]}))),Bo=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiDialogContent"}),{className:s,dividers:a=!1,...c}=r,i={...r,dividers:a},l=Os(i);return o.jsx(Ls,{className:xe(l.root,s),ownerState:i,ref:n,...c})});function Es(e){return We("MuiDialogContentText",e)}Oe("MuiDialogContentText",["root"]);const zs=e=>{const{classes:t}=e,r=Fe({root:["root"]},Es,t);return{...t,...r}},Ds=re(b,{shouldForwardProp:e=>Lo(e)||e==="classes",name:"MuiDialogContentText",slot:"Root"})({}),rn=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiDialogContentText"}),{children:s,className:a,...c}=r,i=zs(c);return o.jsx(Ds,{component:"p",variant:"body1",color:"textSecondary",ref:n,ownerState:c,className:xe(i.root,a),...r,classes:i})}),Ns=e=>{const{classes:t}=e;return Fe({root:["root"]},Is,t)},Ws=re(b,{name:"MuiDialogTitle",slot:"Root"})({padding:"16px 24px",flex:"0 0 auto"}),Oo=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiDialogTitle"}),{className:s,id:a,...c}=r,i=r,l=Ns(i),{titleId:m=a}=g.useContext(En);return o.jsx(Ws,{component:"h2",className:xe(l.root,s),ownerState:i,ref:n,variant:"h6",id:a??m,...c})}),sn=Oe("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"]);function Fs(e){return We("MuiFormControlLabel",e)}const Wt=Oe("MuiFormControlLabel",["root","labelPlacementStart","labelPlacementTop","labelPlacementBottom","disabled","label","error","required","asterisk"]),Vs=e=>{const{classes:t,disabled:n,labelPlacement:r,error:s,required:a}=e,c={root:["root",n&&"disabled",`labelPlacement${me(r)}`,s&&"error",a&&"required"],label:["label",n&&"disabled"],asterisk:["asterisk",s&&"error"]};return Fe(c,Fs,t)},Hs=re("label",{name:"MuiFormControlLabel",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[{[`& .${Wt.label}`]:t.label},t.root,t[`labelPlacement${me(n.labelPlacement)}`]]}})(Re(({theme:e})=>({display:"inline-flex",alignItems:"center",cursor:"pointer",verticalAlign:"middle",WebkitTapHighlightColor:"transparent",marginLeft:-11,marginRight:16,[`&.${Wt.disabled}`]:{cursor:"default"},[`& .${Wt.label}`]:{[`&.${Wt.disabled}`]:{color:(e.vars||e).palette.text.disabled}},variants:[{props:{labelPlacement:"start"},style:{flexDirection:"row-reverse",marginRight:-11}},{props:{labelPlacement:"top"},style:{flexDirection:"column-reverse"}},{props:{labelPlacement:"bottom"},style:{flexDirection:"column"}},{props:({labelPlacement:t})=>t==="start"||t==="top"||t==="bottom",style:{marginLeft:16}}]}))),Us=re("span",{name:"MuiFormControlLabel",slot:"Asterisk"})(Re(({theme:e})=>({[`&.${Wt.error}`]:{color:(e.vars||e).palette.error.main}}))),st=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiFormControlLabel"}),{checked:s,className:a,componentsProps:c={},control:i,disabled:l,disableTypography:m,inputRef:p,label:P,labelPlacement:C="end",name:y,onChange:M,required:w,slots:T={},slotProps:A={},value:h,...f}=r,d=Sn(),x=l??i.props.disabled??(d==null?void 0:d.disabled),S=w??i.props.required,I={disabled:x,required:S};["checked","name","onChange","value","inputRef"].forEach(Z=>{typeof i.props[Z]>"u"&&typeof r[Z]<"u"&&(I[Z]=r[Z])});const Y=Gn({props:r,muiFormControl:d,states:["error"]}),L={...r,disabled:x,labelPlacement:C,required:S,error:Y.error},B=Vs(L),F={slots:T,slotProps:{...c,...A}},[G,j]=Me("typography",{elementType:b,externalForwardedProps:F,ownerState:L});let R=P;return R!=null&&R.type!==b&&!m&&(R=o.jsx(G,{component:"span",...j,className:xe(B.label,j==null?void 0:j.className),children:R})),o.jsxs(Hs,{className:xe(B.root,a),ownerState:L,ref:n,...f,children:[g.cloneElement(i,I),S?o.jsxs("div",{children:[R,o.jsxs(Us,{ownerState:L,"aria-hidden":!0,className:B.asterisk,children:[" ","*"]})]}):R]})}),an=Oe("MuiListItemIcon",["root","alignItemsFlexStart"]),ln=Oe("MuiListItemText",["root","multiline","dense","inset","primary","secondary"]);function qs(e){return We("MuiMenuItem",e)}const zt=Oe("MuiMenuItem",["root","focusVisible","dense","disabled","divider","gutters","selected"]),Ks=(e,t)=>{const{ownerState:n}=e;return[t.root,n.dense&&t.dense,n.divider&&t.divider,!n.disableGutters&&t.gutters]},Xs=e=>{const{disabled:t,dense:n,divider:r,disableGutters:s,selected:a,classes:c}=e,l=Fe({root:["root",n&&"dense",t&&"disabled",!s&&"gutters",r&&"divider",a&&"selected"]},qs,c);return{...c,...l}},Ys=re(go,{shouldForwardProp:e=>Lo(e)||e==="classes",name:"MuiMenuItem",slot:"Root",overridesResolver:Ks})(Re(({theme:e})=>({...e.typography.body1,display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minHeight:48,paddingTop:6,paddingBottom:6,boxSizing:"border-box",whiteSpace:"nowrap","&:hover":{textDecoration:"none",backgroundColor:(e.vars||e).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${zt.selected}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${e.vars.palette.action.selectedOpacity})`:it(e.palette.primary.main,e.palette.action.selectedOpacity),[`&.${zt.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.focusOpacity}))`:it(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.focusOpacity)}},[`&.${zt.selected}:hover`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.hoverOpacity}))`:it(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${e.vars.palette.action.selectedOpacity})`:it(e.palette.primary.main,e.palette.action.selectedOpacity)}},[`&.${zt.focusVisible}`]:{backgroundColor:(e.vars||e).palette.action.focus},[`&.${zt.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity},[`& + .${sn.root}`]:{marginTop:e.spacing(1),marginBottom:e.spacing(1)},[`& + .${sn.inset}`]:{marginLeft:52},[`& .${ln.root}`]:{marginTop:0,marginBottom:0},[`& .${ln.inset}`]:{paddingLeft:36},[`& .${an.root}`]:{minWidth:36},variants:[{props:({ownerState:t})=>!t.disableGutters,style:{paddingLeft:16,paddingRight:16}},{props:({ownerState:t})=>t.divider,style:{borderBottom:`1px solid ${(e.vars||e).palette.divider}`,backgroundClip:"padding-box"}},{props:({ownerState:t})=>!t.dense,style:{[e.breakpoints.up("sm")]:{minHeight:"auto"}}},{props:({ownerState:t})=>t.dense,style:{minHeight:32,paddingTop:4,paddingBottom:4,...e.typography.body2,[`& .${an.root} svg`]:{fontSize:"1.25rem"}}}]}))),So=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiMenuItem"}),{autoFocus:s=!1,component:a="li",dense:c=!1,divider:i=!1,disableGutters:l=!1,focusVisibleClassName:m,role:p="menuitem",tabIndex:P,className:C,...y}=r,M=g.useContext(Xo),w=g.useMemo(()=>({dense:c||M.dense||!1,disableGutters:l}),[M.dense,c,l]),T=g.useRef(null);Kt(()=>{s&&T.current&&T.current.focus()},[s]);const A={...r,dense:w.dense,divider:i,disableGutters:l},h=Xs(r),f=qt(T,n);let d;return r.disabled||(d=P!==void 0?P:-1),o.jsx(Xo.Provider,{value:w,children:o.jsx(Ys,{ref:f,role:p,tabIndex:d,component:a,focusVisibleClassName:xe(h.focusVisible,m),className:xe(h.root,C),...y,ownerState:A,classes:h})})}),Gs={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",whiteSpace:"nowrap",width:"1px"};function _s(e,t,n=(r,s)=>r===s){return e.length===t.length&&e.every((r,s)=>n(r,t[s]))}const Qs=2;function Mt(e,t,n,r,s){return n===1?Math.min(e+t,s):Math.max(e-t,r)}function zn(e,t){return e-t}function cn(e,t){const{index:n}=e.reduce((r,s,a)=>{const c=Math.abs(t-s);return r===null||c<r.distance||c===r.distance?{distance:c,index:a}:r},null)??{};return n}function to(e,t){if(t.current!==void 0&&e.changedTouches){const n=e;for(let r=0;r<n.changedTouches.length;r+=1){const s=n.changedTouches[r];if(s.identifier===t.current)return{x:s.clientX,y:s.clientY}}return!1}return{x:e.clientX,y:e.clientY}}function fo(e,t,n){return(e-t)*100/(n-t)}function Js(e,t,n){return(n-t)*e+t}function Zs(e){if(Math.abs(e)<1){const n=e.toExponential().split("e-"),r=n[0].split(".")[1];return(r?r.length:0)+parseInt(n[1],10)}const t=e.toString().split(".")[1];return t?t.length:0}function ea(e,t,n){const r=Math.round((e-n)/t)*t+n;return Number(r.toFixed(Zs(t)))}function pn({values:e,newValue:t,index:n}){const r=e.slice();return r[n]=t,r.sort(zn)}function oo({sliderRef:e,activeIndex:t,setActive:n}){var s,a,c;const r=kt(e.current);(!((s=e.current)!=null&&s.contains(r.activeElement))||Number((a=r==null?void 0:r.activeElement)==null?void 0:a.getAttribute("data-index"))!==t)&&((c=e.current)==null||c.querySelector(`[type="range"][data-index="${t}"]`).focus()),n&&n(t)}function no(e,t){return typeof e=="number"&&typeof t=="number"?e===t:typeof e=="object"&&typeof t=="object"?_s(e,t):!1}const ta={horizontal:{offset:e=>({left:`${e}%`}),leap:e=>({width:`${e}%`})},"horizontal-reverse":{offset:e=>({right:`${e}%`}),leap:e=>({width:`${e}%`})},vertical:{offset:e=>({bottom:`${e}%`}),leap:e=>({height:`${e}%`})}},oa=e=>e;let ro;function dn(){return ro===void 0&&(typeof CSS<"u"&&typeof CSS.supports=="function"?ro=CSS.supports("touch-action","none"):ro=!0),ro}function na(e){const{"aria-labelledby":t,defaultValue:n,disabled:r=!1,disableSwap:s=!1,isRtl:a=!1,marks:c=!1,max:i=100,min:l=0,name:m,onChange:p,onChangeCommitted:P,orientation:C="horizontal",rootRef:y,scale:M=oa,step:w=1,shiftStep:T=10,tabIndex:A,value:h}=e,f=g.useRef(void 0),[d,x]=g.useState(-1),[S,I]=g.useState(-1),[Y,L]=g.useState(!1),B=g.useRef(0),F=g.useRef(null),[G,j]=zo({controlled:h,default:n??l,name:"Slider"}),R=p&&((v,k,D)=>{const _=v.nativeEvent||v,ne=new _.constructor(_.type,_);Object.defineProperty(ne,"target",{writable:!0,value:{value:k,name:m}}),F.current=k,p(ne,k,D)}),Z=Array.isArray(G);let E=Z?G.slice().sort(zn):[G];E=E.map(v=>v==null?l:Lt(v,l,i));const J=c===!0&&w!==null?[...Array(Math.floor((i-l)/w)+1)].map((v,k)=>({value:l+w*k})):c||[],U=J.map(v=>v.value),[ce,se]=g.useState(-1),q=g.useRef(null),K=qt(y,q),Ae=v=>k=>{var _;const D=Number(k.currentTarget.getAttribute("data-index"));uo(k.target)&&se(D),I(D),(_=v==null?void 0:v.onFocus)==null||_.call(v,k)},ve=v=>k=>{var D;uo(k.target)||se(-1),I(-1),(D=v==null?void 0:v.onBlur)==null||D.call(v,k)},X=(v,k)=>{const D=Number(v.currentTarget.getAttribute("data-index")),_=E[D],ne=U.indexOf(_);let V=k;if(J&&w==null){const ie=U[U.length-1];V>=ie?V=ie:V<=U[0]?V=U[0]:V=V<_?U[ne-1]:U[ne+1]}if(V=Lt(V,l,i),Z){s&&(V=Lt(V,E[D-1]||-1/0,E[D+1]||1/0));const ie=V;V=pn({values:E,newValue:V,index:D});let be=D;s||(be=V.indexOf(ie)),oo({sliderRef:q,activeIndex:be})}j(V),se(D),R&&!no(V,G)&&R(v,V,D),P&&P(v,F.current??V)},te=v=>k=>{var D;if(["ArrowUp","ArrowDown","ArrowLeft","ArrowRight","PageUp","PageDown","Home","End"].includes(k.key)){k.preventDefault();const _=Number(k.currentTarget.getAttribute("data-index")),ne=E[_];let V=null;if(w!=null){const ie=k.shiftKey?T:w;switch(k.key){case"ArrowUp":V=Mt(ne,ie,1,l,i);break;case"ArrowRight":V=Mt(ne,ie,a?-1:1,l,i);break;case"ArrowDown":V=Mt(ne,ie,-1,l,i);break;case"ArrowLeft":V=Mt(ne,ie,a?1:-1,l,i);break;case"PageUp":V=Mt(ne,T,1,l,i);break;case"PageDown":V=Mt(ne,T,-1,l,i);break;case"Home":V=l;break;case"End":V=i;break}}else if(J){const ie=U[U.length-1],be=U.indexOf(ne),ue=[a?"ArrowRight":"ArrowLeft","ArrowDown","PageDown","Home"],fe=[a?"ArrowLeft":"ArrowRight","ArrowUp","PageUp","End"];ue.includes(k.key)?be===0?V=U[0]:V=U[be-1]:fe.includes(k.key)&&(be===U.length-1?V=ie:V=U[be+1])}V!=null&&X(k,V)}(D=v==null?void 0:v.onKeyDown)==null||D.call(v,k)};Kt(()=>{var v;r&&q.current.contains(document.activeElement)&&((v=document.activeElement)==null||v.blur())},[r]),r&&d!==-1&&x(-1),r&&ce!==-1&&se(-1);const de=v=>k=>{var D;(D=v.onChange)==null||D.call(v,k),X(k,k.target.valueAsNumber)},he=g.useRef(void 0);let Te=C;a&&C==="horizontal"&&(Te+="-reverse");const u=({finger:v,move:k=!1})=>{const{current:D}=q,{width:_,height:ne,bottom:V,left:ie}=D.getBoundingClientRect();let be;Te.startsWith("vertical")?be=(V-v.y)/ne:be=(v.x-ie)/_,Te.includes("-reverse")&&(be=1-be);let ue;if(ue=Js(be,l,i),w)ue=ea(ue,w,l);else{const Qe=cn(U,ue);ue=U[Qe]}ue=Lt(ue,l,i);let fe=0;if(Z){k?fe=he.current:fe=cn(E,ue),s&&(ue=Lt(ue,E[fe-1]||-1/0,E[fe+1]||1/0));const Qe=ue;ue=pn({values:E,newValue:ue,index:fe}),s&&k||(fe=ue.indexOf(Qe),he.current=fe)}return{newValue:ue,activeIndex:fe}},O=xt(v=>{const k=to(v,f);if(!k)return;if(B.current+=1,v.type==="mousemove"&&v.buttons===0){z(v);return}const{newValue:D,activeIndex:_}=u({finger:k,move:!0});oo({sliderRef:q,activeIndex:_,setActive:x}),j(D),!Y&&B.current>Qs&&L(!0),R&&!no(D,G)&&R(v,D,_)}),z=xt(v=>{const k=to(v,f);if(L(!1),!k)return;const{newValue:D}=u({finger:k,move:!0});x(-1),v.type==="touchend"&&I(-1),P&&P(v,F.current??D),f.current=void 0,ae()}),H=xt(v=>{if(r)return;dn()||v.preventDefault();const k=v.changedTouches[0];k!=null&&(f.current=k.identifier);const D=to(v,f);if(D!==!1){const{newValue:ne,activeIndex:V}=u({finger:D});oo({sliderRef:q,activeIndex:V,setActive:x}),j(ne),R&&!no(ne,G)&&R(v,ne,V)}B.current=0;const _=kt(q.current);_.addEventListener("touchmove",O,{passive:!0}),_.addEventListener("touchend",z,{passive:!0})}),ae=g.useCallback(()=>{const v=kt(q.current);v.removeEventListener("mousemove",O),v.removeEventListener("mouseup",z),v.removeEventListener("touchmove",O),v.removeEventListener("touchend",z)},[z,O]);g.useEffect(()=>{const{current:v}=q;return v.addEventListener("touchstart",H,{passive:dn()}),()=>{v.removeEventListener("touchstart",H),ae()}},[ae,H]),g.useEffect(()=>{r&&ae()},[r,ae]);const $e=v=>k=>{var ne;if((ne=v.onMouseDown)==null||ne.call(v,k),r||k.defaultPrevented||k.button!==0)return;k.preventDefault();const D=to(k,f);if(D!==!1){const{newValue:V,activeIndex:ie}=u({finger:D});oo({sliderRef:q,activeIndex:ie,setActive:x}),j(V),R&&!no(V,G)&&R(k,V,ie)}B.current=0;const _=kt(q.current);_.addEventListener("mousemove",O,{passive:!0}),_.addEventListener("mouseup",z)},ze=fo(Z?E[0]:l,l,i),oe=fo(E[E.length-1],l,i)-ze,Se=(v={})=>{const k=yo(v),D={onMouseDown:$e(k||{})},_={...k,...D};return{...v,ref:K,..._}},Le=v=>k=>{var _;(_=v.onMouseOver)==null||_.call(v,k);const D=Number(k.currentTarget.getAttribute("data-index"));I(D)},Ie=v=>k=>{var D;(D=v.onMouseLeave)==null||D.call(v,k),I(-1)},Be=(v={})=>{const k=yo(v),D={onMouseOver:Le(k||{}),onMouseLeave:Ie(k||{})};return{...v,...k,...D}},Ve=v=>({pointerEvents:d!==-1&&d!==v?"none":void 0});let De;return C==="vertical"&&(De=a?"vertical-rl":"vertical-lr"),{active:d,axis:Te,axisProps:ta,dragging:Y,focusedThumbIndex:ce,getHiddenInputProps:(v={})=>{const k=yo(v),D={onChange:de(k||{}),onFocus:Ae(k||{}),onBlur:ve(k||{}),onKeyDown:te(k||{})},_={...k,...D};return{tabIndex:A,"aria-labelledby":t,"aria-orientation":C,"aria-valuemax":M(i),"aria-valuemin":M(l),name:m,type:"range",min:e.min,max:e.max,step:e.step===null&&e.marks?"any":e.step??void 0,disabled:r,...v,..._,style:{...Gs,direction:a?"rtl":"ltr",width:"100%",height:"100%",writingMode:De}}},getRootProps:Se,getThumbProps:Be,marks:J,open:S,range:Z,rootRef:K,trackLeap:oe,trackOffset:ze,values:E,getThumbStyle:Ve}}const ra=e=>!e||!co(e);function sa(e){return We("MuiSlider",e)}const Ze=Oe("MuiSlider",["root","active","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","disabled","dragging","focusVisible","mark","markActive","marked","markLabel","markLabelActive","rail","sizeSmall","thumb","thumbColorPrimary","thumbColorSecondary","thumbColorError","thumbColorSuccess","thumbColorInfo","thumbColorWarning","track","trackInverted","trackFalse","thumbSizeSmall","valueLabel","valueLabelOpen","valueLabelCircle","valueLabelLabel","vertical"]),aa=e=>{const{open:t}=e;return{offset:xe(t&&Ze.valueLabelOpen),circle:Ze.valueLabelCircle,label:Ze.valueLabelLabel}};function ia(e){const{children:t,className:n,value:r}=e,s=aa(e);return t?g.cloneElement(t,{className:t.props.className},o.jsxs(g.Fragment,{children:[t.props.children,o.jsx("span",{className:xe(s.offset,n),"aria-hidden":!0,children:o.jsx("span",{className:s.circle,children:o.jsx("span",{className:s.label,children:r})})})]})):null}function un(e){return e}const la=re("span",{name:"MuiSlider",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t[`color${me(n.color)}`],n.size!=="medium"&&t[`size${me(n.size)}`],n.marked&&t.marked,n.orientation==="vertical"&&t.vertical,n.track==="inverted"&&t.trackInverted,n.track===!1&&t.trackFalse]}})(Re(({theme:e})=>({borderRadius:12,boxSizing:"content-box",display:"inline-block",position:"relative",cursor:"pointer",touchAction:"none",WebkitTapHighlightColor:"transparent","@media print":{colorAdjust:"exact"},[`&.${Ze.disabled}`]:{pointerEvents:"none",cursor:"default",color:(e.vars||e).palette.grey[400]},[`&.${Ze.dragging}`]:{[`& .${Ze.thumb}, & .${Ze.track}`]:{transition:"none"}},variants:[...Object.entries(e.palette).filter(xo()).map(([t])=>({props:{color:t},style:{color:(e.vars||e).palette[t].main}})),{props:{orientation:"horizontal"},style:{height:4,width:"100%",padding:"13px 0","@media (pointer: coarse)":{padding:"20px 0"}}},{props:{orientation:"horizontal",size:"small"},style:{height:2}},{props:{orientation:"horizontal",marked:!0},style:{marginBottom:20}},{props:{orientation:"vertical"},style:{height:"100%",width:4,padding:"0 13px","@media (pointer: coarse)":{padding:"0 20px"}}},{props:{orientation:"vertical",size:"small"},style:{width:2}},{props:{orientation:"vertical",marked:!0},style:{marginRight:44}}]}))),ca=re("span",{name:"MuiSlider",slot:"Rail"})({display:"block",position:"absolute",borderRadius:"inherit",backgroundColor:"currentColor",opacity:.38,variants:[{props:{orientation:"horizontal"},style:{width:"100%",height:"inherit",top:"50%",transform:"translateY(-50%)"}},{props:{orientation:"vertical"},style:{height:"100%",width:"inherit",left:"50%",transform:"translateX(-50%)"}},{props:{track:"inverted"},style:{opacity:1}}]}),pa=re("span",{name:"MuiSlider",slot:"Track"})(Re(({theme:e})=>({display:"block",position:"absolute",borderRadius:"inherit",border:"1px solid currentColor",backgroundColor:"currentColor",transition:e.transitions.create(["left","width","bottom","height"],{duration:e.transitions.duration.shortest}),variants:[{props:{size:"small"},style:{border:"none"}},{props:{orientation:"horizontal"},style:{height:"inherit",top:"50%",transform:"translateY(-50%)"}},{props:{orientation:"vertical"},style:{width:"inherit",left:"50%",transform:"translateX(-50%)"}},{props:{track:!1},style:{display:"none"}},...Object.entries(e.palette).filter(xo()).map(([t])=>({props:{color:t,track:"inverted"},style:{...e.vars?{backgroundColor:e.vars.palette.Slider[`${t}Track`],borderColor:e.vars.palette.Slider[`${t}Track`]}:{backgroundColor:To(e.palette[t].main,.62),borderColor:To(e.palette[t].main,.62),...e.applyStyles("dark",{backgroundColor:Po(e.palette[t].main,.5)}),...e.applyStyles("dark",{borderColor:Po(e.palette[t].main,.5)})}}}))]}))),da=re("span",{name:"MuiSlider",slot:"Thumb",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.thumb,t[`thumbColor${me(n.color)}`],n.size!=="medium"&&t[`thumbSize${me(n.size)}`]]}})(Re(({theme:e})=>({position:"absolute",width:20,height:20,boxSizing:"border-box",borderRadius:"50%",outline:0,backgroundColor:"currentColor",display:"flex",alignItems:"center",justifyContent:"center",transition:e.transitions.create(["box-shadow","left","bottom"],{duration:e.transitions.duration.shortest}),"&::before":{position:"absolute",content:'""',borderRadius:"inherit",width:"100%",height:"100%",boxShadow:(e.vars||e).shadows[2]},"&::after":{position:"absolute",content:'""',borderRadius:"50%",width:42,height:42,top:"50%",left:"50%",transform:"translate(-50%, -50%)"},[`&.${Ze.disabled}`]:{"&:hover":{boxShadow:"none"}},variants:[{props:{size:"small"},style:{width:12,height:12,"&::before":{boxShadow:"none"}}},{props:{orientation:"horizontal"},style:{top:"50%",transform:"translate(-50%, -50%)"}},{props:{orientation:"vertical"},style:{left:"50%",transform:"translate(-50%, 50%)"}},...Object.entries(e.palette).filter(xo()).map(([t])=>({props:{color:t},style:{[`&:hover, &.${Ze.focusVisible}`]:{...e.vars?{boxShadow:`0px 0px 0px 8px rgba(${e.vars.palette[t].mainChannel} / 0.16)`}:{boxShadow:`0px 0px 0px 8px ${it(e.palette[t].main,.16)}`},"@media (hover: none)":{boxShadow:"none"}},[`&.${Ze.active}`]:{...e.vars?{boxShadow:`0px 0px 0px 14px rgba(${e.vars.palette[t].mainChannel} / 0.16)`}:{boxShadow:`0px 0px 0px 14px ${it(e.palette[t].main,.16)}`}}}}))]}))),ua=re(ia,{name:"MuiSlider",slot:"ValueLabel"})(Re(({theme:e})=>({zIndex:1,whiteSpace:"nowrap",...e.typography.body2,fontWeight:500,transition:e.transitions.create(["transform"],{duration:e.transitions.duration.shortest}),position:"absolute",backgroundColor:(e.vars||e).palette.grey[600],borderRadius:2,color:(e.vars||e).palette.common.white,display:"flex",alignItems:"center",justifyContent:"center",padding:"0.25rem 0.75rem",variants:[{props:{orientation:"horizontal"},style:{transform:"translateY(-100%) scale(0)",top:"-10px",transformOrigin:"bottom center","&::before":{position:"absolute",content:'""',width:8,height:8,transform:"translate(-50%, 50%) rotate(45deg)",backgroundColor:"inherit",bottom:0,left:"50%"},[`&.${Ze.valueLabelOpen}`]:{transform:"translateY(-100%) scale(1)"}}},{props:{orientation:"vertical"},style:{transform:"translateY(-50%) scale(0)",right:"30px",top:"50%",transformOrigin:"right center","&::before":{position:"absolute",content:'""',width:8,height:8,transform:"translate(-50%, -50%) rotate(45deg)",backgroundColor:"inherit",right:-8,top:"50%"},[`&.${Ze.valueLabelOpen}`]:{transform:"translateY(-50%) scale(1)"}}},{props:{size:"small"},style:{fontSize:e.typography.pxToRem(12),padding:"0.25rem 0.5rem"}},{props:{orientation:"vertical",size:"small"},style:{right:"20px"}}]}))),ha=re("span",{name:"MuiSlider",slot:"Mark",shouldForwardProp:e=>jn(e)&&e!=="markActive",overridesResolver:(e,t)=>{const{markActive:n}=e;return[t.mark,n&&t.markActive]}})(Re(({theme:e})=>({position:"absolute",width:2,height:2,borderRadius:1,backgroundColor:"currentColor",variants:[{props:{orientation:"horizontal"},style:{top:"50%",transform:"translate(-1px, -50%)"}},{props:{orientation:"vertical"},style:{left:"50%",transform:"translate(-50%, 1px)"}},{props:{markActive:!0},style:{backgroundColor:(e.vars||e).palette.background.paper,opacity:.8}}]}))),fa=re("span",{name:"MuiSlider",slot:"MarkLabel",shouldForwardProp:e=>jn(e)&&e!=="markLabelActive"})(Re(({theme:e})=>({...e.typography.body2,color:(e.vars||e).palette.text.secondary,position:"absolute",whiteSpace:"nowrap",variants:[{props:{orientation:"horizontal"},style:{top:30,transform:"translateX(-50%)","@media (pointer: coarse)":{top:40}}},{props:{orientation:"vertical"},style:{left:36,transform:"translateY(50%)","@media (pointer: coarse)":{left:44}}},{props:{markLabelActive:!0},style:{color:(e.vars||e).palette.text.primary}}]}))),ma=e=>{const{disabled:t,dragging:n,marked:r,orientation:s,track:a,classes:c,color:i,size:l}=e,m={root:["root",t&&"disabled",n&&"dragging",r&&"marked",s==="vertical"&&"vertical",a==="inverted"&&"trackInverted",a===!1&&"trackFalse",i&&`color${me(i)}`,l&&`size${me(l)}`],rail:["rail"],track:["track"],mark:["mark"],markActive:["markActive"],markLabel:["markLabel"],markLabelActive:["markLabelActive"],valueLabel:["valueLabel"],thumb:["thumb",t&&"disabled",l&&`thumbSize${me(l)}`,i&&`thumbColor${me(i)}`],active:["active"],disabled:["disabled"],focusVisible:["focusVisible"]};return Fe(m,sa,c)},ga=({children:e})=>e,Co=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiSlider"}),s=Gt(),{"aria-label":a,"aria-valuetext":c,"aria-labelledby":i,component:l="span",components:m={},componentsProps:p={},color:P="primary",classes:C,className:y,disableSwap:M=!1,disabled:w=!1,getAriaLabel:T,getAriaValueText:A,marks:h=!1,max:f=100,min:d=0,name:x,onChange:S,onChangeCommitted:I,orientation:Y="horizontal",shiftStep:L=10,size:B="medium",step:F=1,scale:G=un,slotProps:j,slots:R,tabIndex:Z,track:E="normal",value:J,valueLabelDisplay:U="off",valueLabelFormat:ce=un,...se}=r,q={...r,isRtl:s,max:f,min:d,classes:C,disabled:w,disableSwap:M,orientation:Y,marks:h,color:P,size:B,step:F,shiftStep:L,scale:G,track:E,valueLabelDisplay:U,valueLabelFormat:ce},{axisProps:K,getRootProps:Ae,getHiddenInputProps:ve,getThumbProps:X,open:te,active:de,axis:he,focusedThumbIndex:Te,range:u,dragging:O,marks:z,values:H,trackOffset:ae,trackLeap:$e,getThumbStyle:ze}=na({...q,rootRef:n});q.marked=z.length>0&&z.some(ge=>ge.label),q.dragging=O,q.focusedThumbIndex=Te;const oe=ma(q),Se=(R==null?void 0:R.root)??m.Root??la,Le=(R==null?void 0:R.rail)??m.Rail??ca,Ie=(R==null?void 0:R.track)??m.Track??pa,Be=(R==null?void 0:R.thumb)??m.Thumb??da,Ve=(R==null?void 0:R.valueLabel)??m.ValueLabel??ua,De=(R==null?void 0:R.mark)??m.Mark??ha,Ke=(R==null?void 0:R.markLabel)??m.MarkLabel??fa,v=(R==null?void 0:R.input)??m.Input??"input",k=(j==null?void 0:j.root)??p.root,D=(j==null?void 0:j.rail)??p.rail,_=(j==null?void 0:j.track)??p.track,ne=(j==null?void 0:j.thumb)??p.thumb,V=(j==null?void 0:j.valueLabel)??p.valueLabel,ie=(j==null?void 0:j.mark)??p.mark,be=(j==null?void 0:j.markLabel)??p.markLabel,ue=(j==null?void 0:j.input)??p.input,fe=Ge({elementType:Se,getSlotProps:Ae,externalSlotProps:k,externalForwardedProps:se,additionalProps:{...ra(Se)&&{as:l}},ownerState:{...q,...k==null?void 0:k.ownerState},className:[oe.root,y]}),Qe=Ge({elementType:Le,externalSlotProps:D,ownerState:q,className:oe.rail}),bt=Ge({elementType:Ie,externalSlotProps:_,additionalProps:{style:{...K[he].offset(ae),...K[he].leap($e)}},ownerState:{...q,..._==null?void 0:_.ownerState},className:oe.track}),yt=Ge({elementType:Be,getSlotProps:X,externalSlotProps:ne,ownerState:{...q,...ne==null?void 0:ne.ownerState},className:oe.thumb}),pt=Ge({elementType:Ve,externalSlotProps:V,ownerState:{...q,...V==null?void 0:V.ownerState},className:oe.valueLabel}),nt=Ge({elementType:De,externalSlotProps:ie,ownerState:q,className:oe.mark}),wt=Ge({elementType:Ke,externalSlotProps:be,ownerState:q,className:oe.markLabel}),Bt=Ge({elementType:v,getSlotProps:ve,externalSlotProps:ue,ownerState:q});return o.jsxs(Se,{...fe,children:[o.jsx(Le,{...Qe}),o.jsx(Ie,{...bt}),z.filter(ge=>ge.value>=d&&ge.value<=f).map((ge,Pe)=>{const jt=fo(ge.value,d,f),ut=K[he].offset(jt);let Xe;return E===!1?Xe=H.includes(ge.value):Xe=E==="normal"&&(u?ge.value>=H[0]&&ge.value<=H[H.length-1]:ge.value<=H[0])||E==="inverted"&&(u?ge.value<=H[0]||ge.value>=H[H.length-1]:ge.value>=H[0]),o.jsxs(g.Fragment,{children:[o.jsx(De,{"data-index":Pe,...nt,...!co(De)&&{markActive:Xe},style:{...ut,...nt.style},className:xe(nt.className,Xe&&oe.markActive)}),ge.label!=null?o.jsx(Ke,{"aria-hidden":!0,"data-index":Pe,...wt,...!co(Ke)&&{markLabelActive:Xe},style:{...ut,...wt.style},className:xe(oe.markLabel,wt.className,Xe&&oe.markLabelActive),children:ge.label}):null]},Pe)}),H.map((ge,Pe)=>{const jt=fo(ge,d,f),ut=K[he].offset(jt),Xe=U==="off"?ga:Ve;return o.jsx(Xe,{...!co(Xe)&&{valueLabelFormat:ce,valueLabelDisplay:U,value:typeof ce=="function"?ce(G(ge),Pe):ce,index:Pe,open:te===Pe||de===Pe||U==="on",disabled:w},...pt,children:o.jsx(Be,{"data-index":Pe,...yt,className:xe(oe.thumb,yt.className,de===Pe&&oe.active,Te===Pe&&oe.focusVisible),style:{...ut,...ze(Pe),...yt.style},children:o.jsx(v,{"data-index":Pe,"aria-label":T?T(Pe):a,"aria-valuenow":G(ge),"aria-labelledby":i,"aria-valuetext":A?A(G(ge),Pe):c,value:H[Pe],...Bt})})},Pe)})]})});function xa(e){return We("MuiTooltip",e)}const ke=Oe("MuiTooltip",["popper","popperInteractive","popperArrow","popperClose","tooltip","tooltipArrow","touch","tooltipPlacementLeft","tooltipPlacementRight","tooltipPlacementTop","tooltipPlacementBottom","arrow"]);function va(e){return Math.round(e*1e5)/1e5}const ba=e=>{const{classes:t,disableInteractive:n,arrow:r,touch:s,placement:a}=e,c={popper:["popper",!n&&"popperInteractive",r&&"popperArrow"],tooltip:["tooltip",r&&"tooltipArrow",s&&"touch",`tooltipPlacement${me(a.split("-")[0])}`],arrow:["arrow"]};return Fe(c,xa,t)},ya=re(Ln,{name:"MuiTooltip",slot:"Popper",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.popper,!n.disableInteractive&&t.popperInteractive,n.arrow&&t.popperArrow,!n.open&&t.popperClose]}})(Re(({theme:e})=>({zIndex:(e.vars||e).zIndex.tooltip,pointerEvents:"none",variants:[{props:({ownerState:t})=>!t.disableInteractive,style:{pointerEvents:"auto"}},{props:({open:t})=>!t,style:{pointerEvents:"none"}},{props:({ownerState:t})=>t.arrow,style:{[`&[data-popper-placement*="bottom"] .${ke.arrow}`]:{top:0,marginTop:"-0.71em","&::before":{transformOrigin:"0 100%"}},[`&[data-popper-placement*="top"] .${ke.arrow}`]:{bottom:0,marginBottom:"-0.71em","&::before":{transformOrigin:"100% 0"}},[`&[data-popper-placement*="right"] .${ke.arrow}`]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"100% 100%"}},[`&[data-popper-placement*="left"] .${ke.arrow}`]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"0 0"}}}},{props:({ownerState:t})=>t.arrow&&!t.isRtl,style:{[`&[data-popper-placement*="right"] .${ke.arrow}`]:{left:0,marginLeft:"-0.71em"}}},{props:({ownerState:t})=>t.arrow&&!!t.isRtl,style:{[`&[data-popper-placement*="right"] .${ke.arrow}`]:{right:0,marginRight:"-0.71em"}}},{props:({ownerState:t})=>t.arrow&&!t.isRtl,style:{[`&[data-popper-placement*="left"] .${ke.arrow}`]:{right:0,marginRight:"-0.71em"}}},{props:({ownerState:t})=>t.arrow&&!!t.isRtl,style:{[`&[data-popper-placement*="left"] .${ke.arrow}`]:{left:0,marginLeft:"-0.71em"}}}]}))),wa=re("div",{name:"MuiTooltip",slot:"Tooltip",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.tooltip,n.touch&&t.touch,n.arrow&&t.tooltipArrow,t[`tooltipPlacement${me(n.placement.split("-")[0])}`]]}})(Re(({theme:e})=>({backgroundColor:e.vars?e.vars.palette.Tooltip.bg:it(e.palette.grey[700],.92),borderRadius:(e.vars||e).shape.borderRadius,color:(e.vars||e).palette.common.white,fontFamily:e.typography.fontFamily,padding:"4px 8px",fontSize:e.typography.pxToRem(11),maxWidth:300,margin:2,wordWrap:"break-word",fontWeight:e.typography.fontWeightMedium,[`.${ke.popper}[data-popper-placement*="left"] &`]:{transformOrigin:"right center"},[`.${ke.popper}[data-popper-placement*="right"] &`]:{transformOrigin:"left center"},[`.${ke.popper}[data-popper-placement*="top"] &`]:{transformOrigin:"center bottom",marginBottom:"14px"},[`.${ke.popper}[data-popper-placement*="bottom"] &`]:{transformOrigin:"center top",marginTop:"14px"},variants:[{props:({ownerState:t})=>t.arrow,style:{position:"relative",margin:0}},{props:({ownerState:t})=>t.touch,style:{padding:"8px 16px",fontSize:e.typography.pxToRem(14),lineHeight:`${va(16/14)}em`,fontWeight:e.typography.fontWeightRegular}},{props:({ownerState:t})=>!t.isRtl,style:{[`.${ke.popper}[data-popper-placement*="left"] &`]:{marginRight:"14px"},[`.${ke.popper}[data-popper-placement*="right"] &`]:{marginLeft:"14px"}}},{props:({ownerState:t})=>!t.isRtl&&t.touch,style:{[`.${ke.popper}[data-popper-placement*="left"] &`]:{marginRight:"24px"},[`.${ke.popper}[data-popper-placement*="right"] &`]:{marginLeft:"24px"}}},{props:({ownerState:t})=>!!t.isRtl,style:{[`.${ke.popper}[data-popper-placement*="left"] &`]:{marginLeft:"14px"},[`.${ke.popper}[data-popper-placement*="right"] &`]:{marginRight:"14px"}}},{props:({ownerState:t})=>!!t.isRtl&&t.touch,style:{[`.${ke.popper}[data-popper-placement*="left"] &`]:{marginLeft:"24px"},[`.${ke.popper}[data-popper-placement*="right"] &`]:{marginRight:"24px"}}},{props:({ownerState:t})=>t.touch,style:{[`.${ke.popper}[data-popper-placement*="top"] &`]:{marginBottom:"24px"}}},{props:({ownerState:t})=>t.touch,style:{[`.${ke.popper}[data-popper-placement*="bottom"] &`]:{marginTop:"24px"}}}]}))),ja=re("span",{name:"MuiTooltip",slot:"Arrow"})(Re(({theme:e})=>({overflow:"hidden",position:"absolute",width:"1em",height:"0.71em",boxSizing:"border-box",color:e.vars?e.vars.palette.Tooltip.bg:it(e.palette.grey[700],.9),"&::before":{content:'""',margin:"auto",display:"block",width:"100%",height:"100%",backgroundColor:"currentColor",transform:"rotate(45deg)"}})));let so=!1;const hn=new Nn;let Dt={x:0,y:0};function ao(e,t){return(n,...r)=>{t&&t(n,...r),e(n,...r)}}const fn=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiTooltip"}),{arrow:s=!1,children:a,classes:c,components:i={},componentsProps:l={},describeChild:m=!1,disableFocusListener:p=!1,disableHoverListener:P=!1,disableInteractive:C=!1,disableTouchListener:y=!1,enterDelay:M=100,enterNextDelay:w=0,enterTouchDelay:T=700,followCursor:A=!1,id:h,leaveDelay:f=0,leaveTouchDelay:d=1500,onClose:x,onOpen:S,open:I,placement:Y="bottom",PopperComponent:L,PopperProps:B={},slotProps:F={},slots:G={},title:j,TransitionComponent:R,TransitionProps:Z,...E}=r,J=g.isValidElement(a)?a:o.jsx("span",{children:a}),U=Eo(),ce=Gt(),[se,q]=g.useState(),[K,Ae]=g.useState(null),ve=g.useRef(!1),X=C||A,te=Jt(),de=Jt(),he=Jt(),Te=Jt(),[u,O]=zo({controlled:I,default:!1,name:"Tooltip",state:"open"});let z=u;const H=wn(h),ae=g.useRef(),$e=xt(()=>{ae.current!==void 0&&(document.body.style.WebkitUserSelect=ae.current,ae.current=void 0),Te.clear()});g.useEffect(()=>$e,[$e]);const ze=Q=>{hn.clear(),so=!0,O(!0),S&&!z&&S(Q)},oe=xt(Q=>{hn.start(800+f,()=>{so=!1}),O(!1),x&&z&&x(Q),te.start(U.transitions.duration.shortest,()=>{ve.current=!1})}),Se=Q=>{ve.current&&Q.type!=="touchstart"||(se&&se.removeAttribute("title"),de.clear(),he.clear(),M||so&&w?de.start(so?w:M,()=>{ze(Q)}):ze(Q))},Le=Q=>{de.clear(),he.start(f,()=>{oe(Q)})},[,Ie]=g.useState(!1),Be=Q=>{uo(Q.target)||(Ie(!1),Le(Q))},Ve=Q=>{se||q(Q.currentTarget),uo(Q.target)&&(Ie(!0),Se(Q))},De=Q=>{ve.current=!0;const Ye=J.props;Ye.onTouchStart&&Ye.onTouchStart(Q)},Ke=Q=>{De(Q),he.clear(),te.clear(),$e(),ae.current=document.body.style.WebkitUserSelect,document.body.style.WebkitUserSelect="none",Te.start(T,()=>{document.body.style.WebkitUserSelect=ae.current,Se(Q)})},v=Q=>{J.props.onTouchEnd&&J.props.onTouchEnd(Q),$e(),he.start(d,()=>{oe(Q)})};g.useEffect(()=>{if(!z)return;function Q(Ye){Ye.key==="Escape"&&oe(Ye)}return document.addEventListener("keydown",Q),()=>{document.removeEventListener("keydown",Q)}},[oe,z]);const k=qt(_n(J),q,n);!j&&j!==0&&(z=!1);const D=g.useRef(),_=Q=>{const Ye=J.props;Ye.onMouseMove&&Ye.onMouseMove(Q),Dt={x:Q.clientX,y:Q.clientY},D.current&&D.current.update()},ne={},V=typeof j=="string";m?(ne.title=!z&&V&&!P?j:null,ne["aria-describedby"]=z?H:null):(ne["aria-label"]=V?j:null,ne["aria-labelledby"]=z&&!V?H:null);const ie={...ne,...E,...J.props,className:xe(E.className,J.props.className),onTouchStart:De,ref:k,...A?{onMouseMove:_}:{}},be={};y||(ie.onTouchStart=Ke,ie.onTouchEnd=v),P||(ie.onMouseOver=ao(Se,ie.onMouseOver),ie.onMouseLeave=ao(Le,ie.onMouseLeave),X||(be.onMouseOver=Se,be.onMouseLeave=Le)),p||(ie.onFocus=ao(Ve,ie.onFocus),ie.onBlur=ao(Be,ie.onBlur),X||(be.onFocus=Ve,be.onBlur=Be));const ue={...r,isRtl:ce,arrow:s,disableInteractive:X,placement:Y,PopperComponentProp:L,touch:ve.current},fe=typeof F.popper=="function"?F.popper(ue):F.popper,Qe=g.useMemo(()=>{var Ye,$;let Q=[{name:"arrow",enabled:!!K,options:{element:K,padding:4}}];return(Ye=B.popperOptions)!=null&&Ye.modifiers&&(Q=Q.concat(B.popperOptions.modifiers)),($=fe==null?void 0:fe.popperOptions)!=null&&$.modifiers&&(Q=Q.concat(fe.popperOptions.modifiers)),{...B.popperOptions,...fe==null?void 0:fe.popperOptions,modifiers:Q}},[K,B.popperOptions,fe==null?void 0:fe.popperOptions]),bt=ba(ue),yt=typeof F.transition=="function"?F.transition(ue):F.transition,pt={slots:{popper:i.Popper,transition:i.Transition??R,tooltip:i.Tooltip,arrow:i.Arrow,...G},slotProps:{arrow:F.arrow??l.arrow,popper:{...B,...fe??l.popper},tooltip:F.tooltip??l.tooltip,transition:{...Z,...yt??l.transition}}},[nt,wt]=Me("popper",{elementType:ya,externalForwardedProps:pt,ownerState:ue,className:xe(bt.popper,B==null?void 0:B.className)}),[Bt,ge]=Me("transition",{elementType:Qn,externalForwardedProps:pt,ownerState:ue}),[Pe,jt]=Me("tooltip",{elementType:wa,className:bt.tooltip,externalForwardedProps:pt,ownerState:ue}),[ut,Xe]=Me("arrow",{elementType:ja,className:bt.arrow,externalForwardedProps:pt,ownerState:ue,ref:Ae});return o.jsxs(g.Fragment,{children:[g.cloneElement(J,ie),o.jsx(nt,{as:L??Ln,placement:Y,anchorEl:A?{getBoundingClientRect:()=>({top:Dt.y,left:Dt.x,right:Dt.x,bottom:Dt.y,width:0,height:0})}:se,popperRef:D,open:se?z:!1,id:H,transition:!0,...be,...wt,popperOptions:Qe,children:({TransitionProps:Q})=>o.jsx(Bt,{timeout:U.transitions.duration.shorter,...Q,...ge,children:o.jsxs(Pe,{...jt,children:[j,s?o.jsx(ut,{...Xe}):null]})})})]})});function Sa(e){return We("MuiSwitch",e)}const Ne=Oe("MuiSwitch",["root","edgeStart","edgeEnd","switchBase","colorPrimary","colorSecondary","sizeSmall","sizeMedium","checked","disabled","input","thumb","track"]),Ca=e=>{const{classes:t,edge:n,size:r,color:s,checked:a,disabled:c}=e,i={root:["root",n&&`edge${me(n)}`,`size${me(r)}`],switchBase:["switchBase",`color${me(s)}`,a&&"checked",c&&"disabled"],thumb:["thumb"],track:["track"],input:["input"]},l=Fe(i,Sa,t);return{...t,...l}},ka=re("span",{name:"MuiSwitch",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.edge&&t[`edge${me(n.edge)}`],t[`size${me(n.size)}`]]}})({display:"inline-flex",width:34+12*2,height:14+12*2,overflow:"hidden",padding:12,boxSizing:"border-box",position:"relative",flexShrink:0,zIndex:0,verticalAlign:"middle","@media print":{colorAdjust:"exact"},variants:[{props:{edge:"start"},style:{marginLeft:-8}},{props:{edge:"end"},style:{marginRight:-8}},{props:{size:"small"},style:{width:40,height:24,padding:7,[`& .${Ne.thumb}`]:{width:16,height:16},[`& .${Ne.switchBase}`]:{padding:4,[`&.${Ne.checked}`]:{transform:"translateX(16px)"}}}}]}),Ta=re(ws,{name:"MuiSwitch",slot:"SwitchBase",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.switchBase,{[`& .${Ne.input}`]:t.input},n.color!=="default"&&t[`color${me(n.color)}`]]}})(Re(({theme:e})=>({position:"absolute",top:0,left:0,zIndex:1,color:e.vars?e.vars.palette.Switch.defaultColor:`${e.palette.mode==="light"?e.palette.common.white:e.palette.grey[300]}`,transition:e.transitions.create(["left","transform"],{duration:e.transitions.duration.shortest}),[`&.${Ne.checked}`]:{transform:"translateX(20px)"},[`&.${Ne.disabled}`]:{color:e.vars?e.vars.palette.Switch.defaultDisabledColor:`${e.palette.mode==="light"?e.palette.grey[100]:e.palette.grey[600]}`},[`&.${Ne.checked} + .${Ne.track}`]:{opacity:.5},[`&.${Ne.disabled} + .${Ne.track}`]:{opacity:e.vars?e.vars.opacity.switchTrackDisabled:`${e.palette.mode==="light"?.12:.2}`},[`& .${Ne.input}`]:{left:"-100%",width:"300%"}})),Re(({theme:e})=>({"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette.action.activeChannel} / ${e.vars.palette.action.hoverOpacity})`:it(e.palette.action.active,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},variants:[...Object.entries(e.palette).filter(xo(["light"])).map(([t])=>({props:{color:t},style:{[`&.${Ne.checked}`]:{color:(e.vars||e).palette[t].main,"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette[t].mainChannel} / ${e.vars.palette.action.hoverOpacity})`:it(e.palette[t].main,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${Ne.disabled}`]:{color:e.vars?e.vars.palette.Switch[`${t}DisabledColor`]:`${e.palette.mode==="light"?To(e.palette[t].main,.62):Po(e.palette[t].main,.55)}`}},[`&.${Ne.checked} + .${Ne.track}`]:{backgroundColor:(e.vars||e).palette[t].main}}}))]}))),Pa=re("span",{name:"MuiSwitch",slot:"Track"})(Re(({theme:e})=>({height:"100%",width:"100%",borderRadius:14/2,zIndex:-1,transition:e.transitions.create(["opacity","background-color"],{duration:e.transitions.duration.shortest}),backgroundColor:e.vars?e.vars.palette.common.onBackground:`${e.palette.mode==="light"?e.palette.common.black:e.palette.common.white}`,opacity:e.vars?e.vars.opacity.switchTrack:`${e.palette.mode==="light"?.38:.3}`}))),Ma=re("span",{name:"MuiSwitch",slot:"Thumb"})(Re(({theme:e})=>({boxShadow:(e.vars||e).shadows[1],backgroundColor:"currentColor",width:20,height:20,borderRadius:"50%"}))),at=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiSwitch"}),{className:s,color:a="primary",edge:c=!1,size:i="medium",sx:l,slots:m={},slotProps:p={},...P}=r,C={...r,color:a,edge:c,size:i},y=Ca(C),M={slots:m,slotProps:p},[w,T]=Me("root",{className:xe(y.root,s),elementType:ka,externalForwardedProps:M,ownerState:C,additionalProps:{sx:l}}),[A,h]=Me("thumb",{className:y.thumb,elementType:Ma,externalForwardedProps:M,ownerState:C}),f=o.jsx(A,{...h}),[d,x]=Me("track",{className:y.track,elementType:Pa,externalForwardedProps:M,ownerState:C});return o.jsxs(w,{...T,children:[o.jsx(Ta,{type:"checkbox",icon:f,checkedIcon:f,ref:n,ownerState:C,...P,classes:{...y,root:y.switchBase},slots:{...m.switchBase&&{root:m.switchBase},...m.input&&{input:m.input}},slotProps:{...p.switchBase&&{root:typeof p.switchBase=="function"?p.switchBase(C):p.switchBase},...p.input&&{input:typeof p.input=="function"?p.input(C):p.input}}}),o.jsx(d,{...x})]})});function Ra(e){return We("MuiTab",e)}const Je=Oe("MuiTab",["root","labelIcon","textColorInherit","textColorPrimary","textColorSecondary","selected","disabled","fullWidth","wrapped","iconWrapper","icon"]),Aa=e=>{const{classes:t,textColor:n,fullWidth:r,wrapped:s,icon:a,label:c,selected:i,disabled:l}=e,m={root:["root",a&&c&&"labelIcon",`textColor${me(n)}`,r&&"fullWidth",s&&"wrapped",i&&"selected",l&&"disabled"],icon:["iconWrapper","icon"]};return Fe(m,Ra,t)},$a=re(go,{name:"MuiTab",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.label&&n.icon&&t.labelIcon,t[`textColor${me(n.textColor)}`],n.fullWidth&&t.fullWidth,n.wrapped&&t.wrapped,{[`& .${Je.iconWrapper}`]:t.iconWrapper},{[`& .${Je.icon}`]:t.icon}]}})(Re(({theme:e})=>({...e.typography.button,maxWidth:360,minWidth:90,position:"relative",minHeight:48,flexShrink:0,padding:"12px 16px",overflow:"hidden",whiteSpace:"normal",textAlign:"center",lineHeight:1.25,variants:[{props:({ownerState:t})=>t.label&&(t.iconPosition==="top"||t.iconPosition==="bottom"),style:{flexDirection:"column"}},{props:({ownerState:t})=>t.label&&t.iconPosition!=="top"&&t.iconPosition!=="bottom",style:{flexDirection:"row"}},{props:({ownerState:t})=>t.icon&&t.label,style:{minHeight:72,paddingTop:9,paddingBottom:9}},{props:({ownerState:t,iconPosition:n})=>t.icon&&t.label&&n==="top",style:{[`& > .${Je.icon}`]:{marginBottom:6}}},{props:({ownerState:t,iconPosition:n})=>t.icon&&t.label&&n==="bottom",style:{[`& > .${Je.icon}`]:{marginTop:6}}},{props:({ownerState:t,iconPosition:n})=>t.icon&&t.label&&n==="start",style:{[`& > .${Je.icon}`]:{marginRight:e.spacing(1)}}},{props:({ownerState:t,iconPosition:n})=>t.icon&&t.label&&n==="end",style:{[`& > .${Je.icon}`]:{marginLeft:e.spacing(1)}}},{props:{textColor:"inherit"},style:{color:"inherit",opacity:.6,[`&.${Je.selected}`]:{opacity:1},[`&.${Je.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity}}},{props:{textColor:"primary"},style:{color:(e.vars||e).palette.text.secondary,[`&.${Je.selected}`]:{color:(e.vars||e).palette.primary.main},[`&.${Je.disabled}`]:{color:(e.vars||e).palette.text.disabled}}},{props:{textColor:"secondary"},style:{color:(e.vars||e).palette.text.secondary,[`&.${Je.selected}`]:{color:(e.vars||e).palette.secondary.main},[`&.${Je.disabled}`]:{color:(e.vars||e).palette.text.disabled}}},{props:({ownerState:t})=>t.fullWidth,style:{flexShrink:1,flexGrow:1,flexBasis:0,maxWidth:"none"}},{props:({ownerState:t})=>t.wrapped,style:{fontSize:e.typography.pxToRem(12)}}]}))),ht=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiTab"}),{className:s,disabled:a=!1,disableFocusRipple:c=!1,fullWidth:i,icon:l,iconPosition:m="top",indicator:p,label:P,onChange:C,onClick:y,onFocus:M,selected:w,selectionFollowsFocus:T,textColor:A="inherit",value:h,wrapped:f=!1,...d}=r,x={...r,disabled:a,disableFocusRipple:c,selected:w,icon:!!l,iconPosition:m,label:!!P,fullWidth:i,textColor:A,wrapped:f},S=Aa(x),I=l&&P&&g.isValidElement(l)?g.cloneElement(l,{className:xe(S.icon,l.props.className)}):l,Y=B=>{!w&&C&&C(B,h),y&&y(B)},L=B=>{T&&!w&&C&&C(B,h),M&&M(B)};return o.jsxs($a,{focusRipple:!c,className:xe(S.root,s),ref:n,role:"tab","aria-selected":w,disabled:a,onClick:Y,onFocus:L,ownerState:x,tabIndex:w?0:-1,...d,children:[m==="top"||m==="start"?o.jsxs(g.Fragment,{children:[I,P]}):o.jsxs(g.Fragment,{children:[P,I]}),p]})}),Ia=Ee(o.jsx("path",{d:"M15.41 16.09l-4.58-4.59 4.58-4.59L14 5.5l-6 6 6 6z"})),Ba=Ee(o.jsx("path",{d:"M8.59 16.34l4.58-4.59-4.58-4.59L10 5.75l6 6-6 6z"}));function Oa(e){return(1+Math.sin(Math.PI*e-Math.PI/2))/2}function La(e,t,n,r={},s=()=>{}){const{ease:a=Oa,duration:c=300}=r;let i=null;const l=t[e];let m=!1;const p=()=>{m=!0},P=C=>{if(m){s(new Error("Animation cancelled"));return}i===null&&(i=C);const y=Math.min(1,(C-i)/c);if(t[e]=a(y)*(n-l)+l,y>=1){requestAnimationFrame(()=>{s(null)});return}requestAnimationFrame(P)};return l===n?(s(new Error("Element already at target position")),p):(requestAnimationFrame(P),p)}const Ea={width:99,height:99,position:"absolute",top:-9999,overflow:"scroll"};function za(e){const{onChange:t,...n}=e,r=g.useRef(),s=g.useRef(null),a=()=>{r.current=s.current.offsetHeight-s.current.clientHeight};return Kt(()=>{const c=Cn(()=>{const l=r.current;a(),l!==r.current&&t(r.current)}),i=kn(s.current);return i.addEventListener("resize",c),()=>{c.clear(),i.removeEventListener("resize",c)}},[t]),g.useEffect(()=>{a(),t(r.current)},[t]),o.jsx("div",{style:Ea,...n,ref:s})}function Da(e){return We("MuiTabScrollButton",e)}const Na=Oe("MuiTabScrollButton",["root","vertical","horizontal","disabled"]),Wa=e=>{const{classes:t,orientation:n,disabled:r}=e;return Fe({root:["root",n,r&&"disabled"]},Da,t)},Fa=re(go,{name:"MuiTabScrollButton",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,n.orientation&&t[n.orientation]]}})({width:40,flexShrink:0,opacity:.8,[`&.${Na.disabled}`]:{opacity:0},variants:[{props:{orientation:"vertical"},style:{width:"100%",height:40,"& svg":{transform:"var(--TabScrollButton-svgRotate)"}}}]}),Va=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiTabScrollButton"}),{className:s,slots:a={},slotProps:c={},direction:i,orientation:l,disabled:m,...p}=r,P=Gt(),C={isRtl:P,...r},y=Wa(C),M=a.StartScrollButtonIcon??Ia,w=a.EndScrollButtonIcon??Ba,T=Ge({elementType:M,externalSlotProps:c.startScrollButtonIcon,additionalProps:{fontSize:"small"},ownerState:C}),A=Ge({elementType:w,externalSlotProps:c.endScrollButtonIcon,additionalProps:{fontSize:"small"},ownerState:C});return o.jsx(Fa,{component:"div",className:xe(y.root,s),ref:n,role:null,ownerState:C,tabIndex:null,...p,style:{...p.style,...l==="vertical"&&{"--TabScrollButton-svgRotate":`rotate(${P?-90:90}deg)`}},children:i==="left"?o.jsx(M,{...T}):o.jsx(w,{...A})})});function Ha(e){return We("MuiTabs",e)}const ko=Oe("MuiTabs",["root","vertical","list","flexContainer","flexContainerVertical","centered","scroller","fixed","scrollableX","scrollableY","hideScrollbar","scrollButtons","scrollButtonsHideMobile","indicator"]),mn=(e,t)=>e===t?e.firstChild:t&&t.nextElementSibling?t.nextElementSibling:e.firstChild,gn=(e,t)=>e===t?e.lastChild:t&&t.previousElementSibling?t.previousElementSibling:e.lastChild,io=(e,t,n)=>{let r=!1,s=n(e,t);for(;s;){if(s===e.firstChild){if(r)return;r=!0}const a=s.disabled||s.getAttribute("aria-disabled")==="true";if(!s.hasAttribute("tabindex")||a)s=n(e,s);else{s.focus();return}}},Ua=e=>{const{vertical:t,fixed:n,hideScrollbar:r,scrollableX:s,scrollableY:a,centered:c,scrollButtonsHideMobile:i,classes:l}=e;return Fe({root:["root",t&&"vertical"],scroller:["scroller",n&&"fixed",r&&"hideScrollbar",s&&"scrollableX",a&&"scrollableY"],list:["list","flexContainer",t&&"flexContainerVertical",t&&"vertical",c&&"centered"],indicator:["indicator"],scrollButtons:["scrollButtons",i&&"scrollButtonsHideMobile"],scrollableX:[s&&"scrollableX"],hideScrollbar:[r&&"hideScrollbar"]},Ha,l)},qa=re("div",{name:"MuiTabs",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[{[`& .${ko.scrollButtons}`]:t.scrollButtons},{[`& .${ko.scrollButtons}`]:n.scrollButtonsHideMobile&&t.scrollButtonsHideMobile},t.root,n.vertical&&t.vertical]}})(Re(({theme:e})=>({overflow:"hidden",minHeight:48,WebkitOverflowScrolling:"touch",display:"flex",variants:[{props:({ownerState:t})=>t.vertical,style:{flexDirection:"column"}},{props:({ownerState:t})=>t.scrollButtonsHideMobile,style:{[`& .${ko.scrollButtons}`]:{[e.breakpoints.down("sm")]:{display:"none"}}}}]}))),Ka=re("div",{name:"MuiTabs",slot:"Scroller",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.scroller,n.fixed&&t.fixed,n.hideScrollbar&&t.hideScrollbar,n.scrollableX&&t.scrollableX,n.scrollableY&&t.scrollableY]}})({position:"relative",display:"inline-block",flex:"1 1 auto",whiteSpace:"nowrap",variants:[{props:({ownerState:e})=>e.fixed,style:{overflowX:"hidden",width:"100%"}},{props:({ownerState:e})=>e.hideScrollbar,style:{scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}}},{props:({ownerState:e})=>e.scrollableX,style:{overflowX:"auto",overflowY:"hidden"}},{props:({ownerState:e})=>e.scrollableY,style:{overflowY:"auto",overflowX:"hidden"}}]}),Xa=re("div",{name:"MuiTabs",slot:"List",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.list,t.flexContainer,n.vertical&&t.flexContainerVertical,n.centered&&t.centered]}})({display:"flex",variants:[{props:({ownerState:e})=>e.vertical,style:{flexDirection:"column"}},{props:({ownerState:e})=>e.centered,style:{justifyContent:"center"}}]}),Ya=re("span",{name:"MuiTabs",slot:"Indicator"})(Re(({theme:e})=>({position:"absolute",height:2,bottom:0,width:"100%",transition:e.transitions.create(),variants:[{props:{indicatorColor:"primary"},style:{backgroundColor:(e.vars||e).palette.primary.main}},{props:{indicatorColor:"secondary"},style:{backgroundColor:(e.vars||e).palette.secondary.main}},{props:({ownerState:t})=>t.vertical,style:{height:"100%",width:2,right:0}}]}))),Ga=re(za)({overflowX:"auto",overflowY:"hidden",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}}),xn={},_a=g.forwardRef(function(t,n){const r=qe({props:t,name:"MuiTabs"}),s=Eo(),a=Gt(),{"aria-label":c,"aria-labelledby":i,action:l,centered:m=!1,children:p,className:P,component:C="div",allowScrollButtonsMobile:y=!1,indicatorColor:M="primary",onChange:w,orientation:T="horizontal",ScrollButtonComponent:A,scrollButtons:h="auto",selectionFollowsFocus:f,slots:d={},slotProps:x={},TabIndicatorProps:S={},TabScrollButtonProps:I={},textColor:Y="primary",value:L,variant:B="standard",visibleScrollbar:F=!1,...G}=r,j=B==="scrollable",R=T==="vertical",Z=R?"scrollTop":"scrollLeft",E=R?"top":"left",J=R?"bottom":"right",U=R?"clientHeight":"clientWidth",ce=R?"height":"width",se={...r,component:C,allowScrollButtonsMobile:y,indicatorColor:M,orientation:T,vertical:R,scrollButtons:h,textColor:Y,variant:B,visibleScrollbar:F,fixed:!j,hideScrollbar:j&&!F,scrollableX:j&&!R,scrollableY:j&&R,centered:m&&!j,scrollButtonsHideMobile:!y},q=Ua(se),K=Ge({elementType:d.StartScrollButtonIcon,externalSlotProps:x.startScrollButtonIcon,ownerState:se}),Ae=Ge({elementType:d.EndScrollButtonIcon,externalSlotProps:x.endScrollButtonIcon,ownerState:se}),[ve,X]=g.useState(!1),[te,de]=g.useState(xn),[he,Te]=g.useState(!1),[u,O]=g.useState(!1),[z,H]=g.useState(!1),[ae,$e]=g.useState({overflow:"hidden",scrollbarWidth:0}),ze=new Map,oe=g.useRef(null),Se=g.useRef(null),Le={slots:d,slotProps:{indicator:S,scrollButton:I,...x}},Ie=()=>{const $=oe.current;let N;if($){const pe=$.getBoundingClientRect();N={clientWidth:$.clientWidth,scrollLeft:$.scrollLeft,scrollTop:$.scrollTop,scrollWidth:$.scrollWidth,top:pe.top,bottom:pe.bottom,left:pe.left,right:pe.right}}let le;if($&&L!==!1){const pe=Se.current.children;if(pe.length>0){const Ce=pe[ze.get(L)];le=Ce?Ce.getBoundingClientRect():null}}return{tabsMeta:N,tabMeta:le}},Be=xt(()=>{const{tabsMeta:$,tabMeta:N}=Ie();let le=0,pe;R?(pe="top",N&&$&&(le=N.top-$.top+$.scrollTop)):(pe=a?"right":"left",N&&$&&(le=(a?-1:1)*(N[pe]-$[pe]+$.scrollLeft)));const Ce={[pe]:le,[ce]:N?N[ce]:0};if(typeof te[pe]!="number"||typeof te[ce]!="number")de(Ce);else{const rt=Math.abs(te[pe]-Ce[pe]),St=Math.abs(te[ce]-Ce[ce]);(rt>=1||St>=1)&&de(Ce)}}),Ve=($,{animation:N=!0}={})=>{N?La(Z,oe.current,$,{duration:s.transitions.duration.standard}):oe.current[Z]=$},De=$=>{let N=oe.current[Z];R?N+=$:N+=$*(a?-1:1),Ve(N)},Ke=()=>{const $=oe.current[U];let N=0;const le=Array.from(Se.current.children);for(let pe=0;pe<le.length;pe+=1){const Ce=le[pe];if(N+Ce[U]>$){pe===0&&(N=$);break}N+=Ce[U]}return N},v=()=>{De(-1*Ke())},k=()=>{De(Ke())},[D,{onChange:_,...ne}]=Me("scrollbar",{className:xe(q.scrollableX,q.hideScrollbar),elementType:Ga,shouldForwardComponentProp:!0,externalForwardedProps:Le,ownerState:se}),V=g.useCallback($=>{_==null||_($),$e({overflow:null,scrollbarWidth:$})},[_]),[ie,be]=Me("scrollButtons",{className:xe(q.scrollButtons,I.className),elementType:Va,externalForwardedProps:Le,ownerState:se,additionalProps:{orientation:T,slots:{StartScrollButtonIcon:d.startScrollButtonIcon||d.StartScrollButtonIcon,EndScrollButtonIcon:d.endScrollButtonIcon||d.EndScrollButtonIcon},slotProps:{startScrollButtonIcon:K,endScrollButtonIcon:Ae}}}),ue=()=>{const $={};$.scrollbarSizeListener=j?o.jsx(D,{...ne,onChange:V}):null;const le=j&&(h==="auto"&&(he||u)||h===!0);return $.scrollButtonStart=le?o.jsx(ie,{direction:a?"right":"left",onClick:v,disabled:!he,...be}):null,$.scrollButtonEnd=le?o.jsx(ie,{direction:a?"left":"right",onClick:k,disabled:!u,...be}):null,$},fe=xt($=>{const{tabsMeta:N,tabMeta:le}=Ie();if(!(!le||!N)){if(le[E]<N[E]){const pe=N[Z]+(le[E]-N[E]);Ve(pe,{animation:$})}else if(le[J]>N[J]){const pe=N[Z]+(le[J]-N[J]);Ve(pe,{animation:$})}}}),Qe=xt(()=>{j&&h!==!1&&H(!z)});g.useEffect(()=>{const $=Cn(()=>{oe.current&&Be()});let N;const le=rt=>{rt.forEach(St=>{St.removedNodes.forEach(Ot=>{N==null||N.unobserve(Ot)}),St.addedNodes.forEach(Ot=>{N==null||N.observe(Ot)})}),$(),Qe()},pe=kn(oe.current);pe.addEventListener("resize",$);let Ce;return typeof ResizeObserver<"u"&&(N=new ResizeObserver($),Array.from(Se.current.children).forEach(rt=>{N.observe(rt)})),typeof MutationObserver<"u"&&(Ce=new MutationObserver(le),Ce.observe(Se.current,{childList:!0})),()=>{$.clear(),pe.removeEventListener("resize",$),Ce==null||Ce.disconnect(),N==null||N.disconnect()}},[Be,Qe]),g.useEffect(()=>{const $=Array.from(Se.current.children),N=$.length;if(typeof IntersectionObserver<"u"&&N>0&&j&&h!==!1){const le=$[0],pe=$[N-1],Ce={root:oe.current,threshold:.99},rt=bo=>{Te(!bo[0].isIntersecting)},St=new IntersectionObserver(rt,Ce);St.observe(le);const Ot=bo=>{O(!bo[0].isIntersecting)},qo=new IntersectionObserver(Ot,Ce);return qo.observe(pe),()=>{St.disconnect(),qo.disconnect()}}},[j,h,z,p==null?void 0:p.length]),g.useEffect(()=>{X(!0)},[]),g.useEffect(()=>{Be()}),g.useEffect(()=>{fe(xn!==te)},[fe,te]),g.useImperativeHandle(l,()=>({updateIndicator:Be,updateScrollButtons:Qe}),[Be,Qe]);const[bt,yt]=Me("indicator",{className:xe(q.indicator,S.className),elementType:Ya,externalForwardedProps:Le,ownerState:se,additionalProps:{style:te}}),pt=o.jsx(bt,{...yt});let nt=0;const wt=g.Children.map(p,$=>{if(!g.isValidElement($))return null;const N=$.props.value===void 0?nt:$.props.value;ze.set(N,nt);const le=N===L;return nt+=1,g.cloneElement($,{fullWidth:B==="fullWidth",indicator:le&&!ve&&pt,selected:le,selectionFollowsFocus:f,onChange:w,textColor:Y,value:N,...nt===1&&L===!1&&!$.props.tabIndex?{tabIndex:0}:{}})}),Bt=$=>{if($.altKey||$.shiftKey||$.ctrlKey||$.metaKey)return;const N=Se.current,le=kt(N).activeElement;if(le.getAttribute("role")!=="tab")return;let Ce=T==="horizontal"?"ArrowLeft":"ArrowUp",rt=T==="horizontal"?"ArrowRight":"ArrowDown";switch(T==="horizontal"&&a&&(Ce="ArrowRight",rt="ArrowLeft"),$.key){case Ce:$.preventDefault(),io(N,le,gn);break;case rt:$.preventDefault(),io(N,le,mn);break;case"Home":$.preventDefault(),io(N,null,mn);break;case"End":$.preventDefault(),io(N,null,gn);break}},ge=ue(),[Pe,jt]=Me("root",{ref:n,className:xe(q.root,P),elementType:qa,externalForwardedProps:{...Le,...G,component:C},ownerState:se}),[ut,Xe]=Me("scroller",{ref:oe,className:q.scroller,elementType:Ka,externalForwardedProps:Le,ownerState:se,additionalProps:{style:{overflow:ae.overflow,[R?`margin${a?"Left":"Right"}`:"marginBottom"]:F?void 0:-ae.scrollbarWidth}}}),[Q,Ye]=Me("list",{ref:Se,className:xe(q.list,q.flexContainer),elementType:Xa,externalForwardedProps:Le,ownerState:se,getSlotProps:$=>({...$,onKeyDown:N=>{var le;Bt(N),(le=$.onKeyDown)==null||le.call($,N)}})});return o.jsxs(Pe,{...jt,children:[ge.scrollButtonStart,ge.scrollbarSizeListener,o.jsxs(ut,{...Xe,children:[o.jsx(Q,{"aria-label":c,"aria-labelledby":i,"aria-orientation":T==="vertical"?"vertical":null,role:"tablist",...Ye,children:wt}),ve&&pt]}),ge.scrollButtonEnd]})}),mo=Ee(o.jsx("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"})),Qa=Ee(o.jsx("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})),Ut=Ee(o.jsx("path",{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6zM19 4h-3.5l-1-1h-5l-1 1H5v2h14z"})),Ja=Ee(o.jsx("path",{d:"M5 20h14v-2H5zM19 9h-4V3H9v6H5l7 7z"})),Za=Ee(o.jsx("path",{d:"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75z"})),vn=Ee(o.jsx("path",{d:"m12 21.35-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54z"})),ei=Ee(o.jsx("path",{d:"M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2m6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1z"})),ti=Ee(o.jsx("path",{d:"M12 2C6.49 2 2 6.49 2 12s4.49 10 10 10c1.38 0 2.5-1.12 2.5-2.5 0-.61-.23-1.2-.64-1.67-.08-.1-.13-.21-.13-.33 0-.28.22-.5.5-.5H16c3.31 0 6-2.69 6-6 0-4.96-4.49-9-10-9m5.5 11c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5m-3-4c-.83 0-1.5-.67-1.5-1.5S13.67 6 14.5 6s1.5.67 1.5 1.5S15.33 9 14.5 9M5 11.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5S7.33 13 6.5 13 5 12.33 5 11.5m6-4c0 .83-.67 1.5-1.5 1.5S8 8.33 8 7.5 8.67 6 9.5 6s1.5.67 1.5 1.5"})),oi=Ee(o.jsx("path",{d:"M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9m-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8z"})),Dn=Ee(o.jsx("path",{d:"M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3m3-10H5V5h10z"})),ni=Ee(o.jsx("path",{d:"M12 1 3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11z"})),lo=Ee([o.jsx("circle",{cx:"12",cy:"6",r:"2"},"0"),o.jsx("path",{d:"M21 16v-2c-2.24 0-4.16-.96-5.6-2.68l-1.34-1.6c-.38-.46-.94-.72-1.53-.72h-1.05c-.59 0-1.15.26-1.53.72l-1.34 1.6C7.16 13.04 5.24 14 3 14v2c2.77 0 5.19-1.17 7-3.25V15l-3.88 1.55c-.67.27-1.12.93-1.12 1.66C5 19.2 5.8 20 6.79 20H9v-.5c0-1.38 1.12-2.5 2.5-2.5h3c.28 0 .5.22.5.5s-.22.5-.5.5h-3c-.83 0-1.5.67-1.5 1.5v.5h7.21c.99 0 1.79-.8 1.79-1.79 0-.73-.45-1.39-1.12-1.66L14 15v-2.25c1.81 2.08 4.23 3.25 7 3.25"},"1")]),bn=Ee(o.jsx("path",{d:"M20 9V7c0-1.1-.9-2-2-2h-3c0-1.66-1.34-3-3-3S9 3.34 9 5H6c-1.1 0-2 .9-2 2v2c-1.66 0-3 1.34-3 3s1.34 3 3 3v4c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-4c1.66 0 3-1.34 3-3s-1.34-3-3-3M7.5 11.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5S9.83 13 9 13s-1.5-.67-1.5-1.5M16 17H8v-2h8zm-1-4c-.83 0-1.5-.67-1.5-1.5S14.17 10 15 10s1.5.67 1.5 1.5S15.83 13 15 13"})),ri=Ee(o.jsx("path",{d:"M2 20h20v-4H2zm2-3h2v2H4zM2 4v4h20V4zm4 3H4V5h2zm-4 7h20v-4H2zm2-3h2v2H4z"})),si=Ee(o.jsx("path",{d:"M5 20h14v-2H5zm0-10h4v6h6v-6h4l-7-7z"})),ai=({open:e,onClose:t,onSave:n,editingMethod:r})=>{const[s,a]=g.useState({name:"",icon:"📈",description:"",questions:[],totalMaxScore:0,isCustom:!0}),[c,i]=g.useState([]);g.useEffect(()=>{a(r||{name:"",icon:"📈",description:"",questions:[{text:"",type:"radio",options:[{value:"option_0",label:"",weight:10},{value:"option_1",label:"",weight:5}]},{text:"",type:"radio",options:[{value:"option_0",label:"",weight:10},{value:"option_1",label:"",weight:5}]},{text:"",type:"radio",options:[{value:"option_0",label:"",weight:10},{value:"option_1",label:"",weight:5}]}],totalMaxScore:0,isCustom:!0})},[r,e]);const l=()=>{s.questions.length<7&&a(h=>({...h,questions:[...h.questions,{text:"",type:"radio",options:[{value:"option_0",label:"",weight:10},{value:"option_1",label:"",weight:5}]}]}))},m=h=>{s.questions.length>3&&a(f=>({...f,questions:f.questions.filter((d,x)=>x!==h)}))},p=(h,f,d)=>{a(x=>({...x,questions:x.questions.map((S,I)=>I===h?{...S,[f]:d}:S)}))},P=h=>{const f=s.questions[h];if(f.options&&f.options.length<6){const d={value:`option_${f.options.length}`,label:"",weight:5};p(h,"options",[...f.options,d])}},C=(h,f)=>{const d=s.questions[h];if(d.options&&d.options.length>2){const x=d.options.filter((S,I)=>I!==f);p(h,"options",x)}},y=(h,f,d,x)=>{const S=s.questions[h];if(S.options){const I=S.options.map((Y,L)=>L===f?{...Y,[d]:x}:Y);p(h,"options",I)}},M=()=>{let h=0;return s.questions.forEach(f=>{if(f.type==="textarea")h+=10;else if(f.options&&f.options.length>0){const d=Math.max(...f.options.map(x=>x.weight));h+=d}}),h},w=()=>{const h=[];(!s.name||s.name.length<3)&&h.push("Tên phương pháp phải có ít nhất 3 ký tự"),(!s.description||s.description.length<10)&&h.push("Mô tả phải có ít nhất 10 ký tự"),s.icon||h.push("Vui lòng chọn icon"),s.questions.length<3&&h.push("Cần ít nhất 3 câu hỏi"),s.questions.forEach((d,x)=>{(!d.text||d.text.length<5)&&h.push(`Câu hỏi ${x+1}: Nội dung phải có ít nhất 5 ký tự`),d.type!=="textarea"&&(!d.options||d.options.length<2?h.push(`Câu hỏi ${x+1}: Cần ít nhất 2 đáp án`):d.options.forEach((S,I)=>{(!S.label||S.label.length<2)&&h.push(`Câu hỏi ${x+1}, Đáp án ${I+1}: Nội dung quá ngắn`),(S.weight<0||S.weight>50)&&h.push(`Câu hỏi ${x+1}, Đáp án ${I+1}: Điểm phải từ 0-50`)}))});const f=M();return f<50&&h.push("Tổng điểm tối đa quá thấp (< 50)"),f>200&&h.push("Tổng điểm tối đa quá cao (> 200)"),h},T=()=>{const h=w();if(h.length>0){i(h);return}const f={...s,id:(r==null?void 0:r.id)||`custom_${Date.now()}`,totalMaxScore:M(),createdAt:(r==null?void 0:r.createdAt)||new Date().toISOString()};n(f),t()},A=()=>{i([]),t()};return o.jsxs($o,{open:e,onClose:A,maxWidth:"md",fullWidth:!0,children:[o.jsx(Oo,{children:o.jsxs(ee,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[o.jsx(b,{variant:"h6",children:r?"✏️ Chỉnh sửa phương pháp":"⚙️ Tạo phương pháp tùy chỉnh"}),o.jsx(wo,{onClick:A,children:o.jsx(Qa,{})})]})}),o.jsxs(Bo,{children:[c.length>0&&o.jsx(Nt,{severity:"error",sx:{mb:2},children:o.jsx("ul",{style:{margin:0,paddingLeft:20},children:c.map((h,f)=>o.jsx("li",{children:h},f))})}),o.jsxs(W,{container:!0,spacing:2,children:[o.jsx(W,{size:12,children:o.jsx(ye,{variant:"outlined",children:o.jsxs(we,{children:[o.jsx(b,{variant:"h6",sx:{mb:2},children:"📊 Thông tin cơ bản"}),o.jsxs(W,{container:!0,spacing:2,children:[o.jsx(W,{size:{xs:12,md:8},children:o.jsx(gt,{fullWidth:!0,label:"Tên phương pháp",value:s.name,onChange:h=>a(f=>({...f,name:h.target.value})),placeholder:"Ví dụ: Fibonacci Retracement Strategy"})}),o.jsx(W,{size:{xs:12,md:4},children:o.jsx(gt,{fullWidth:!0,label:"Icon (emoji)",value:s.icon,onChange:h=>a(f=>({...f,icon:h.target.value})),inputProps:{maxLength:2,style:{textAlign:"center",fontSize:"20px"}}})}),o.jsx(W,{size:12,children:o.jsx(gt,{fullWidth:!0,multiline:!0,rows:3,label:"Mô tả phương pháp",value:s.description,onChange:h=>a(f=>({...f,description:h.target.value})),placeholder:"Mô tả ngắn gọn về phương pháp giao dịch này..."})})]})]})})}),o.jsx(W,{size:12,children:o.jsx(ye,{variant:"outlined",children:o.jsxs(we,{children:[o.jsxs(ee,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[o.jsx(b,{variant:"h6",children:"❓ Câu hỏi phân tích"}),o.jsxs(ee,{sx:{display:"flex",gap:1,alignItems:"center"},children:[o.jsx(mt,{label:`${s.questions.length} câu hỏi`,size:"small"}),o.jsx(mt,{label:`${M()} điểm`,size:"small",color:"primary"}),o.jsx(je,{size:"small",startIcon:o.jsx(mo,{}),onClick:l,disabled:s.questions.length>=7,children:"Thêm câu hỏi"})]})]}),s.questions.map((h,f)=>o.jsx(ye,{variant:"outlined",sx:{mb:2},children:o.jsxs(we,{children:[o.jsxs(ee,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[o.jsxs(b,{variant:"subtitle1",sx:{fontWeight:"bold"},children:["Câu hỏi ",f+1]}),s.questions.length>3&&o.jsx(wo,{size:"small",color:"error",onClick:()=>m(f),children:o.jsx(Ut,{})})]}),o.jsxs(W,{container:!0,spacing:2,children:[o.jsx(W,{size:{xs:12,md:8},children:o.jsx(gt,{fullWidth:!0,label:"Nội dung câu hỏi",value:h.text,onChange:d=>p(f,"text",d.target.value),placeholder:"Ví dụ: Có tín hiệu divergence rõ ràng không?"})}),o.jsx(W,{size:{xs:12,md:4},children:o.jsxs(Jn,{fullWidth:!0,children:[o.jsx(Zn,{children:"Loại câu hỏi"}),o.jsxs(er,{value:h.type,label:"Loại câu hỏi",onChange:d=>{const x=d.target.value;x==="textarea"?(p(f,"type",x),p(f,"options",void 0),p(f,"placeholder","Mô tả chi tiết setup...")):(p(f,"type",x),h.options||p(f,"options",[{value:"option_0",label:"",weight:10},{value:"option_1",label:"",weight:5}]))},children:[o.jsx(So,{value:"radio",children:"Multiple Choice (Radio)"}),o.jsx(So,{value:"select",children:"Dropdown (Select)"}),o.jsx(So,{value:"textarea",children:"Mô tả chi tiết (Textarea)"})]})]})}),h.type!=="textarea"&&h.options&&o.jsxs(W,{size:12,children:[o.jsxs(ee,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[o.jsx(b,{variant:"body2",color:"text.secondary",children:"Đáp án:"}),o.jsx(je,{size:"small",startIcon:o.jsx(mo,{}),onClick:()=>P(f),disabled:!h.options||h.options.length>=6,children:"Thêm đáp án"})]}),h.options.map((d,x)=>o.jsxs(ee,{sx:{display:"flex",gap:1,mb:1,alignItems:"center"},children:[o.jsx(gt,{size:"small",placeholder:"Nội dung đáp án...",value:d.label,onChange:S=>y(f,x,"label",S.target.value),sx:{flex:1}}),o.jsx(gt,{size:"small",type:"number",placeholder:"Điểm",value:d.weight,onChange:S=>y(f,x,"weight",parseInt(S.target.value)||0),inputProps:{min:0,max:50},sx:{width:80}}),h.options&&h.options.length>2&&o.jsx(wo,{size:"small",color:"error",onClick:()=>C(f,x),children:o.jsx(Ut,{})})]},x))]})]})]})},f))]})})})]})]}),o.jsxs(Io,{children:[o.jsx(je,{onClick:A,children:"Hủy"}),o.jsx(je,{variant:"contained",startIcon:o.jsx(Dn,{}),onClick:T,children:r?"Cập nhật":"Tạo phương pháp"})]})]})},ii=Wn({palette:{primary:{main:"#007bff"},secondary:{main:"#6c757d"}}});function ft(e){const{children:t,value:n,index:r,...s}=e;return o.jsx("div",{role:"tabpanel",hidden:n!==r,id:`options-tabpanel-${r}`,"aria-labelledby":`options-tab-${r}`,...s,children:n===r&&o.jsx(ee,{sx:{p:3},children:t})})}const li=()=>{var Te;const[e,t]=g.useState(0),[n,r]=g.useState([]),[s,a]=g.useState([]),[c,i]=g.useState({}),[l,m]=g.useState(!1),[p,P]=g.useState(null),[C,y]=g.useState(!1),[M,w]=g.useState(null),[T,A]=g.useState(!1),[h,f]=g.useState("mindfulness"),[d,x]=g.useState({theme:"light",language:"vi",fontSize:14,notifications:!0,soundAlerts:!0,desktopNotifications:!0,emailNotifications:!1,defaultTradeAmount:10,defaultTradeDuration:5,maxDailyTrades:50,riskWarnings:!0,autoStopLoss:!0,requireConfirmation:!0,sessionTimeout:30,autoLock:!1,autoBackup:!0,backupFrequency:"daily",maxBackups:10,apiUrl:"http://localhost:3001",openaiApiKey:"",useAIPsychology:!1}),[S,I]=g.useState(!1),[Y,L]=g.useState(!1),[B,F]=g.useState(null);g.useEffect(()=>{q(),R(),G()},[]);const G=async()=>{try{const u=await chrome.storage.local.get(["requestedTab"]);u.requestedTab==="meditation"?(t(4),await chrome.storage.local.remove(["requestedTab"])):u.requestedTab==="ai"&&(t(5),await chrome.storage.local.remove(["requestedTab"]))}catch{console.log("Could not check requested tab")}},j=()=>[{id:"bollinger_bands",name:"Bollinger Bands Breakout",icon:"📈",description:"Giao dịch dựa trên việc giá breakout khỏi dải Bollinger Bands với volume xác nhận",questions:5},{id:"rsi_divergence",name:"RSI Divergence",icon:"📊",description:"Tìm kiếm sự phân kỳ giữa giá và RSI để dự đoán sự đảo chiều xu hướng",questions:5},{id:"support_resistance",name:"Support & Resistance",icon:"🔄",description:"Giao dịch tại các vùng support/resistance quan trọng với xác nhận price action",questions:5},{id:"moving_average",name:"Moving Average Crossover",icon:"📉",description:"Sử dụng sự cắt nhau của các đường MA để xác định xu hướng và điểm vào lệnh",questions:5},{id:"price_action",name:"Price Action Patterns",icon:"🕯️",description:"Phân tích các mô hình nến Nhật và price action để dự đoán hướng di chuyển",questions:5}],R=async()=>{try{const O=await fetch("http://localhost:3001/customMethods");if(O.ok){const z=await O.json();a(z)}}catch{console.log("No custom methods found or server not available")}const u=await chrome.storage.local.get(["methodSettings"]);if(u.methodSettings)i(u.methodSettings);else{const O={};j().forEach(z=>{O[z.id]=!0}),i(O)}},Z=u=>c[u]!==!1,E=async(u,O)=>{const z={...c,[u]:O};i(z),await chrome.storage.local.set({methodSettings:z});try{(await chrome.tabs.query({url:"*://binomo1.com/trading*"})).forEach(ae=>{ae.id&&chrome.tabs.sendMessage(ae.id,{action:"updateMethodSettings",methodSettings:z}).catch($e=>{console.log(`Could not update content script in tab ${ae.id}:`,$e)})})}catch(H){console.log("Could not query tabs or update content script:",H)}},J=u=>{console.log("Editing method:",u.name),P(u),m(!0)},U=u=>{w(u),y(!0)},ce=async()=>{if(!M)return;const u=M.id;I(!0),y(!1);try{const O=await fetch(`http://localhost:3001/customMethods/${u}`,{method:"DELETE"});if(O.ok){a(H=>H.filter(ae=>ae.id!==u));const z={...c};delete z[u],i(z),await chrome.storage.local.set({methodSettings:z});try{(await chrome.tabs.query({url:"*://binomo1.com/trading*"})).forEach(ae=>{ae.id&&chrome.tabs.sendMessage(ae.id,{action:"updateMethodSettings",methodSettings:z}).catch($e=>{console.log(`Could not update content script in tab ${ae.id}:`,$e)})})}catch(H){console.log("Could not query tabs or update content script:",H)}L(!0),setTimeout(()=>L(!1),3e3)}else throw new Error(`HTTP ${O.status}`)}catch(O){console.error("Error deleting method:",O),F("Không thể xóa phương pháp. Hãy kiểm tra JSON Server đang chạy!"),setTimeout(()=>F(null),5e3)}finally{I(!1),w(null)}},se=async u=>{try{let O;if(u.id&&p?O=await fetch(`http://localhost:3001/customMethods/${u.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(u)}):O=await fetch("http://localhost:3001/customMethods",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(u)}),O.ok){if(await R(),!p){const z={...c,[u.id]:!0};i(z),await chrome.storage.local.set({methodSettings:z})}try{(await chrome.tabs.query({url:"*://binomo1.com/trading*"})).forEach(H=>{H.id&&chrome.tabs.sendMessage(H.id,{action:"updateMethodSettings",methodSettings:c}).catch(ae=>{console.log(`Could not update content script in tab ${H.id}:`,ae)})})}catch(z){console.log("Could not query tabs or update content script:",z)}L(!0),setTimeout(()=>L(!1),3e3)}else throw new Error("Failed to save method")}catch{F("Không thể lưu phương pháp. Hãy kiểm tra JSON Server đang chạy!"),setTimeout(()=>F(null),5e3)}},q=async()=>{try{const u=await chrome.storage.local.get(["userSettings","openaiApiKey"]);u.userSettings&&x(O=>({...O,...u.userSettings})),u.openaiApiKey&&x(O=>({...O,openaiApiKey:u.openaiApiKey}))}catch(u){console.error("Error loading settings:",u)}},K=(u,O)=>{x(z=>({...z,[u]:O}))},Ae=async()=>{I(!0),F(null);try{const{openaiApiKey:u,...O}=d;await chrome.storage.local.set({userSettings:O}),u?await chrome.storage.local.set({openaiApiKey:u}):await chrome.storage.local.remove(["openaiApiKey"]),L(!0),setTimeout(()=>L(!1),3e3)}catch(u){F("Có lỗi xảy ra khi lưu cài đặt"),console.error("Error saving settings:",u)}finally{I(!1)}},ve=async()=>{if(!confirm("Bạn có chắc chắn muốn reset tất cả cài đặt về mặc định không?"))return;x({theme:"light",language:"vi",fontSize:14,notifications:!0,soundAlerts:!0,desktopNotifications:!0,emailNotifications:!1,defaultTradeAmount:10,defaultTradeDuration:5,maxDailyTrades:50,riskWarnings:!0,autoStopLoss:!0,requireConfirmation:!0,sessionTimeout:30,autoLock:!1,autoBackup:!0,backupFrequency:"daily",maxBackups:10,apiUrl:"http://localhost:3001"}),await Ae()},X=()=>{const u=JSON.stringify(d,null,2),O=new Blob([u],{type:"application/json"}),z=URL.createObjectURL(O),H=document.createElement("a");H.href=z,H.download=`binomo-settings-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(H),H.click(),document.body.removeChild(H),URL.revokeObjectURL(z)},te=()=>{const u=document.createElement("input");u.type="file",u.accept=".json",u.onchange=O=>{var H;const z=(H=O.target.files)==null?void 0:H[0];if(z){const ae=new FileReader;ae.onload=$e=>{var ze;try{const oe=JSON.parse((ze=$e.target)==null?void 0:ze.result);x(Se=>({...Se,...oe})),alert("Cài đặt đã được import thành công!")}catch{alert("File không hợp lệ!")}},ae.readAsText(z)}},u.click()},de=async()=>{try{await chrome.storage.local.set({needsPsychologyConfirmation:!1,lastConfirmationTime:Date.now(),confirmedPsychologyState:"meditation_completed"}),A(!1),chrome.tabs.create({url:"https://binomo1.com/trading"}),console.log("✅ Psychology confirmed after meditation - Opening Binomo")}catch(u){console.error("Error confirming psychology:",u),window.open("https://binomo1.com/trading","_blank")}},he=async()=>{if(!d.openaiApiKey){F("Vui lòng nhập OpenAI API key trước"),setTimeout(()=>F(null),3e3);return}I(!0);try{const u=await fetch("https://api.openai.com/v1/models",{headers:{Authorization:`Bearer ${d.openaiApiKey}`}});if(u.ok)L(!0),setTimeout(()=>L(!1),3e3),alert("✅ Kết nối OpenAI thành công!");else throw new Error(`HTTP ${u.status}`)}catch(u){F("Không thể kết nối OpenAI. Kiểm tra lại API key."),setTimeout(()=>F(null),5e3),console.error("OpenAI connection test failed:",u)}I(!1)};return o.jsxs(Fn,{theme:ii,children:[o.jsx(Vn,{}),o.jsxs(ee,{sx:{maxWidth:1200,mx:"auto",p:3},children:[o.jsx(W,{container:!0,spacing:3,sx:{mb:4},children:o.jsx(W,{size:12,children:o.jsxs(Ft,{sx:{p:3,textAlign:"center",background:"linear-gradient(45deg, #007bff 30%, #0056b3 90%)"},children:[o.jsxs(b,{variant:"h4",sx:{color:"white",mb:1,display:"flex",alignItems:"center",justifyContent:"center",gap:2},children:[o.jsx(or,{fontSize:"large"}),"🎯 Binomo Trading Assistant - Cài đặt"]}),o.jsx(b,{variant:"body1",sx:{color:"rgba(255,255,255,0.8)"},children:"Tùy chỉnh extension theo nhu cầu của bạn"})]})})}),Y&&o.jsx(W,{container:!0,spacing:3,sx:{mb:3},children:o.jsx(W,{size:12,children:o.jsx(Nt,{severity:"success",children:"Cài đặt đã được lưu thành công!"})})}),B&&o.jsx(W,{container:!0,spacing:3,sx:{mb:3},children:o.jsx(W,{size:12,children:o.jsx(Nt,{severity:"error",children:B})})}),o.jsx(W,{container:!0,spacing:3,children:o.jsx(W,{size:12,children:o.jsx(Ft,{sx:{mb:3},children:o.jsxs(_a,{value:e,onChange:(u,O)=>t(O),variant:"scrollable",scrollButtons:"auto",children:[o.jsx(ht,{icon:o.jsx(ti,{}),label:"Giao diện"}),o.jsx(ht,{icon:o.jsx(ei,{}),label:"Thông báo"}),o.jsx(ht,{icon:o.jsx(Hn,{}),label:"Giao dịch"}),o.jsx(ht,{icon:o.jsx(tr,{}),label:"Phương pháp"}),o.jsx(ht,{icon:o.jsx(lo,{}),label:"Thiền"}),o.jsx(ht,{icon:o.jsx(bn,{}),label:"AI"}),o.jsx(ht,{icon:o.jsx(ni,{}),label:"Bảo mật"}),o.jsx(ht,{icon:o.jsx(ri,{}),label:"Dữ liệu"})]})})})}),o.jsx(W,{container:!0,spacing:3,children:o.jsxs(W,{size:12,children:[o.jsx(ft,{value:e,index:0,children:o.jsxs(W,{container:!0,spacing:3,children:[o.jsx(W,{size:12,md:6,children:o.jsx(ye,{children:o.jsxs(we,{children:[o.jsx(b,{variant:"h6",sx:{mb:2},children:"🎨 Theme"}),o.jsxs(Zt,{fullWidth:!0,sx:{mb:2},children:[o.jsx(je,{variant:d.theme==="light"?"contained":"outlined",onClick:()=>K("theme","light"),children:"Light"}),o.jsx(je,{variant:d.theme==="dark"?"contained":"outlined",onClick:()=>K("theme","dark"),children:"Dark"})]}),o.jsxs(b,{variant:"body2",sx:{mb:1},children:["Font Size: ",d.fontSize,"px"]}),o.jsx(Co,{value:d.fontSize,onChange:(u,O)=>K("fontSize",O),min:12,max:20,step:1,marks:!0,valueLabelDisplay:"auto"})]})})}),o.jsx(W,{size:12,md:6,children:o.jsx(ye,{children:o.jsxs(we,{children:[o.jsx(b,{variant:"h6",sx:{mb:2},children:"🌐 Ngôn ngữ"}),o.jsxs(Zt,{fullWidth:!0,children:[o.jsx(je,{variant:d.language==="vi"?"contained":"outlined",onClick:()=>K("language","vi"),children:"Tiếng Việt"}),o.jsx(je,{variant:d.language==="en"?"contained":"outlined",onClick:()=>K("language","en"),children:"English"})]})]})})})]})}),o.jsx(ft,{value:e,index:1,children:o.jsx(W,{container:!0,spacing:3,children:o.jsx(W,{size:12,md:6,children:o.jsx(ye,{children:o.jsxs(we,{children:[o.jsx(b,{variant:"h6",sx:{mb:2},children:"🔔 Thông báo"}),o.jsxs(Ct,{spacing:2,children:[o.jsx(st,{control:o.jsx(at,{checked:d.notifications,onChange:u=>K("notifications",u.target.checked)}),label:"Bật thông báo"}),o.jsx(st,{control:o.jsx(at,{checked:d.soundAlerts,onChange:u=>K("soundAlerts",u.target.checked)}),label:"Âm thanh cảnh báo"}),o.jsx(st,{control:o.jsx(at,{checked:d.desktopNotifications,onChange:u=>K("desktopNotifications",u.target.checked)}),label:"Thông báo desktop"})]})]})})})})}),o.jsx(ft,{value:e,index:2,children:o.jsxs(W,{container:!0,spacing:3,children:[o.jsx(W,{size:12,md:6,children:o.jsx(ye,{children:o.jsxs(we,{children:[o.jsx(b,{variant:"h6",sx:{mb:2},children:"💰 Giao dịch mặc định"}),o.jsxs(Ct,{spacing:3,children:[o.jsxs(ee,{children:[o.jsx(b,{variant:"body2",sx:{mb:1},children:"Số tiền mặc định ($):"}),o.jsx(Zt,{fullWidth:!0,children:[1,5,10,25,50].map(u=>o.jsxs(je,{variant:d.defaultTradeAmount===u?"contained":"outlined",onClick:()=>K("defaultTradeAmount",u),children:["$",u]},u))})]}),o.jsxs(ee,{children:[o.jsx(b,{variant:"body2",sx:{mb:1},children:"Thời gian mặc định:"}),o.jsx(Zt,{fullWidth:!0,children:[1,5,15,30,60].map(u=>o.jsxs(je,{variant:d.defaultTradeDuration===u?"contained":"outlined",onClick:()=>K("defaultTradeDuration",u),children:[u,"m"]},u))})]})]})]})})}),o.jsx(W,{size:12,md:6,children:o.jsx(ye,{children:o.jsxs(we,{children:[o.jsx(b,{variant:"h6",sx:{mb:2},children:"⚠️ Quản lý rủi ro"}),o.jsxs(Ct,{spacing:2,children:[o.jsx(st,{control:o.jsx(at,{checked:d.riskWarnings,onChange:u=>K("riskWarnings",u.target.checked)}),label:"Cảnh báo rủi ro"}),o.jsx(st,{control:o.jsx(at,{checked:d.autoStopLoss,onChange:u=>K("autoStopLoss",u.target.checked)}),label:"Tự động dừng khi thua lỗ"}),o.jsxs(ee,{children:[o.jsxs(b,{variant:"body2",sx:{mb:1},children:["Giới hạn lệnh/ngày: ",d.maxDailyTrades]}),o.jsx(Co,{value:d.maxDailyTrades,onChange:(u,O)=>K("maxDailyTrades",O),min:10,max:100,step:5,marks:!0,valueLabelDisplay:"auto"})]})]})]})})})]})}),o.jsx(ft,{value:e,index:3,children:o.jsxs(W,{container:!0,spacing:3,children:[o.jsx(W,{size:12,children:o.jsx(ye,{children:o.jsxs(we,{children:[o.jsxs(ee,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[o.jsx(b,{variant:"h6",children:"📊 Phương pháp có sẵn"}),o.jsx(mt,{label:`${j().length} phương pháp`,color:"primary"})]}),o.jsx(W,{container:!0,spacing:2,children:j().map(u=>{var O;return o.jsx(W,{size:12,md:6,lg:4,children:o.jsx(ye,{variant:"outlined",sx:{height:"100%"},children:o.jsxs(we,{children:[o.jsxs(ee,{sx:{display:"flex",alignItems:"center",mb:1},children:[o.jsx(b,{variant:"h6",sx:{mr:1},children:u.icon}),o.jsx(b,{variant:"subtitle1",sx:{fontWeight:"bold"},children:u.name})]}),o.jsx(b,{variant:"body2",color:"text.secondary",sx:{mb:2},children:u.description}),o.jsxs(ee,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[o.jsx(mt,{label:`${((O=u.questions)==null?void 0:O.length)||5} câu hỏi`,size:"small",variant:"outlined"}),o.jsx(st,{control:o.jsx(at,{checked:Z(u.id),onChange:z=>E(u.id,z.target.checked),size:"small"}),label:"",sx:{m:0}})]})]})})},u.id)})})]})})}),o.jsx(W,{size:12,children:o.jsx(ye,{children:o.jsxs(we,{children:[o.jsxs(ee,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[o.jsx(b,{variant:"h6",children:"⚙️ Phương pháp tùy chỉnh"}),o.jsxs(ee,{sx:{display:"flex",gap:1},children:[o.jsx(mt,{label:`${s.length} phương pháp`,color:"secondary"}),o.jsx(je,{variant:"contained",size:"small",startIcon:o.jsx(mo,{}),onClick:()=>m(!0),children:"Tạo mới"})]})]}),s.length===0?o.jsxs(ee,{sx:{textAlign:"center",py:4},children:[o.jsx(b,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"Chưa có phương pháp tùy chỉnh nào"}),o.jsx(je,{variant:"outlined",startIcon:o.jsx(mo,{}),onClick:()=>m(!0),children:"Tạo phương pháp đầu tiên"})]}):o.jsx(W,{container:!0,spacing:2,children:s.map(u=>{var O;return o.jsx(W,{size:12,md:6,lg:4,children:o.jsx(ye,{variant:"outlined",sx:{height:"100%",border:"2px solid #4caf50"},children:o.jsxs(we,{children:[o.jsxs(ee,{sx:{display:"flex",alignItems:"center",mb:1},children:[o.jsx(b,{variant:"h6",sx:{mr:1},children:u.icon}),o.jsx(b,{variant:"subtitle1",sx:{fontWeight:"bold",flex:1},children:u.name}),o.jsx(mt,{label:"CUSTOM",size:"small",color:"success"})]}),o.jsx(b,{variant:"body2",color:"text.secondary",sx:{mb:2},children:u.description}),o.jsxs(ee,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[o.jsx(mt,{label:`${((O=u.questions)==null?void 0:O.length)||0} câu hỏi`,size:"small",variant:"outlined"}),o.jsx(mt,{label:`${u.totalMaxScore||0} điểm`,size:"small",variant:"outlined"})]}),o.jsxs(ee,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[o.jsxs(ee,{sx:{display:"flex",gap:1},children:[o.jsx(fn,{title:"Chỉnh sửa phương pháp này",children:o.jsx("span",{children:o.jsx(je,{size:"small",startIcon:o.jsx(Za,{}),onClick:()=>J(u),disabled:S,children:"Sửa"})})}),o.jsx(fn,{title:"Xóa phương pháp này vĩnh viễn",children:o.jsx("span",{children:o.jsx(je,{size:"small",color:"error",startIcon:o.jsx(Ut,{}),onClick:()=>U(u),disabled:S,children:"Xóa"})})})]}),o.jsx(st,{control:o.jsx(at,{checked:Z(u.id),onChange:z=>E(u.id,z.target.checked),size:"small"}),label:"",sx:{m:0}})]})]})})},u.id)})})]})})})]})}),o.jsx(ft,{value:e,index:4,children:o.jsx(W,{container:!0,spacing:3,children:o.jsx(W,{size:12,children:o.jsx(ye,{children:o.jsxs(we,{children:[o.jsxs(ee,{sx:{textAlign:"center",mb:3},children:[o.jsx(bn,{sx:{fontSize:64,color:"primary.main",mb:2}}),o.jsx(b,{variant:"h4",gutterBottom:!0,children:"🤖 AI Psychology Assessment"}),o.jsx(b,{variant:"body1",color:"text.secondary",children:"Cấu hình OpenAI để đánh giá tâm lý giao dịch thông minh"})]}),o.jsxs(W,{container:!0,spacing:3,children:[o.jsx(W,{size:12,children:o.jsx(ye,{variant:"outlined",children:o.jsxs(we,{children:[o.jsx(b,{variant:"h6",gutterBottom:!0,children:"🔑 OpenAI API Configuration"}),o.jsx(b,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"Nhập OpenAI API key để sử dụng tính năng đánh giá tâm lý AI"}),o.jsx(gt,{fullWidth:!0,label:"OpenAI API Key",type:"password",value:d.openaiApiKey,onChange:u=>K("openaiApiKey",u.target.value),placeholder:"sk-...",helperText:"API key sẽ được lưu trữ an toàn trong Chrome storage",sx:{mb:2}}),o.jsx(st,{control:o.jsx(at,{checked:d.useAIPsychology,onChange:u=>K("useAIPsychology",u.target.checked)}),label:"Sử dụng AI để đánh giá tâm lý"}),d.openaiApiKey&&o.jsxs(ee,{sx:{mt:2},children:[o.jsx(Nt,{severity:"info",sx:{mb:2},children:o.jsx(b,{variant:"body2",children:"✅ API key đã được cấu hình. AI psychology assessment sẽ hoạt động trong sidebar."})}),o.jsx(je,{variant:"outlined",size:"small",onClick:he,disabled:S,children:"🧪 Test kết nối"})]})]})})}),o.jsx(W,{size:12,md:6,children:o.jsx(ye,{variant:"outlined",sx:{height:"100%"},children:o.jsxs(we,{children:[o.jsx(b,{variant:"h6",gutterBottom:!0,children:"🧠 AI Psychology Features"}),o.jsxs(ee,{component:"ul",sx:{pl:2,m:0},children:[o.jsx(b,{component:"li",variant:"body2",sx:{mb:1},children:"Phân tích đa yếu tố: cảm xúc, tài chính, thể chất, tinh thần"}),o.jsx(b,{component:"li",variant:"body2",sx:{mb:1},children:"Tính toán thời gian khóa linh hoạt (15 phút - 24 giờ)"}),o.jsx(b,{component:"li",variant:"body2",sx:{mb:1},children:"Khuyến nghị cá nhân hóa dựa trên tình trạng hiện tại"}),o.jsx(b,{component:"li",variant:"body2",sx:{mb:1},children:"Phân tích rủi ro và yếu tố tích cực"})]})]})})}),o.jsx(W,{size:12,md:6,children:o.jsx(ye,{variant:"outlined",sx:{height:"100%"},children:o.jsxs(we,{children:[o.jsx(b,{variant:"h6",gutterBottom:!0,children:"📖 Hướng dẫn sử dụng"}),o.jsxs(ee,{component:"ol",sx:{pl:2,m:0},children:[o.jsxs(b,{component:"li",variant:"body2",sx:{mb:1},children:["Đăng ký tài khoản OpenAI tại ",o.jsx("strong",{children:"platform.openai.com"})]}),o.jsx(b,{component:"li",variant:"body2",sx:{mb:1},children:"Tạo API key trong phần API keys"}),o.jsx(b,{component:"li",variant:"body2",sx:{mb:1},children:"Copy và paste API key vào ô trên"}),o.jsx(b,{component:"li",variant:"body2",sx:{mb:1},children:"Bật tính năng AI psychology assessment"}),o.jsx(b,{component:"li",variant:"body2",sx:{mb:1},children:"Sử dụng trong sidebar khi đánh giá tâm lý"})]})]})})}),o.jsx(W,{size:12,children:o.jsx(Nt,{severity:"warning",children:o.jsxs(b,{variant:"body2",children:[o.jsx("strong",{children:"💰 Chi phí:"})," Mỗi lần đánh giá AI sẽ tốn khoảng $0.001-0.002 USD. Với 100 lần đánh giá/tháng, chi phí khoảng $0.10-0.20 USD."]})})})]})]})})})})}),o.jsx(ft,{value:e,index:5,children:o.jsx(W,{container:!0,spacing:3,children:o.jsx(W,{size:12,children:o.jsx(ye,{children:o.jsxs(we,{children:[o.jsxs(ee,{sx:{textAlign:"center",mb:3},children:[o.jsx(lo,{sx:{fontSize:64,color:"primary.main",mb:2}}),o.jsx(b,{variant:"h4",gutterBottom:!0,children:"🧘‍♂️ Thiền Chánh Niệm"}),o.jsx(b,{variant:"body1",color:"text.secondary",children:"Thời gian để tâm hồn nghỉ ngơi và tái tạo năng lượng tích cực"})]}),o.jsxs(W,{container:!0,spacing:3,children:[o.jsx(W,{size:12,md:4,children:o.jsx(ye,{variant:"outlined",sx:{height:"100%",cursor:"pointer"},onClick:()=>{f("mindfulness"),A(!0)},children:o.jsxs(we,{sx:{textAlign:"center"},children:[o.jsx(lo,{sx:{fontSize:48,color:"primary.main",mb:2}}),o.jsx(b,{variant:"h6",gutterBottom:!0,children:"Thiền Chánh Niệm"}),o.jsx(b,{variant:"body2",color:"text.secondary",children:"Quan sát hơi thở, nhận biết cảm xúc và suy nghĩ mà không phán xét"})]})})}),o.jsx(W,{size:12,md:4,children:o.jsx(ye,{variant:"outlined",sx:{height:"100%",cursor:"pointer"},onClick:()=>{f("gratitude"),A(!0)},children:o.jsxs(we,{sx:{textAlign:"center"},children:[o.jsx(vn,{sx:{fontSize:48,color:"secondary.main",mb:2}}),o.jsx(b,{variant:"h6",gutterBottom:!0,children:"Thiền Biết Ơn"}),o.jsx(b,{variant:"body2",color:"text.secondary",children:"Cảm ơn những gì đã có, nuôi dưỡng lòng biết ơn và sự hài lòng"})]})})}),o.jsx(W,{size:12,md:4,children:o.jsx(ye,{variant:"outlined",sx:{height:"100%",cursor:"pointer"},onClick:()=>{f("compassion"),A(!0)},children:o.jsxs(we,{sx:{textAlign:"center"},children:[o.jsx(Yo,{sx:{fontSize:48,color:"success.main",mb:2}}),o.jsx(b,{variant:"h6",gutterBottom:!0,children:"Thiền Từ Bi"}),o.jsx(b,{variant:"body2",color:"text.secondary",children:"Gửi tình yêu thương đến bản thân và mọi người xung quanh"})]})})})]}),o.jsx(ee,{sx:{mt:3,p:2,bgcolor:"info.light",borderRadius:1},children:o.jsxs(b,{variant:"body2",color:"info.contrastText",children:["💡 ",o.jsx("strong",{children:"Lưu ý:"})," Khi tâm lý không phù hợp để giao dịch, hãy dành thời gian thiền để tái tạo năng lượng tích cực. Giao dịch với tâm hồn bình an sẽ mang lại kết quả tốt hơn."]})})]})})})})}),o.jsx(ft,{value:e,index:6,children:o.jsx(W,{container:!0,spacing:3,children:o.jsx(W,{size:12,md:6,children:o.jsx(ye,{children:o.jsxs(we,{children:[o.jsx(b,{variant:"h6",sx:{mb:2},children:"🔒 Bảo mật"}),o.jsxs(Ct,{spacing:2,children:[o.jsx(st,{control:o.jsx(at,{checked:d.requireConfirmation,onChange:u=>K("requireConfirmation",u.target.checked)}),label:"Yêu cầu xác nhận trước khi giao dịch"}),o.jsxs(ee,{children:[o.jsxs(b,{variant:"body2",sx:{mb:1},children:["Session timeout: ",d.sessionTimeout," phút"]}),o.jsx(Co,{value:d.sessionTimeout,onChange:(u,O)=>K("sessionTimeout",O),min:5,max:120,step:5,marks:!0,valueLabelDisplay:"auto"})]})]})]})})})})}),o.jsx(ft,{value:e,index:7,children:o.jsxs(W,{container:!0,spacing:3,children:[o.jsx(W,{size:12,md:6,children:o.jsx(ye,{children:o.jsxs(we,{children:[o.jsx(b,{variant:"h6",sx:{mb:2},children:"💾 Backup & Storage"}),o.jsxs(Ct,{spacing:2,children:[o.jsx(st,{control:o.jsx(at,{checked:d.autoBackup,onChange:u=>K("autoBackup",u.target.checked)}),label:"Tự động backup"}),o.jsx(gt,{label:"API URL",value:d.apiUrl,onChange:u=>K("apiUrl",u.target.value),fullWidth:!0,size:"small"})]})]})})}),o.jsx(W,{size:12,md:6,children:o.jsx(ye,{children:o.jsxs(we,{children:[o.jsx(b,{variant:"h6",sx:{mb:2},children:"📤 Import/Export"}),o.jsxs(Ct,{spacing:2,children:[o.jsx(je,{variant:"outlined",startIcon:o.jsx(Ja,{}),onClick:X,fullWidth:!0,children:"Xuất cài đặt"}),o.jsx(je,{variant:"outlined",startIcon:o.jsx(si,{}),onClick:te,fullWidth:!0,children:"Nhập cài đặt"})]})]})})})]})})]})}),o.jsx(W,{container:!0,spacing:3,sx:{mt:3},children:o.jsx(W,{size:12,children:o.jsx(Ft,{sx:{p:3},children:o.jsxs(Ct,{direction:"row",spacing:2,justifyContent:"center",children:[o.jsx(je,{variant:"outlined",color:"secondary",startIcon:o.jsx(oi,{}),onClick:ve,children:"Reset về mặc định"}),o.jsx(je,{variant:"contained",startIcon:o.jsx(Dn,{}),onClick:Ae,disabled:S,size:"large",children:S?"Đang lưu...":"Lưu cài đặt"})]})})})}),o.jsx(ai,{open:l,onClose:()=>{m(!1),P(null)},onSave:se,editingMethod:p}),o.jsxs($o,{open:T,onClose:()=>A(!1),maxWidth:"md",fullWidth:!0,children:[o.jsx(Oo,{children:o.jsxs(ee,{sx:{display:"flex",alignItems:"center",gap:1},children:[h==="mindfulness"&&o.jsx(lo,{color:"primary"}),h==="gratitude"&&o.jsx(vn,{color:"secondary"}),h==="compassion"&&o.jsx(Yo,{color:"success"}),o.jsxs(b,{variant:"h6",children:[h==="mindfulness"&&"🧘‍♂️ Thiền Chánh Niệm",h==="gratitude"&&"🙏 Thiền Biết Ơn",h==="compassion"&&"💝 Thiền Từ Bi"]})]})}),o.jsxs(Bo,{children:[h==="mindfulness"&&o.jsxs(ee,{children:[o.jsx(b,{variant:"h6",gutterBottom:!0,children:"Hướng dẫn Thiền Chánh Niệm"}),o.jsxs(b,{paragraph:!0,children:["1. ",o.jsx("strong",{children:"Tìm tư thế thoải mái:"})," Ngồi thẳng lưng, thả lỏng vai, đặt tay trên đùi."]}),o.jsxs(b,{paragraph:!0,children:["2. ",o.jsx("strong",{children:"Quan sát hơi thở:"})," Tập trung vào cảm giác hơi thở vào ra tự nhiên."]}),o.jsxs(b,{paragraph:!0,children:["3. ",o.jsx("strong",{children:"Nhận biết suy nghĩ:"})," Khi tâm trí lang thang, nhẹ nhàng đưa về hơi thở."]}),o.jsxs(b,{paragraph:!0,children:["4. ",o.jsx("strong",{children:"Không phán xét:"})," Quan sát mọi cảm xúc, suy nghĩ mà không đánh giá."]}),o.jsxs(b,{paragraph:!0,children:["5. ",o.jsx("strong",{children:"Thực hành 10-15 phút:"})," Bắt đầu với thời gian ngắn, tăng dần."]}),o.jsx(ee,{sx:{mt:3,p:2,bgcolor:"primary.light",borderRadius:1},children:o.jsxs(b,{variant:"body2",color:"primary.contrastText",children:["💡 ",o.jsx("strong",{children:"Lợi ích:"})," Giảm stress, tăng khả năng tập trung, cải thiện khả năng ra quyết định trong giao dịch."]})})]}),h==="gratitude"&&o.jsxs(ee,{children:[o.jsx(b,{variant:"h6",gutterBottom:!0,children:"Hướng dẫn Thiền Biết Ơn"}),o.jsxs(b,{paragraph:!0,children:["1. ",o.jsx("strong",{children:"Ngồi yên tĩnh:"})," Tìm không gian thoải mái, thở sâu 3 lần."]}),o.jsxs(b,{paragraph:!0,children:["2. ",o.jsx("strong",{children:"Nghĩ về những điều tốt đẹp:"})," Gia đình, sức khỏe, cơ hội học hỏi từ thị trường."]}),o.jsxs(b,{paragraph:!0,children:["3. ",o.jsx("strong",{children:"Cảm ơn thị trường:"}),' "Tôi cảm ơn thị trường đã tạo ra nơi để tôi rèn luyện tâm và kiếm lợi nhuận đủ sống."']}),o.jsxs(b,{paragraph:!0,children:["4. ",o.jsx("strong",{children:"Cảm ơn bản thân:"}),' "Tôi cảm ơn bản thân đã kiên nhẫn học hỏi và rèn luyện."']}),o.jsxs(b,{paragraph:!0,children:["5. ",o.jsx("strong",{children:"Gửi lòng biết ơn:"})," Đến mọi người đã hỗ trợ hành trình của bạn."]}),o.jsx(ee,{sx:{mt:3,p:2,bgcolor:"secondary.light",borderRadius:1},children:o.jsxs(b,{variant:"body2",color:"secondary.contrastText",children:["💝 ",o.jsx("strong",{children:"Lợi ích:"})," Tăng cảm giác hạnh phúc, giảm tham lam, tạo tâm thái tích cực trong giao dịch."]})})]}),h==="compassion"&&o.jsxs(ee,{children:[o.jsx(b,{variant:"h6",gutterBottom:!0,children:"Hướng dẫn Thiền Từ Bi"}),o.jsxs(b,{paragraph:!0,children:["1. ",o.jsx("strong",{children:"Bắt đầu với bản thân:"}),' "Mong tôi được bình an, hạnh phúc và thành công."']}),o.jsxs(b,{paragraph:!0,children:["2. ",o.jsx("strong",{children:"Gửi đến người thân:"}),' "Mong gia đình tôi được khỏe mạnh và hạnh phúc."']}),o.jsxs(b,{paragraph:!0,children:["3. ",o.jsx("strong",{children:"Gửi đến trader khác:"}),' "Mong tất cả trader đều học hỏi và phát triển."']}),o.jsxs(b,{paragraph:!0,children:["4. ",o.jsx("strong",{children:"Tha thứ cho bản thân:"}),' "Tôi tha thứ cho những sai lầm trong giao dịch và sẽ học hỏi."']}),o.jsxs(b,{paragraph:!0,children:["5. ",o.jsx("strong",{children:"Tình yêu thương rộng lớn:"})," Gửi tình yêu thương đến tất cả chúng sinh."]}),o.jsx(ee,{sx:{mt:3,p:2,bgcolor:"success.light",borderRadius:1},children:o.jsxs(b,{variant:"body2",color:"success.contrastText",children:["🌟 ",o.jsx("strong",{children:"Lợi ích:"})," Giảm tức giận khi thua lỗ, tăng khả năng tha thứ, tạo tâm thái bình an."]})})]})]}),o.jsxs(Io,{children:[o.jsx(je,{onClick:()=>A(!1),children:"Đóng"}),o.jsx(je,{variant:"contained",color:"success",onClick:de,children:"✅ Tâm đã ổn định - Mở Binomo"})]})]}),o.jsxs($o,{open:C,onClose:()=>{y(!1),w(null)},maxWidth:"sm",fullWidth:!0,children:[o.jsx(Oo,{children:o.jsxs(ee,{sx:{display:"flex",alignItems:"center",gap:1},children:[o.jsx(Ut,{color:"error"}),o.jsx(b,{variant:"h6",children:"Xác nhận xóa phương pháp"})]})}),o.jsxs(Bo,{children:[o.jsxs(rn,{children:["Bạn có chắc chắn muốn xóa phương pháp"," ",o.jsxs("strong",{children:['"',M==null?void 0:M.name,'"']}),"?"]}),o.jsx(rn,{sx:{mt:2,color:"error.main"},children:"⚠️ Hành động này không thể hoàn tác. Tất cả dữ liệu liên quan đến phương pháp này sẽ bị xóa vĩnh viễn."}),M&&o.jsxs(ee,{sx:{mt:2,p:2,bgcolor:"grey.100",borderRadius:1},children:[o.jsx(b,{variant:"body2",color:"text.secondary",children:o.jsx("strong",{children:"Thông tin phương pháp:"})}),o.jsxs(b,{variant:"body2",children:["• Tên: ",M.name]}),o.jsxs(b,{variant:"body2",children:["• Số câu hỏi: ",((Te=M.questions)==null?void 0:Te.length)||0]}),o.jsxs(b,{variant:"body2",children:["• Điểm tối đa: ",M.totalMaxScore||0]}),o.jsxs(b,{variant:"body2",children:["• Ngày tạo: ",M.createdAt?new Date(M.createdAt).toLocaleDateString("vi-VN"):"N/A"]})]})]}),o.jsxs(Io,{children:[o.jsx(je,{onClick:()=>{y(!1),w(null)},disabled:S,children:"Hủy"}),o.jsx(je,{onClick:ce,color:"error",variant:"contained",disabled:S,startIcon:S?o.jsx(Un,{size:16}):o.jsx(Ut,{}),children:S?"Đang xóa...":"Xóa phương pháp"})]})]})]})]})},yn=document.getElementById("root");yn&&qn.createRoot(yn).render(o.jsx(li,{}));
