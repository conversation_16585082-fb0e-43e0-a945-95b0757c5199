import{r as u,c as ue,p as as,q as ls,_ as cs,t as ds,v as So,w as $r,x as ps,y as us,z as Ye,D as fs,E as pt,g as Ie,a as we,j as r,b as Te,F as _t,u as $e,s as V,H as dt,J as hs,K as ye,L as me,M as Lr,d as Ke,N as xo,O as be,Q as Ko,U as ct,V as No,P as Vt,W as Xo,f as q,X as kn,Y as jt,Z as tn,$ as ao,a0 as mt,a1 as Tn,a2 as eo,a3 as zo,a4 as pn,a5 as un,a6 as ms,B as je,I as on,A as fn,G as ie,h as Ve,i as qe,m as Ct,l as Ue,e as gs,T as bs,C as vs,S as xs,k as ys,o as Co,n as Mt,a7 as ws,a8 as Ss}from"../assets/TrendingUp-BecRsdBW.js";function Un(...e){return e.reduce((t,o)=>o==null?t:function(...s){t.apply(this,s),o.apply(this,s)},()=>{})}function Go(e,t=166){let o;function n(...s){const i=()=>{e.apply(this,s)};clearTimeout(o),o=setTimeout(i,t)}return n.clear=()=>{clearTimeout(o)},n}function Xe(e){return e&&e.ownerDocument||document}function ut(e){return Xe(e).defaultView||window}function Vn(e,t){typeof e=="function"?e(t):e&&(e.current=t)}function go(e){const{controlled:t,default:o,name:n,state:s="value"}=e,{current:i}=u.useRef(t!==void 0),[l,a]=u.useState(o),c=i?t:l,h=u.useCallback(p=>{i||a(p)},[]);return[c,h]}function Cs(e,t){const o=e.charCodeAt(2);return e[0]==="o"&&e[1]==="n"&&o>=65&&o<=90&&typeof t=="function"}function js(e,t){if(!e)return t;function o(l,a){const c={};return Object.keys(a).forEach(h=>{Cs(h,a[h])&&typeof l[h]=="function"&&(c[h]=(...p)=>{l[h](...p),a[h](...p)})}),c}if(typeof e=="function"||typeof t=="function")return l=>{const a=typeof t=="function"?t(l):t,c=typeof e=="function"?e({...l,...a}):e,h=ue(l==null?void 0:l.className,a==null?void 0:a.className,c==null?void 0:c.className),p=o(c,a);return{...a,...c,...p,...!!h&&{className:h},...(a==null?void 0:a.style)&&(c==null?void 0:c.style)&&{style:{...a.style,...c.style}},...(a==null?void 0:a.sx)&&(c==null?void 0:c.sx)&&{sx:[...Array.isArray(a.sx)?a.sx:[a.sx],...Array.isArray(c.sx)?c.sx:[c.sx]]}}};const n=t,s=o(e,n),i=ue(n==null?void 0:n.className,e==null?void 0:e.className);return{...t,...e,...s,...!!i&&{className:i},...(n==null?void 0:n.style)&&(e==null?void 0:e.style)&&{style:{...n.style,...e.style}},...(n==null?void 0:n.sx)&&(e==null?void 0:e.sx)&&{sx:[...Array.isArray(n.sx)?n.sx:[n.sx],...Array.isArray(e.sx)?e.sx:[e.sx]]}}}var Br=ls();const jo=as(Br),qn={disabled:!1};var ks=function(t){return t.scrollTop},lo="unmounted",Lt="exited",Bt="entering",Ut="entered",hn="exiting",vt=function(e){cs(t,e);function t(n,s){var i;i=e.call(this,n,s)||this;var l=s,a=l&&!l.isMounting?n.enter:n.appear,c;return i.appearStatus=null,n.in?a?(c=Lt,i.appearStatus=Bt):c=Ut:n.unmountOnExit||n.mountOnEnter?c=lo:c=Lt,i.state={status:c},i.nextCallback=null,i}t.getDerivedStateFromProps=function(s,i){var l=s.in;return l&&i.status===lo?{status:Lt}:null};var o=t.prototype;return o.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},o.componentDidUpdate=function(s){var i=null;if(s!==this.props){var l=this.state.status;this.props.in?l!==Bt&&l!==Ut&&(i=Bt):(l===Bt||l===Ut)&&(i=hn)}this.updateStatus(!1,i)},o.componentWillUnmount=function(){this.cancelNextCallback()},o.getTimeouts=function(){var s=this.props.timeout,i,l,a;return i=l=a=s,s!=null&&typeof s!="number"&&(i=s.exit,l=s.enter,a=s.appear!==void 0?s.appear:l),{exit:i,enter:l,appear:a}},o.updateStatus=function(s,i){if(s===void 0&&(s=!1),i!==null)if(this.cancelNextCallback(),i===Bt){if(this.props.unmountOnExit||this.props.mountOnEnter){var l=this.props.nodeRef?this.props.nodeRef.current:jo.findDOMNode(this);l&&ks(l)}this.performEnter(s)}else this.performExit();else this.props.unmountOnExit&&this.state.status===Lt&&this.setState({status:lo})},o.performEnter=function(s){var i=this,l=this.props.enter,a=this.context?this.context.isMounting:s,c=this.props.nodeRef?[a]:[jo.findDOMNode(this),a],h=c[0],p=c[1],v=this.getTimeouts(),w=a?v.appear:v.enter;if(!s&&!l||qn.disabled){this.safeSetState({status:Ut},function(){i.props.onEntered(h)});return}this.props.onEnter(h,p),this.safeSetState({status:Bt},function(){i.props.onEntering(h,p),i.onTransitionEnd(w,function(){i.safeSetState({status:Ut},function(){i.props.onEntered(h,p)})})})},o.performExit=function(){var s=this,i=this.props.exit,l=this.getTimeouts(),a=this.props.nodeRef?void 0:jo.findDOMNode(this);if(!i||qn.disabled){this.safeSetState({status:Lt},function(){s.props.onExited(a)});return}this.props.onExit(a),this.safeSetState({status:hn},function(){s.props.onExiting(a),s.onTransitionEnd(l.exit,function(){s.safeSetState({status:Lt},function(){s.props.onExited(a)})})})},o.cancelNextCallback=function(){this.nextCallback!==null&&(this.nextCallback.cancel(),this.nextCallback=null)},o.safeSetState=function(s,i){i=this.setNextCallback(i),this.setState(s,i)},o.setNextCallback=function(s){var i=this,l=!0;return this.nextCallback=function(a){l&&(l=!1,i.nextCallback=null,s(a))},this.nextCallback.cancel=function(){l=!1},this.nextCallback},o.onTransitionEnd=function(s,i){this.setNextCallback(i);var l=this.props.nodeRef?this.props.nodeRef.current:jo.findDOMNode(this),a=s==null&&!this.props.addEndListener;if(!l||a){setTimeout(this.nextCallback,0);return}if(this.props.addEndListener){var c=this.props.nodeRef?[this.nextCallback]:[l,this.nextCallback],h=c[0],p=c[1];this.props.addEndListener(h,p)}s!=null&&setTimeout(this.nextCallback,s)},o.render=function(){var s=this.state.status;if(s===lo)return null;var i=this.props,l=i.children;i.in,i.mountOnEnter,i.unmountOnExit,i.appear,i.enter,i.exit,i.timeout,i.addEndListener,i.onEnter,i.onEntering,i.onEntered,i.onExit,i.onExiting,i.onExited,i.nodeRef;var a=ds(i,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]);return So.createElement($r.Provider,{value:null},typeof l=="function"?l(s,a):So.cloneElement(So.Children.only(l),a))},t}(So.Component);vt.contextType=$r;vt.propTypes={};function Wt(){}vt.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:Wt,onEntering:Wt,onEntered:Wt,onExit:Wt,onExiting:Wt,onExited:Wt};vt.UNMOUNTED=lo;vt.EXITED=Lt;vt.ENTERING=Bt;vt.ENTERED=Ut;vt.EXITING=hn;const Ar=e=>e.scrollTop;function Do(e,t){const{timeout:o,easing:n,style:s={}}=e;return{duration:s.transitionDuration??(typeof o=="number"?o:o[t.mode]||0),easing:s.transitionTimingFunction??(typeof n=="object"?n[t.mode]:n),delay:s.transitionDelay}}var Je="top",at="bottom",lt="right",Ze="left",Rn="auto",yo=[Je,at,lt,Ze],qt="start",bo="end",Ts="clippingParents",Nr="viewport",to="popper",Rs="reference",Kn=yo.reduce(function(e,t){return e.concat([t+"-"+qt,t+"-"+bo])},[]),Fr=[].concat(yo,[Rn]).reduce(function(e,t){return e.concat([t,t+"-"+qt,t+"-"+bo])},[]),Es="beforeRead",Ps="read",Ms="afterRead",Is="beforeMain",Os="main",$s="afterMain",Ls="beforeWrite",Bs="write",As="afterWrite",Ns=[Es,Ps,Ms,Is,Os,$s,Ls,Bs,As];function bt(e){return e?(e.nodeName||"").toLowerCase():null}function nt(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function zt(e){var t=nt(e).Element;return e instanceof t||e instanceof Element}function it(e){var t=nt(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function En(e){if(typeof ShadowRoot>"u")return!1;var t=nt(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function Fs(e){var t=e.state;Object.keys(t.elements).forEach(function(o){var n=t.styles[o]||{},s=t.attributes[o]||{},i=t.elements[o];!it(i)||!bt(i)||(Object.assign(i.style,n),Object.keys(s).forEach(function(l){var a=s[l];a===!1?i.removeAttribute(l):i.setAttribute(l,a===!0?"":a)}))})}function zs(e){var t=e.state,o={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,o.popper),t.styles=o,t.elements.arrow&&Object.assign(t.elements.arrow.style,o.arrow),function(){Object.keys(t.elements).forEach(function(n){var s=t.elements[n],i=t.attributes[n]||{},l=Object.keys(t.styles.hasOwnProperty(n)?t.styles[n]:o[n]),a=l.reduce(function(c,h){return c[h]="",c},{});!it(s)||!bt(s)||(Object.assign(s.style,a),Object.keys(i).forEach(function(c){s.removeAttribute(c)}))})}}const Ds={name:"applyStyles",enabled:!0,phase:"write",fn:Fs,effect:zs,requires:["computeStyles"]};function gt(e){return e.split("-")[0]}var Nt=Math.max,Wo=Math.min,Kt=Math.round;function mn(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function zr(){return!/^((?!chrome|android).)*safari/i.test(mn())}function Xt(e,t,o){t===void 0&&(t=!1),o===void 0&&(o=!1);var n=e.getBoundingClientRect(),s=1,i=1;t&&it(e)&&(s=e.offsetWidth>0&&Kt(n.width)/e.offsetWidth||1,i=e.offsetHeight>0&&Kt(n.height)/e.offsetHeight||1);var l=zt(e)?nt(e):window,a=l.visualViewport,c=!zr()&&o,h=(n.left+(c&&a?a.offsetLeft:0))/s,p=(n.top+(c&&a?a.offsetTop:0))/i,v=n.width/s,w=n.height/i;return{width:v,height:w,top:p,right:h+v,bottom:p+w,left:h,x:h,y:p}}function Pn(e){var t=Xt(e),o=e.offsetWidth,n=e.offsetHeight;return Math.abs(t.width-o)<=1&&(o=t.width),Math.abs(t.height-n)<=1&&(n=t.height),{x:e.offsetLeft,y:e.offsetTop,width:o,height:n}}function Dr(e,t){var o=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(o&&En(o)){var n=t;do{if(n&&e.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function wt(e){return nt(e).getComputedStyle(e)}function Ws(e){return["table","td","th"].indexOf(bt(e))>=0}function kt(e){return((zt(e)?e.ownerDocument:e.document)||window.document).documentElement}function Yo(e){return bt(e)==="html"?e:e.assignedSlot||e.parentNode||(En(e)?e.host:null)||kt(e)}function Xn(e){return!it(e)||wt(e).position==="fixed"?null:e.offsetParent}function Hs(e){var t=/firefox/i.test(mn()),o=/Trident/i.test(mn());if(o&&it(e)){var n=wt(e);if(n.position==="fixed")return null}var s=Yo(e);for(En(s)&&(s=s.host);it(s)&&["html","body"].indexOf(bt(s))<0;){var i=wt(s);if(i.transform!=="none"||i.perspective!=="none"||i.contain==="paint"||["transform","perspective"].indexOf(i.willChange)!==-1||t&&i.willChange==="filter"||t&&i.filter&&i.filter!=="none")return s;s=s.parentNode}return null}function wo(e){for(var t=nt(e),o=Xn(e);o&&Ws(o)&&wt(o).position==="static";)o=Xn(o);return o&&(bt(o)==="html"||bt(o)==="body"&&wt(o).position==="static")?t:o||Hs(e)||t}function Mn(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function po(e,t,o){return Nt(e,Wo(t,o))}function Us(e,t,o){var n=po(e,t,o);return n>o?o:n}function Wr(){return{top:0,right:0,bottom:0,left:0}}function Hr(e){return Object.assign({},Wr(),e)}function Ur(e,t){return t.reduce(function(o,n){return o[n]=e,o},{})}var Vs=function(t,o){return t=typeof t=="function"?t(Object.assign({},o.rects,{placement:o.placement})):t,Hr(typeof t!="number"?t:Ur(t,yo))};function qs(e){var t,o=e.state,n=e.name,s=e.options,i=o.elements.arrow,l=o.modifiersData.popperOffsets,a=gt(o.placement),c=Mn(a),h=[Ze,lt].indexOf(a)>=0,p=h?"height":"width";if(!(!i||!l)){var v=Vs(s.padding,o),w=Pn(i),g=c==="y"?Je:Ze,x=c==="y"?at:lt,j=o.rects.reference[p]+o.rects.reference[c]-l[c]-o.rects.popper[p],y=l[c]-o.rects.reference[c],S=wo(i),m=S?c==="y"?S.clientHeight||0:S.clientWidth||0:0,f=j/2-y/2,d=v[g],b=m-w[p]-v[x],C=m/2-w[p]/2+f,P=po(d,C,b),$=c;o.modifiersData[n]=(t={},t[$]=P,t.centerOffset=P-C,t)}}function Ks(e){var t=e.state,o=e.options,n=o.element,s=n===void 0?"[data-popper-arrow]":n;s!=null&&(typeof s=="string"&&(s=t.elements.popper.querySelector(s),!s)||Dr(t.elements.popper,s)&&(t.elements.arrow=s))}const Xs={name:"arrow",enabled:!0,phase:"main",fn:qs,effect:Ks,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Gt(e){return e.split("-")[1]}var Gs={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ys(e,t){var o=e.x,n=e.y,s=t.devicePixelRatio||1;return{x:Kt(o*s)/s||0,y:Kt(n*s)/s||0}}function Gn(e){var t,o=e.popper,n=e.popperRect,s=e.placement,i=e.variation,l=e.offsets,a=e.position,c=e.gpuAcceleration,h=e.adaptive,p=e.roundOffsets,v=e.isFixed,w=l.x,g=w===void 0?0:w,x=l.y,j=x===void 0?0:x,y=typeof p=="function"?p({x:g,y:j}):{x:g,y:j};g=y.x,j=y.y;var S=l.hasOwnProperty("x"),m=l.hasOwnProperty("y"),f=Ze,d=Je,b=window;if(h){var C=wo(o),P="clientHeight",$="clientWidth";if(C===nt(o)&&(C=kt(o),wt(C).position!=="static"&&a==="absolute"&&(P="scrollHeight",$="scrollWidth")),C=C,s===Je||(s===Ze||s===lt)&&i===bo){d=at;var L=v&&C===b&&b.visualViewport?b.visualViewport.height:C[P];j-=L-n.height,j*=c?1:-1}if(s===Ze||(s===Je||s===at)&&i===bo){f=lt;var O=v&&C===b&&b.visualViewport?b.visualViewport.width:C[$];g-=O-n.width,g*=c?1:-1}}var N=Object.assign({position:a},h&&Gs),F=p===!0?Ys({x:g,y:j},nt(o)):{x:g,y:j};if(g=F.x,j=F.y,c){var R;return Object.assign({},N,(R={},R[d]=m?"0":"",R[f]=S?"0":"",R.transform=(b.devicePixelRatio||1)<=1?"translate("+g+"px, "+j+"px)":"translate3d("+g+"px, "+j+"px, 0)",R))}return Object.assign({},N,(t={},t[d]=m?j+"px":"",t[f]=S?g+"px":"",t.transform="",t))}function _s(e){var t=e.state,o=e.options,n=o.gpuAcceleration,s=n===void 0?!0:n,i=o.adaptive,l=i===void 0?!0:i,a=o.roundOffsets,c=a===void 0?!0:a,h={placement:gt(t.placement),variation:Gt(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:s,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,Gn(Object.assign({},h,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:l,roundOffsets:c})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,Gn(Object.assign({},h,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:c})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}const Qs={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:_s,data:{}};var ko={passive:!0};function Js(e){var t=e.state,o=e.instance,n=e.options,s=n.scroll,i=s===void 0?!0:s,l=n.resize,a=l===void 0?!0:l,c=nt(t.elements.popper),h=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&h.forEach(function(p){p.addEventListener("scroll",o.update,ko)}),a&&c.addEventListener("resize",o.update,ko),function(){i&&h.forEach(function(p){p.removeEventListener("scroll",o.update,ko)}),a&&c.removeEventListener("resize",o.update,ko)}}const Zs={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Js,data:{}};var ei={left:"right",right:"left",bottom:"top",top:"bottom"};function Fo(e){return e.replace(/left|right|bottom|top/g,function(t){return ei[t]})}var ti={start:"end",end:"start"};function Yn(e){return e.replace(/start|end/g,function(t){return ti[t]})}function In(e){var t=nt(e),o=t.pageXOffset,n=t.pageYOffset;return{scrollLeft:o,scrollTop:n}}function On(e){return Xt(kt(e)).left+In(e).scrollLeft}function oi(e,t){var o=nt(e),n=kt(e),s=o.visualViewport,i=n.clientWidth,l=n.clientHeight,a=0,c=0;if(s){i=s.width,l=s.height;var h=zr();(h||!h&&t==="fixed")&&(a=s.offsetLeft,c=s.offsetTop)}return{width:i,height:l,x:a+On(e),y:c}}function ni(e){var t,o=kt(e),n=In(e),s=(t=e.ownerDocument)==null?void 0:t.body,i=Nt(o.scrollWidth,o.clientWidth,s?s.scrollWidth:0,s?s.clientWidth:0),l=Nt(o.scrollHeight,o.clientHeight,s?s.scrollHeight:0,s?s.clientHeight:0),a=-n.scrollLeft+On(e),c=-n.scrollTop;return wt(s||o).direction==="rtl"&&(a+=Nt(o.clientWidth,s?s.clientWidth:0)-i),{width:i,height:l,x:a,y:c}}function $n(e){var t=wt(e),o=t.overflow,n=t.overflowX,s=t.overflowY;return/auto|scroll|overlay|hidden/.test(o+s+n)}function Vr(e){return["html","body","#document"].indexOf(bt(e))>=0?e.ownerDocument.body:it(e)&&$n(e)?e:Vr(Yo(e))}function uo(e,t){var o;t===void 0&&(t=[]);var n=Vr(e),s=n===((o=e.ownerDocument)==null?void 0:o.body),i=nt(n),l=s?[i].concat(i.visualViewport||[],$n(n)?n:[]):n,a=t.concat(l);return s?a:a.concat(uo(Yo(l)))}function gn(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function ri(e,t){var o=Xt(e,!1,t==="fixed");return o.top=o.top+e.clientTop,o.left=o.left+e.clientLeft,o.bottom=o.top+e.clientHeight,o.right=o.left+e.clientWidth,o.width=e.clientWidth,o.height=e.clientHeight,o.x=o.left,o.y=o.top,o}function _n(e,t,o){return t===Nr?gn(oi(e,o)):zt(t)?ri(t,o):gn(ni(kt(e)))}function si(e){var t=uo(Yo(e)),o=["absolute","fixed"].indexOf(wt(e).position)>=0,n=o&&it(e)?wo(e):e;return zt(n)?t.filter(function(s){return zt(s)&&Dr(s,n)&&bt(s)!=="body"}):[]}function ii(e,t,o,n){var s=t==="clippingParents"?si(e):[].concat(t),i=[].concat(s,[o]),l=i[0],a=i.reduce(function(c,h){var p=_n(e,h,n);return c.top=Nt(p.top,c.top),c.right=Wo(p.right,c.right),c.bottom=Wo(p.bottom,c.bottom),c.left=Nt(p.left,c.left),c},_n(e,l,n));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function qr(e){var t=e.reference,o=e.element,n=e.placement,s=n?gt(n):null,i=n?Gt(n):null,l=t.x+t.width/2-o.width/2,a=t.y+t.height/2-o.height/2,c;switch(s){case Je:c={x:l,y:t.y-o.height};break;case at:c={x:l,y:t.y+t.height};break;case lt:c={x:t.x+t.width,y:a};break;case Ze:c={x:t.x-o.width,y:a};break;default:c={x:t.x,y:t.y}}var h=s?Mn(s):null;if(h!=null){var p=h==="y"?"height":"width";switch(i){case qt:c[h]=c[h]-(t[p]/2-o[p]/2);break;case bo:c[h]=c[h]+(t[p]/2-o[p]/2);break}}return c}function vo(e,t){t===void 0&&(t={});var o=t,n=o.placement,s=n===void 0?e.placement:n,i=o.strategy,l=i===void 0?e.strategy:i,a=o.boundary,c=a===void 0?Ts:a,h=o.rootBoundary,p=h===void 0?Nr:h,v=o.elementContext,w=v===void 0?to:v,g=o.altBoundary,x=g===void 0?!1:g,j=o.padding,y=j===void 0?0:j,S=Hr(typeof y!="number"?y:Ur(y,yo)),m=w===to?Rs:to,f=e.rects.popper,d=e.elements[x?m:w],b=ii(zt(d)?d:d.contextElement||kt(e.elements.popper),c,p,l),C=Xt(e.elements.reference),P=qr({reference:C,element:f,placement:s}),$=gn(Object.assign({},f,P)),L=w===to?$:C,O={top:b.top-L.top+S.top,bottom:L.bottom-b.bottom+S.bottom,left:b.left-L.left+S.left,right:L.right-b.right+S.right},N=e.modifiersData.offset;if(w===to&&N){var F=N[s];Object.keys(O).forEach(function(R){var E=[lt,at].indexOf(R)>=0?1:-1,M=[Je,at].indexOf(R)>=0?"y":"x";O[R]+=F[M]*E})}return O}function ai(e,t){t===void 0&&(t={});var o=t,n=o.placement,s=o.boundary,i=o.rootBoundary,l=o.padding,a=o.flipVariations,c=o.allowedAutoPlacements,h=c===void 0?Fr:c,p=Gt(n),v=p?a?Kn:Kn.filter(function(x){return Gt(x)===p}):yo,w=v.filter(function(x){return h.indexOf(x)>=0});w.length===0&&(w=v);var g=w.reduce(function(x,j){return x[j]=vo(e,{placement:j,boundary:s,rootBoundary:i,padding:l})[gt(j)],x},{});return Object.keys(g).sort(function(x,j){return g[x]-g[j]})}function li(e){if(gt(e)===Rn)return[];var t=Fo(e);return[Yn(e),t,Yn(t)]}function ci(e){var t=e.state,o=e.options,n=e.name;if(!t.modifiersData[n]._skip){for(var s=o.mainAxis,i=s===void 0?!0:s,l=o.altAxis,a=l===void 0?!0:l,c=o.fallbackPlacements,h=o.padding,p=o.boundary,v=o.rootBoundary,w=o.altBoundary,g=o.flipVariations,x=g===void 0?!0:g,j=o.allowedAutoPlacements,y=t.options.placement,S=gt(y),m=S===y,f=c||(m||!x?[Fo(y)]:li(y)),d=[y].concat(f).reduce(function(de,K){return de.concat(gt(K)===Rn?ai(t,{placement:K,boundary:p,rootBoundary:v,padding:h,flipVariations:x,allowedAutoPlacements:j}):K)},[]),b=t.rects.reference,C=t.rects.popper,P=new Map,$=!0,L=d[0],O=0;O<d.length;O++){var N=d[O],F=gt(N),R=Gt(N)===qt,E=[Je,at].indexOf(F)>=0,M=E?"width":"height",k=vo(t,{placement:N,boundary:p,rootBoundary:v,altBoundary:w,padding:h}),W=E?R?lt:Ze:R?at:Je;b[M]>C[M]&&(W=Fo(W));var H=Fo(W),A=[];if(i&&A.push(k[F]<=0),a&&A.push(k[W]<=0,k[H]<=0),A.every(function(de){return de})){L=N,$=!1;break}P.set(N,A)}if($)for(var _=x?3:1,Y=function(K){var le=d.find(function(ce){var T=P.get(ce);if(T)return T.slice(0,K).every(function(D){return D})});if(le)return L=le,"break"},ee=_;ee>0;ee--){var ae=Y(ee);if(ae==="break")break}t.placement!==L&&(t.modifiersData[n]._skip=!0,t.placement=L,t.reset=!0)}}const di={name:"flip",enabled:!0,phase:"main",fn:ci,requiresIfExists:["offset"],data:{_skip:!1}};function Qn(e,t,o){return o===void 0&&(o={x:0,y:0}),{top:e.top-t.height-o.y,right:e.right-t.width+o.x,bottom:e.bottom-t.height+o.y,left:e.left-t.width-o.x}}function Jn(e){return[Je,lt,at,Ze].some(function(t){return e[t]>=0})}function pi(e){var t=e.state,o=e.name,n=t.rects.reference,s=t.rects.popper,i=t.modifiersData.preventOverflow,l=vo(t,{elementContext:"reference"}),a=vo(t,{altBoundary:!0}),c=Qn(l,n),h=Qn(a,s,i),p=Jn(c),v=Jn(h);t.modifiersData[o]={referenceClippingOffsets:c,popperEscapeOffsets:h,isReferenceHidden:p,hasPopperEscaped:v},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":p,"data-popper-escaped":v})}const ui={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:pi};function fi(e,t,o){var n=gt(e),s=[Ze,Je].indexOf(n)>=0?-1:1,i=typeof o=="function"?o(Object.assign({},t,{placement:e})):o,l=i[0],a=i[1];return l=l||0,a=(a||0)*s,[Ze,lt].indexOf(n)>=0?{x:a,y:l}:{x:l,y:a}}function hi(e){var t=e.state,o=e.options,n=e.name,s=o.offset,i=s===void 0?[0,0]:s,l=Fr.reduce(function(p,v){return p[v]=fi(v,t.rects,i),p},{}),a=l[t.placement],c=a.x,h=a.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=c,t.modifiersData.popperOffsets.y+=h),t.modifiersData[n]=l}const mi={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:hi};function gi(e){var t=e.state,o=e.name;t.modifiersData[o]=qr({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})}const bi={name:"popperOffsets",enabled:!0,phase:"read",fn:gi,data:{}};function vi(e){return e==="x"?"y":"x"}function xi(e){var t=e.state,o=e.options,n=e.name,s=o.mainAxis,i=s===void 0?!0:s,l=o.altAxis,a=l===void 0?!1:l,c=o.boundary,h=o.rootBoundary,p=o.altBoundary,v=o.padding,w=o.tether,g=w===void 0?!0:w,x=o.tetherOffset,j=x===void 0?0:x,y=vo(t,{boundary:c,rootBoundary:h,padding:v,altBoundary:p}),S=gt(t.placement),m=Gt(t.placement),f=!m,d=Mn(S),b=vi(d),C=t.modifiersData.popperOffsets,P=t.rects.reference,$=t.rects.popper,L=typeof j=="function"?j(Object.assign({},t.rects,{placement:t.placement})):j,O=typeof L=="number"?{mainAxis:L,altAxis:L}:Object.assign({mainAxis:0,altAxis:0},L),N=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,F={x:0,y:0};if(C){if(i){var R,E=d==="y"?Je:Ze,M=d==="y"?at:lt,k=d==="y"?"height":"width",W=C[d],H=W+y[E],A=W-y[M],_=g?-$[k]/2:0,Y=m===qt?P[k]:$[k],ee=m===qt?-$[k]:-P[k],ae=t.elements.arrow,de=g&&ae?Pn(ae):{width:0,height:0},K=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:Wr(),le=K[E],ce=K[M],T=po(0,P[k],de[k]),D=f?P[k]/2-_-T-le-O.mainAxis:Y-T-le-O.mainAxis,B=f?-P[k]/2+_+T+ce+O.mainAxis:ee+T+ce+O.mainAxis,X=t.elements.arrow&&wo(t.elements.arrow),U=X?d==="y"?X.clientTop||0:X.clientLeft||0:0,ne=(R=N==null?void 0:N[d])!=null?R:0,re=W+D-ne-U,ge=W+B-ne,Le=po(g?Wo(H,re):H,W,g?Nt(A,ge):A);C[d]=Le,F[d]=Le-W}if(a){var pe,Se=d==="x"?Je:Ze,Be=d==="x"?at:lt,Re=C[b],ve=b==="y"?"height":"width",Ne=Re+y[Se],ze=Re-y[Be],Ee=[Je,Ze].indexOf(S)!==-1,I=(pe=N==null?void 0:N[b])!=null?pe:0,z=Ee?Ne:Re-P[ve]-$[ve]-I+O.altAxis,Q=Ee?Re+P[ve]+$[ve]-I-O.altAxis:ze,se=g&&Ee?Us(z,Re,Q):po(g?z:Ne,Re,g?Q:ze);C[b]=se,F[b]=se-Re}t.modifiersData[n]=F}}const yi={name:"preventOverflow",enabled:!0,phase:"main",fn:xi,requiresIfExists:["offset"]};function wi(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function Si(e){return e===nt(e)||!it(e)?In(e):wi(e)}function Ci(e){var t=e.getBoundingClientRect(),o=Kt(t.width)/e.offsetWidth||1,n=Kt(t.height)/e.offsetHeight||1;return o!==1||n!==1}function ji(e,t,o){o===void 0&&(o=!1);var n=it(t),s=it(t)&&Ci(t),i=kt(t),l=Xt(e,s,o),a={scrollLeft:0,scrollTop:0},c={x:0,y:0};return(n||!n&&!o)&&((bt(t)!=="body"||$n(i))&&(a=Si(t)),it(t)?(c=Xt(t,!0),c.x+=t.clientLeft,c.y+=t.clientTop):i&&(c.x=On(i))),{x:l.left+a.scrollLeft-c.x,y:l.top+a.scrollTop-c.y,width:l.width,height:l.height}}function ki(e){var t=new Map,o=new Set,n=[];e.forEach(function(i){t.set(i.name,i)});function s(i){o.add(i.name);var l=[].concat(i.requires||[],i.requiresIfExists||[]);l.forEach(function(a){if(!o.has(a)){var c=t.get(a);c&&s(c)}}),n.push(i)}return e.forEach(function(i){o.has(i.name)||s(i)}),n}function Ti(e){var t=ki(e);return Ns.reduce(function(o,n){return o.concat(t.filter(function(s){return s.phase===n}))},[])}function Ri(e){var t;return function(){return t||(t=new Promise(function(o){Promise.resolve().then(function(){t=void 0,o(e())})})),t}}function Ei(e){var t=e.reduce(function(o,n){var s=o[n.name];return o[n.name]=s?Object.assign({},s,n,{options:Object.assign({},s.options,n.options),data:Object.assign({},s.data,n.data)}):n,o},{});return Object.keys(t).map(function(o){return t[o]})}var Zn={placement:"bottom",modifiers:[],strategy:"absolute"};function er(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return!t.some(function(n){return!(n&&typeof n.getBoundingClientRect=="function")})}function Pi(e){e===void 0&&(e={});var t=e,o=t.defaultModifiers,n=o===void 0?[]:o,s=t.defaultOptions,i=s===void 0?Zn:s;return function(a,c,h){h===void 0&&(h=i);var p={placement:"bottom",orderedModifiers:[],options:Object.assign({},Zn,i),modifiersData:{},elements:{reference:a,popper:c},attributes:{},styles:{}},v=[],w=!1,g={state:p,setOptions:function(S){var m=typeof S=="function"?S(p.options):S;j(),p.options=Object.assign({},i,p.options,m),p.scrollParents={reference:zt(a)?uo(a):a.contextElement?uo(a.contextElement):[],popper:uo(c)};var f=Ti(Ei([].concat(n,p.options.modifiers)));return p.orderedModifiers=f.filter(function(d){return d.enabled}),x(),g.update()},forceUpdate:function(){if(!w){var S=p.elements,m=S.reference,f=S.popper;if(er(m,f)){p.rects={reference:ji(m,wo(f),p.options.strategy==="fixed"),popper:Pn(f)},p.reset=!1,p.placement=p.options.placement,p.orderedModifiers.forEach(function(O){return p.modifiersData[O.name]=Object.assign({},O.data)});for(var d=0;d<p.orderedModifiers.length;d++){if(p.reset===!0){p.reset=!1,d=-1;continue}var b=p.orderedModifiers[d],C=b.fn,P=b.options,$=P===void 0?{}:P,L=b.name;typeof C=="function"&&(p=C({state:p,options:$,name:L,instance:g})||p)}}}},update:Ri(function(){return new Promise(function(y){g.forceUpdate(),y(p)})}),destroy:function(){j(),w=!0}};if(!er(a,c))return g;g.setOptions(h).then(function(y){!w&&h.onFirstUpdate&&h.onFirstUpdate(y)});function x(){p.orderedModifiers.forEach(function(y){var S=y.name,m=y.options,f=m===void 0?{}:m,d=y.effect;if(typeof d=="function"){var b=d({state:p,name:S,instance:g,options:f}),C=function(){};v.push(b||C)}})}function j(){v.forEach(function(y){return y()}),v=[]}return g}}var Mi=[Zs,bi,Qs,Ds,mi,di,yi,Xs,ui],Ii=Pi({defaultModifiers:Mi});function Qe(e){var v;const{elementType:t,externalSlotProps:o,ownerState:n,skipResolvingSlotProps:s=!1,...i}=e,l=s?{}:ps(o,n),{props:a,internalRef:c}=us({...i,externalSlotProps:l}),h=Ye(c,l==null?void 0:l.ref,(v=e.additionalProps)==null?void 0:v.ref);return fs(t,{...a,ref:h},n)}function Qt(e){var t;return parseInt(u.version,10)>=19?((t=e==null?void 0:e.props)==null?void 0:t.ref)||null:(e==null?void 0:e.ref)||null}function Oi(e){return typeof e=="function"?e():e}const Kr=u.forwardRef(function(t,o){const{children:n,container:s,disablePortal:i=!1}=t,[l,a]=u.useState(null),c=Ye(u.isValidElement(n)?Qt(n):null,o);if(pt(()=>{i||a(Oi(s)||document.body)},[s,i]),pt(()=>{if(l&&!i)return Vn(o,l),()=>{Vn(o,null)}},[o,l,i]),i){if(u.isValidElement(n)){const h={ref:c};return u.cloneElement(n,h)}return n}return l&&Br.createPortal(n,l)});function $i(e){return Ie("MuiPopper",e)}we("MuiPopper",["root"]);function Li(e,t){if(t==="ltr")return e;switch(e){case"bottom-end":return"bottom-start";case"bottom-start":return"bottom-end";case"top-end":return"top-start";case"top-start":return"top-end";default:return e}}function bn(e){return typeof e=="function"?e():e}function Bi(e){return e.nodeType!==void 0}const Ai=e=>{const{classes:t}=e;return Te({root:["root"]},$i,t)},Ni={},Fi=u.forwardRef(function(t,o){const{anchorEl:n,children:s,direction:i,disablePortal:l,modifiers:a,open:c,placement:h,popperOptions:p,popperRef:v,slotProps:w={},slots:g={},TransitionProps:x,ownerState:j,...y}=t,S=u.useRef(null),m=Ye(S,o),f=u.useRef(null),d=Ye(f,v),b=u.useRef(d);pt(()=>{b.current=d},[d]),u.useImperativeHandle(v,()=>f.current,[]);const C=Li(h,i),[P,$]=u.useState(C),[L,O]=u.useState(bn(n));u.useEffect(()=>{f.current&&f.current.forceUpdate()}),u.useEffect(()=>{n&&O(bn(n))},[n]),pt(()=>{if(!L||!c)return;const M=H=>{$(H.placement)};let k=[{name:"preventOverflow",options:{altBoundary:l}},{name:"flip",options:{altBoundary:l}},{name:"onUpdate",enabled:!0,phase:"afterWrite",fn:({state:H})=>{M(H)}}];a!=null&&(k=k.concat(a)),p&&p.modifiers!=null&&(k=k.concat(p.modifiers));const W=Ii(L,S.current,{placement:C,...p,modifiers:k});return b.current(W),()=>{W.destroy(),b.current(null)}},[L,l,a,c,p,C]);const N={placement:P};x!==null&&(N.TransitionProps=x);const F=Ai(t),R=g.root??"div",E=Qe({elementType:R,externalSlotProps:w.root,externalForwardedProps:y,additionalProps:{role:"tooltip",ref:m},ownerState:t,className:F.root});return r.jsx(R,{...E,children:typeof s=="function"?s(N):s})}),zi=u.forwardRef(function(t,o){const{anchorEl:n,children:s,container:i,direction:l="ltr",disablePortal:a=!1,keepMounted:c=!1,modifiers:h,open:p,placement:v="bottom",popperOptions:w=Ni,popperRef:g,style:x,transition:j=!1,slotProps:y={},slots:S={},...m}=t,[f,d]=u.useState(!0),b=()=>{d(!1)},C=()=>{d(!0)};if(!c&&!p&&(!j||f))return null;let P;if(i)P=i;else if(n){const O=bn(n);P=O&&Bi(O)?Xe(O).body:Xe(null).body}const $=!p&&c&&(!j||f)?"none":void 0,L=j?{in:p,onEnter:b,onExited:C}:void 0;return r.jsx(Kr,{disablePortal:a,container:P,children:r.jsx(Fi,{anchorEl:n,direction:l,disablePortal:a,modifiers:h,ref:o,open:j?!f:p,placement:v,popperOptions:w,popperRef:g,slotProps:y,slots:S,...m,style:{position:"fixed",top:0,left:0,display:$,...x},TransitionProps:L,children:s})})}),Di=V(zi,{name:"MuiPopper",slot:"Root"})({}),Xr=u.forwardRef(function(t,o){const n=_t(),s=$e({props:t,name:"MuiPopper"}),{anchorEl:i,component:l,components:a,componentsProps:c,container:h,disablePortal:p,keepMounted:v,modifiers:w,open:g,placement:x,popperOptions:j,popperRef:y,transition:S,slots:m,slotProps:f,...d}=s,b=(m==null?void 0:m.root)??(a==null?void 0:a.Root),C={anchorEl:i,container:h,disablePortal:p,keepMounted:v,modifiers:w,open:g,placement:x,popperOptions:j,popperRef:y,transition:S,...d};return r.jsx(Di,{as:l,direction:n?"rtl":"ltr",slots:{root:b},slotProps:f??c,...C,ref:o})});function To(e){return parseInt(e,10)||0}const Wi={shadow:{visibility:"hidden",position:"absolute",overflow:"hidden",height:0,top:0,left:0,transform:"translateZ(0)"}};function Hi(e){for(const t in e)return!1;return!0}function tr(e){return Hi(e)||e.outerHeightStyle===0&&!e.overflowing}const Ui=u.forwardRef(function(t,o){const{onChange:n,maxRows:s,minRows:i=1,style:l,value:a,...c}=t,{current:h}=u.useRef(a!=null),p=u.useRef(null),v=Ye(o,p),w=u.useRef(null),g=u.useRef(null),x=u.useCallback(()=>{const f=p.current,d=g.current;if(!f||!d)return;const C=ut(f).getComputedStyle(f);if(C.width==="0px")return{outerHeightStyle:0,overflowing:!1};d.style.width=C.width,d.value=f.value||t.placeholder||"x",d.value.slice(-1)===`
`&&(d.value+=" ");const P=C.boxSizing,$=To(C.paddingBottom)+To(C.paddingTop),L=To(C.borderBottomWidth)+To(C.borderTopWidth),O=d.scrollHeight;d.value="x";const N=d.scrollHeight;let F=O;i&&(F=Math.max(Number(i)*N,F)),s&&(F=Math.min(Number(s)*N,F)),F=Math.max(F,N);const R=F+(P==="border-box"?$+L:0),E=Math.abs(F-O)<=1;return{outerHeightStyle:R,overflowing:E}},[s,i,t.placeholder]),j=dt(()=>{const f=p.current,d=x();if(!f||!d||tr(d))return!1;const b=d.outerHeightStyle;return w.current!=null&&w.current!==b}),y=u.useCallback(()=>{const f=p.current,d=x();if(!f||!d||tr(d))return;const b=d.outerHeightStyle;w.current!==b&&(w.current=b,f.style.height=`${b}px`),f.style.overflow=d.overflowing?"hidden":""},[x]),S=u.useRef(-1);pt(()=>{const f=Go(y),d=p==null?void 0:p.current;if(!d)return;const b=ut(d);b.addEventListener("resize",f);let C;return typeof ResizeObserver<"u"&&(C=new ResizeObserver(()=>{j()&&(C.unobserve(d),cancelAnimationFrame(S.current),y(),S.current=requestAnimationFrame(()=>{C.observe(d)}))}),C.observe(d)),()=>{f.clear(),cancelAnimationFrame(S.current),b.removeEventListener("resize",f),C&&C.disconnect()}},[x,y,j]),pt(()=>{y()});const m=f=>{h||y();const d=f.target,b=d.value.length,C=d.value.endsWith(`
`),P=d.selectionStart===b;C&&P&&d.setSelectionRange(b,b),n&&n(f)};return r.jsxs(u.Fragment,{children:[r.jsx("textarea",{value:a,onChange:m,ref:v,rows:i,style:l,...c}),r.jsx("textarea",{"aria-hidden":!0,className:t.className,readOnly:!0,ref:g,tabIndex:-1,style:{...Wi.shadow,...l,paddingTop:0,paddingBottom:0}})]})});function Ft(e){return typeof e=="string"}function Dt({props:e,states:t,muiFormControl:o}){return t.reduce((n,s)=>(n[s]=e[s],o&&typeof e[s]>"u"&&(n[s]=o[s]),n),{})}const Ln=u.createContext(void 0);function Tt(){return u.useContext(Ln)}function or(e){return e!=null&&!(Array.isArray(e)&&e.length===0)}function Ho(e,t=!1){return e&&(or(e.value)&&e.value!==""||t&&or(e.defaultValue)&&e.defaultValue!=="")}function Vi(e){return e.startAdornment}function qi(e){return Ie("MuiInputBase",e)}const Yt=we("MuiInputBase",["root","formControl","focused","disabled","adornedStart","adornedEnd","error","sizeSmall","multiline","colorSecondary","fullWidth","hiddenLabel","readOnly","input","inputSizeSmall","inputMultiline","inputTypeSearch","inputAdornedStart","inputAdornedEnd","inputHiddenLabel"]);var nr;const _o=(e,t)=>{const{ownerState:o}=e;return[t.root,o.formControl&&t.formControl,o.startAdornment&&t.adornedStart,o.endAdornment&&t.adornedEnd,o.error&&t.error,o.size==="small"&&t.sizeSmall,o.multiline&&t.multiline,o.color&&t[`color${me(o.color)}`],o.fullWidth&&t.fullWidth,o.hiddenLabel&&t.hiddenLabel]},Qo=(e,t)=>{const{ownerState:o}=e;return[t.input,o.size==="small"&&t.inputSizeSmall,o.multiline&&t.inputMultiline,o.type==="search"&&t.inputTypeSearch,o.startAdornment&&t.inputAdornedStart,o.endAdornment&&t.inputAdornedEnd,o.hiddenLabel&&t.inputHiddenLabel]},Ki=e=>{const{classes:t,color:o,disabled:n,error:s,endAdornment:i,focused:l,formControl:a,fullWidth:c,hiddenLabel:h,multiline:p,readOnly:v,size:w,startAdornment:g,type:x}=e,j={root:["root",`color${me(o)}`,n&&"disabled",s&&"error",c&&"fullWidth",l&&"focused",a&&"formControl",w&&w!=="medium"&&`size${me(w)}`,p&&"multiline",g&&"adornedStart",i&&"adornedEnd",h&&"hiddenLabel",v&&"readOnly"],input:["input",n&&"disabled",x==="search"&&"inputTypeSearch",p&&"inputMultiline",w==="small"&&"inputSizeSmall",h&&"inputHiddenLabel",g&&"inputAdornedStart",i&&"inputAdornedEnd",v&&"readOnly"]};return Te(j,qi,t)},Jo=V("div",{name:"MuiInputBase",slot:"Root",overridesResolver:_o})(ye(({theme:e})=>({...e.typography.body1,color:(e.vars||e).palette.text.primary,lineHeight:"1.4375em",boxSizing:"border-box",position:"relative",cursor:"text",display:"inline-flex",alignItems:"center",[`&.${Yt.disabled}`]:{color:(e.vars||e).palette.text.disabled,cursor:"default"},variants:[{props:({ownerState:t})=>t.multiline,style:{padding:"4px 0 5px"}},{props:({ownerState:t,size:o})=>t.multiline&&o==="small",style:{paddingTop:1}},{props:({ownerState:t})=>t.fullWidth,style:{width:"100%"}}]}))),Zo=V("input",{name:"MuiInputBase",slot:"Input",overridesResolver:Qo})(ye(({theme:e})=>{const t=e.palette.mode==="light",o={color:"currentColor",...e.vars?{opacity:e.vars.opacity.inputPlaceholder}:{opacity:t?.42:.5},transition:e.transitions.create("opacity",{duration:e.transitions.duration.shorter})},n={opacity:"0 !important"},s=e.vars?{opacity:e.vars.opacity.inputPlaceholder}:{opacity:t?.42:.5};return{font:"inherit",letterSpacing:"inherit",color:"currentColor",padding:"4px 0 5px",border:0,boxSizing:"content-box",background:"none",height:"1.4375em",margin:0,WebkitTapHighlightColor:"transparent",display:"block",minWidth:0,width:"100%","&::-webkit-input-placeholder":o,"&::-moz-placeholder":o,"&::-ms-input-placeholder":o,"&:focus":{outline:0},"&:invalid":{boxShadow:"none"},"&::-webkit-search-decoration":{WebkitAppearance:"none"},[`label[data-shrink=false] + .${Yt.formControl} &`]:{"&::-webkit-input-placeholder":n,"&::-moz-placeholder":n,"&::-ms-input-placeholder":n,"&:focus::-webkit-input-placeholder":s,"&:focus::-moz-placeholder":s,"&:focus::-ms-input-placeholder":s},[`&.${Yt.disabled}`]:{opacity:1,WebkitTextFillColor:(e.vars||e).palette.text.disabled},variants:[{props:({ownerState:i})=>!i.disableInjectingGlobalStyles,style:{animationName:"mui-auto-fill-cancel",animationDuration:"10ms","&:-webkit-autofill":{animationDuration:"5000s",animationName:"mui-auto-fill"}}},{props:{size:"small"},style:{paddingTop:1}},{props:({ownerState:i})=>i.multiline,style:{height:"auto",resize:"none",padding:0,paddingTop:0}},{props:{type:"search"},style:{MozAppearance:"textfield"}}]}})),rr=hs({"@keyframes mui-auto-fill":{from:{display:"block"}},"@keyframes mui-auto-fill-cancel":{from:{display:"block"}}}),Bn=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiInputBase"}),{"aria-describedby":s,autoComplete:i,autoFocus:l,className:a,color:c,components:h={},componentsProps:p={},defaultValue:v,disabled:w,disableInjectingGlobalStyles:g,endAdornment:x,error:j,fullWidth:y=!1,id:S,inputComponent:m="input",inputProps:f={},inputRef:d,margin:b,maxRows:C,minRows:P,multiline:$=!1,name:L,onBlur:O,onChange:N,onClick:F,onFocus:R,onKeyDown:E,onKeyUp:M,placeholder:k,readOnly:W,renderSuffix:H,rows:A,size:_,slotProps:Y={},slots:ee={},startAdornment:ae,type:de="text",value:K,...le}=n,ce=f.value!=null?f.value:K,{current:T}=u.useRef(ce!=null),D=u.useRef(),B=u.useCallback(te=>{},[]),X=Ye(D,d,f.ref,B),[U,ne]=u.useState(!1),re=Tt(),ge=Dt({props:n,muiFormControl:re,states:["color","disabled","error","hiddenLabel","size","required","filled"]});ge.focused=re?re.focused:U,u.useEffect(()=>{!re&&w&&U&&(ne(!1),O&&O())},[re,w,U,O]);const Le=re&&re.onFilled,pe=re&&re.onEmpty,Se=u.useCallback(te=>{Ho(te)?Le&&Le():pe&&pe()},[Le,pe]);pt(()=>{T&&Se({value:ce})},[ce,Se,T]);const Be=te=>{R&&R(te),f.onFocus&&f.onFocus(te),re&&re.onFocus?re.onFocus(te):ne(!0)},Re=te=>{O&&O(te),f.onBlur&&f.onBlur(te),re&&re.onBlur?re.onBlur(te):ne(!1)},ve=(te,...Pe)=>{if(!T){const xe=te.target||D.current;if(xe==null)throw new Error(Lr(1));Se({value:xe.value})}f.onChange&&f.onChange(te,...Pe),N&&N(te,...Pe)};u.useEffect(()=>{Se(D.current)},[]);const Ne=te=>{D.current&&te.currentTarget===te.target&&D.current.focus(),F&&F(te)};let ze=m,Ee=f;$&&ze==="input"&&(A?Ee={type:void 0,minRows:A,maxRows:A,...Ee}:Ee={type:void 0,maxRows:C,minRows:P,...Ee},ze=Ui);const I=te=>{Se(te.animationName==="mui-auto-fill-cancel"?D.current:{value:"x"})};u.useEffect(()=>{re&&re.setAdornedStart(!!ae)},[re,ae]);const z={...n,color:ge.color||"primary",disabled:ge.disabled,endAdornment:x,error:ge.error,focused:ge.focused,formControl:re,fullWidth:y,hiddenLabel:ge.hiddenLabel,multiline:$,size:ge.size,startAdornment:ae,type:de},Q=Ki(z),se=ee.root||h.Root||Jo,fe=Y.root||p.root||{},J=ee.input||h.Input||Zo;return Ee={...Ee,...Y.input??p.input},r.jsxs(u.Fragment,{children:[!g&&typeof rr=="function"&&(nr||(nr=r.jsx(rr,{}))),r.jsxs(se,{...fe,ref:o,onClick:Ne,...le,...!Ft(se)&&{ownerState:{...z,...fe.ownerState}},className:ue(Q.root,fe.className,a,W&&"MuiInputBase-readOnly"),children:[ae,r.jsx(Ln.Provider,{value:null,children:r.jsx(J,{"aria-invalid":ge.error,"aria-describedby":s,autoComplete:i,autoFocus:l,defaultValue:v,disabled:ge.disabled,id:S,onAnimationStart:I,name:L,placeholder:k,readOnly:W,required:ge.required,rows:A,value:ce,onKeyDown:E,onKeyUp:M,type:de,...Ee,...!Ft(J)&&{as:ze,ownerState:{...z,...Ee.ownerState}},ref:X,className:ue(Q.input,Ee.className,W&&"MuiInputBase-readOnly"),onBlur:Re,onChange:ve,onFocus:Be})}),x,H?H({...ge,startAdornment:ae}):null]})]})});function Xi(e){return Ie("MuiInput",e)}const oo={...Yt,...we("MuiInput",["root","underline","input"])};function Gi(e){return Ie("MuiOutlinedInput",e)}const ht={...Yt,...we("MuiOutlinedInput",["root","notchedOutline","input"])};function Yi(e){return Ie("MuiFilledInput",e)}const It={...Yt,...we("MuiFilledInput",["root","underline","input","adornedStart","adornedEnd","sizeSmall","multiline","hiddenLabel"])},_i=Ke(r.jsx("path",{d:"M7 10l5 5 5-5z"})),Qi={entering:{opacity:1},entered:{opacity:1}},vn=u.forwardRef(function(t,o){const n=xo(),s={enter:n.transitions.duration.enteringScreen,exit:n.transitions.duration.leavingScreen},{addEndListener:i,appear:l=!0,children:a,easing:c,in:h,onEnter:p,onEntered:v,onEntering:w,onExit:g,onExited:x,onExiting:j,style:y,timeout:S=s,TransitionComponent:m=vt,...f}=t,d=u.useRef(null),b=Ye(d,Qt(a),o),C=E=>M=>{if(E){const k=d.current;M===void 0?E(k):E(k,M)}},P=C(w),$=C((E,M)=>{Ar(E);const k=Do({style:y,timeout:S,easing:c},{mode:"enter"});E.style.webkitTransition=n.transitions.create("opacity",k),E.style.transition=n.transitions.create("opacity",k),p&&p(E,M)}),L=C(v),O=C(j),N=C(E=>{const M=Do({style:y,timeout:S,easing:c},{mode:"exit"});E.style.webkitTransition=n.transitions.create("opacity",M),E.style.transition=n.transitions.create("opacity",M),g&&g(E)}),F=C(x),R=E=>{i&&i(d.current,E)};return r.jsx(m,{appear:l,in:h,nodeRef:d,onEnter:$,onEntered:L,onEntering:P,onExit:N,onExited:F,onExiting:O,addEndListener:R,timeout:S,...f,children:(E,{ownerState:M,...k})=>u.cloneElement(a,{style:{opacity:0,visibility:E==="exited"&&!h?"hidden":void 0,...Qi[E],...y,...a.props.style},ref:b,...k})})});function Ji(e){return Ie("MuiBackdrop",e)}we("MuiBackdrop",["root","invisible"]);const Zi=e=>{const{classes:t,invisible:o}=e;return Te({root:["root",o&&"invisible"]},Ji,t)},ea=V("div",{name:"MuiBackdrop",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,o.invisible&&t.invisible]}})({position:"fixed",display:"flex",alignItems:"center",justifyContent:"center",right:0,bottom:0,top:0,left:0,backgroundColor:"rgba(0, 0, 0, 0.5)",WebkitTapHighlightColor:"transparent",variants:[{props:{invisible:!0},style:{backgroundColor:"transparent"}}]}),Gr=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiBackdrop"}),{children:s,className:i,component:l="div",invisible:a=!1,open:c,components:h={},componentsProps:p={},slotProps:v={},slots:w={},TransitionComponent:g,transitionDuration:x,...j}=n,y={...n,component:l,invisible:a},S=Zi(y),m={transition:g,root:h.Root,...w},f={...p,...v},d={slots:m,slotProps:f},[b,C]=be("root",{elementType:ea,externalForwardedProps:d,className:ue(S.root,i),ownerState:y}),[P,$]=be("transition",{elementType:vn,externalForwardedProps:d,ownerState:y});return r.jsx(P,{in:c,timeout:x,...j,...$,children:r.jsx(b,{"aria-hidden":!0,...C,classes:S,ref:o,children:s})})});function ta(e){return Ie("PrivateSwitchBase",e)}we("PrivateSwitchBase",["root","checked","disabled","input","edgeStart","edgeEnd"]);const oa=e=>{const{classes:t,checked:o,disabled:n,edge:s}=e,i={root:["root",o&&"checked",n&&"disabled",s&&`edge${me(s)}`],input:["input"]};return Te(i,ta,t)},na=V(Ko)({padding:9,borderRadius:"50%",variants:[{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:({edge:e,ownerState:t})=>e==="start"&&t.size!=="small",style:{marginLeft:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}},{props:({edge:e,ownerState:t})=>e==="end"&&t.size!=="small",style:{marginRight:-12}}]}),ra=V("input",{shouldForwardProp:ct})({cursor:"inherit",position:"absolute",opacity:0,width:"100%",height:"100%",top:0,left:0,margin:0,padding:0,zIndex:1}),sa=u.forwardRef(function(t,o){const{autoFocus:n,checked:s,checkedIcon:i,defaultChecked:l,disabled:a,disableFocusRipple:c=!1,edge:h=!1,icon:p,id:v,inputProps:w,inputRef:g,name:x,onBlur:j,onChange:y,onFocus:S,readOnly:m,required:f=!1,tabIndex:d,type:b,value:C,slots:P={},slotProps:$={},...L}=t,[O,N]=go({controlled:s,default:!!l,name:"SwitchBase",state:"checked"}),F=Tt(),R=K=>{S&&S(K),F&&F.onFocus&&F.onFocus(K)},E=K=>{j&&j(K),F&&F.onBlur&&F.onBlur(K)},M=K=>{if(K.nativeEvent.defaultPrevented)return;const le=K.target.checked;N(le),y&&y(K,le)};let k=a;F&&typeof k>"u"&&(k=F.disabled);const W=b==="checkbox"||b==="radio",H={...t,checked:O,disabled:k,disableFocusRipple:c,edge:h},A=oa(H),_={slots:P,slotProps:{input:w,...$}},[Y,ee]=be("root",{ref:o,elementType:na,className:A.root,shouldForwardComponentProp:!0,externalForwardedProps:{..._,component:"span",...L},getSlotProps:K=>({...K,onFocus:le=>{var ce;(ce=K.onFocus)==null||ce.call(K,le),R(le)},onBlur:le=>{var ce;(ce=K.onBlur)==null||ce.call(K,le),E(le)}}),ownerState:H,additionalProps:{centerRipple:!0,focusRipple:!c,disabled:k,role:void 0,tabIndex:null}}),[ae,de]=be("input",{ref:g,elementType:ra,className:A.input,externalForwardedProps:_,getSlotProps:K=>({...K,onChange:le=>{var ce;(ce=K.onChange)==null||ce.call(K,le),M(le)}}),ownerState:H,additionalProps:{autoFocus:n,checked:s,defaultChecked:l,disabled:k,id:W?v:void 0,name:x,readOnly:m,required:f,tabIndex:d,type:b,...b==="checkbox"&&C===void 0?{}:{value:C}}});return r.jsxs(Y,{...ee,children:[r.jsx(ae,{...de}),O?i:p]})});function Yr(e=window){const t=e.document.documentElement.clientWidth;return e.innerWidth-t}function ia(e){const t=Xe(e);return t.body===e?ut(e).innerWidth>t.documentElement.clientWidth:e.scrollHeight>e.clientHeight}function fo(e,t){t?e.setAttribute("aria-hidden","true"):e.removeAttribute("aria-hidden")}function sr(e){return parseInt(ut(e).getComputedStyle(e).paddingRight,10)||0}function aa(e){const o=["TEMPLATE","SCRIPT","STYLE","LINK","MAP","META","NOSCRIPT","PICTURE","COL","COLGROUP","PARAM","SLOT","SOURCE","TRACK"].includes(e.tagName),n=e.tagName==="INPUT"&&e.getAttribute("type")==="hidden";return o||n}function ir(e,t,o,n,s){const i=[t,o,...n];[].forEach.call(e.children,l=>{const a=!i.includes(l),c=!aa(l);a&&c&&fo(l,s)})}function nn(e,t){let o=-1;return e.some((n,s)=>t(n)?(o=s,!0):!1),o}function la(e,t){const o=[],n=e.container;if(!t.disableScrollLock){if(ia(n)){const l=Yr(ut(n));o.push({value:n.style.paddingRight,property:"padding-right",el:n}),n.style.paddingRight=`${sr(n)+l}px`;const a=Xe(n).querySelectorAll(".mui-fixed");[].forEach.call(a,c=>{o.push({value:c.style.paddingRight,property:"padding-right",el:c}),c.style.paddingRight=`${sr(c)+l}px`})}let i;if(n.parentNode instanceof DocumentFragment)i=Xe(n).body;else{const l=n.parentElement,a=ut(n);i=(l==null?void 0:l.nodeName)==="HTML"&&a.getComputedStyle(l).overflowY==="scroll"?l:n}o.push({value:i.style.overflow,property:"overflow",el:i},{value:i.style.overflowX,property:"overflow-x",el:i},{value:i.style.overflowY,property:"overflow-y",el:i}),i.style.overflow="hidden"}return()=>{o.forEach(({value:i,el:l,property:a})=>{i?l.style.setProperty(a,i):l.style.removeProperty(a)})}}function ca(e){const t=[];return[].forEach.call(e.children,o=>{o.getAttribute("aria-hidden")==="true"&&t.push(o)}),t}class da{constructor(){this.modals=[],this.containers=[]}add(t,o){let n=this.modals.indexOf(t);if(n!==-1)return n;n=this.modals.length,this.modals.push(t),t.modalRef&&fo(t.modalRef,!1);const s=ca(o);ir(o,t.mount,t.modalRef,s,!0);const i=nn(this.containers,l=>l.container===o);return i!==-1?(this.containers[i].modals.push(t),n):(this.containers.push({modals:[t],container:o,restore:null,hiddenSiblings:s}),n)}mount(t,o){const n=nn(this.containers,i=>i.modals.includes(t)),s=this.containers[n];s.restore||(s.restore=la(s,o))}remove(t,o=!0){const n=this.modals.indexOf(t);if(n===-1)return n;const s=nn(this.containers,l=>l.modals.includes(t)),i=this.containers[s];if(i.modals.splice(i.modals.indexOf(t),1),this.modals.splice(n,1),i.modals.length===0)i.restore&&i.restore(),t.modalRef&&fo(t.modalRef,o),ir(i.container,t.mount,t.modalRef,i.hiddenSiblings,!1),this.containers.splice(s,1);else{const l=i.modals[i.modals.length-1];l.modalRef&&fo(l.modalRef,!1)}return n}isTopModal(t){return this.modals.length>0&&this.modals[this.modals.length-1]===t}}const pa=["input","select","textarea","a[href]","button","[tabindex]","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])'].join(",");function ua(e){const t=parseInt(e.getAttribute("tabindex")||"",10);return Number.isNaN(t)?e.contentEditable==="true"||(e.nodeName==="AUDIO"||e.nodeName==="VIDEO"||e.nodeName==="DETAILS")&&e.getAttribute("tabindex")===null?0:e.tabIndex:t}function fa(e){if(e.tagName!=="INPUT"||e.type!=="radio"||!e.name)return!1;const t=n=>e.ownerDocument.querySelector(`input[type="radio"]${n}`);let o=t(`[name="${e.name}"]:checked`);return o||(o=t(`[name="${e.name}"]`)),o!==e}function ha(e){return!(e.disabled||e.tagName==="INPUT"&&e.type==="hidden"||fa(e))}function ma(e){const t=[],o=[];return Array.from(e.querySelectorAll(pa)).forEach((n,s)=>{const i=ua(n);i===-1||!ha(n)||(i===0?t.push(n):o.push({documentOrder:s,tabIndex:i,node:n}))}),o.sort((n,s)=>n.tabIndex===s.tabIndex?n.documentOrder-s.documentOrder:n.tabIndex-s.tabIndex).map(n=>n.node).concat(t)}function ga(){return!0}function ba(e){const{children:t,disableAutoFocus:o=!1,disableEnforceFocus:n=!1,disableRestoreFocus:s=!1,getTabbable:i=ma,isEnabled:l=ga,open:a}=e,c=u.useRef(!1),h=u.useRef(null),p=u.useRef(null),v=u.useRef(null),w=u.useRef(null),g=u.useRef(!1),x=u.useRef(null),j=Ye(Qt(t),x),y=u.useRef(null);u.useEffect(()=>{!a||!x.current||(g.current=!o)},[o,a]),u.useEffect(()=>{if(!a||!x.current)return;const f=Xe(x.current);return x.current.contains(f.activeElement)||(x.current.hasAttribute("tabIndex")||x.current.setAttribute("tabIndex","-1"),g.current&&x.current.focus()),()=>{s||(v.current&&v.current.focus&&(c.current=!0,v.current.focus()),v.current=null)}},[a]),u.useEffect(()=>{if(!a||!x.current)return;const f=Xe(x.current),d=P=>{y.current=P,!(n||!l()||P.key!=="Tab")&&f.activeElement===x.current&&P.shiftKey&&(c.current=!0,p.current&&p.current.focus())},b=()=>{var L,O;const P=x.current;if(P===null)return;if(!f.hasFocus()||!l()||c.current){c.current=!1;return}if(P.contains(f.activeElement)||n&&f.activeElement!==h.current&&f.activeElement!==p.current)return;if(f.activeElement!==w.current)w.current=null;else if(w.current!==null)return;if(!g.current)return;let $=[];if((f.activeElement===h.current||f.activeElement===p.current)&&($=i(x.current)),$.length>0){const N=!!((L=y.current)!=null&&L.shiftKey&&((O=y.current)==null?void 0:O.key)==="Tab"),F=$[0],R=$[$.length-1];typeof F!="string"&&typeof R!="string"&&(N?R.focus():F.focus())}else P.focus()};f.addEventListener("focusin",b),f.addEventListener("keydown",d,!0);const C=setInterval(()=>{f.activeElement&&f.activeElement.tagName==="BODY"&&b()},50);return()=>{clearInterval(C),f.removeEventListener("focusin",b),f.removeEventListener("keydown",d,!0)}},[o,n,s,l,a,i]);const S=f=>{v.current===null&&(v.current=f.relatedTarget),g.current=!0,w.current=f.target;const d=t.props.onFocus;d&&d(f)},m=f=>{v.current===null&&(v.current=f.relatedTarget),g.current=!0};return r.jsxs(u.Fragment,{children:[r.jsx("div",{tabIndex:a?0:-1,onFocus:m,ref:h,"data-testid":"sentinelStart"}),u.cloneElement(t,{ref:j,onFocus:S}),r.jsx("div",{tabIndex:a?0:-1,onFocus:m,ref:p,"data-testid":"sentinelEnd"})]})}function va(e){return typeof e=="function"?e():e}function xa(e){return e?e.props.hasOwnProperty("in"):!1}const ar=()=>{},Ro=new da;function ya(e){const{container:t,disableEscapeKeyDown:o=!1,disableScrollLock:n=!1,closeAfterTransition:s=!1,onTransitionEnter:i,onTransitionExited:l,children:a,onClose:c,open:h,rootRef:p}=e,v=u.useRef({}),w=u.useRef(null),g=u.useRef(null),x=Ye(g,p),[j,y]=u.useState(!h),S=xa(a);let m=!0;(e["aria-hidden"]==="false"||e["aria-hidden"]===!1)&&(m=!1);const f=()=>Xe(w.current),d=()=>(v.current.modalRef=g.current,v.current.mount=w.current,v.current),b=()=>{Ro.mount(d(),{disableScrollLock:n}),g.current&&(g.current.scrollTop=0)},C=dt(()=>{const M=va(t)||f().body;Ro.add(d(),M),g.current&&b()}),P=()=>Ro.isTopModal(d()),$=dt(M=>{w.current=M,M&&(h&&P()?b():g.current&&fo(g.current,m))}),L=u.useCallback(()=>{Ro.remove(d(),m)},[m]);u.useEffect(()=>()=>{L()},[L]),u.useEffect(()=>{h?C():(!S||!s)&&L()},[h,L,S,s,C]);const O=M=>k=>{var W;(W=M.onKeyDown)==null||W.call(M,k),!(k.key!=="Escape"||k.which===229||!P())&&(o||(k.stopPropagation(),c&&c(k,"escapeKeyDown")))},N=M=>k=>{var W;(W=M.onClick)==null||W.call(M,k),k.target===k.currentTarget&&c&&c(k,"backdropClick")};return{getRootProps:(M={})=>{const k=No(e);delete k.onTransitionEnter,delete k.onTransitionExited;const W={...k,...M};return{role:"presentation",...W,onKeyDown:O(W),ref:x}},getBackdropProps:(M={})=>{const k=M;return{"aria-hidden":!0,...k,onClick:N(k),open:h}},getTransitionProps:()=>{const M=()=>{y(!1),i&&i()},k=()=>{y(!0),l&&l(),s&&L()};return{onEnter:Un(M,(a==null?void 0:a.props.onEnter)??ar),onExited:Un(k,(a==null?void 0:a.props.onExited)??ar)}},rootRef:x,portalRef:$,isTopModal:P,exited:j,hasTransition:S}}function wa(e){return Ie("MuiModal",e)}we("MuiModal",["root","hidden","backdrop"]);const Sa=e=>{const{open:t,exited:o,classes:n}=e;return Te({root:["root",!t&&o&&"hidden"],backdrop:["backdrop"]},wa,n)},Ca=V("div",{name:"MuiModal",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,!o.open&&o.exited&&t.hidden]}})(ye(({theme:e})=>({position:"fixed",zIndex:(e.vars||e).zIndex.modal,right:0,bottom:0,top:0,left:0,variants:[{props:({ownerState:t})=>!t.open&&t.exited,style:{visibility:"hidden"}}]}))),ja=V(Gr,{name:"MuiModal",slot:"Backdrop"})({zIndex:-1}),_r=u.forwardRef(function(t,o){const n=$e({name:"MuiModal",props:t}),{BackdropComponent:s=ja,BackdropProps:i,classes:l,className:a,closeAfterTransition:c=!1,children:h,container:p,component:v,components:w={},componentsProps:g={},disableAutoFocus:x=!1,disableEnforceFocus:j=!1,disableEscapeKeyDown:y=!1,disablePortal:S=!1,disableRestoreFocus:m=!1,disableScrollLock:f=!1,hideBackdrop:d=!1,keepMounted:b=!1,onClose:C,onTransitionEnter:P,onTransitionExited:$,open:L,slotProps:O={},slots:N={},theme:F,...R}=n,E={...n,closeAfterTransition:c,disableAutoFocus:x,disableEnforceFocus:j,disableEscapeKeyDown:y,disablePortal:S,disableRestoreFocus:m,disableScrollLock:f,hideBackdrop:d,keepMounted:b},{getRootProps:M,getBackdropProps:k,getTransitionProps:W,portalRef:H,isTopModal:A,exited:_,hasTransition:Y}=ya({...E,rootRef:o}),ee={...E,exited:_},ae=Sa(ee),de={};if(h.props.tabIndex===void 0&&(de.tabIndex="-1"),Y){const{onEnter:B,onExited:X}=W();de.onEnter=B,de.onExited=X}const K={slots:{root:w.Root,backdrop:w.Backdrop,...N},slotProps:{...g,...O}},[le,ce]=be("root",{ref:o,elementType:Ca,externalForwardedProps:{...K,...R,component:v},getSlotProps:M,ownerState:ee,className:ue(a,ae==null?void 0:ae.root,!ee.open&&ee.exited&&(ae==null?void 0:ae.hidden))}),[T,D]=be("backdrop",{ref:i==null?void 0:i.ref,elementType:s,externalForwardedProps:K,shouldForwardComponentProp:!0,additionalProps:i,getSlotProps:B=>k({...B,onClick:X=>{B!=null&&B.onClick&&B.onClick(X)}}),className:ue(i==null?void 0:i.className,ae==null?void 0:ae.backdrop),ownerState:ee});return!b&&!L&&(!Y||_)?null:r.jsx(Kr,{ref:H,container:p,disablePortal:S,children:r.jsxs(le,{...ce,children:[!d&&s?r.jsx(T,{...D}):null,r.jsx(ba,{disableEnforceFocus:j,disableAutoFocus:x,disableRestoreFocus:m,isEnabled:A,open:L,children:u.cloneElement(h,de)})]})})});function ka(e){return Ie("MuiDialog",e)}const rn=we("MuiDialog",["root","scrollPaper","scrollBody","container","paper","paperScrollPaper","paperScrollBody","paperWidthFalse","paperWidthXs","paperWidthSm","paperWidthMd","paperWidthLg","paperWidthXl","paperFullWidth","paperFullScreen"]),Qr=u.createContext({}),Ta=V(Gr,{name:"MuiDialog",slot:"Backdrop",overrides:(e,t)=>t.backdrop})({zIndex:-1}),Ra=e=>{const{classes:t,scroll:o,maxWidth:n,fullWidth:s,fullScreen:i}=e,l={root:["root"],container:["container",`scroll${me(o)}`],paper:["paper",`paperScroll${me(o)}`,`paperWidth${me(String(n))}`,s&&"paperFullWidth",i&&"paperFullScreen"]};return Te(l,ka,t)},Ea=V(_r,{name:"MuiDialog",slot:"Root"})({"@media print":{position:"absolute !important"}}),Pa=V("div",{name:"MuiDialog",slot:"Container",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.container,t[`scroll${me(o.scroll)}`]]}})({height:"100%","@media print":{height:"auto"},outline:0,variants:[{props:{scroll:"paper"},style:{display:"flex",justifyContent:"center",alignItems:"center"}},{props:{scroll:"body"},style:{overflowY:"auto",overflowX:"hidden",textAlign:"center","&::after":{content:'""',display:"inline-block",verticalAlign:"middle",height:"100%",width:"0"}}}]}),Ma=V(Vt,{name:"MuiDialog",slot:"Paper",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.paper,t[`scrollPaper${me(o.scroll)}`],t[`paperWidth${me(String(o.maxWidth))}`],o.fullWidth&&t.paperFullWidth,o.fullScreen&&t.paperFullScreen]}})(ye(({theme:e})=>({margin:32,position:"relative",overflowY:"auto","@media print":{overflowY:"visible",boxShadow:"none"},variants:[{props:{scroll:"paper"},style:{display:"flex",flexDirection:"column",maxHeight:"calc(100% - 64px)"}},{props:{scroll:"body"},style:{display:"inline-block",verticalAlign:"middle",textAlign:"initial"}},{props:({ownerState:t})=>!t.maxWidth,style:{maxWidth:"calc(100% - 64px)"}},{props:{maxWidth:"xs"},style:{maxWidth:e.breakpoints.unit==="px"?Math.max(e.breakpoints.values.xs,444):`max(${e.breakpoints.values.xs}${e.breakpoints.unit}, 444px)`,[`&.${rn.paperScrollBody}`]:{[e.breakpoints.down(Math.max(e.breakpoints.values.xs,444)+32*2)]:{maxWidth:"calc(100% - 64px)"}}}},...Object.keys(e.breakpoints.values).filter(t=>t!=="xs").map(t=>({props:{maxWidth:t},style:{maxWidth:`${e.breakpoints.values[t]}${e.breakpoints.unit}`,[`&.${rn.paperScrollBody}`]:{[e.breakpoints.down(e.breakpoints.values[t]+32*2)]:{maxWidth:"calc(100% - 64px)"}}}})),{props:({ownerState:t})=>t.fullWidth,style:{width:"calc(100% - 64px)"}},{props:({ownerState:t})=>t.fullScreen,style:{margin:0,width:"100%",maxWidth:"100%",height:"100%",maxHeight:"none",borderRadius:0,[`&.${rn.paperScrollBody}`]:{margin:0,maxWidth:"100%"}}}]}))),xn=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiDialog"}),s=xo(),i={enter:s.transitions.duration.enteringScreen,exit:s.transitions.duration.leavingScreen},{"aria-describedby":l,"aria-labelledby":a,"aria-modal":c=!0,BackdropComponent:h,BackdropProps:p,children:v,className:w,disableEscapeKeyDown:g=!1,fullScreen:x=!1,fullWidth:j=!1,maxWidth:y="sm",onClick:S,onClose:m,open:f,PaperComponent:d=Vt,PaperProps:b={},scroll:C="paper",slots:P={},slotProps:$={},TransitionComponent:L=vn,transitionDuration:O=i,TransitionProps:N,...F}=n,R={...n,disableEscapeKeyDown:g,fullScreen:x,fullWidth:j,maxWidth:y,scroll:C},E=Ra(R),M=u.useRef(),k=ne=>{M.current=ne.target===ne.currentTarget},W=ne=>{S&&S(ne),M.current&&(M.current=null,m&&m(ne,"backdropClick"))},H=Xo(a),A=u.useMemo(()=>({titleId:H}),[H]),_={transition:L,...P},Y={transition:N,paper:b,backdrop:p,...$},ee={slots:_,slotProps:Y},[ae,de]=be("root",{elementType:Ea,shouldForwardComponentProp:!0,externalForwardedProps:ee,ownerState:R,className:ue(E.root,w),ref:o}),[K,le]=be("backdrop",{elementType:Ta,shouldForwardComponentProp:!0,externalForwardedProps:ee,ownerState:R}),[ce,T]=be("paper",{elementType:Ma,shouldForwardComponentProp:!0,externalForwardedProps:ee,ownerState:R,className:ue(E.paper,b.className)}),[D,B]=be("container",{elementType:Pa,externalForwardedProps:ee,ownerState:R,className:E.container}),[X,U]=be("transition",{elementType:vn,externalForwardedProps:ee,ownerState:R,additionalProps:{appear:!0,in:f,timeout:O,role:"presentation"}});return r.jsx(ae,{closeAfterTransition:!0,slots:{backdrop:K},slotProps:{backdrop:{transitionDuration:O,as:h,...le}},disableEscapeKeyDown:g,onClose:m,open:f,onClick:W,...de,...F,children:r.jsx(X,{...U,children:r.jsx(D,{onMouseDown:k,...B,children:r.jsx(ce,{as:d,elevation:24,role:"dialog","aria-describedby":l,"aria-labelledby":H,"aria-modal":c,...T,children:r.jsx(Qr.Provider,{value:A,children:v})})})})})});function Ia(e){return Ie("MuiDialogActions",e)}we("MuiDialogActions",["root","spacing"]);const Oa=e=>{const{classes:t,disableSpacing:o}=e;return Te({root:["root",!o&&"spacing"]},Ia,t)},$a=V("div",{name:"MuiDialogActions",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,!o.disableSpacing&&t.spacing]}})({display:"flex",alignItems:"center",padding:8,justifyContent:"flex-end",flex:"0 0 auto",variants:[{props:({ownerState:e})=>!e.disableSpacing,style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),yn=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiDialogActions"}),{className:s,disableSpacing:i=!1,...l}=n,a={...n,disableSpacing:i},c=Oa(a);return r.jsx($a,{className:ue(c.root,s),ownerState:a,ref:o,...l})});function La(e){return Ie("MuiDialogContent",e)}we("MuiDialogContent",["root","dividers"]);function Ba(e){return Ie("MuiDialogTitle",e)}const Aa=we("MuiDialogTitle",["root"]),Na=e=>{const{classes:t,dividers:o}=e;return Te({root:["root",o&&"dividers"]},La,t)},Fa=V("div",{name:"MuiDialogContent",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,o.dividers&&t.dividers]}})(ye(({theme:e})=>({flex:"1 1 auto",WebkitOverflowScrolling:"touch",overflowY:"auto",padding:"20px 24px",variants:[{props:({ownerState:t})=>t.dividers,style:{padding:"16px 24px",borderTop:`1px solid ${(e.vars||e).palette.divider}`,borderBottom:`1px solid ${(e.vars||e).palette.divider}`}},{props:({ownerState:t})=>!t.dividers,style:{[`.${Aa.root} + &`]:{paddingTop:0}}}]}))),wn=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiDialogContent"}),{className:s,dividers:i=!1,...l}=n,a={...n,dividers:i},c=Na(a);return r.jsx(Fa,{className:ue(c.root,s),ownerState:a,ref:o,...l})});function za(e){return Ie("MuiDialogContentText",e)}we("MuiDialogContentText",["root"]);const Da=e=>{const{classes:t}=e,n=Te({root:["root"]},za,t);return{...t,...n}},Wa=V(q,{shouldForwardProp:e=>ct(e)||e==="classes",name:"MuiDialogContentText",slot:"Root"})({}),lr=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiDialogContentText"}),{children:s,className:i,...l}=n,a=Da(l);return r.jsx(Wa,{component:"p",variant:"body1",color:"textSecondary",ref:o,ownerState:l,className:ue(a.root,i),...n,classes:a})}),Ha=e=>{const{classes:t}=e;return Te({root:["root"]},Ba,t)},Ua=V(q,{name:"MuiDialogTitle",slot:"Root"})({padding:"16px 24px",flex:"0 0 auto"}),Sn=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiDialogTitle"}),{className:s,id:i,...l}=n,a=n,c=Ha(a),{titleId:h=i}=u.useContext(Qr);return r.jsx(Ua,{component:"h2",className:ue(c.root,s),ownerState:a,ref:o,variant:"h6",id:i??h,...l})}),cr=we("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"]),Va=e=>{const{classes:t,disableUnderline:o,startAdornment:n,endAdornment:s,size:i,hiddenLabel:l,multiline:a}=e,c={root:["root",!o&&"underline",n&&"adornedStart",s&&"adornedEnd",i==="small"&&`size${me(i)}`,l&&"hiddenLabel",a&&"multiline"],input:["input"]},h=Te(c,Yi,t);return{...t,...h}},qa=V(Jo,{shouldForwardProp:e=>ct(e)||e==="classes",name:"MuiFilledInput",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[..._o(e,t),!o.disableUnderline&&t.underline]}})(ye(({theme:e})=>{const t=e.palette.mode==="light",o=t?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)",n=t?"rgba(0, 0, 0, 0.06)":"rgba(255, 255, 255, 0.09)",s=t?"rgba(0, 0, 0, 0.09)":"rgba(255, 255, 255, 0.13)",i=t?"rgba(0, 0, 0, 0.12)":"rgba(255, 255, 255, 0.12)";return{position:"relative",backgroundColor:e.vars?e.vars.palette.FilledInput.bg:n,borderTopLeftRadius:(e.vars||e).shape.borderRadius,borderTopRightRadius:(e.vars||e).shape.borderRadius,transition:e.transitions.create("background-color",{duration:e.transitions.duration.shorter,easing:e.transitions.easing.easeOut}),"&:hover":{backgroundColor:e.vars?e.vars.palette.FilledInput.hoverBg:s,"@media (hover: none)":{backgroundColor:e.vars?e.vars.palette.FilledInput.bg:n}},[`&.${It.focused}`]:{backgroundColor:e.vars?e.vars.palette.FilledInput.bg:n},[`&.${It.disabled}`]:{backgroundColor:e.vars?e.vars.palette.FilledInput.disabledBg:i},variants:[{props:({ownerState:l})=>!l.disableUnderline,style:{"&::after":{left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:e.transitions.create("transform",{duration:e.transitions.duration.shorter,easing:e.transitions.easing.easeOut}),pointerEvents:"none"},[`&.${It.focused}:after`]:{transform:"scaleX(1) translateX(0)"},[`&.${It.error}`]:{"&::before, &::after":{borderBottomColor:(e.vars||e).palette.error.main}},"&::before":{borderBottom:`1px solid ${e.vars?`rgba(${e.vars.palette.common.onBackgroundChannel} / ${e.vars.opacity.inputUnderline})`:o}`,left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:e.transitions.create("border-bottom-color",{duration:e.transitions.duration.shorter}),pointerEvents:"none"},[`&:hover:not(.${It.disabled}, .${It.error}):before`]:{borderBottom:`1px solid ${(e.vars||e).palette.text.primary}`},[`&.${It.disabled}:before`]:{borderBottomStyle:"dotted"}}},...Object.entries(e.palette).filter(jt()).map(([l])=>{var a;return{props:{disableUnderline:!1,color:l},style:{"&::after":{borderBottom:`2px solid ${(a=(e.vars||e).palette[l])==null?void 0:a.main}`}}}}),{props:({ownerState:l})=>l.startAdornment,style:{paddingLeft:12}},{props:({ownerState:l})=>l.endAdornment,style:{paddingRight:12}},{props:({ownerState:l})=>l.multiline,style:{padding:"25px 12px 8px"}},{props:({ownerState:l,size:a})=>l.multiline&&a==="small",style:{paddingTop:21,paddingBottom:4}},{props:({ownerState:l})=>l.multiline&&l.hiddenLabel,style:{paddingTop:16,paddingBottom:17}},{props:({ownerState:l})=>l.multiline&&l.hiddenLabel&&l.size==="small",style:{paddingTop:8,paddingBottom:9}}]}})),Ka=V(Zo,{name:"MuiFilledInput",slot:"Input",overridesResolver:Qo})(ye(({theme:e})=>({paddingTop:25,paddingRight:12,paddingBottom:8,paddingLeft:12,...!e.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:e.palette.mode==="light"?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:e.palette.mode==="light"?null:"#fff",caretColor:e.palette.mode==="light"?null:"#fff",borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"}},...e.vars&&{"&:-webkit-autofill":{borderTopLeftRadius:"inherit",borderTopRightRadius:"inherit"},[e.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}},variants:[{props:{size:"small"},style:{paddingTop:21,paddingBottom:4}},{props:({ownerState:t})=>t.hiddenLabel,style:{paddingTop:16,paddingBottom:17}},{props:({ownerState:t})=>t.startAdornment,style:{paddingLeft:0}},{props:({ownerState:t})=>t.endAdornment,style:{paddingRight:0}},{props:({ownerState:t})=>t.hiddenLabel&&t.size==="small",style:{paddingTop:8,paddingBottom:9}},{props:({ownerState:t})=>t.multiline,style:{paddingTop:0,paddingBottom:0,paddingLeft:0,paddingRight:0}}]}))),An=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiFilledInput"}),{disableUnderline:s=!1,components:i={},componentsProps:l,fullWidth:a=!1,hiddenLabel:c,inputComponent:h="input",multiline:p=!1,slotProps:v,slots:w={},type:g="text",...x}=n,j={...n,disableUnderline:s,fullWidth:a,inputComponent:h,multiline:p,type:g},y=Va(n),S={root:{ownerState:j},input:{ownerState:j}},m=v??l?kn(S,v??l):S,f=w.root??i.Root??qa,d=w.input??i.Input??Ka;return r.jsx(Bn,{slots:{root:f,input:d},slotProps:m,fullWidth:a,inputComponent:h,multiline:p,ref:o,type:g,...x,classes:y})});An.muiName="Input";function Xa(e){return Ie("MuiFormControl",e)}we("MuiFormControl",["root","marginNone","marginNormal","marginDense","fullWidth","disabled"]);const Ga=e=>{const{classes:t,margin:o,fullWidth:n}=e,s={root:["root",o!=="none"&&`margin${me(o)}`,n&&"fullWidth"]};return Te(s,Xa,t)},Ya=V("div",{name:"MuiFormControl",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,t[`margin${me(o.margin)}`],o.fullWidth&&t.fullWidth]}})({display:"inline-flex",flexDirection:"column",position:"relative",minWidth:0,padding:0,margin:0,border:0,verticalAlign:"top",variants:[{props:{margin:"normal"},style:{marginTop:16,marginBottom:8}},{props:{margin:"dense"},style:{marginTop:8,marginBottom:4}},{props:{fullWidth:!0},style:{width:"100%"}}]}),Jr=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiFormControl"}),{children:s,className:i,color:l="primary",component:a="div",disabled:c=!1,error:h=!1,focused:p,fullWidth:v=!1,hiddenLabel:w=!1,margin:g="none",required:x=!1,size:j="medium",variant:y="outlined",...S}=n,m={...n,color:l,component:a,disabled:c,error:h,fullWidth:v,hiddenLabel:w,margin:g,required:x,size:j,variant:y},f=Ga(m),[d,b]=u.useState(()=>{let M=!1;return s&&u.Children.forEach(s,k=>{if(!tn(k,["Input","Select"]))return;const W=tn(k,["Select"])?k.props.input:k;W&&Vi(W.props)&&(M=!0)}),M}),[C,P]=u.useState(()=>{let M=!1;return s&&u.Children.forEach(s,k=>{tn(k,["Input","Select"])&&(Ho(k.props,!0)||Ho(k.props.inputProps,!0))&&(M=!0)}),M}),[$,L]=u.useState(!1);c&&$&&L(!1);const O=p!==void 0&&!c?p:$;let N;u.useRef(!1);const F=u.useCallback(()=>{P(!0)},[]),R=u.useCallback(()=>{P(!1)},[]),E=u.useMemo(()=>({adornedStart:d,setAdornedStart:b,color:l,disabled:c,error:h,filled:C,focused:O,fullWidth:v,hiddenLabel:w,size:j,onBlur:()=>{L(!1)},onFocus:()=>{L(!0)},onEmpty:R,onFilled:F,registerEffect:N,required:x,variant:y}),[d,l,c,h,C,O,v,w,N,R,F,x,j,y]);return r.jsx(Ln.Provider,{value:E,children:r.jsx(Ya,{as:a,ownerState:m,className:ue(f.root,i),ref:o,...S,children:s})})});function _a(e){return Ie("MuiFormControlLabel",e)}const co=we("MuiFormControlLabel",["root","labelPlacementStart","labelPlacementTop","labelPlacementBottom","disabled","label","error","required","asterisk"]),Qa=e=>{const{classes:t,disabled:o,labelPlacement:n,error:s,required:i}=e,l={root:["root",o&&"disabled",`labelPlacement${me(n)}`,s&&"error",i&&"required"],label:["label",o&&"disabled"],asterisk:["asterisk",s&&"error"]};return Te(l,_a,t)},Ja=V("label",{name:"MuiFormControlLabel",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[{[`& .${co.label}`]:t.label},t.root,t[`labelPlacement${me(o.labelPlacement)}`]]}})(ye(({theme:e})=>({display:"inline-flex",alignItems:"center",cursor:"pointer",verticalAlign:"middle",WebkitTapHighlightColor:"transparent",marginLeft:-11,marginRight:16,[`&.${co.disabled}`]:{cursor:"default"},[`& .${co.label}`]:{[`&.${co.disabled}`]:{color:(e.vars||e).palette.text.disabled}},variants:[{props:{labelPlacement:"start"},style:{flexDirection:"row-reverse",marginRight:-11}},{props:{labelPlacement:"top"},style:{flexDirection:"column-reverse"}},{props:{labelPlacement:"bottom"},style:{flexDirection:"column"}},{props:({labelPlacement:t})=>t==="start"||t==="top"||t==="bottom",style:{marginLeft:16}}]}))),Za=V("span",{name:"MuiFormControlLabel",slot:"Asterisk"})(ye(({theme:e})=>({[`&.${co.error}`]:{color:(e.vars||e).palette.error.main}}))),xt=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiFormControlLabel"}),{checked:s,className:i,componentsProps:l={},control:a,disabled:c,disableTypography:h,inputRef:p,label:v,labelPlacement:w="end",name:g,onChange:x,required:j,slots:y={},slotProps:S={},value:m,...f}=n,d=Tt(),b=c??a.props.disabled??(d==null?void 0:d.disabled),C=j??a.props.required,P={disabled:b,required:C};["checked","name","onChange","value","inputRef"].forEach(M=>{typeof a.props[M]>"u"&&typeof n[M]<"u"&&(P[M]=n[M])});const $=Dt({props:n,muiFormControl:d,states:["error"]}),L={...n,disabled:b,labelPlacement:w,required:C,error:$.error},O=Qa(L),N={slots:y,slotProps:{...l,...S}},[F,R]=be("typography",{elementType:q,externalForwardedProps:N,ownerState:L});let E=v;return E!=null&&E.type!==q&&!h&&(E=r.jsx(F,{component:"span",...R,className:ue(O.label,R==null?void 0:R.className),children:E})),r.jsxs(Ja,{className:ue(O.root,i),ownerState:L,ref:o,...f,children:[u.cloneElement(a,P),C?r.jsxs("div",{children:[E,r.jsxs(Za,{ownerState:L,"aria-hidden":!0,className:O.asterisk,children:[" ","*"]})]}):E]})});function el(e){return Ie("MuiFormHelperText",e)}const dr=we("MuiFormHelperText",["root","error","disabled","sizeSmall","sizeMedium","contained","focused","filled","required"]);var pr;const tl=e=>{const{classes:t,contained:o,size:n,disabled:s,error:i,filled:l,focused:a,required:c}=e,h={root:["root",s&&"disabled",i&&"error",n&&`size${me(n)}`,o&&"contained",a&&"focused",l&&"filled",c&&"required"]};return Te(h,el,t)},ol=V("p",{name:"MuiFormHelperText",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,o.size&&t[`size${me(o.size)}`],o.contained&&t.contained,o.filled&&t.filled]}})(ye(({theme:e})=>({color:(e.vars||e).palette.text.secondary,...e.typography.caption,textAlign:"left",marginTop:3,marginRight:0,marginBottom:0,marginLeft:0,[`&.${dr.disabled}`]:{color:(e.vars||e).palette.text.disabled},[`&.${dr.error}`]:{color:(e.vars||e).palette.error.main},variants:[{props:{size:"small"},style:{marginTop:4}},{props:({ownerState:t})=>t.contained,style:{marginLeft:14,marginRight:14}}]}))),nl=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiFormHelperText"}),{children:s,className:i,component:l="p",disabled:a,error:c,filled:h,focused:p,margin:v,required:w,variant:g,...x}=n,j=Tt(),y=Dt({props:n,muiFormControl:j,states:["variant","size","disabled","error","filled","focused","required"]}),S={...n,component:l,contained:y.variant==="filled"||y.variant==="outlined",variant:y.variant,size:y.size,disabled:y.disabled,error:y.error,filled:y.filled,focused:y.focused,required:y.required};delete S.ownerState;const m=tl(S);return r.jsx(ol,{as:l,className:ue(m.root,i),ref:o,...x,ownerState:S,children:s===" "?pr||(pr=r.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"})):s})});function rl(e){return Ie("MuiFormLabel",e)}const ho=we("MuiFormLabel",["root","colorSecondary","focused","disabled","error","filled","required","asterisk"]),sl=e=>{const{classes:t,color:o,focused:n,disabled:s,error:i,filled:l,required:a}=e,c={root:["root",`color${me(o)}`,s&&"disabled",i&&"error",l&&"filled",n&&"focused",a&&"required"],asterisk:["asterisk",i&&"error"]};return Te(c,rl,t)},il=V("label",{name:"MuiFormLabel",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,o.color==="secondary"&&t.colorSecondary,o.filled&&t.filled]}})(ye(({theme:e})=>({color:(e.vars||e).palette.text.secondary,...e.typography.body1,lineHeight:"1.4375em",padding:0,position:"relative",variants:[...Object.entries(e.palette).filter(jt()).map(([t])=>({props:{color:t},style:{[`&.${ho.focused}`]:{color:(e.vars||e).palette[t].main}}})),{props:{},style:{[`&.${ho.disabled}`]:{color:(e.vars||e).palette.text.disabled},[`&.${ho.error}`]:{color:(e.vars||e).palette.error.main}}}]}))),al=V("span",{name:"MuiFormLabel",slot:"Asterisk"})(ye(({theme:e})=>({[`&.${ho.error}`]:{color:(e.vars||e).palette.error.main}}))),ll=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiFormLabel"}),{children:s,className:i,color:l,component:a="label",disabled:c,error:h,filled:p,focused:v,required:w,...g}=n,x=Tt(),j=Dt({props:n,muiFormControl:x,states:["color","required","focused","disabled","error","filled"]}),y={...n,color:j.color||"primary",component:a,disabled:j.disabled,error:j.error,filled:j.filled,focused:j.focused,required:j.required},S=sl(y);return r.jsxs(il,{as:a,ownerState:y,className:ue(S.root,i),ref:o,...g,children:[s,j.required&&r.jsxs(al,{ownerState:y,"aria-hidden":!0,className:S.asterisk,children:[" ","*"]})]})});function Cn(e){return`scale(${e}, ${e**2})`}const cl={entering:{opacity:1,transform:Cn(1)},entered:{opacity:1,transform:"none"}},sn=typeof navigator<"u"&&/^((?!chrome|android).)*(safari|mobile)/i.test(navigator.userAgent)&&/(os |version\/)15(.|_)4/i.test(navigator.userAgent),Uo=u.forwardRef(function(t,o){const{addEndListener:n,appear:s=!0,children:i,easing:l,in:a,onEnter:c,onEntered:h,onEntering:p,onExit:v,onExited:w,onExiting:g,style:x,timeout:j="auto",TransitionComponent:y=vt,...S}=t,m=ao(),f=u.useRef(),d=xo(),b=u.useRef(null),C=Ye(b,Qt(i),o),P=M=>k=>{if(M){const W=b.current;k===void 0?M(W):M(W,k)}},$=P(p),L=P((M,k)=>{Ar(M);const{duration:W,delay:H,easing:A}=Do({style:x,timeout:j,easing:l},{mode:"enter"});let _;j==="auto"?(_=d.transitions.getAutoHeightDuration(M.clientHeight),f.current=_):_=W,M.style.transition=[d.transitions.create("opacity",{duration:_,delay:H}),d.transitions.create("transform",{duration:sn?_:_*.666,delay:H,easing:A})].join(","),c&&c(M,k)}),O=P(h),N=P(g),F=P(M=>{const{duration:k,delay:W,easing:H}=Do({style:x,timeout:j,easing:l},{mode:"exit"});let A;j==="auto"?(A=d.transitions.getAutoHeightDuration(M.clientHeight),f.current=A):A=k,M.style.transition=[d.transitions.create("opacity",{duration:A,delay:W}),d.transitions.create("transform",{duration:sn?A:A*.666,delay:sn?W:W||A*.333,easing:H})].join(","),M.style.opacity=0,M.style.transform=Cn(.75),v&&v(M)}),R=P(w),E=M=>{j==="auto"&&m.start(f.current||0,M),n&&n(b.current,M)};return r.jsx(y,{appear:s,in:a,nodeRef:b,onEnter:L,onEntered:O,onEntering:$,onExit:F,onExited:R,onExiting:N,addEndListener:E,timeout:j==="auto"?null:j,...S,children:(M,{ownerState:k,...W})=>u.cloneElement(i,{style:{opacity:0,transform:Cn(.75),visibility:M==="exited"&&!a?"hidden":void 0,...cl[M],...x,...i.props.style},ref:C,...W})})});Uo&&(Uo.muiSupportAuto=!0);const dl=e=>{const{classes:t,disableUnderline:o}=e,s=Te({root:["root",!o&&"underline"],input:["input"]},Xi,t);return{...t,...s}},pl=V(Jo,{shouldForwardProp:e=>ct(e)||e==="classes",name:"MuiInput",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[..._o(e,t),!o.disableUnderline&&t.underline]}})(ye(({theme:e})=>{let o=e.palette.mode==="light"?"rgba(0, 0, 0, 0.42)":"rgba(255, 255, 255, 0.7)";return e.vars&&(o=`rgba(${e.vars.palette.common.onBackgroundChannel} / ${e.vars.opacity.inputUnderline})`),{position:"relative",variants:[{props:({ownerState:n})=>n.formControl,style:{"label + &":{marginTop:16}}},{props:({ownerState:n})=>!n.disableUnderline,style:{"&::after":{left:0,bottom:0,content:'""',position:"absolute",right:0,transform:"scaleX(0)",transition:e.transitions.create("transform",{duration:e.transitions.duration.shorter,easing:e.transitions.easing.easeOut}),pointerEvents:"none"},[`&.${oo.focused}:after`]:{transform:"scaleX(1) translateX(0)"},[`&.${oo.error}`]:{"&::before, &::after":{borderBottomColor:(e.vars||e).palette.error.main}},"&::before":{borderBottom:`1px solid ${o}`,left:0,bottom:0,content:'"\\00a0"',position:"absolute",right:0,transition:e.transitions.create("border-bottom-color",{duration:e.transitions.duration.shorter}),pointerEvents:"none"},[`&:hover:not(.${oo.disabled}, .${oo.error}):before`]:{borderBottom:`2px solid ${(e.vars||e).palette.text.primary}`,"@media (hover: none)":{borderBottom:`1px solid ${o}`}},[`&.${oo.disabled}:before`]:{borderBottomStyle:"dotted"}}},...Object.entries(e.palette).filter(jt()).map(([n])=>({props:{color:n,disableUnderline:!1},style:{"&::after":{borderBottom:`2px solid ${(e.vars||e).palette[n].main}`}}}))]}})),ul=V(Zo,{name:"MuiInput",slot:"Input",overridesResolver:Qo})({}),Nn=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiInput"}),{disableUnderline:s=!1,components:i={},componentsProps:l,fullWidth:a=!1,inputComponent:c="input",multiline:h=!1,slotProps:p,slots:v={},type:w="text",...g}=n,x=dl(n),y={root:{ownerState:{disableUnderline:s}}},S=p??l?kn(p??l,y):y,m=v.root??i.Root??pl,f=v.input??i.Input??ul;return r.jsx(Bn,{slots:{root:m,input:f},slotProps:S,fullWidth:a,inputComponent:c,multiline:h,ref:o,type:w,...g,classes:x})});Nn.muiName="Input";function fl(e){return Ie("MuiInputLabel",e)}we("MuiInputLabel",["root","focused","disabled","error","required","asterisk","formControl","sizeSmall","shrink","animated","standard","filled","outlined"]);const hl=e=>{const{classes:t,formControl:o,size:n,shrink:s,disableAnimation:i,variant:l,required:a}=e,c={root:["root",o&&"formControl",!i&&"animated",s&&"shrink",n&&n!=="medium"&&`size${me(n)}`,l],asterisk:[a&&"asterisk"]},h=Te(c,fl,t);return{...t,...h}},ml=V(ll,{shouldForwardProp:e=>ct(e)||e==="classes",name:"MuiInputLabel",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[{[`& .${ho.asterisk}`]:t.asterisk},t.root,o.formControl&&t.formControl,o.size==="small"&&t.sizeSmall,o.shrink&&t.shrink,!o.disableAnimation&&t.animated,o.focused&&t.focused,t[o.variant]]}})(ye(({theme:e})=>({display:"block",transformOrigin:"top left",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",variants:[{props:({ownerState:t})=>t.formControl,style:{position:"absolute",left:0,top:0,transform:"translate(0, 20px) scale(1)"}},{props:{size:"small"},style:{transform:"translate(0, 17px) scale(1)"}},{props:({ownerState:t})=>t.shrink,style:{transform:"translate(0, -1.5px) scale(0.75)",transformOrigin:"top left",maxWidth:"133%"}},{props:({ownerState:t})=>!t.disableAnimation,style:{transition:e.transitions.create(["color","transform","max-width"],{duration:e.transitions.duration.shorter,easing:e.transitions.easing.easeOut})}},{props:{variant:"filled"},style:{zIndex:1,pointerEvents:"none",transform:"translate(12px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"filled",size:"small"},style:{transform:"translate(12px, 13px) scale(1)"}},{props:({variant:t,ownerState:o})=>t==="filled"&&o.shrink,style:{userSelect:"none",pointerEvents:"auto",transform:"translate(12px, 7px) scale(0.75)",maxWidth:"calc(133% - 24px)"}},{props:({variant:t,ownerState:o,size:n})=>t==="filled"&&o.shrink&&n==="small",style:{transform:"translate(12px, 4px) scale(0.75)"}},{props:{variant:"outlined"},style:{zIndex:1,pointerEvents:"none",transform:"translate(14px, 16px) scale(1)",maxWidth:"calc(100% - 24px)"}},{props:{variant:"outlined",size:"small"},style:{transform:"translate(14px, 9px) scale(1)"}},{props:({variant:t,ownerState:o})=>t==="outlined"&&o.shrink,style:{userSelect:"none",pointerEvents:"auto",maxWidth:"calc(133% - 32px)",transform:"translate(14px, -9px) scale(0.75)"}}]}))),Zr=u.forwardRef(function(t,o){const n=$e({name:"MuiInputLabel",props:t}),{disableAnimation:s=!1,margin:i,shrink:l,variant:a,className:c,...h}=n,p=Tt();let v=l;typeof v>"u"&&p&&(v=p.filled||p.focused||p.adornedStart);const w=Dt({props:n,muiFormControl:p,states:["size","variant","required","focused"]}),g={...n,disableAnimation:s,formControl:p,shrink:v,size:w.size,variant:w.variant,required:w.required,focused:w.focused},x=hl(g);return r.jsx(ml,{"data-shrink":v,ref:o,className:ue(x.root,c),...h,ownerState:g,classes:x})}),jn=u.createContext({});function gl(e){return Ie("MuiList",e)}we("MuiList",["root","padding","dense","subheader"]);const bl=e=>{const{classes:t,disablePadding:o,dense:n,subheader:s}=e;return Te({root:["root",!o&&"padding",n&&"dense",s&&"subheader"]},gl,t)},vl=V("ul",{name:"MuiList",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,!o.disablePadding&&t.padding,o.dense&&t.dense,o.subheader&&t.subheader]}})({listStyle:"none",margin:0,padding:0,position:"relative",variants:[{props:({ownerState:e})=>!e.disablePadding,style:{paddingTop:8,paddingBottom:8}},{props:({ownerState:e})=>e.subheader,style:{paddingTop:0}}]}),xl=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiList"}),{children:s,className:i,component:l="ul",dense:a=!1,disablePadding:c=!1,subheader:h,...p}=n,v=u.useMemo(()=>({dense:a}),[a]),w={...n,component:l,dense:a,disablePadding:c},g=bl(w);return r.jsx(jn.Provider,{value:v,children:r.jsxs(vl,{as:l,className:ue(g.root,i),ref:o,ownerState:w,...p,children:[h,s]})})}),ur=we("MuiListItemIcon",["root","alignItemsFlexStart"]),fr=we("MuiListItemText",["root","multiline","dense","inset","primary","secondary"]);function an(e,t,o){return e===t?e.firstChild:t&&t.nextElementSibling?t.nextElementSibling:o?null:e.firstChild}function hr(e,t,o){return e===t?o?e.firstChild:e.lastChild:t&&t.previousElementSibling?t.previousElementSibling:o?null:e.lastChild}function es(e,t){if(t===void 0)return!0;let o=e.innerText;return o===void 0&&(o=e.textContent),o=o.trim().toLowerCase(),o.length===0?!1:t.repeating?o[0]===t.keys[0]:o.startsWith(t.keys.join(""))}function no(e,t,o,n,s,i){let l=!1,a=s(e,t,t?o:!1);for(;a;){if(a===e.firstChild){if(l)return!1;l=!0}const c=n?!1:a.disabled||a.getAttribute("aria-disabled")==="true";if(!a.hasAttribute("tabindex")||!es(a,i)||c)a=s(e,a,o);else return a.focus(),!0}return!1}const yl=u.forwardRef(function(t,o){const{actions:n,autoFocus:s=!1,autoFocusItem:i=!1,children:l,className:a,disabledItemsFocusable:c=!1,disableListWrap:h=!1,onKeyDown:p,variant:v="selectedMenu",...w}=t,g=u.useRef(null),x=u.useRef({keys:[],repeating:!0,previousKeyMatched:!0,lastTime:null});pt(()=>{s&&g.current.focus()},[s]),u.useImperativeHandle(n,()=>({adjustStyleForScrollbar:(f,{direction:d})=>{const b=!g.current.style.width;if(f.clientHeight<g.current.clientHeight&&b){const C=`${Yr(ut(f))}px`;g.current.style[d==="rtl"?"paddingLeft":"paddingRight"]=C,g.current.style.width=`calc(100% + ${C})`}return g.current}}),[]);const j=f=>{const d=g.current,b=f.key;if(f.ctrlKey||f.metaKey||f.altKey){p&&p(f);return}const P=Xe(d).activeElement;if(b==="ArrowDown")f.preventDefault(),no(d,P,h,c,an);else if(b==="ArrowUp")f.preventDefault(),no(d,P,h,c,hr);else if(b==="Home")f.preventDefault(),no(d,null,h,c,an);else if(b==="End")f.preventDefault(),no(d,null,h,c,hr);else if(b.length===1){const $=x.current,L=b.toLowerCase(),O=performance.now();$.keys.length>0&&(O-$.lastTime>500?($.keys=[],$.repeating=!0,$.previousKeyMatched=!0):$.repeating&&L!==$.keys[0]&&($.repeating=!1)),$.lastTime=O,$.keys.push(L);const N=P&&!$.repeating&&es(P,$);$.previousKeyMatched&&(N||no(d,P,!1,c,an,$))?f.preventDefault():$.previousKeyMatched=!1}p&&p(f)},y=Ye(g,o);let S=-1;u.Children.forEach(l,(f,d)=>{if(!u.isValidElement(f)){S===d&&(S+=1,S>=l.length&&(S=-1));return}f.props.disabled||(v==="selectedMenu"&&f.props.selected||S===-1)&&(S=d),S===d&&(f.props.disabled||f.props.muiSkipListHighlight||f.type.muiSkipListHighlight)&&(S+=1,S>=l.length&&(S=-1))});const m=u.Children.map(l,(f,d)=>{if(d===S){const b={};return i&&(b.autoFocus=!0),f.props.tabIndex===void 0&&v==="selectedMenu"&&(b.tabIndex=0),u.cloneElement(f,b)}return f});return r.jsx(xl,{role:"menu",ref:y,className:a,onKeyDown:j,tabIndex:s?0:-1,...w,children:m})});function wl(e){return Ie("MuiPopover",e)}we("MuiPopover",["root","paper"]);function mr(e,t){let o=0;return typeof t=="number"?o=t:t==="center"?o=e.height/2:t==="bottom"&&(o=e.height),o}function gr(e,t){let o=0;return typeof t=="number"?o=t:t==="center"?o=e.width/2:t==="right"&&(o=e.width),o}function br(e){return[e.horizontal,e.vertical].map(t=>typeof t=="number"?`${t}px`:t).join(" ")}function Eo(e){return typeof e=="function"?e():e}const Sl=e=>{const{classes:t}=e;return Te({root:["root"],paper:["paper"]},wl,t)},Cl=V(_r,{name:"MuiPopover",slot:"Root"})({}),ts=V(Vt,{name:"MuiPopover",slot:"Paper"})({position:"absolute",overflowY:"auto",overflowX:"hidden",minWidth:16,minHeight:16,maxWidth:"calc(100% - 32px)",maxHeight:"calc(100% - 32px)",outline:0}),jl=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiPopover"}),{action:s,anchorEl:i,anchorOrigin:l={vertical:"top",horizontal:"left"},anchorPosition:a,anchorReference:c="anchorEl",children:h,className:p,container:v,elevation:w=8,marginThreshold:g=16,open:x,PaperProps:j={},slots:y={},slotProps:S={},transformOrigin:m={vertical:"top",horizontal:"left"},TransitionComponent:f,transitionDuration:d="auto",TransitionProps:b={},disableScrollLock:C=!1,...P}=n,$=u.useRef(),L={...n,anchorOrigin:l,anchorReference:c,elevation:w,marginThreshold:g,transformOrigin:m,TransitionComponent:f,transitionDuration:d,TransitionProps:b},O=Sl(L),N=u.useCallback(()=>{if(c==="anchorPosition")return a;const B=Eo(i),U=(B&&B.nodeType===1?B:Xe($.current).body).getBoundingClientRect();return{top:U.top+mr(U,l.vertical),left:U.left+gr(U,l.horizontal)}},[i,l.horizontal,l.vertical,a,c]),F=u.useCallback(B=>({vertical:mr(B,m.vertical),horizontal:gr(B,m.horizontal)}),[m.horizontal,m.vertical]),R=u.useCallback(B=>{const X={width:B.offsetWidth,height:B.offsetHeight},U=F(X);if(c==="none")return{top:null,left:null,transformOrigin:br(U)};const ne=N();let re=ne.top-U.vertical,ge=ne.left-U.horizontal;const Le=re+X.height,pe=ge+X.width,Se=ut(Eo(i)),Be=Se.innerHeight-g,Re=Se.innerWidth-g;if(g!==null&&re<g){const ve=re-g;re-=ve,U.vertical+=ve}else if(g!==null&&Le>Be){const ve=Le-Be;re-=ve,U.vertical+=ve}if(g!==null&&ge<g){const ve=ge-g;ge-=ve,U.horizontal+=ve}else if(pe>Re){const ve=pe-Re;ge-=ve,U.horizontal+=ve}return{top:`${Math.round(re)}px`,left:`${Math.round(ge)}px`,transformOrigin:br(U)}},[i,c,N,F,g]),[E,M]=u.useState(x),k=u.useCallback(()=>{const B=$.current;if(!B)return;const X=R(B);X.top!==null&&B.style.setProperty("top",X.top),X.left!==null&&(B.style.left=X.left),B.style.transformOrigin=X.transformOrigin,M(!0)},[R]);u.useEffect(()=>(C&&window.addEventListener("scroll",k),()=>window.removeEventListener("scroll",k)),[i,C,k]);const W=()=>{k()},H=()=>{M(!1)};u.useEffect(()=>{x&&k()}),u.useImperativeHandle(s,()=>x?{updatePosition:()=>{k()}}:null,[x,k]),u.useEffect(()=>{if(!x)return;const B=Go(()=>{k()}),X=ut(Eo(i));return X.addEventListener("resize",B),()=>{B.clear(),X.removeEventListener("resize",B)}},[i,x,k]);let A=d;const _={slots:{transition:f,...y},slotProps:{transition:b,paper:j,...S}},[Y,ee]=be("transition",{elementType:Uo,externalForwardedProps:_,ownerState:L,getSlotProps:B=>({...B,onEntering:(X,U)=>{var ne;(ne=B.onEntering)==null||ne.call(B,X,U),W()},onExited:X=>{var U;(U=B.onExited)==null||U.call(B,X),H()}}),additionalProps:{appear:!0,in:x}});d==="auto"&&!Y.muiSupportAuto&&(A=void 0);const ae=v||(i?Xe(Eo(i)).body:void 0),[de,{slots:K,slotProps:le,...ce}]=be("root",{ref:o,elementType:Cl,externalForwardedProps:{..._,...P},shouldForwardComponentProp:!0,additionalProps:{slots:{backdrop:y.backdrop},slotProps:{backdrop:js(typeof S.backdrop=="function"?S.backdrop(L):S.backdrop,{invisible:!0})},container:ae,open:x},ownerState:L,className:ue(O.root,p)}),[T,D]=be("paper",{ref:$,className:O.paper,elementType:ts,externalForwardedProps:_,shouldForwardComponentProp:!0,additionalProps:{elevation:w,style:E?void 0:{opacity:0}},ownerState:L});return r.jsx(de,{...ce,...!Ft(de)&&{slots:K,slotProps:le,disableScrollLock:C},children:r.jsx(Y,{...ee,timeout:A,children:r.jsx(T,{...D,children:h})})})});function kl(e){return Ie("MuiMenu",e)}we("MuiMenu",["root","paper","list"]);const Tl={vertical:"top",horizontal:"right"},Rl={vertical:"top",horizontal:"left"},El=e=>{const{classes:t}=e;return Te({root:["root"],paper:["paper"],list:["list"]},kl,t)},Pl=V(jl,{shouldForwardProp:e=>ct(e)||e==="classes",name:"MuiMenu",slot:"Root"})({}),Ml=V(ts,{name:"MuiMenu",slot:"Paper"})({maxHeight:"calc(100% - 96px)",WebkitOverflowScrolling:"touch"}),Il=V(yl,{name:"MuiMenu",slot:"List"})({outline:0}),Ol=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiMenu"}),{autoFocus:s=!0,children:i,className:l,disableAutoFocusItem:a=!1,MenuListProps:c={},onClose:h,open:p,PaperProps:v={},PopoverClasses:w,transitionDuration:g="auto",TransitionProps:{onEntering:x,...j}={},variant:y="selectedMenu",slots:S={},slotProps:m={},...f}=n,d=_t(),b={...n,autoFocus:s,disableAutoFocusItem:a,MenuListProps:c,onEntering:x,PaperProps:v,transitionDuration:g,TransitionProps:j,variant:y},C=El(b),P=s&&!a&&p,$=u.useRef(null),L=(A,_)=>{$.current&&$.current.adjustStyleForScrollbar(A,{direction:d?"rtl":"ltr"}),x&&x(A,_)},O=A=>{A.key==="Tab"&&(A.preventDefault(),h&&h(A,"tabKeyDown"))};let N=-1;u.Children.map(i,(A,_)=>{u.isValidElement(A)&&(A.props.disabled||(y==="selectedMenu"&&A.props.selected||N===-1)&&(N=_))});const F={slots:S,slotProps:{list:c,transition:j,paper:v,...m}},R=Qe({elementType:S.root,externalSlotProps:m.root,ownerState:b,className:[C.root,l]}),[E,M]=be("paper",{className:C.paper,elementType:Ml,externalForwardedProps:F,shouldForwardComponentProp:!0,ownerState:b}),[k,W]=be("list",{className:ue(C.list,c.className),elementType:Il,shouldForwardComponentProp:!0,externalForwardedProps:F,getSlotProps:A=>({...A,onKeyDown:_=>{var Y;O(_),(Y=A.onKeyDown)==null||Y.call(A,_)}}),ownerState:b}),H=typeof F.slotProps.transition=="function"?F.slotProps.transition(b):F.slotProps.transition;return r.jsx(Pl,{onClose:h,anchorOrigin:{vertical:"bottom",horizontal:d?"right":"left"},transformOrigin:d?Tl:Rl,slots:{root:S.root,paper:E,backdrop:S.backdrop,...S.transition&&{transition:S.transition}},slotProps:{root:R,paper:M,backdrop:typeof m.backdrop=="function"?m.backdrop(b):m.backdrop,transition:{...H,onEntering:(...A)=>{var _;L(...A),(_=H==null?void 0:H.onEntering)==null||_.call(H,...A)}}},open:p,ref:o,transitionDuration:g,ownerState:b,...f,classes:w,children:r.jsx(k,{actions:$,autoFocus:s&&(N===-1||a),autoFocusItem:P,variant:y,...W,children:i})})});function $l(e){return Ie("MuiMenuItem",e)}const ro=we("MuiMenuItem",["root","focusVisible","dense","disabled","divider","gutters","selected"]),Ll=(e,t)=>{const{ownerState:o}=e;return[t.root,o.dense&&t.dense,o.divider&&t.divider,!o.disableGutters&&t.gutters]},Bl=e=>{const{disabled:t,dense:o,divider:n,disableGutters:s,selected:i,classes:l}=e,c=Te({root:["root",o&&"dense",t&&"disabled",!s&&"gutters",n&&"divider",i&&"selected"]},$l,l);return{...l,...c}},Al=V(Ko,{shouldForwardProp:e=>ct(e)||e==="classes",name:"MuiMenuItem",slot:"Root",overridesResolver:Ll})(ye(({theme:e})=>({...e.typography.body1,display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minHeight:48,paddingTop:6,paddingBottom:6,boxSizing:"border-box",whiteSpace:"nowrap","&:hover":{textDecoration:"none",backgroundColor:(e.vars||e).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${ro.selected}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${e.vars.palette.action.selectedOpacity})`:mt(e.palette.primary.main,e.palette.action.selectedOpacity),[`&.${ro.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.focusOpacity}))`:mt(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.focusOpacity)}},[`&.${ro.selected}:hover`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.hoverOpacity}))`:mt(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${e.vars.palette.action.selectedOpacity})`:mt(e.palette.primary.main,e.palette.action.selectedOpacity)}},[`&.${ro.focusVisible}`]:{backgroundColor:(e.vars||e).palette.action.focus},[`&.${ro.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity},[`& + .${cr.root}`]:{marginTop:e.spacing(1),marginBottom:e.spacing(1)},[`& + .${cr.inset}`]:{marginLeft:52},[`& .${fr.root}`]:{marginTop:0,marginBottom:0},[`& .${fr.inset}`]:{paddingLeft:36},[`& .${ur.root}`]:{minWidth:36},variants:[{props:({ownerState:t})=>!t.disableGutters,style:{paddingLeft:16,paddingRight:16}},{props:({ownerState:t})=>t.divider,style:{borderBottom:`1px solid ${(e.vars||e).palette.divider}`,backgroundClip:"padding-box"}},{props:({ownerState:t})=>!t.dense,style:{[e.breakpoints.up("sm")]:{minHeight:"auto"}}},{props:({ownerState:t})=>t.dense,style:{minHeight:32,paddingTop:4,paddingBottom:4,...e.typography.body2,[`& .${ur.root} svg`]:{fontSize:"1.25rem"}}}]}))),ln=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiMenuItem"}),{autoFocus:s=!1,component:i="li",dense:l=!1,divider:a=!1,disableGutters:c=!1,focusVisibleClassName:h,role:p="menuitem",tabIndex:v,className:w,...g}=n,x=u.useContext(jn),j=u.useMemo(()=>({dense:l||x.dense||!1,disableGutters:c}),[x.dense,l,c]),y=u.useRef(null);pt(()=>{s&&y.current&&y.current.focus()},[s]);const S={...n,dense:j.dense,divider:a,disableGutters:c},m=Bl(n),f=Ye(y,o);let d;return n.disabled||(d=v!==void 0?v:-1),r.jsx(jn.Provider,{value:j,children:r.jsx(Al,{ref:f,role:p,tabIndex:d,component:i,focusVisibleClassName:ue(m.focusVisible,h),className:ue(m.root,w),...g,ownerState:S,classes:m})})});function Nl(e){return Ie("MuiNativeSelect",e)}const Fn=we("MuiNativeSelect",["root","select","multiple","filled","outlined","standard","disabled","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]),Fl=e=>{const{classes:t,variant:o,disabled:n,multiple:s,open:i,error:l}=e,a={select:["select",o,n&&"disabled",s&&"multiple",l&&"error"],icon:["icon",`icon${me(o)}`,i&&"iconOpen",n&&"disabled"]};return Te(a,Nl,t)},os=V("select")(({theme:e})=>({MozAppearance:"none",WebkitAppearance:"none",userSelect:"none",borderRadius:0,cursor:"pointer","&:focus":{borderRadius:0},[`&.${Fn.disabled}`]:{cursor:"default"},"&[multiple]":{height:"auto"},"&:not([multiple]) option, &:not([multiple]) optgroup":{backgroundColor:(e.vars||e).palette.background.paper},variants:[{props:({ownerState:t})=>t.variant!=="filled"&&t.variant!=="outlined",style:{"&&&":{paddingRight:24,minWidth:16}}},{props:{variant:"filled"},style:{"&&&":{paddingRight:32}}},{props:{variant:"outlined"},style:{borderRadius:(e.vars||e).shape.borderRadius,"&:focus":{borderRadius:(e.vars||e).shape.borderRadius},"&&&":{paddingRight:32}}}]})),zl=V(os,{name:"MuiNativeSelect",slot:"Select",shouldForwardProp:ct,overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.select,t[o.variant],o.error&&t.error,{[`&.${Fn.multiple}`]:t.multiple}]}})({}),ns=V("svg")(({theme:e})=>({position:"absolute",right:0,top:"calc(50% - .5em)",pointerEvents:"none",color:(e.vars||e).palette.action.active,[`&.${Fn.disabled}`]:{color:(e.vars||e).palette.action.disabled},variants:[{props:({ownerState:t})=>t.open,style:{transform:"rotate(180deg)"}},{props:{variant:"filled"},style:{right:7}},{props:{variant:"outlined"},style:{right:7}}]})),Dl=V(ns,{name:"MuiNativeSelect",slot:"Icon",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.icon,o.variant&&t[`icon${me(o.variant)}`],o.open&&t.iconOpen]}})({}),Wl=u.forwardRef(function(t,o){const{className:n,disabled:s,error:i,IconComponent:l,inputRef:a,variant:c="standard",...h}=t,p={...t,disabled:s,variant:c,error:i},v=Fl(p);return r.jsxs(u.Fragment,{children:[r.jsx(zl,{ownerState:p,className:ue(v.select,n),disabled:s,ref:a||o,...h}),t.multiple?null:r.jsx(Dl,{as:l,ownerState:p,className:v.icon})]})});var vr;const Hl=V("fieldset",{shouldForwardProp:ct})({textAlign:"left",position:"absolute",bottom:0,right:0,top:-5,left:0,margin:0,padding:"0 8px",pointerEvents:"none",borderRadius:"inherit",borderStyle:"solid",borderWidth:1,overflow:"hidden",minWidth:"0%"}),Ul=V("legend",{shouldForwardProp:ct})(ye(({theme:e})=>({float:"unset",width:"auto",overflow:"hidden",variants:[{props:({ownerState:t})=>!t.withLabel,style:{padding:0,lineHeight:"11px",transition:e.transitions.create("width",{duration:150,easing:e.transitions.easing.easeOut})}},{props:({ownerState:t})=>t.withLabel,style:{display:"block",padding:0,height:11,fontSize:"0.75em",visibility:"hidden",maxWidth:.01,transition:e.transitions.create("max-width",{duration:50,easing:e.transitions.easing.easeOut}),whiteSpace:"nowrap","& > span":{paddingLeft:5,paddingRight:5,display:"inline-block",opacity:0,visibility:"visible"}}},{props:({ownerState:t})=>t.withLabel&&t.notched,style:{maxWidth:"100%",transition:e.transitions.create("max-width",{duration:100,easing:e.transitions.easing.easeOut,delay:50})}}]})));function Vl(e){const{children:t,classes:o,className:n,label:s,notched:i,...l}=e,a=s!=null&&s!=="",c={...e,notched:i,withLabel:a};return r.jsx(Hl,{"aria-hidden":!0,className:n,ownerState:c,...l,children:r.jsx(Ul,{ownerState:c,children:a?r.jsx("span",{children:s}):vr||(vr=r.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"}))})})}const ql=e=>{const{classes:t}=e,n=Te({root:["root"],notchedOutline:["notchedOutline"],input:["input"]},Gi,t);return{...t,...n}},Kl=V(Jo,{shouldForwardProp:e=>ct(e)||e==="classes",name:"MuiOutlinedInput",slot:"Root",overridesResolver:_o})(ye(({theme:e})=>{const t=e.palette.mode==="light"?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return{position:"relative",borderRadius:(e.vars||e).shape.borderRadius,[`&:hover .${ht.notchedOutline}`]:{borderColor:(e.vars||e).palette.text.primary},"@media (hover: none)":{[`&:hover .${ht.notchedOutline}`]:{borderColor:e.vars?`rgba(${e.vars.palette.common.onBackgroundChannel} / 0.23)`:t}},[`&.${ht.focused} .${ht.notchedOutline}`]:{borderWidth:2},variants:[...Object.entries(e.palette).filter(jt()).map(([o])=>({props:{color:o},style:{[`&.${ht.focused} .${ht.notchedOutline}`]:{borderColor:(e.vars||e).palette[o].main}}})),{props:{},style:{[`&.${ht.error} .${ht.notchedOutline}`]:{borderColor:(e.vars||e).palette.error.main},[`&.${ht.disabled} .${ht.notchedOutline}`]:{borderColor:(e.vars||e).palette.action.disabled}}},{props:({ownerState:o})=>o.startAdornment,style:{paddingLeft:14}},{props:({ownerState:o})=>o.endAdornment,style:{paddingRight:14}},{props:({ownerState:o})=>o.multiline,style:{padding:"16.5px 14px"}},{props:({ownerState:o,size:n})=>o.multiline&&n==="small",style:{padding:"8.5px 14px"}}]}})),Xl=V(Vl,{name:"MuiOutlinedInput",slot:"NotchedOutline"})(ye(({theme:e})=>{const t=e.palette.mode==="light"?"rgba(0, 0, 0, 0.23)":"rgba(255, 255, 255, 0.23)";return{borderColor:e.vars?`rgba(${e.vars.palette.common.onBackgroundChannel} / 0.23)`:t}})),Gl=V(Zo,{name:"MuiOutlinedInput",slot:"Input",overridesResolver:Qo})(ye(({theme:e})=>({padding:"16.5px 14px",...!e.vars&&{"&:-webkit-autofill":{WebkitBoxShadow:e.palette.mode==="light"?null:"0 0 0 100px #266798 inset",WebkitTextFillColor:e.palette.mode==="light"?null:"#fff",caretColor:e.palette.mode==="light"?null:"#fff",borderRadius:"inherit"}},...e.vars&&{"&:-webkit-autofill":{borderRadius:"inherit"},[e.getColorSchemeSelector("dark")]:{"&:-webkit-autofill":{WebkitBoxShadow:"0 0 0 100px #266798 inset",WebkitTextFillColor:"#fff",caretColor:"#fff"}}},variants:[{props:{size:"small"},style:{padding:"8.5px 14px"}},{props:({ownerState:t})=>t.multiline,style:{padding:0}},{props:({ownerState:t})=>t.startAdornment,style:{paddingLeft:0}},{props:({ownerState:t})=>t.endAdornment,style:{paddingRight:0}}]}))),zn=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiOutlinedInput"}),{components:s={},fullWidth:i=!1,inputComponent:l="input",label:a,multiline:c=!1,notched:h,slots:p={},slotProps:v={},type:w="text",...g}=n,x=ql(n),j=Tt(),y=Dt({props:n,muiFormControl:j,states:["color","disabled","error","focused","hiddenLabel","size","required"]}),S={...n,color:y.color||"primary",disabled:y.disabled,error:y.error,focused:y.focused,formControl:j,fullWidth:i,hiddenLabel:y.hiddenLabel,multiline:c,size:y.size,type:w},m=p.root??s.Root??Kl,f=p.input??s.Input??Gl,[d,b]=be("notchedOutline",{elementType:Xl,className:x.notchedOutline,shouldForwardComponentProp:!0,ownerState:S,externalForwardedProps:{slots:p,slotProps:v},additionalProps:{label:a!=null&&a!==""&&y.required?r.jsxs(u.Fragment,{children:[a," ","*"]}):a}});return r.jsx(Bn,{slots:{root:m,input:f},slotProps:v,renderSuffix:C=>r.jsx(d,{...b,notched:typeof h<"u"?h:!!(C.startAdornment||C.filled||C.focused)}),fullWidth:i,inputComponent:l,multiline:c,ref:o,type:w,...g,classes:{...x,notchedOutline:null}})});zn.muiName="Input";const Yl={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",whiteSpace:"nowrap",width:"1px"};function rs(e){return Ie("MuiSelect",e)}const so=we("MuiSelect",["root","select","multiple","filled","outlined","standard","disabled","focused","icon","iconOpen","iconFilled","iconOutlined","iconStandard","nativeInput","error"]);var xr;const _l=V(os,{name:"MuiSelect",slot:"Select",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[{[`&.${so.select}`]:t.select},{[`&.${so.select}`]:t[o.variant]},{[`&.${so.error}`]:t.error},{[`&.${so.multiple}`]:t.multiple}]}})({[`&.${so.select}`]:{height:"auto",minHeight:"1.4375em",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden"}}),Ql=V(ns,{name:"MuiSelect",slot:"Icon",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.icon,o.variant&&t[`icon${me(o.variant)}`],o.open&&t.iconOpen]}})({}),Jl=V("input",{shouldForwardProp:e=>Tn(e)&&e!=="classes",name:"MuiSelect",slot:"NativeInput"})({bottom:0,left:0,position:"absolute",opacity:0,pointerEvents:"none",width:"100%",boxSizing:"border-box"});function yr(e,t){return typeof t=="object"&&t!==null?e===t:String(e)===String(t)}function Zl(e){return e==null||typeof e=="string"&&!e.trim()}const ec=e=>{const{classes:t,variant:o,disabled:n,multiple:s,open:i,error:l}=e,a={select:["select",o,n&&"disabled",s&&"multiple",l&&"error"],icon:["icon",`icon${me(o)}`,i&&"iconOpen",n&&"disabled"],nativeInput:["nativeInput"]};return Te(a,rs,t)},tc=u.forwardRef(function(t,o){var Oe;const{"aria-describedby":n,"aria-label":s,autoFocus:i,autoWidth:l,children:a,className:c,defaultOpen:h,defaultValue:p,disabled:v,displayEmpty:w,error:g=!1,IconComponent:x,inputRef:j,labelId:y,MenuProps:S={},multiple:m,name:f,onBlur:d,onChange:b,onClose:C,onFocus:P,onOpen:$,open:L,readOnly:O,renderValue:N,required:F,SelectDisplayProps:R={},tabIndex:E,type:M,value:k,variant:W="standard",...H}=t,[A,_]=go({controlled:k,default:p,name:"Select"}),[Y,ee]=go({controlled:L,default:h,name:"Select"}),ae=u.useRef(null),de=u.useRef(null),[K,le]=u.useState(null),{current:ce}=u.useRef(L!=null),[T,D]=u.useState(),B=Ye(o,j),X=u.useCallback(Z=>{de.current=Z,Z&&le(Z)},[]),U=K==null?void 0:K.parentNode;u.useImperativeHandle(B,()=>({focus:()=>{de.current.focus()},node:ae.current,value:A}),[A]),u.useEffect(()=>{h&&Y&&K&&!ce&&(D(l?null:U.clientWidth),de.current.focus())},[K,l]),u.useEffect(()=>{i&&de.current.focus()},[i]),u.useEffect(()=>{if(!y)return;const Z=Xe(de.current).getElementById(y);if(Z){const Ce=()=>{getSelection().isCollapsed&&de.current.focus()};return Z.addEventListener("click",Ce),()=>{Z.removeEventListener("click",Ce)}}},[y]);const ne=(Z,Ce)=>{Z?$&&$(Ce):C&&C(Ce),ce||(D(l?null:U.clientWidth),ee(Z))},re=Z=>{Z.button===0&&(Z.preventDefault(),de.current.focus(),ne(!0,Z))},ge=Z=>{ne(!1,Z)},Le=u.Children.toArray(a),pe=Z=>{const Ce=Le.find(Fe=>Fe.props.value===Z.target.value);Ce!==void 0&&(_(Ce.props.value),b&&b(Z,Ce))},Se=Z=>Ce=>{let Fe;if(Ce.currentTarget.hasAttribute("tabindex")){if(m){Fe=Array.isArray(A)?A.slice():[];const Ge=A.indexOf(Z.props.value);Ge===-1?Fe.push(Z.props.value):Fe.splice(Ge,1)}else Fe=Z.props.value;if(Z.props.onClick&&Z.props.onClick(Ce),A!==Fe&&(_(Fe),b)){const Ge=Ce.nativeEvent||Ce,et=new Ge.constructor(Ge.type,Ge);Object.defineProperty(et,"target",{writable:!0,value:{value:Fe,name:f}}),b(et,Z)}m||ne(!1,Ce)}},Be=Z=>{O||[" ","ArrowUp","ArrowDown","Enter"].includes(Z.key)&&(Z.preventDefault(),ne(!0,Z))},Re=K!==null&&Y,ve=Z=>{!Re&&d&&(Object.defineProperty(Z,"target",{writable:!0,value:{value:A,name:f}}),d(Z))};delete H["aria-invalid"];let Ne,ze;const Ee=[];let I=!1;(Ho({value:A})||w)&&(N?Ne=N(A):I=!0);const z=Le.map(Z=>{if(!u.isValidElement(Z))return null;let Ce;if(m){if(!Array.isArray(A))throw new Error(Lr(2));Ce=A.some(Fe=>yr(Fe,Z.props.value)),Ce&&I&&Ee.push(Z.props.children)}else Ce=yr(A,Z.props.value),Ce&&I&&(ze=Z.props.children);return u.cloneElement(Z,{"aria-selected":Ce?"true":"false",onClick:Se(Z),onKeyUp:Fe=>{Fe.key===" "&&Fe.preventDefault(),Z.props.onKeyUp&&Z.props.onKeyUp(Fe)},role:"option",selected:Ce,value:void 0,"data-value":Z.props.value})});I&&(m?Ee.length===0?Ne=null:Ne=Ee.reduce((Z,Ce,Fe)=>(Z.push(Ce),Fe<Ee.length-1&&Z.push(", "),Z),[]):Ne=ze);let Q=T;!l&&ce&&K&&(Q=U.clientWidth);let se;typeof E<"u"?se=E:se=v?null:0;const fe=R.id||(f?`mui-component-select-${f}`:void 0),J={...t,variant:W,value:A,open:Re,error:g},te=ec(J),Pe={...S.PaperProps,...(Oe=S.slotProps)==null?void 0:Oe.paper},xe=Xo();return r.jsxs(u.Fragment,{children:[r.jsx(_l,{as:"div",ref:X,tabIndex:se,role:"combobox","aria-controls":Re?xe:void 0,"aria-disabled":v?"true":void 0,"aria-expanded":Re?"true":"false","aria-haspopup":"listbox","aria-label":s,"aria-labelledby":[y,fe].filter(Boolean).join(" ")||void 0,"aria-describedby":n,"aria-required":F?"true":void 0,"aria-invalid":g?"true":void 0,onKeyDown:Be,onMouseDown:v||O?null:re,onBlur:ve,onFocus:P,...R,ownerState:J,className:ue(R.className,te.select,c),id:fe,children:Zl(Ne)?xr||(xr=r.jsx("span",{className:"notranslate","aria-hidden":!0,children:"​"})):Ne}),r.jsx(Jl,{"aria-invalid":g,value:Array.isArray(A)?A.join(","):A,name:f,ref:ae,"aria-hidden":!0,onChange:pe,tabIndex:-1,disabled:v,className:te.nativeInput,autoFocus:i,required:F,...H,ownerState:J}),r.jsx(Ql,{as:x,className:te.icon,ownerState:J}),r.jsx(Ol,{id:`menu-${f||""}`,anchorEl:U,open:Re,onClose:ge,anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"},...S,slotProps:{...S.slotProps,list:{"aria-labelledby":y,role:"listbox","aria-multiselectable":m?"true":void 0,disableListWrap:!0,id:xe,...S.MenuListProps},paper:{...Pe,style:{minWidth:Q,...Pe!=null?Pe.style:null}}},children:z})]})}),oc=e=>{const{classes:t}=e,n=Te({root:["root"]},rs,t);return{...t,...n}},Dn={name:"MuiSelect",slot:"Root",shouldForwardProp:e=>ct(e)&&e!=="variant"},nc=V(Nn,Dn)(""),rc=V(zn,Dn)(""),sc=V(An,Dn)(""),Wn=u.forwardRef(function(t,o){const n=$e({name:"MuiSelect",props:t}),{autoWidth:s=!1,children:i,classes:l={},className:a,defaultOpen:c=!1,displayEmpty:h=!1,IconComponent:p=_i,id:v,input:w,inputProps:g,label:x,labelId:j,MenuProps:y,multiple:S=!1,native:m=!1,onClose:f,onOpen:d,open:b,renderValue:C,SelectDisplayProps:P,variant:$="outlined",...L}=n,O=m?Wl:tc,N=Tt(),F=Dt({props:n,muiFormControl:N,states:["variant","error"]}),R=F.variant||$,E={...n,variant:R,classes:l},M=oc(E),{root:k,...W}=M,H=w||{standard:r.jsx(nc,{ownerState:E}),outlined:r.jsx(rc,{label:x,ownerState:E}),filled:r.jsx(sc,{ownerState:E})}[R],A=Ye(o,Qt(H));return r.jsx(u.Fragment,{children:u.cloneElement(H,{inputComponent:O,inputProps:{children:i,error:F.error,IconComponent:p,variant:R,type:void 0,multiple:S,...m?{id:v}:{autoWidth:s,defaultOpen:c,displayEmpty:h,labelId:j,MenuProps:y,onClose:f,onOpen:d,open:b,renderValue:C,SelectDisplayProps:{id:v,...P}},...g,classes:g?kn(W,g.classes):W,...w?w.props.inputProps:{}},...(S&&m||h)&&R==="outlined"?{notched:!0}:{},ref:A,className:ue(H.props.className,a,M.root),...!w&&{variant:R},...L})})});Wn.muiName="Select";function ic(e,t,o=(n,s)=>n===s){return e.length===t.length&&e.every((n,s)=>o(n,t[s]))}const ac=2;function Ht(e,t,o,n,s){return o===1?Math.min(e+t,s):Math.max(e-t,n)}function ss(e,t){return e-t}function wr(e,t){const{index:o}=e.reduce((n,s,i)=>{const l=Math.abs(t-s);return n===null||l<n.distance||l===n.distance?{distance:l,index:i}:n},null)??{};return o}function Po(e,t){if(t.current!==void 0&&e.changedTouches){const o=e;for(let n=0;n<o.changedTouches.length;n+=1){const s=o.changedTouches[n];if(s.identifier===t.current)return{x:s.clientX,y:s.clientY}}return!1}return{x:e.clientX,y:e.clientY}}function Vo(e,t,o){return(e-t)*100/(o-t)}function lc(e,t,o){return(o-t)*e+t}function cc(e){if(Math.abs(e)<1){const o=e.toExponential().split("e-"),n=o[0].split(".")[1];return(n?n.length:0)+parseInt(o[1],10)}const t=e.toString().split(".")[1];return t?t.length:0}function dc(e,t,o){const n=Math.round((e-o)/t)*t+o;return Number(n.toFixed(cc(t)))}function Sr({values:e,newValue:t,index:o}){const n=e.slice();return n[o]=t,n.sort(ss)}function Mo({sliderRef:e,activeIndex:t,setActive:o}){var s,i,l;const n=Xe(e.current);(!((s=e.current)!=null&&s.contains(n.activeElement))||Number((i=n==null?void 0:n.activeElement)==null?void 0:i.getAttribute("data-index"))!==t)&&((l=e.current)==null||l.querySelector(`[type="range"][data-index="${t}"]`).focus()),o&&o(t)}function Io(e,t){return typeof e=="number"&&typeof t=="number"?e===t:typeof e=="object"&&typeof t=="object"?ic(e,t):!1}const pc={horizontal:{offset:e=>({left:`${e}%`}),leap:e=>({width:`${e}%`})},"horizontal-reverse":{offset:e=>({right:`${e}%`}),leap:e=>({width:`${e}%`})},vertical:{offset:e=>({bottom:`${e}%`}),leap:e=>({height:`${e}%`})}},uc=e=>e;let Oo;function Cr(){return Oo===void 0&&(typeof CSS<"u"&&typeof CSS.supports=="function"?Oo=CSS.supports("touch-action","none"):Oo=!0),Oo}function fc(e){const{"aria-labelledby":t,defaultValue:o,disabled:n=!1,disableSwap:s=!1,isRtl:i=!1,marks:l=!1,max:a=100,min:c=0,name:h,onChange:p,onChangeCommitted:v,orientation:w="horizontal",rootRef:g,scale:x=uc,step:j=1,shiftStep:y=10,tabIndex:S,value:m}=e,f=u.useRef(void 0),[d,b]=u.useState(-1),[C,P]=u.useState(-1),[$,L]=u.useState(!1),O=u.useRef(0),N=u.useRef(null),[F,R]=go({controlled:m,default:o??c,name:"Slider"}),E=p&&((I,z,Q)=>{const se=I.nativeEvent||I,fe=new se.constructor(se.type,se);Object.defineProperty(fe,"target",{writable:!0,value:{value:z,name:h}}),N.current=z,p(fe,z,Q)}),M=Array.isArray(F);let k=M?F.slice().sort(ss):[F];k=k.map(I=>I==null?c:eo(I,c,a));const W=l===!0&&j!==null?[...Array(Math.floor((a-c)/j)+1)].map((I,z)=>({value:c+j*z})):l||[],H=W.map(I=>I.value),[A,_]=u.useState(-1),Y=u.useRef(null),ee=Ye(g,Y),ae=I=>z=>{var se;const Q=Number(z.currentTarget.getAttribute("data-index"));zo(z.target)&&_(Q),P(Q),(se=I==null?void 0:I.onFocus)==null||se.call(I,z)},de=I=>z=>{var Q;zo(z.target)||_(-1),P(-1),(Q=I==null?void 0:I.onBlur)==null||Q.call(I,z)},K=(I,z)=>{const Q=Number(I.currentTarget.getAttribute("data-index")),se=k[Q],fe=H.indexOf(se);let J=z;if(W&&j==null){const te=H[H.length-1];J>=te?J=te:J<=H[0]?J=H[0]:J=J<se?H[fe-1]:H[fe+1]}if(J=eo(J,c,a),M){s&&(J=eo(J,k[Q-1]||-1/0,k[Q+1]||1/0));const te=J;J=Sr({values:k,newValue:J,index:Q});let Pe=Q;s||(Pe=J.indexOf(te)),Mo({sliderRef:Y,activeIndex:Pe})}R(J),_(Q),E&&!Io(J,F)&&E(I,J,Q),v&&v(I,N.current??J)},le=I=>z=>{var Q;if(["ArrowUp","ArrowDown","ArrowLeft","ArrowRight","PageUp","PageDown","Home","End"].includes(z.key)){z.preventDefault();const se=Number(z.currentTarget.getAttribute("data-index")),fe=k[se];let J=null;if(j!=null){const te=z.shiftKey?y:j;switch(z.key){case"ArrowUp":J=Ht(fe,te,1,c,a);break;case"ArrowRight":J=Ht(fe,te,i?-1:1,c,a);break;case"ArrowDown":J=Ht(fe,te,-1,c,a);break;case"ArrowLeft":J=Ht(fe,te,i?1:-1,c,a);break;case"PageUp":J=Ht(fe,y,1,c,a);break;case"PageDown":J=Ht(fe,y,-1,c,a);break;case"Home":J=c;break;case"End":J=a;break}}else if(W){const te=H[H.length-1],Pe=H.indexOf(fe),xe=[i?"ArrowRight":"ArrowLeft","ArrowDown","PageDown","Home"],Oe=[i?"ArrowLeft":"ArrowRight","ArrowUp","PageUp","End"];xe.includes(z.key)?Pe===0?J=H[0]:J=H[Pe-1]:Oe.includes(z.key)&&(Pe===H.length-1?J=te:J=H[Pe+1])}J!=null&&K(z,J)}(Q=I==null?void 0:I.onKeyDown)==null||Q.call(I,z)};pt(()=>{var I;n&&Y.current.contains(document.activeElement)&&((I=document.activeElement)==null||I.blur())},[n]),n&&d!==-1&&b(-1),n&&A!==-1&&_(-1);const ce=I=>z=>{var Q;(Q=I.onChange)==null||Q.call(I,z),K(z,z.target.valueAsNumber)},T=u.useRef(void 0);let D=w;i&&w==="horizontal"&&(D+="-reverse");const B=({finger:I,move:z=!1})=>{const{current:Q}=Y,{width:se,height:fe,bottom:J,left:te}=Q.getBoundingClientRect();let Pe;D.startsWith("vertical")?Pe=(J-I.y)/fe:Pe=(I.x-te)/se,D.includes("-reverse")&&(Pe=1-Pe);let xe;if(xe=lc(Pe,c,a),j)xe=dc(xe,j,c);else{const Z=wr(H,xe);xe=H[Z]}xe=eo(xe,c,a);let Oe=0;if(M){z?Oe=T.current:Oe=wr(k,xe),s&&(xe=eo(xe,k[Oe-1]||-1/0,k[Oe+1]||1/0));const Z=xe;xe=Sr({values:k,newValue:xe,index:Oe}),s&&z||(Oe=xe.indexOf(Z),T.current=Oe)}return{newValue:xe,activeIndex:Oe}},X=dt(I=>{const z=Po(I,f);if(!z)return;if(O.current+=1,I.type==="mousemove"&&I.buttons===0){U(I);return}const{newValue:Q,activeIndex:se}=B({finger:z,move:!0});Mo({sliderRef:Y,activeIndex:se,setActive:b}),R(Q),!$&&O.current>ac&&L(!0),E&&!Io(Q,F)&&E(I,Q,se)}),U=dt(I=>{const z=Po(I,f);if(L(!1),!z)return;const{newValue:Q}=B({finger:z,move:!0});b(-1),I.type==="touchend"&&P(-1),v&&v(I,N.current??Q),f.current=void 0,re()}),ne=dt(I=>{if(n)return;Cr()||I.preventDefault();const z=I.changedTouches[0];z!=null&&(f.current=z.identifier);const Q=Po(I,f);if(Q!==!1){const{newValue:fe,activeIndex:J}=B({finger:Q});Mo({sliderRef:Y,activeIndex:J,setActive:b}),R(fe),E&&!Io(fe,F)&&E(I,fe,J)}O.current=0;const se=Xe(Y.current);se.addEventListener("touchmove",X,{passive:!0}),se.addEventListener("touchend",U,{passive:!0})}),re=u.useCallback(()=>{const I=Xe(Y.current);I.removeEventListener("mousemove",X),I.removeEventListener("mouseup",U),I.removeEventListener("touchmove",X),I.removeEventListener("touchend",U)},[U,X]);u.useEffect(()=>{const{current:I}=Y;return I.addEventListener("touchstart",ne,{passive:Cr()}),()=>{I.removeEventListener("touchstart",ne),re()}},[re,ne]),u.useEffect(()=>{n&&re()},[n,re]);const ge=I=>z=>{var fe;if((fe=I.onMouseDown)==null||fe.call(I,z),n||z.defaultPrevented||z.button!==0)return;z.preventDefault();const Q=Po(z,f);if(Q!==!1){const{newValue:J,activeIndex:te}=B({finger:Q});Mo({sliderRef:Y,activeIndex:te,setActive:b}),R(J),E&&!Io(J,F)&&E(z,J,te)}O.current=0;const se=Xe(Y.current);se.addEventListener("mousemove",X,{passive:!0}),se.addEventListener("mouseup",U)},Le=Vo(M?k[0]:c,c,a),pe=Vo(k[k.length-1],c,a)-Le,Se=(I={})=>{const z=No(I),Q={onMouseDown:ge(z||{})},se={...z,...Q};return{...I,ref:ee,...se}},Be=I=>z=>{var se;(se=I.onMouseOver)==null||se.call(I,z);const Q=Number(z.currentTarget.getAttribute("data-index"));P(Q)},Re=I=>z=>{var Q;(Q=I.onMouseLeave)==null||Q.call(I,z),P(-1)},ve=(I={})=>{const z=No(I),Q={onMouseOver:Be(z||{}),onMouseLeave:Re(z||{})};return{...I,...z,...Q}},Ne=I=>({pointerEvents:d!==-1&&d!==I?"none":void 0});let ze;return w==="vertical"&&(ze=i?"vertical-rl":"vertical-lr"),{active:d,axis:D,axisProps:pc,dragging:$,focusedThumbIndex:A,getHiddenInputProps:(I={})=>{const z=No(I),Q={onChange:ce(z||{}),onFocus:ae(z||{}),onBlur:de(z||{}),onKeyDown:le(z||{})},se={...z,...Q};return{tabIndex:S,"aria-labelledby":t,"aria-orientation":w,"aria-valuemax":x(a),"aria-valuemin":x(c),name:h,type:"range",min:e.min,max:e.max,step:e.step===null&&e.marks?"any":e.step??void 0,disabled:n,...I,...se,style:{...Yl,direction:i?"rtl":"ltr",width:"100%",height:"100%",writingMode:ze}}},getRootProps:Se,getThumbProps:ve,marks:W,open:C,range:M,rootRef:ee,trackLeap:pe,trackOffset:Le,values:k,getThumbStyle:Ne}}const hc=e=>!e||!Ft(e);function mc(e){return Ie("MuiSlider",e)}const st=we("MuiSlider",["root","active","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","disabled","dragging","focusVisible","mark","markActive","marked","markLabel","markLabelActive","rail","sizeSmall","thumb","thumbColorPrimary","thumbColorSecondary","thumbColorError","thumbColorSuccess","thumbColorInfo","thumbColorWarning","track","trackInverted","trackFalse","thumbSizeSmall","valueLabel","valueLabelOpen","valueLabelCircle","valueLabelLabel","vertical"]),gc=e=>{const{open:t}=e;return{offset:ue(t&&st.valueLabelOpen),circle:st.valueLabelCircle,label:st.valueLabelLabel}};function bc(e){const{children:t,className:o,value:n}=e,s=gc(e);return t?u.cloneElement(t,{className:t.props.className},r.jsxs(u.Fragment,{children:[t.props.children,r.jsx("span",{className:ue(s.offset,o),"aria-hidden":!0,children:r.jsx("span",{className:s.circle,children:r.jsx("span",{className:s.label,children:n})})})]})):null}function jr(e){return e}const vc=V("span",{name:"MuiSlider",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,t[`color${me(o.color)}`],o.size!=="medium"&&t[`size${me(o.size)}`],o.marked&&t.marked,o.orientation==="vertical"&&t.vertical,o.track==="inverted"&&t.trackInverted,o.track===!1&&t.trackFalse]}})(ye(({theme:e})=>({borderRadius:12,boxSizing:"content-box",display:"inline-block",position:"relative",cursor:"pointer",touchAction:"none",WebkitTapHighlightColor:"transparent","@media print":{colorAdjust:"exact"},[`&.${st.disabled}`]:{pointerEvents:"none",cursor:"default",color:(e.vars||e).palette.grey[400]},[`&.${st.dragging}`]:{[`& .${st.thumb}, & .${st.track}`]:{transition:"none"}},variants:[...Object.entries(e.palette).filter(jt()).map(([t])=>({props:{color:t},style:{color:(e.vars||e).palette[t].main}})),{props:{orientation:"horizontal"},style:{height:4,width:"100%",padding:"13px 0","@media (pointer: coarse)":{padding:"20px 0"}}},{props:{orientation:"horizontal",size:"small"},style:{height:2}},{props:{orientation:"horizontal",marked:!0},style:{marginBottom:20}},{props:{orientation:"vertical"},style:{height:"100%",width:4,padding:"0 13px","@media (pointer: coarse)":{padding:"0 20px"}}},{props:{orientation:"vertical",size:"small"},style:{width:2}},{props:{orientation:"vertical",marked:!0},style:{marginRight:44}}]}))),xc=V("span",{name:"MuiSlider",slot:"Rail"})({display:"block",position:"absolute",borderRadius:"inherit",backgroundColor:"currentColor",opacity:.38,variants:[{props:{orientation:"horizontal"},style:{width:"100%",height:"inherit",top:"50%",transform:"translateY(-50%)"}},{props:{orientation:"vertical"},style:{height:"100%",width:"inherit",left:"50%",transform:"translateX(-50%)"}},{props:{track:"inverted"},style:{opacity:1}}]}),yc=V("span",{name:"MuiSlider",slot:"Track"})(ye(({theme:e})=>({display:"block",position:"absolute",borderRadius:"inherit",border:"1px solid currentColor",backgroundColor:"currentColor",transition:e.transitions.create(["left","width","bottom","height"],{duration:e.transitions.duration.shortest}),variants:[{props:{size:"small"},style:{border:"none"}},{props:{orientation:"horizontal"},style:{height:"inherit",top:"50%",transform:"translateY(-50%)"}},{props:{orientation:"vertical"},style:{width:"inherit",left:"50%",transform:"translateX(-50%)"}},{props:{track:!1},style:{display:"none"}},...Object.entries(e.palette).filter(jt()).map(([t])=>({props:{color:t,track:"inverted"},style:{...e.vars?{backgroundColor:e.vars.palette.Slider[`${t}Track`],borderColor:e.vars.palette.Slider[`${t}Track`]}:{backgroundColor:pn(e.palette[t].main,.62),borderColor:pn(e.palette[t].main,.62),...e.applyStyles("dark",{backgroundColor:un(e.palette[t].main,.5)}),...e.applyStyles("dark",{borderColor:un(e.palette[t].main,.5)})}}}))]}))),wc=V("span",{name:"MuiSlider",slot:"Thumb",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.thumb,t[`thumbColor${me(o.color)}`],o.size!=="medium"&&t[`thumbSize${me(o.size)}`]]}})(ye(({theme:e})=>({position:"absolute",width:20,height:20,boxSizing:"border-box",borderRadius:"50%",outline:0,backgroundColor:"currentColor",display:"flex",alignItems:"center",justifyContent:"center",transition:e.transitions.create(["box-shadow","left","bottom"],{duration:e.transitions.duration.shortest}),"&::before":{position:"absolute",content:'""',borderRadius:"inherit",width:"100%",height:"100%",boxShadow:(e.vars||e).shadows[2]},"&::after":{position:"absolute",content:'""',borderRadius:"50%",width:42,height:42,top:"50%",left:"50%",transform:"translate(-50%, -50%)"},[`&.${st.disabled}`]:{"&:hover":{boxShadow:"none"}},variants:[{props:{size:"small"},style:{width:12,height:12,"&::before":{boxShadow:"none"}}},{props:{orientation:"horizontal"},style:{top:"50%",transform:"translate(-50%, -50%)"}},{props:{orientation:"vertical"},style:{left:"50%",transform:"translate(-50%, 50%)"}},...Object.entries(e.palette).filter(jt()).map(([t])=>({props:{color:t},style:{[`&:hover, &.${st.focusVisible}`]:{...e.vars?{boxShadow:`0px 0px 0px 8px rgba(${e.vars.palette[t].mainChannel} / 0.16)`}:{boxShadow:`0px 0px 0px 8px ${mt(e.palette[t].main,.16)}`},"@media (hover: none)":{boxShadow:"none"}},[`&.${st.active}`]:{...e.vars?{boxShadow:`0px 0px 0px 14px rgba(${e.vars.palette[t].mainChannel} / 0.16)`}:{boxShadow:`0px 0px 0px 14px ${mt(e.palette[t].main,.16)}`}}}}))]}))),Sc=V(bc,{name:"MuiSlider",slot:"ValueLabel"})(ye(({theme:e})=>({zIndex:1,whiteSpace:"nowrap",...e.typography.body2,fontWeight:500,transition:e.transitions.create(["transform"],{duration:e.transitions.duration.shortest}),position:"absolute",backgroundColor:(e.vars||e).palette.grey[600],borderRadius:2,color:(e.vars||e).palette.common.white,display:"flex",alignItems:"center",justifyContent:"center",padding:"0.25rem 0.75rem",variants:[{props:{orientation:"horizontal"},style:{transform:"translateY(-100%) scale(0)",top:"-10px",transformOrigin:"bottom center","&::before":{position:"absolute",content:'""',width:8,height:8,transform:"translate(-50%, 50%) rotate(45deg)",backgroundColor:"inherit",bottom:0,left:"50%"},[`&.${st.valueLabelOpen}`]:{transform:"translateY(-100%) scale(1)"}}},{props:{orientation:"vertical"},style:{transform:"translateY(-50%) scale(0)",right:"30px",top:"50%",transformOrigin:"right center","&::before":{position:"absolute",content:'""',width:8,height:8,transform:"translate(-50%, -50%) rotate(45deg)",backgroundColor:"inherit",right:-8,top:"50%"},[`&.${st.valueLabelOpen}`]:{transform:"translateY(-50%) scale(1)"}}},{props:{size:"small"},style:{fontSize:e.typography.pxToRem(12),padding:"0.25rem 0.5rem"}},{props:{orientation:"vertical",size:"small"},style:{right:"20px"}}]}))),Cc=V("span",{name:"MuiSlider",slot:"Mark",shouldForwardProp:e=>Tn(e)&&e!=="markActive",overridesResolver:(e,t)=>{const{markActive:o}=e;return[t.mark,o&&t.markActive]}})(ye(({theme:e})=>({position:"absolute",width:2,height:2,borderRadius:1,backgroundColor:"currentColor",variants:[{props:{orientation:"horizontal"},style:{top:"50%",transform:"translate(-1px, -50%)"}},{props:{orientation:"vertical"},style:{left:"50%",transform:"translate(-50%, 1px)"}},{props:{markActive:!0},style:{backgroundColor:(e.vars||e).palette.background.paper,opacity:.8}}]}))),jc=V("span",{name:"MuiSlider",slot:"MarkLabel",shouldForwardProp:e=>Tn(e)&&e!=="markLabelActive"})(ye(({theme:e})=>({...e.typography.body2,color:(e.vars||e).palette.text.secondary,position:"absolute",whiteSpace:"nowrap",variants:[{props:{orientation:"horizontal"},style:{top:30,transform:"translateX(-50%)","@media (pointer: coarse)":{top:40}}},{props:{orientation:"vertical"},style:{left:36,transform:"translateY(50%)","@media (pointer: coarse)":{left:44}}},{props:{markLabelActive:!0},style:{color:(e.vars||e).palette.text.primary}}]}))),kc=e=>{const{disabled:t,dragging:o,marked:n,orientation:s,track:i,classes:l,color:a,size:c}=e,h={root:["root",t&&"disabled",o&&"dragging",n&&"marked",s==="vertical"&&"vertical",i==="inverted"&&"trackInverted",i===!1&&"trackFalse",a&&`color${me(a)}`,c&&`size${me(c)}`],rail:["rail"],track:["track"],mark:["mark"],markActive:["markActive"],markLabel:["markLabel"],markLabelActive:["markLabelActive"],valueLabel:["valueLabel"],thumb:["thumb",t&&"disabled",c&&`thumbSize${me(c)}`,a&&`thumbColor${me(a)}`],active:["active"],disabled:["disabled"],focusVisible:["focusVisible"]};return Te(h,mc,l)},Tc=({children:e})=>e,cn=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiSlider"}),s=_t(),{"aria-label":i,"aria-valuetext":l,"aria-labelledby":a,component:c="span",components:h={},componentsProps:p={},color:v="primary",classes:w,className:g,disableSwap:x=!1,disabled:j=!1,getAriaLabel:y,getAriaValueText:S,marks:m=!1,max:f=100,min:d=0,name:b,onChange:C,onChangeCommitted:P,orientation:$="horizontal",shiftStep:L=10,size:O="medium",step:N=1,scale:F=jr,slotProps:R,slots:E,tabIndex:M,track:k="normal",value:W,valueLabelDisplay:H="off",valueLabelFormat:A=jr,..._}=n,Y={...n,isRtl:s,max:f,min:d,classes:w,disabled:j,disableSwap:x,orientation:$,marks:m,color:v,size:O,step:N,shiftStep:L,scale:F,track:k,valueLabelDisplay:H,valueLabelFormat:A},{axisProps:ee,getRootProps:ae,getHiddenInputProps:de,getThumbProps:K,open:le,active:ce,axis:T,focusedThumbIndex:D,range:B,dragging:X,marks:U,values:ne,trackOffset:re,trackLeap:ge,getThumbStyle:Le}=fc({...Y,rootRef:o});Y.marked=U.length>0&&U.some(Ae=>Ae.label),Y.dragging=X,Y.focusedThumbIndex=D;const pe=kc(Y),Se=(E==null?void 0:E.root)??h.Root??vc,Be=(E==null?void 0:E.rail)??h.Rail??xc,Re=(E==null?void 0:E.track)??h.Track??yc,ve=(E==null?void 0:E.thumb)??h.Thumb??wc,Ne=(E==null?void 0:E.valueLabel)??h.ValueLabel??Sc,ze=(E==null?void 0:E.mark)??h.Mark??Cc,Ee=(E==null?void 0:E.markLabel)??h.MarkLabel??jc,I=(E==null?void 0:E.input)??h.Input??"input",z=(R==null?void 0:R.root)??p.root,Q=(R==null?void 0:R.rail)??p.rail,se=(R==null?void 0:R.track)??p.track,fe=(R==null?void 0:R.thumb)??p.thumb,J=(R==null?void 0:R.valueLabel)??p.valueLabel,te=(R==null?void 0:R.mark)??p.mark,Pe=(R==null?void 0:R.markLabel)??p.markLabel,xe=(R==null?void 0:R.input)??p.input,Oe=Qe({elementType:Se,getSlotProps:ae,externalSlotProps:z,externalForwardedProps:_,additionalProps:{...hc(Se)&&{as:c}},ownerState:{...Y,...z==null?void 0:z.ownerState},className:[pe.root,g]}),Z=Qe({elementType:Be,externalSlotProps:Q,ownerState:Y,className:pe.rail}),Ce=Qe({elementType:Re,externalSlotProps:se,additionalProps:{style:{...ee[T].offset(re),...ee[T].leap(ge)}},ownerState:{...Y,...se==null?void 0:se.ownerState},className:pe.track}),Fe=Qe({elementType:ve,getSlotProps:K,externalSlotProps:fe,ownerState:{...Y,...fe==null?void 0:fe.ownerState},className:pe.thumb}),Ge=Qe({elementType:Ne,externalSlotProps:J,ownerState:{...Y,...J==null?void 0:J.ownerState},className:pe.valueLabel}),et=Qe({elementType:ze,externalSlotProps:te,ownerState:Y,className:pe.mark}),Rt=Qe({elementType:Ee,externalSlotProps:Pe,ownerState:Y,className:pe.markLabel}),Jt=Qe({elementType:I,getSlotProps:de,externalSlotProps:xe,ownerState:Y});return r.jsxs(Se,{...Oe,children:[r.jsx(Be,{...Z}),r.jsx(Re,{...Ce}),U.filter(Ae=>Ae.value>=d&&Ae.value<=f).map((Ae,He)=>{const Et=Vo(Ae.value,d,f),St=ee[T].offset(Et);let tt;return k===!1?tt=ne.includes(Ae.value):tt=k==="normal"&&(B?Ae.value>=ne[0]&&Ae.value<=ne[ne.length-1]:Ae.value<=ne[0])||k==="inverted"&&(B?Ae.value<=ne[0]||Ae.value>=ne[ne.length-1]:Ae.value>=ne[0]),r.jsxs(u.Fragment,{children:[r.jsx(ze,{"data-index":He,...et,...!Ft(ze)&&{markActive:tt},style:{...St,...et.style},className:ue(et.className,tt&&pe.markActive)}),Ae.label!=null?r.jsx(Ee,{"aria-hidden":!0,"data-index":He,...Rt,...!Ft(Ee)&&{markLabelActive:tt},style:{...St,...Rt.style},className:ue(pe.markLabel,Rt.className,tt&&pe.markLabelActive),children:Ae.label}):null]},He)}),ne.map((Ae,He)=>{const Et=Vo(Ae,d,f),St=ee[T].offset(Et),tt=H==="off"?Tc:Ne;return r.jsx(tt,{...!Ft(tt)&&{valueLabelFormat:A,valueLabelDisplay:H,value:typeof A=="function"?A(F(Ae),He):A,index:He,open:le===He||ce===He||H==="on",disabled:j},...Ge,children:r.jsx(ve,{"data-index":He,...Fe,className:ue(pe.thumb,Fe.className,ce===He&&pe.active,D===He&&pe.focusVisible),style:{...St,...Le(He),...Fe.style},children:r.jsx(I,{"data-index":He,"aria-label":y?y(He):i,"aria-valuenow":F(Ae),"aria-labelledby":a,"aria-valuetext":S?S(F(Ae),He):l,value:ne[He],...Jt})})},He)})]})});function Rc(e){return Ie("MuiTooltip",e)}const We=we("MuiTooltip",["popper","popperInteractive","popperArrow","popperClose","tooltip","tooltipArrow","touch","tooltipPlacementLeft","tooltipPlacementRight","tooltipPlacementTop","tooltipPlacementBottom","arrow"]);function Ec(e){return Math.round(e*1e5)/1e5}const Pc=e=>{const{classes:t,disableInteractive:o,arrow:n,touch:s,placement:i}=e,l={popper:["popper",!o&&"popperInteractive",n&&"popperArrow"],tooltip:["tooltip",n&&"tooltipArrow",s&&"touch",`tooltipPlacement${me(i.split("-")[0])}`],arrow:["arrow"]};return Te(l,Rc,t)},Mc=V(Xr,{name:"MuiTooltip",slot:"Popper",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.popper,!o.disableInteractive&&t.popperInteractive,o.arrow&&t.popperArrow,!o.open&&t.popperClose]}})(ye(({theme:e})=>({zIndex:(e.vars||e).zIndex.tooltip,pointerEvents:"none",variants:[{props:({ownerState:t})=>!t.disableInteractive,style:{pointerEvents:"auto"}},{props:({open:t})=>!t,style:{pointerEvents:"none"}},{props:({ownerState:t})=>t.arrow,style:{[`&[data-popper-placement*="bottom"] .${We.arrow}`]:{top:0,marginTop:"-0.71em","&::before":{transformOrigin:"0 100%"}},[`&[data-popper-placement*="top"] .${We.arrow}`]:{bottom:0,marginBottom:"-0.71em","&::before":{transformOrigin:"100% 0"}},[`&[data-popper-placement*="right"] .${We.arrow}`]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"100% 100%"}},[`&[data-popper-placement*="left"] .${We.arrow}`]:{height:"1em",width:"0.71em","&::before":{transformOrigin:"0 0"}}}},{props:({ownerState:t})=>t.arrow&&!t.isRtl,style:{[`&[data-popper-placement*="right"] .${We.arrow}`]:{left:0,marginLeft:"-0.71em"}}},{props:({ownerState:t})=>t.arrow&&!!t.isRtl,style:{[`&[data-popper-placement*="right"] .${We.arrow}`]:{right:0,marginRight:"-0.71em"}}},{props:({ownerState:t})=>t.arrow&&!t.isRtl,style:{[`&[data-popper-placement*="left"] .${We.arrow}`]:{right:0,marginRight:"-0.71em"}}},{props:({ownerState:t})=>t.arrow&&!!t.isRtl,style:{[`&[data-popper-placement*="left"] .${We.arrow}`]:{left:0,marginLeft:"-0.71em"}}}]}))),Ic=V("div",{name:"MuiTooltip",slot:"Tooltip",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.tooltip,o.touch&&t.touch,o.arrow&&t.tooltipArrow,t[`tooltipPlacement${me(o.placement.split("-")[0])}`]]}})(ye(({theme:e})=>({backgroundColor:e.vars?e.vars.palette.Tooltip.bg:mt(e.palette.grey[700],.92),borderRadius:(e.vars||e).shape.borderRadius,color:(e.vars||e).palette.common.white,fontFamily:e.typography.fontFamily,padding:"4px 8px",fontSize:e.typography.pxToRem(11),maxWidth:300,margin:2,wordWrap:"break-word",fontWeight:e.typography.fontWeightMedium,[`.${We.popper}[data-popper-placement*="left"] &`]:{transformOrigin:"right center"},[`.${We.popper}[data-popper-placement*="right"] &`]:{transformOrigin:"left center"},[`.${We.popper}[data-popper-placement*="top"] &`]:{transformOrigin:"center bottom",marginBottom:"14px"},[`.${We.popper}[data-popper-placement*="bottom"] &`]:{transformOrigin:"center top",marginTop:"14px"},variants:[{props:({ownerState:t})=>t.arrow,style:{position:"relative",margin:0}},{props:({ownerState:t})=>t.touch,style:{padding:"8px 16px",fontSize:e.typography.pxToRem(14),lineHeight:`${Ec(16/14)}em`,fontWeight:e.typography.fontWeightRegular}},{props:({ownerState:t})=>!t.isRtl,style:{[`.${We.popper}[data-popper-placement*="left"] &`]:{marginRight:"14px"},[`.${We.popper}[data-popper-placement*="right"] &`]:{marginLeft:"14px"}}},{props:({ownerState:t})=>!t.isRtl&&t.touch,style:{[`.${We.popper}[data-popper-placement*="left"] &`]:{marginRight:"24px"},[`.${We.popper}[data-popper-placement*="right"] &`]:{marginLeft:"24px"}}},{props:({ownerState:t})=>!!t.isRtl,style:{[`.${We.popper}[data-popper-placement*="left"] &`]:{marginLeft:"14px"},[`.${We.popper}[data-popper-placement*="right"] &`]:{marginRight:"14px"}}},{props:({ownerState:t})=>!!t.isRtl&&t.touch,style:{[`.${We.popper}[data-popper-placement*="left"] &`]:{marginLeft:"24px"},[`.${We.popper}[data-popper-placement*="right"] &`]:{marginRight:"24px"}}},{props:({ownerState:t})=>t.touch,style:{[`.${We.popper}[data-popper-placement*="top"] &`]:{marginBottom:"24px"}}},{props:({ownerState:t})=>t.touch,style:{[`.${We.popper}[data-popper-placement*="bottom"] &`]:{marginTop:"24px"}}}]}))),Oc=V("span",{name:"MuiTooltip",slot:"Arrow"})(ye(({theme:e})=>({overflow:"hidden",position:"absolute",width:"1em",height:"0.71em",boxSizing:"border-box",color:e.vars?e.vars.palette.Tooltip.bg:mt(e.palette.grey[700],.9),"&::before":{content:'""',margin:"auto",display:"block",width:"100%",height:"100%",backgroundColor:"currentColor",transform:"rotate(45deg)"}})));let $o=!1;const kr=new ms;let io={x:0,y:0};function Lo(e,t){return(o,...n)=>{t&&t(o,...n),e(o,...n)}}const Tr=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiTooltip"}),{arrow:s=!1,children:i,classes:l,components:a={},componentsProps:c={},describeChild:h=!1,disableFocusListener:p=!1,disableHoverListener:v=!1,disableInteractive:w=!1,disableTouchListener:g=!1,enterDelay:x=100,enterNextDelay:j=0,enterTouchDelay:y=700,followCursor:S=!1,id:m,leaveDelay:f=0,leaveTouchDelay:d=1500,onClose:b,onOpen:C,open:P,placement:$="bottom",PopperComponent:L,PopperProps:O={},slotProps:N={},slots:F={},title:R,TransitionComponent:E,TransitionProps:M,...k}=n,W=u.isValidElement(i)?i:r.jsx("span",{children:i}),H=xo(),A=_t(),[_,Y]=u.useState(),[ee,ae]=u.useState(null),de=u.useRef(!1),K=w||S,le=ao(),ce=ao(),T=ao(),D=ao(),[B,X]=go({controlled:P,default:!1,name:"Tooltip",state:"open"});let U=B;const ne=Xo(m),re=u.useRef(),ge=dt(()=>{re.current!==void 0&&(document.body.style.WebkitUserSelect=re.current,re.current=void 0),D.clear()});u.useEffect(()=>ge,[ge]);const Le=he=>{kr.clear(),$o=!0,X(!0),C&&!U&&C(he)},pe=dt(he=>{kr.start(800+f,()=>{$o=!1}),X(!1),b&&U&&b(he),le.start(H.transitions.duration.shortest,()=>{de.current=!1})}),Se=he=>{de.current&&he.type!=="touchstart"||(_&&_.removeAttribute("title"),ce.clear(),T.clear(),x||$o&&j?ce.start($o?j:x,()=>{Le(he)}):Le(he))},Be=he=>{ce.clear(),T.start(f,()=>{pe(he)})},[,Re]=u.useState(!1),ve=he=>{zo(he.target)||(Re(!1),Be(he))},Ne=he=>{_||Y(he.currentTarget),zo(he.target)&&(Re(!0),Se(he))},ze=he=>{de.current=!0;const ot=W.props;ot.onTouchStart&&ot.onTouchStart(he)},Ee=he=>{ze(he),T.clear(),le.clear(),ge(),re.current=document.body.style.WebkitUserSelect,document.body.style.WebkitUserSelect="none",D.start(y,()=>{document.body.style.WebkitUserSelect=re.current,Se(he)})},I=he=>{W.props.onTouchEnd&&W.props.onTouchEnd(he),ge(),T.start(d,()=>{pe(he)})};u.useEffect(()=>{if(!U)return;function he(ot){ot.key==="Escape"&&pe(ot)}return document.addEventListener("keydown",he),()=>{document.removeEventListener("keydown",he)}},[pe,U]);const z=Ye(Qt(W),Y,o);!R&&R!==0&&(U=!1);const Q=u.useRef(),se=he=>{const ot=W.props;ot.onMouseMove&&ot.onMouseMove(he),io={x:he.clientX,y:he.clientY},Q.current&&Q.current.update()},fe={},J=typeof R=="string";h?(fe.title=!U&&J&&!v?R:null,fe["aria-describedby"]=U?ne:null):(fe["aria-label"]=J?R:null,fe["aria-labelledby"]=U&&!J?ne:null);const te={...fe,...k,...W.props,className:ue(k.className,W.props.className),onTouchStart:ze,ref:z,...S?{onMouseMove:se}:{}},Pe={};g||(te.onTouchStart=Ee,te.onTouchEnd=I),v||(te.onMouseOver=Lo(Se,te.onMouseOver),te.onMouseLeave=Lo(Be,te.onMouseLeave),K||(Pe.onMouseOver=Se,Pe.onMouseLeave=Be)),p||(te.onFocus=Lo(Ne,te.onFocus),te.onBlur=Lo(ve,te.onBlur),K||(Pe.onFocus=Ne,Pe.onBlur=ve));const xe={...n,isRtl:A,arrow:s,disableInteractive:K,placement:$,PopperComponentProp:L,touch:de.current},Oe=typeof N.popper=="function"?N.popper(xe):N.popper,Z=u.useMemo(()=>{var ot,G;let he=[{name:"arrow",enabled:!!ee,options:{element:ee,padding:4}}];return(ot=O.popperOptions)!=null&&ot.modifiers&&(he=he.concat(O.popperOptions.modifiers)),(G=Oe==null?void 0:Oe.popperOptions)!=null&&G.modifiers&&(he=he.concat(Oe.popperOptions.modifiers)),{...O.popperOptions,...Oe==null?void 0:Oe.popperOptions,modifiers:he}},[ee,O.popperOptions,Oe==null?void 0:Oe.popperOptions]),Ce=Pc(xe),Fe=typeof N.transition=="function"?N.transition(xe):N.transition,Ge={slots:{popper:a.Popper,transition:a.Transition??E,tooltip:a.Tooltip,arrow:a.Arrow,...F},slotProps:{arrow:N.arrow??c.arrow,popper:{...O,...Oe??c.popper},tooltip:N.tooltip??c.tooltip,transition:{...M,...Fe??c.transition}}},[et,Rt]=be("popper",{elementType:Mc,externalForwardedProps:Ge,ownerState:xe,className:ue(Ce.popper,O==null?void 0:O.className)}),[Jt,Ae]=be("transition",{elementType:Uo,externalForwardedProps:Ge,ownerState:xe}),[He,Et]=be("tooltip",{elementType:Ic,className:Ce.tooltip,externalForwardedProps:Ge,ownerState:xe}),[St,tt]=be("arrow",{elementType:Oc,className:Ce.arrow,externalForwardedProps:Ge,ownerState:xe,ref:ae});return r.jsxs(u.Fragment,{children:[u.cloneElement(W,te),r.jsx(et,{as:L??Xr,placement:$,anchorEl:S?{getBoundingClientRect:()=>({top:io.y,left:io.x,right:io.x,bottom:io.y,width:0,height:0})}:_,popperRef:Q,open:_?U:!1,id:ne,transition:!0,...Pe,...Rt,popperOptions:Z,children:({TransitionProps:he})=>r.jsx(Jt,{timeout:H.transitions.duration.shorter,...he,...Ae,children:r.jsxs(He,{...Et,children:[R,s?r.jsx(St,{...tt}):null]})})})]})});function $c(e){return Ie("MuiSwitch",e)}const _e=we("MuiSwitch",["root","edgeStart","edgeEnd","switchBase","colorPrimary","colorSecondary","sizeSmall","sizeMedium","checked","disabled","input","thumb","track"]),Lc=e=>{const{classes:t,edge:o,size:n,color:s,checked:i,disabled:l}=e,a={root:["root",o&&`edge${me(o)}`,`size${me(n)}`],switchBase:["switchBase",`color${me(s)}`,i&&"checked",l&&"disabled"],thumb:["thumb"],track:["track"],input:["input"]},c=Te(a,$c,t);return{...t,...c}},Bc=V("span",{name:"MuiSwitch",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,o.edge&&t[`edge${me(o.edge)}`],t[`size${me(o.size)}`]]}})({display:"inline-flex",width:34+12*2,height:14+12*2,overflow:"hidden",padding:12,boxSizing:"border-box",position:"relative",flexShrink:0,zIndex:0,verticalAlign:"middle","@media print":{colorAdjust:"exact"},variants:[{props:{edge:"start"},style:{marginLeft:-8}},{props:{edge:"end"},style:{marginRight:-8}},{props:{size:"small"},style:{width:40,height:24,padding:7,[`& .${_e.thumb}`]:{width:16,height:16},[`& .${_e.switchBase}`]:{padding:4,[`&.${_e.checked}`]:{transform:"translateX(16px)"}}}}]}),Ac=V(sa,{name:"MuiSwitch",slot:"SwitchBase",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.switchBase,{[`& .${_e.input}`]:t.input},o.color!=="default"&&t[`color${me(o.color)}`]]}})(ye(({theme:e})=>({position:"absolute",top:0,left:0,zIndex:1,color:e.vars?e.vars.palette.Switch.defaultColor:`${e.palette.mode==="light"?e.palette.common.white:e.palette.grey[300]}`,transition:e.transitions.create(["left","transform"],{duration:e.transitions.duration.shortest}),[`&.${_e.checked}`]:{transform:"translateX(20px)"},[`&.${_e.disabled}`]:{color:e.vars?e.vars.palette.Switch.defaultDisabledColor:`${e.palette.mode==="light"?e.palette.grey[100]:e.palette.grey[600]}`},[`&.${_e.checked} + .${_e.track}`]:{opacity:.5},[`&.${_e.disabled} + .${_e.track}`]:{opacity:e.vars?e.vars.opacity.switchTrackDisabled:`${e.palette.mode==="light"?.12:.2}`},[`& .${_e.input}`]:{left:"-100%",width:"300%"}})),ye(({theme:e})=>({"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette.action.activeChannel} / ${e.vars.palette.action.hoverOpacity})`:mt(e.palette.action.active,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},variants:[...Object.entries(e.palette).filter(jt(["light"])).map(([t])=>({props:{color:t},style:{[`&.${_e.checked}`]:{color:(e.vars||e).palette[t].main,"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette[t].mainChannel} / ${e.vars.palette.action.hoverOpacity})`:mt(e.palette[t].main,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${_e.disabled}`]:{color:e.vars?e.vars.palette.Switch[`${t}DisabledColor`]:`${e.palette.mode==="light"?pn(e.palette[t].main,.62):un(e.palette[t].main,.55)}`}},[`&.${_e.checked} + .${_e.track}`]:{backgroundColor:(e.vars||e).palette[t].main}}}))]}))),Nc=V("span",{name:"MuiSwitch",slot:"Track"})(ye(({theme:e})=>({height:"100%",width:"100%",borderRadius:14/2,zIndex:-1,transition:e.transitions.create(["opacity","background-color"],{duration:e.transitions.duration.shortest}),backgroundColor:e.vars?e.vars.palette.common.onBackground:`${e.palette.mode==="light"?e.palette.common.black:e.palette.common.white}`,opacity:e.vars?e.vars.opacity.switchTrack:`${e.palette.mode==="light"?.38:.3}`}))),Fc=V("span",{name:"MuiSwitch",slot:"Thumb"})(ye(({theme:e})=>({boxShadow:(e.vars||e).shadows[1],backgroundColor:"currentColor",width:20,height:20,borderRadius:"50%"}))),yt=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiSwitch"}),{className:s,color:i="primary",edge:l=!1,size:a="medium",sx:c,slots:h={},slotProps:p={},...v}=n,w={...n,color:i,edge:l,size:a},g=Lc(w),x={slots:h,slotProps:p},[j,y]=be("root",{className:ue(g.root,s),elementType:Bc,externalForwardedProps:x,ownerState:w,additionalProps:{sx:c}}),[S,m]=be("thumb",{className:g.thumb,elementType:Fc,externalForwardedProps:x,ownerState:w}),f=r.jsx(S,{...m}),[d,b]=be("track",{className:g.track,elementType:Nc,externalForwardedProps:x,ownerState:w});return r.jsxs(j,{...y,children:[r.jsx(Ac,{type:"checkbox",icon:f,checkedIcon:f,ref:o,ownerState:w,...v,classes:{...g,root:g.switchBase},slots:{...h.switchBase&&{root:h.switchBase},...h.input&&{input:h.input}},slotProps:{...p.switchBase&&{root:typeof p.switchBase=="function"?p.switchBase(w):p.switchBase},...p.input&&{input:typeof p.input=="function"?p.input(w):p.input}}}),r.jsx(d,{...b})]})});function zc(e){return Ie("MuiTab",e)}const rt=we("MuiTab",["root","labelIcon","textColorInherit","textColorPrimary","textColorSecondary","selected","disabled","fullWidth","wrapped","iconWrapper","icon"]),Dc=e=>{const{classes:t,textColor:o,fullWidth:n,wrapped:s,icon:i,label:l,selected:a,disabled:c}=e,h={root:["root",i&&l&&"labelIcon",`textColor${me(o)}`,n&&"fullWidth",s&&"wrapped",a&&"selected",c&&"disabled"],icon:["iconWrapper","icon"]};return Te(h,zc,t)},Wc=V(Ko,{name:"MuiTab",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,o.label&&o.icon&&t.labelIcon,t[`textColor${me(o.textColor)}`],o.fullWidth&&t.fullWidth,o.wrapped&&t.wrapped,{[`& .${rt.iconWrapper}`]:t.iconWrapper},{[`& .${rt.icon}`]:t.icon}]}})(ye(({theme:e})=>({...e.typography.button,maxWidth:360,minWidth:90,position:"relative",minHeight:48,flexShrink:0,padding:"12px 16px",overflow:"hidden",whiteSpace:"normal",textAlign:"center",lineHeight:1.25,variants:[{props:({ownerState:t})=>t.label&&(t.iconPosition==="top"||t.iconPosition==="bottom"),style:{flexDirection:"column"}},{props:({ownerState:t})=>t.label&&t.iconPosition!=="top"&&t.iconPosition!=="bottom",style:{flexDirection:"row"}},{props:({ownerState:t})=>t.icon&&t.label,style:{minHeight:72,paddingTop:9,paddingBottom:9}},{props:({ownerState:t,iconPosition:o})=>t.icon&&t.label&&o==="top",style:{[`& > .${rt.icon}`]:{marginBottom:6}}},{props:({ownerState:t,iconPosition:o})=>t.icon&&t.label&&o==="bottom",style:{[`& > .${rt.icon}`]:{marginTop:6}}},{props:({ownerState:t,iconPosition:o})=>t.icon&&t.label&&o==="start",style:{[`& > .${rt.icon}`]:{marginRight:e.spacing(1)}}},{props:({ownerState:t,iconPosition:o})=>t.icon&&t.label&&o==="end",style:{[`& > .${rt.icon}`]:{marginLeft:e.spacing(1)}}},{props:{textColor:"inherit"},style:{color:"inherit",opacity:.6,[`&.${rt.selected}`]:{opacity:1},[`&.${rt.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity}}},{props:{textColor:"primary"},style:{color:(e.vars||e).palette.text.secondary,[`&.${rt.selected}`]:{color:(e.vars||e).palette.primary.main},[`&.${rt.disabled}`]:{color:(e.vars||e).palette.text.disabled}}},{props:{textColor:"secondary"},style:{color:(e.vars||e).palette.text.secondary,[`&.${rt.selected}`]:{color:(e.vars||e).palette.secondary.main},[`&.${rt.disabled}`]:{color:(e.vars||e).palette.text.disabled}}},{props:({ownerState:t})=>t.fullWidth,style:{flexShrink:1,flexGrow:1,flexBasis:0,maxWidth:"none"}},{props:({ownerState:t})=>t.wrapped,style:{fontSize:e.typography.pxToRem(12)}}]}))),Ot=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiTab"}),{className:s,disabled:i=!1,disableFocusRipple:l=!1,fullWidth:a,icon:c,iconPosition:h="top",indicator:p,label:v,onChange:w,onClick:g,onFocus:x,selected:j,selectionFollowsFocus:y,textColor:S="inherit",value:m,wrapped:f=!1,...d}=n,b={...n,disabled:i,disableFocusRipple:l,selected:j,icon:!!c,iconPosition:h,label:!!v,fullWidth:a,textColor:S,wrapped:f},C=Dc(b),P=c&&v&&u.isValidElement(c)?u.cloneElement(c,{className:ue(C.icon,c.props.className)}):c,$=O=>{!j&&w&&w(O,m),g&&g(O)},L=O=>{y&&!j&&w&&w(O,m),x&&x(O)};return r.jsxs(Wc,{focusRipple:!l,className:ue(C.root,s),ref:o,role:"tab","aria-selected":j,disabled:i,onClick:$,onFocus:L,ownerState:b,tabIndex:j?0:-1,...d,children:[h==="top"||h==="start"?r.jsxs(u.Fragment,{children:[P,v]}):r.jsxs(u.Fragment,{children:[v,P]}),p]})}),Hc=Ke(r.jsx("path",{d:"M15.41 16.09l-4.58-4.59 4.58-4.59L14 5.5l-6 6 6 6z"})),Uc=Ke(r.jsx("path",{d:"M8.59 16.34l4.58-4.59-4.58-4.59L10 5.75l6 6-6 6z"}));function Vc(e){return(1+Math.sin(Math.PI*e-Math.PI/2))/2}function qc(e,t,o,n={},s=()=>{}){const{ease:i=Vc,duration:l=300}=n;let a=null;const c=t[e];let h=!1;const p=()=>{h=!0},v=w=>{if(h){s(new Error("Animation cancelled"));return}a===null&&(a=w);const g=Math.min(1,(w-a)/l);if(t[e]=i(g)*(o-c)+c,g>=1){requestAnimationFrame(()=>{s(null)});return}requestAnimationFrame(v)};return c===o?(s(new Error("Element already at target position")),p):(requestAnimationFrame(v),p)}const Kc={width:99,height:99,position:"absolute",top:-9999,overflow:"scroll"};function Xc(e){const{onChange:t,...o}=e,n=u.useRef(),s=u.useRef(null),i=()=>{n.current=s.current.offsetHeight-s.current.clientHeight};return pt(()=>{const l=Go(()=>{const c=n.current;i(),c!==n.current&&t(n.current)}),a=ut(s.current);return a.addEventListener("resize",l),()=>{l.clear(),a.removeEventListener("resize",l)}},[t]),u.useEffect(()=>{i(),t(n.current)},[t]),r.jsx("div",{style:Kc,...o,ref:s})}function Gc(e){return Ie("MuiTabScrollButton",e)}const Yc=we("MuiTabScrollButton",["root","vertical","horizontal","disabled"]),_c=e=>{const{classes:t,orientation:o,disabled:n}=e;return Te({root:["root",o,n&&"disabled"]},Gc,t)},Qc=V(Ko,{name:"MuiTabScrollButton",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,o.orientation&&t[o.orientation]]}})({width:40,flexShrink:0,opacity:.8,[`&.${Yc.disabled}`]:{opacity:0},variants:[{props:{orientation:"vertical"},style:{width:"100%",height:40,"& svg":{transform:"var(--TabScrollButton-svgRotate)"}}}]}),Jc=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiTabScrollButton"}),{className:s,slots:i={},slotProps:l={},direction:a,orientation:c,disabled:h,...p}=n,v=_t(),w={isRtl:v,...n},g=_c(w),x=i.StartScrollButtonIcon??Hc,j=i.EndScrollButtonIcon??Uc,y=Qe({elementType:x,externalSlotProps:l.startScrollButtonIcon,additionalProps:{fontSize:"small"},ownerState:w}),S=Qe({elementType:j,externalSlotProps:l.endScrollButtonIcon,additionalProps:{fontSize:"small"},ownerState:w});return r.jsx(Qc,{component:"div",className:ue(g.root,s),ref:o,role:null,ownerState:w,tabIndex:null,...p,style:{...p.style,...c==="vertical"&&{"--TabScrollButton-svgRotate":`rotate(${v?-90:90}deg)`}},children:a==="left"?r.jsx(x,{...y}):r.jsx(j,{...S})})});function Zc(e){return Ie("MuiTabs",e)}const dn=we("MuiTabs",["root","vertical","list","flexContainer","flexContainerVertical","centered","scroller","fixed","scrollableX","scrollableY","hideScrollbar","scrollButtons","scrollButtonsHideMobile","indicator"]),Rr=(e,t)=>e===t?e.firstChild:t&&t.nextElementSibling?t.nextElementSibling:e.firstChild,Er=(e,t)=>e===t?e.lastChild:t&&t.previousElementSibling?t.previousElementSibling:e.lastChild,Bo=(e,t,o)=>{let n=!1,s=o(e,t);for(;s;){if(s===e.firstChild){if(n)return;n=!0}const i=s.disabled||s.getAttribute("aria-disabled")==="true";if(!s.hasAttribute("tabindex")||i)s=o(e,s);else{s.focus();return}}},ed=e=>{const{vertical:t,fixed:o,hideScrollbar:n,scrollableX:s,scrollableY:i,centered:l,scrollButtonsHideMobile:a,classes:c}=e;return Te({root:["root",t&&"vertical"],scroller:["scroller",o&&"fixed",n&&"hideScrollbar",s&&"scrollableX",i&&"scrollableY"],list:["list","flexContainer",t&&"flexContainerVertical",t&&"vertical",l&&"centered"],indicator:["indicator"],scrollButtons:["scrollButtons",a&&"scrollButtonsHideMobile"],scrollableX:[s&&"scrollableX"],hideScrollbar:[n&&"hideScrollbar"]},Zc,c)},td=V("div",{name:"MuiTabs",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[{[`& .${dn.scrollButtons}`]:t.scrollButtons},{[`& .${dn.scrollButtons}`]:o.scrollButtonsHideMobile&&t.scrollButtonsHideMobile},t.root,o.vertical&&t.vertical]}})(ye(({theme:e})=>({overflow:"hidden",minHeight:48,WebkitOverflowScrolling:"touch",display:"flex",variants:[{props:({ownerState:t})=>t.vertical,style:{flexDirection:"column"}},{props:({ownerState:t})=>t.scrollButtonsHideMobile,style:{[`& .${dn.scrollButtons}`]:{[e.breakpoints.down("sm")]:{display:"none"}}}}]}))),od=V("div",{name:"MuiTabs",slot:"Scroller",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.scroller,o.fixed&&t.fixed,o.hideScrollbar&&t.hideScrollbar,o.scrollableX&&t.scrollableX,o.scrollableY&&t.scrollableY]}})({position:"relative",display:"inline-block",flex:"1 1 auto",whiteSpace:"nowrap",variants:[{props:({ownerState:e})=>e.fixed,style:{overflowX:"hidden",width:"100%"}},{props:({ownerState:e})=>e.hideScrollbar,style:{scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}}},{props:({ownerState:e})=>e.scrollableX,style:{overflowX:"auto",overflowY:"hidden"}},{props:({ownerState:e})=>e.scrollableY,style:{overflowY:"auto",overflowX:"hidden"}}]}),nd=V("div",{name:"MuiTabs",slot:"List",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.list,t.flexContainer,o.vertical&&t.flexContainerVertical,o.centered&&t.centered]}})({display:"flex",variants:[{props:({ownerState:e})=>e.vertical,style:{flexDirection:"column"}},{props:({ownerState:e})=>e.centered,style:{justifyContent:"center"}}]}),rd=V("span",{name:"MuiTabs",slot:"Indicator"})(ye(({theme:e})=>({position:"absolute",height:2,bottom:0,width:"100%",transition:e.transitions.create(),variants:[{props:{indicatorColor:"primary"},style:{backgroundColor:(e.vars||e).palette.primary.main}},{props:{indicatorColor:"secondary"},style:{backgroundColor:(e.vars||e).palette.secondary.main}},{props:({ownerState:t})=>t.vertical,style:{height:"100%",width:2,right:0}}]}))),sd=V(Xc)({overflowX:"auto",overflowY:"hidden",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}}),Pr={},id=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiTabs"}),s=xo(),i=_t(),{"aria-label":l,"aria-labelledby":a,action:c,centered:h=!1,children:p,className:v,component:w="div",allowScrollButtonsMobile:g=!1,indicatorColor:x="primary",onChange:j,orientation:y="horizontal",ScrollButtonComponent:S,scrollButtons:m="auto",selectionFollowsFocus:f,slots:d={},slotProps:b={},TabIndicatorProps:C={},TabScrollButtonProps:P={},textColor:$="primary",value:L,variant:O="standard",visibleScrollbar:N=!1,...F}=n,R=O==="scrollable",E=y==="vertical",M=E?"scrollTop":"scrollLeft",k=E?"top":"left",W=E?"bottom":"right",H=E?"clientHeight":"clientWidth",A=E?"height":"width",_={...n,component:w,allowScrollButtonsMobile:g,indicatorColor:x,orientation:y,vertical:E,scrollButtons:m,textColor:$,variant:O,visibleScrollbar:N,fixed:!R,hideScrollbar:R&&!N,scrollableX:R&&!E,scrollableY:R&&E,centered:h&&!R,scrollButtonsHideMobile:!g},Y=ed(_),ee=Qe({elementType:d.StartScrollButtonIcon,externalSlotProps:b.startScrollButtonIcon,ownerState:_}),ae=Qe({elementType:d.EndScrollButtonIcon,externalSlotProps:b.endScrollButtonIcon,ownerState:_}),[de,K]=u.useState(!1),[le,ce]=u.useState(Pr),[T,D]=u.useState(!1),[B,X]=u.useState(!1),[U,ne]=u.useState(!1),[re,ge]=u.useState({overflow:"hidden",scrollbarWidth:0}),Le=new Map,pe=u.useRef(null),Se=u.useRef(null),Be={slots:d,slotProps:{indicator:C,scrollButton:P,...b}},Re=()=>{const G=pe.current;let oe;if(G){const Me=G.getBoundingClientRect();oe={clientWidth:G.clientWidth,scrollLeft:G.scrollLeft,scrollTop:G.scrollTop,scrollWidth:G.scrollWidth,top:Me.top,bottom:Me.bottom,left:Me.left,right:Me.right}}let ke;if(G&&L!==!1){const Me=Se.current.children;if(Me.length>0){const De=Me[Le.get(L)];ke=De?De.getBoundingClientRect():null}}return{tabsMeta:oe,tabMeta:ke}},ve=dt(()=>{const{tabsMeta:G,tabMeta:oe}=Re();let ke=0,Me;E?(Me="top",oe&&G&&(ke=oe.top-G.top+G.scrollTop)):(Me=i?"right":"left",oe&&G&&(ke=(i?-1:1)*(oe[Me]-G[Me]+G.scrollLeft)));const De={[Me]:ke,[A]:oe?oe[A]:0};if(typeof le[Me]!="number"||typeof le[A]!="number")ce(De);else{const ft=Math.abs(le[Me]-De[Me]),Pt=Math.abs(le[A]-De[A]);(ft>=1||Pt>=1)&&ce(De)}}),Ne=(G,{animation:oe=!0}={})=>{oe?qc(M,pe.current,G,{duration:s.transitions.duration.standard}):pe.current[M]=G},ze=G=>{let oe=pe.current[M];E?oe+=G:oe+=G*(i?-1:1),Ne(oe)},Ee=()=>{const G=pe.current[H];let oe=0;const ke=Array.from(Se.current.children);for(let Me=0;Me<ke.length;Me+=1){const De=ke[Me];if(oe+De[H]>G){Me===0&&(oe=G);break}oe+=De[H]}return oe},I=()=>{ze(-1*Ee())},z=()=>{ze(Ee())},[Q,{onChange:se,...fe}]=be("scrollbar",{className:ue(Y.scrollableX,Y.hideScrollbar),elementType:sd,shouldForwardComponentProp:!0,externalForwardedProps:Be,ownerState:_}),J=u.useCallback(G=>{se==null||se(G),ge({overflow:null,scrollbarWidth:G})},[se]),[te,Pe]=be("scrollButtons",{className:ue(Y.scrollButtons,P.className),elementType:Jc,externalForwardedProps:Be,ownerState:_,additionalProps:{orientation:y,slots:{StartScrollButtonIcon:d.startScrollButtonIcon||d.StartScrollButtonIcon,EndScrollButtonIcon:d.endScrollButtonIcon||d.EndScrollButtonIcon},slotProps:{startScrollButtonIcon:ee,endScrollButtonIcon:ae}}}),xe=()=>{const G={};G.scrollbarSizeListener=R?r.jsx(Q,{...fe,onChange:J}):null;const ke=R&&(m==="auto"&&(T||B)||m===!0);return G.scrollButtonStart=ke?r.jsx(te,{direction:i?"right":"left",onClick:I,disabled:!T,...Pe}):null,G.scrollButtonEnd=ke?r.jsx(te,{direction:i?"left":"right",onClick:z,disabled:!B,...Pe}):null,G},Oe=dt(G=>{const{tabsMeta:oe,tabMeta:ke}=Re();if(!(!ke||!oe)){if(ke[k]<oe[k]){const Me=oe[M]+(ke[k]-oe[k]);Ne(Me,{animation:G})}else if(ke[W]>oe[W]){const Me=oe[M]+(ke[W]-oe[W]);Ne(Me,{animation:G})}}}),Z=dt(()=>{R&&m!==!1&&ne(!U)});u.useEffect(()=>{const G=Go(()=>{pe.current&&ve()});let oe;const ke=ft=>{ft.forEach(Pt=>{Pt.removedNodes.forEach(Zt=>{oe==null||oe.unobserve(Zt)}),Pt.addedNodes.forEach(Zt=>{oe==null||oe.observe(Zt)})}),G(),Z()},Me=ut(pe.current);Me.addEventListener("resize",G);let De;return typeof ResizeObserver<"u"&&(oe=new ResizeObserver(G),Array.from(Se.current.children).forEach(ft=>{oe.observe(ft)})),typeof MutationObserver<"u"&&(De=new MutationObserver(ke),De.observe(Se.current,{childList:!0})),()=>{G.clear(),Me.removeEventListener("resize",G),De==null||De.disconnect(),oe==null||oe.disconnect()}},[ve,Z]),u.useEffect(()=>{const G=Array.from(Se.current.children),oe=G.length;if(typeof IntersectionObserver<"u"&&oe>0&&R&&m!==!1){const ke=G[0],Me=G[oe-1],De={root:pe.current,threshold:.99},ft=en=>{D(!en[0].isIntersecting)},Pt=new IntersectionObserver(ft,De);Pt.observe(ke);const Zt=en=>{X(!en[0].isIntersecting)},Hn=new IntersectionObserver(Zt,De);return Hn.observe(Me),()=>{Pt.disconnect(),Hn.disconnect()}}},[R,m,U,p==null?void 0:p.length]),u.useEffect(()=>{K(!0)},[]),u.useEffect(()=>{ve()}),u.useEffect(()=>{Oe(Pr!==le)},[Oe,le]),u.useImperativeHandle(c,()=>({updateIndicator:ve,updateScrollButtons:Z}),[ve,Z]);const[Ce,Fe]=be("indicator",{className:ue(Y.indicator,C.className),elementType:rd,externalForwardedProps:Be,ownerState:_,additionalProps:{style:le}}),Ge=r.jsx(Ce,{...Fe});let et=0;const Rt=u.Children.map(p,G=>{if(!u.isValidElement(G))return null;const oe=G.props.value===void 0?et:G.props.value;Le.set(oe,et);const ke=oe===L;return et+=1,u.cloneElement(G,{fullWidth:O==="fullWidth",indicator:ke&&!de&&Ge,selected:ke,selectionFollowsFocus:f,onChange:j,textColor:$,value:oe,...et===1&&L===!1&&!G.props.tabIndex?{tabIndex:0}:{}})}),Jt=G=>{if(G.altKey||G.shiftKey||G.ctrlKey||G.metaKey)return;const oe=Se.current,ke=Xe(oe).activeElement;if(ke.getAttribute("role")!=="tab")return;let De=y==="horizontal"?"ArrowLeft":"ArrowUp",ft=y==="horizontal"?"ArrowRight":"ArrowDown";switch(y==="horizontal"&&i&&(De="ArrowRight",ft="ArrowLeft"),G.key){case De:G.preventDefault(),Bo(oe,ke,Er);break;case ft:G.preventDefault(),Bo(oe,ke,Rr);break;case"Home":G.preventDefault(),Bo(oe,null,Rr);break;case"End":G.preventDefault(),Bo(oe,null,Er);break}},Ae=xe(),[He,Et]=be("root",{ref:o,className:ue(Y.root,v),elementType:td,externalForwardedProps:{...Be,...F,component:w},ownerState:_}),[St,tt]=be("scroller",{ref:pe,className:Y.scroller,elementType:od,externalForwardedProps:Be,ownerState:_,additionalProps:{style:{overflow:re.overflow,[E?`margin${i?"Left":"Right"}`:"marginBottom"]:N?void 0:-re.scrollbarWidth}}}),[he,ot]=be("list",{ref:Se,className:ue(Y.list,Y.flexContainer),elementType:nd,externalForwardedProps:Be,ownerState:_,getSlotProps:G=>({...G,onKeyDown:oe=>{var ke;Jt(oe),(ke=G.onKeyDown)==null||ke.call(G,oe)}})});return r.jsxs(He,{...Et,children:[Ae.scrollButtonStart,Ae.scrollbarSizeListener,r.jsxs(St,{...tt,children:[r.jsx(he,{"aria-label":l,"aria-labelledby":a,"aria-orientation":y==="vertical"?"vertical":null,role:"tablist",...ot,children:Rt}),de&&Ge]}),Ae.scrollButtonEnd]})});function ad(e){return Ie("MuiTextField",e)}we("MuiTextField",["root"]);const ld={standard:Nn,filled:An,outlined:zn},cd=e=>{const{classes:t}=e;return Te({root:["root"]},ad,t)},dd=V(Jr,{name:"MuiTextField",slot:"Root"})({}),At=u.forwardRef(function(t,o){const n=$e({props:t,name:"MuiTextField"}),{autoComplete:s,autoFocus:i=!1,children:l,className:a,color:c="primary",defaultValue:h,disabled:p=!1,error:v=!1,FormHelperTextProps:w,fullWidth:g=!1,helperText:x,id:j,InputLabelProps:y,inputProps:S,InputProps:m,inputRef:f,label:d,maxRows:b,minRows:C,multiline:P=!1,name:$,onBlur:L,onChange:O,onFocus:N,placeholder:F,required:R=!1,rows:E,select:M=!1,SelectProps:k,slots:W={},slotProps:H={},type:A,value:_,variant:Y="outlined",...ee}=n,ae={...n,autoFocus:i,color:c,disabled:p,error:v,fullWidth:g,multiline:P,required:R,select:M,variant:Y},de=cd(ae),K=Xo(j),le=x&&K?`${K}-helper-text`:void 0,ce=d&&K?`${K}-label`:void 0,T=ld[Y],D={slots:W,slotProps:{input:m,inputLabel:y,htmlInput:S,formHelperText:w,select:k,...H}},B={},X=D.slotProps.inputLabel;Y==="outlined"&&(X&&typeof X.shrink<"u"&&(B.notched=X.shrink),B.label=d),M&&((!k||!k.native)&&(B.id=void 0),B["aria-describedby"]=void 0);const[U,ne]=be("root",{elementType:dd,shouldForwardComponentProp:!0,externalForwardedProps:{...D,...ee},ownerState:ae,className:ue(de.root,a),ref:o,additionalProps:{disabled:p,error:v,fullWidth:g,required:R,color:c,variant:Y}}),[re,ge]=be("input",{elementType:T,externalForwardedProps:D,additionalProps:B,ownerState:ae}),[Le,pe]=be("inputLabel",{elementType:Zr,externalForwardedProps:D,ownerState:ae}),[Se,Be]=be("htmlInput",{elementType:"input",externalForwardedProps:D,ownerState:ae}),[Re,ve]=be("formHelperText",{elementType:nl,externalForwardedProps:D,ownerState:ae}),[Ne,ze]=be("select",{elementType:Wn,externalForwardedProps:D,ownerState:ae}),Ee=r.jsx(re,{"aria-describedby":le,autoComplete:s,autoFocus:i,defaultValue:h,fullWidth:g,multiline:P,name:$,rows:E,maxRows:b,minRows:C,type:A,value:_,id:K,inputRef:f,onBlur:L,onChange:O,onFocus:N,placeholder:F,inputProps:Be,slots:{input:W.htmlInput?Se:void 0},...ge});return r.jsxs(U,{...ne,children:[d!=null&&d!==""&&r.jsx(Le,{htmlFor:K,id:ce,...pe,children:d}),M?r.jsx(Ne,{"aria-describedby":le,id:K,labelId:ce,value:_,input:Ee,...ze,children:l}):Ee,x&&r.jsx(Re,{id:le,...ve,children:x})]})}),qo=Ke(r.jsx("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"})),pd=Ke(r.jsx("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2M9 17H7v-5h2zm4 0h-2v-3h2zm0-5h-2v-2h2zm4 5h-2V7h2z"})),ud=Ke(r.jsx("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})),mo=Ke(r.jsx("path",{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6zM19 4h-3.5l-1-1h-5l-1 1H5v2h14z"})),fd=Ke(r.jsx("path",{d:"M5 20h14v-2H5zM19 9h-4V3H9v6H5l7 7z"})),hd=Ke(r.jsx("path",{d:"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75z"})),Mr=Ke(r.jsx("path",{d:"m12 21.35-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54z"})),md=Ke(r.jsx("path",{d:"M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2m6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1z"})),gd=Ke(r.jsx("path",{d:"M12 2C6.49 2 2 6.49 2 12s4.49 10 10 10c1.38 0 2.5-1.12 2.5-2.5 0-.61-.23-1.2-.64-1.67-.08-.1-.13-.21-.13-.33 0-.28.22-.5.5-.5H16c3.31 0 6-2.69 6-6 0-4.96-4.49-9-10-9m5.5 11c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5m-3-4c-.83 0-1.5-.67-1.5-1.5S13.67 6 14.5 6s1.5.67 1.5 1.5S15.33 9 14.5 9M5 11.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5S7.33 13 6.5 13 5 12.33 5 11.5m6-4c0 .83-.67 1.5-1.5 1.5S8 8.33 8 7.5 8.67 6 9.5 6s1.5.67 1.5 1.5"})),Ir=Ke([r.jsx("path",{d:"M13 8.57c-.79 0-1.43.64-1.43 1.43s.64 1.43 1.43 1.43 1.43-.64 1.43-1.43-.64-1.43-1.43-1.43"},"0"),r.jsx("path",{d:"M13 3C9.25 3 6.2 5.94 6.02 9.64L4.1 12.2c-.25.33-.01.8.4.8H6v3c0 1.1.9 2 2 2h1v3h7v-4.68c2.36-1.12 4-3.53 4-6.32 0-3.87-3.13-7-7-7m3 7c0 .13-.01.26-.02.39l.83.66c.08.06.1.16.05.25l-.8 1.39c-.05.09-.16.12-.24.09l-.99-.4c-.21.16-.43.29-.67.39L14 13.83c-.01.1-.1.17-.2.17h-1.6c-.1 0-.18-.07-.2-.17l-.15-1.06c-.25-.1-.47-.23-.68-.39l-.99.4c-.09.03-.2 0-.25-.09l-.8-1.39c-.05-.08-.03-.19.05-.25l.84-.66c-.01-.13-.02-.26-.02-.39s.02-.27.04-.39l-.85-.66c-.08-.06-.1-.16-.05-.26l.8-1.38c.05-.09.15-.12.24-.09l1 .4c.2-.15.43-.29.67-.39L12 6.17c.02-.1.1-.17.2-.17h1.6c.1 0 .18.07.2.17l.15 1.06c.24.1.46.23.67.39l1-.4c.09-.03.2 0 .24.09l.8 1.38c.05.09.03.2-.05.26l-.85.66c.03.12.04.25.04.39"},"1")]),bd=Ke(r.jsx("path",{d:"M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9m-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8z"})),is=Ke(r.jsx("path",{d:"M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3m3-10H5V5h10z"})),vd=Ke(r.jsx("path",{d:"M12 1 3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11z"})),Ao=Ke([r.jsx("circle",{cx:"12",cy:"6",r:"2"},"0"),r.jsx("path",{d:"M21 16v-2c-2.24 0-4.16-.96-5.6-2.68l-1.34-1.6c-.38-.46-.94-.72-1.53-.72h-1.05c-.59 0-1.15.26-1.53.72l-1.34 1.6C7.16 13.04 5.24 14 3 14v2c2.77 0 5.19-1.17 7-3.25V15l-3.88 1.55c-.67.27-1.12.93-1.12 1.66C5 19.2 5.8 20 6.79 20H9v-.5c0-1.38 1.12-2.5 2.5-2.5h3c.28 0 .5.22.5.5s-.22.5-.5.5h-3c-.83 0-1.5.67-1.5 1.5v.5h7.21c.99 0 1.79-.8 1.79-1.79 0-.73-.45-1.39-1.12-1.66L14 15v-2.25c1.81 2.08 4.23 3.25 7 3.25"},"1")]),xd=Ke(r.jsx("path",{d:"M2 20h20v-4H2zm2-3h2v2H4zM2 4v4h20V4zm4 3H4V5h2zm-4 7h20v-4H2zm2-3h2v2H4z"})),yd=Ke(r.jsx("path",{d:"M5 20h14v-2H5zm0-10h4v6h6v-6h4l-7-7z"})),wd=({open:e,onClose:t,onSave:o,editingMethod:n})=>{const[s,i]=u.useState({name:"",icon:"📈",description:"",questions:[],totalMaxScore:0,isCustom:!0}),[l,a]=u.useState([]);u.useEffect(()=>{i(n||{name:"",icon:"📈",description:"",questions:[{text:"",type:"radio",options:[{value:"option_0",label:"",weight:10},{value:"option_1",label:"",weight:5}]},{text:"",type:"radio",options:[{value:"option_0",label:"",weight:10},{value:"option_1",label:"",weight:5}]},{text:"",type:"radio",options:[{value:"option_0",label:"",weight:10},{value:"option_1",label:"",weight:5}]}],totalMaxScore:0,isCustom:!0})},[n,e]);const c=()=>{s.questions.length<7&&i(m=>({...m,questions:[...m.questions,{text:"",type:"radio",options:[{value:"option_0",label:"",weight:10},{value:"option_1",label:"",weight:5}]}]}))},h=m=>{s.questions.length>3&&i(f=>({...f,questions:f.questions.filter((d,b)=>b!==m)}))},p=(m,f,d)=>{i(b=>({...b,questions:b.questions.map((C,P)=>P===m?{...C,[f]:d}:C)}))},v=m=>{const f=s.questions[m];if(f.options&&f.options.length<6){const d={value:`option_${f.options.length}`,label:"",weight:5};p(m,"options",[...f.options,d])}},w=(m,f)=>{const d=s.questions[m];if(d.options&&d.options.length>2){const b=d.options.filter((C,P)=>P!==f);p(m,"options",b)}},g=(m,f,d,b)=>{const C=s.questions[m];if(C.options){const P=C.options.map(($,L)=>L===f?{...$,[d]:b}:$);p(m,"options",P)}},x=()=>{let m=0;return s.questions.forEach(f=>{if(f.type==="textarea")m+=10;else if(f.options&&f.options.length>0){const d=Math.max(...f.options.map(b=>b.weight));m+=d}}),m},j=()=>{const m=[];(!s.name||s.name.length<3)&&m.push("Tên phương pháp phải có ít nhất 3 ký tự"),(!s.description||s.description.length<10)&&m.push("Mô tả phải có ít nhất 10 ký tự"),s.icon||m.push("Vui lòng chọn icon"),s.questions.length<3&&m.push("Cần ít nhất 3 câu hỏi"),s.questions.forEach((d,b)=>{(!d.text||d.text.length<5)&&m.push(`Câu hỏi ${b+1}: Nội dung phải có ít nhất 5 ký tự`),d.type!=="textarea"&&(!d.options||d.options.length<2?m.push(`Câu hỏi ${b+1}: Cần ít nhất 2 đáp án`):d.options.forEach((C,P)=>{(!C.label||C.label.length<2)&&m.push(`Câu hỏi ${b+1}, Đáp án ${P+1}: Nội dung quá ngắn`),(C.weight<0||C.weight>50)&&m.push(`Câu hỏi ${b+1}, Đáp án ${P+1}: Điểm phải từ 0-50`)}))});const f=x();return f<50&&m.push("Tổng điểm tối đa quá thấp (< 50)"),f>200&&m.push("Tổng điểm tối đa quá cao (> 200)"),m},y=()=>{const m=j();if(m.length>0){a(m);return}const f={...s,id:(n==null?void 0:n.id)||`custom_${Date.now()}`,totalMaxScore:x(),createdAt:(n==null?void 0:n.createdAt)||new Date().toISOString()};o(f),t()},S=()=>{a([]),t()};return r.jsxs(xn,{open:e,onClose:S,maxWidth:"md",fullWidth:!0,children:[r.jsx(Sn,{children:r.jsxs(je,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[r.jsx(q,{variant:"h6",children:n?"✏️ Chỉnh sửa phương pháp":"⚙️ Tạo phương pháp tùy chỉnh"}),r.jsx(on,{onClick:S,children:r.jsx(ud,{})})]})}),r.jsxs(wn,{children:[l.length>0&&r.jsx(fn,{severity:"error",sx:{mb:2},children:r.jsx("ul",{style:{margin:0,paddingLeft:20},children:l.map((m,f)=>r.jsx("li",{children:m},f))})}),r.jsxs(ie,{container:!0,spacing:2,children:[r.jsx(ie,{size:12,children:r.jsx(Ve,{variant:"outlined",children:r.jsxs(qe,{children:[r.jsx(q,{variant:"h6",sx:{mb:2},children:"📊 Thông tin cơ bản"}),r.jsxs(ie,{container:!0,spacing:2,children:[r.jsx(ie,{size:{xs:12,md:8},children:r.jsx(At,{fullWidth:!0,label:"Tên phương pháp",value:s.name,onChange:m=>i(f=>({...f,name:m.target.value})),placeholder:"Ví dụ: Fibonacci Retracement Strategy"})}),r.jsx(ie,{size:{xs:12,md:4},children:r.jsx(At,{fullWidth:!0,label:"Icon (emoji)",value:s.icon,onChange:m=>i(f=>({...f,icon:m.target.value})),inputProps:{maxLength:2,style:{textAlign:"center",fontSize:"20px"}}})}),r.jsx(ie,{size:12,children:r.jsx(At,{fullWidth:!0,multiline:!0,rows:3,label:"Mô tả phương pháp",value:s.description,onChange:m=>i(f=>({...f,description:m.target.value})),placeholder:"Mô tả ngắn gọn về phương pháp giao dịch này..."})})]})]})})}),r.jsx(ie,{size:12,children:r.jsx(Ve,{variant:"outlined",children:r.jsxs(qe,{children:[r.jsxs(je,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[r.jsx(q,{variant:"h6",children:"❓ Câu hỏi phân tích"}),r.jsxs(je,{sx:{display:"flex",gap:1,alignItems:"center"},children:[r.jsx(Ct,{label:`${s.questions.length} câu hỏi`,size:"small"}),r.jsx(Ct,{label:`${x()} điểm`,size:"small",color:"primary"}),r.jsx(Ue,{size:"small",startIcon:r.jsx(qo,{}),onClick:c,disabled:s.questions.length>=7,children:"Thêm câu hỏi"})]})]}),s.questions.map((m,f)=>r.jsx(Ve,{variant:"outlined",sx:{mb:2},children:r.jsxs(qe,{children:[r.jsxs(je,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[r.jsxs(q,{variant:"subtitle1",sx:{fontWeight:"bold"},children:["Câu hỏi ",f+1]}),s.questions.length>3&&r.jsx(on,{size:"small",color:"error",onClick:()=>h(f),children:r.jsx(mo,{})})]}),r.jsxs(ie,{container:!0,spacing:2,children:[r.jsx(ie,{size:{xs:12,md:8},children:r.jsx(At,{fullWidth:!0,label:"Nội dung câu hỏi",value:m.text,onChange:d=>p(f,"text",d.target.value),placeholder:"Ví dụ: Có tín hiệu divergence rõ ràng không?"})}),r.jsx(ie,{size:{xs:12,md:4},children:r.jsxs(Jr,{fullWidth:!0,children:[r.jsx(Zr,{children:"Loại câu hỏi"}),r.jsxs(Wn,{value:m.type,label:"Loại câu hỏi",onChange:d=>{const b=d.target.value;b==="textarea"?(p(f,"type",b),p(f,"options",void 0),p(f,"placeholder","Mô tả chi tiết setup...")):(p(f,"type",b),m.options||p(f,"options",[{value:"option_0",label:"",weight:10},{value:"option_1",label:"",weight:5}]))},children:[r.jsx(ln,{value:"radio",children:"Multiple Choice (Radio)"}),r.jsx(ln,{value:"select",children:"Dropdown (Select)"}),r.jsx(ln,{value:"textarea",children:"Mô tả chi tiết (Textarea)"})]})]})}),m.type!=="textarea"&&m.options&&r.jsxs(ie,{size:12,children:[r.jsxs(je,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[r.jsx(q,{variant:"body2",color:"text.secondary",children:"Đáp án:"}),r.jsx(Ue,{size:"small",startIcon:r.jsx(qo,{}),onClick:()=>v(f),disabled:!m.options||m.options.length>=6,children:"Thêm đáp án"})]}),m.options.map((d,b)=>r.jsxs(je,{sx:{display:"flex",gap:1,mb:1,alignItems:"center"},children:[r.jsx(At,{size:"small",placeholder:"Nội dung đáp án...",value:d.label,onChange:C=>g(f,b,"label",C.target.value),sx:{flex:1}}),r.jsx(At,{size:"small",type:"number",placeholder:"Điểm",value:d.weight,onChange:C=>g(f,b,"weight",parseInt(C.target.value)||0),inputProps:{min:0,max:50},sx:{width:80}}),m.options&&m.options.length>2&&r.jsx(on,{size:"small",color:"error",onClick:()=>w(f,b),children:r.jsx(mo,{})})]},b))]})]})]})},f))]})})})]})]}),r.jsxs(yn,{children:[r.jsx(Ue,{onClick:S,children:"Hủy"}),r.jsx(Ue,{variant:"contained",startIcon:r.jsx(is,{}),onClick:y,children:n?"Cập nhật":"Tạo phương pháp"})]})]})},Sd=gs({palette:{primary:{main:"#007bff"},secondary:{main:"#6c757d"}}});function $t(e){const{children:t,value:o,index:n,...s}=e;return r.jsx("div",{role:"tabpanel",hidden:o!==n,id:`options-tabpanel-${n}`,"aria-labelledby":`options-tab-${n}`,...s,children:o===n&&r.jsx(je,{sx:{p:3},children:t})})}const Cd=()=>{var ce;const[e,t]=u.useState(0),[o,n]=u.useState([]),[s,i]=u.useState([]),[l,a]=u.useState({}),[c,h]=u.useState(!1),[p,v]=u.useState(null),[w,g]=u.useState(!1),[x,j]=u.useState(null),[y,S]=u.useState(!1),[m,f]=u.useState("mindfulness"),[d,b]=u.useState({theme:"light",language:"vi",fontSize:14,notifications:!0,soundAlerts:!0,desktopNotifications:!0,emailNotifications:!1,defaultTradeAmount:10,defaultTradeDuration:5,maxDailyTrades:50,riskWarnings:!0,autoStopLoss:!0,requireConfirmation:!0,sessionTimeout:30,autoLock:!1,autoBackup:!0,backupFrequency:"daily",maxBackups:10,apiUrl:"http://localhost:3001"}),[C,P]=u.useState(!1),[$,L]=u.useState(!1),[O,N]=u.useState(null);u.useEffect(()=>{Y(),E(),F()},[]);const F=async()=>{try{(await chrome.storage.local.get(["requestedTab"])).requestedTab==="meditation"&&(t(4),await chrome.storage.local.remove(["requestedTab"]))}catch{console.log("Could not check requested tab")}},R=()=>[{id:"bollinger_bands",name:"Bollinger Bands Breakout",icon:"📈",description:"Giao dịch dựa trên việc giá breakout khỏi dải Bollinger Bands với volume xác nhận",questions:5},{id:"rsi_divergence",name:"RSI Divergence",icon:"📊",description:"Tìm kiếm sự phân kỳ giữa giá và RSI để dự đoán sự đảo chiều xu hướng",questions:5},{id:"support_resistance",name:"Support & Resistance",icon:"🔄",description:"Giao dịch tại các vùng support/resistance quan trọng với xác nhận price action",questions:5},{id:"moving_average",name:"Moving Average Crossover",icon:"📉",description:"Sử dụng sự cắt nhau của các đường MA để xác định xu hướng và điểm vào lệnh",questions:5},{id:"price_action",name:"Price Action Patterns",icon:"🕯️",description:"Phân tích các mô hình nến Nhật và price action để dự đoán hướng di chuyển",questions:5}],E=async()=>{try{const D=await fetch("http://localhost:3001/customMethods");if(D.ok){const B=await D.json();i(B)}}catch{console.log("No custom methods found or server not available")}const T=await chrome.storage.local.get(["methodSettings"]);if(T.methodSettings)a(T.methodSettings);else{const D={};R().forEach(B=>{D[B.id]=!0}),a(D)}},M=T=>l[T]!==!1,k=async(T,D)=>{const B={...l,[T]:D};a(B),await chrome.storage.local.set({methodSettings:B});try{(await chrome.tabs.query({url:"*://binomo1.com/trading*"})).forEach(U=>{U.id&&chrome.tabs.sendMessage(U.id,{action:"updateMethodSettings",methodSettings:B}).catch(ne=>{console.log(`Could not update content script in tab ${U.id}:`,ne)})})}catch(X){console.log("Could not query tabs or update content script:",X)}},W=T=>{console.log("Editing method:",T.name),v(T),h(!0)},H=T=>{j(T),g(!0)},A=async()=>{if(!x)return;const T=x.id;P(!0),g(!1);try{const D=await fetch(`http://localhost:3001/customMethods/${T}`,{method:"DELETE"});if(D.ok){i(X=>X.filter(U=>U.id!==T));const B={...l};delete B[T],a(B),await chrome.storage.local.set({methodSettings:B});try{(await chrome.tabs.query({url:"*://binomo1.com/trading*"})).forEach(U=>{U.id&&chrome.tabs.sendMessage(U.id,{action:"updateMethodSettings",methodSettings:B}).catch(ne=>{console.log(`Could not update content script in tab ${U.id}:`,ne)})})}catch(X){console.log("Could not query tabs or update content script:",X)}L(!0),setTimeout(()=>L(!1),3e3)}else throw new Error(`HTTP ${D.status}`)}catch(D){console.error("Error deleting method:",D),N("Không thể xóa phương pháp. Hãy kiểm tra JSON Server đang chạy!"),setTimeout(()=>N(null),5e3)}finally{P(!1),j(null)}},_=async T=>{try{let D;if(T.id&&p?D=await fetch(`http://localhost:3001/customMethods/${T.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(T)}):D=await fetch("http://localhost:3001/customMethods",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(T)}),D.ok){if(await E(),!p){const B={...l,[T.id]:!0};a(B),await chrome.storage.local.set({methodSettings:B})}try{(await chrome.tabs.query({url:"*://binomo1.com/trading*"})).forEach(X=>{X.id&&chrome.tabs.sendMessage(X.id,{action:"updateMethodSettings",methodSettings:l}).catch(U=>{console.log(`Could not update content script in tab ${X.id}:`,U)})})}catch(B){console.log("Could not query tabs or update content script:",B)}L(!0),setTimeout(()=>L(!1),3e3)}else throw new Error("Failed to save method")}catch{N("Không thể lưu phương pháp. Hãy kiểm tra JSON Server đang chạy!"),setTimeout(()=>N(null),5e3)}},Y=async()=>{try{const T=await chrome.storage.local.get("userSettings");T.userSettings&&b(D=>({...D,...T.userSettings}))}catch(T){console.error("Error loading settings:",T)}},ee=(T,D)=>{b(B=>({...B,[T]:D}))},ae=async()=>{P(!0),N(null);try{await chrome.storage.local.set({userSettings:d}),L(!0),setTimeout(()=>L(!1),3e3)}catch(T){N("Có lỗi xảy ra khi lưu cài đặt"),console.error("Error saving settings:",T)}finally{P(!1)}},de=async()=>{if(!confirm("Bạn có chắc chắn muốn reset tất cả cài đặt về mặc định không?"))return;b({theme:"light",language:"vi",fontSize:14,notifications:!0,soundAlerts:!0,desktopNotifications:!0,emailNotifications:!1,defaultTradeAmount:10,defaultTradeDuration:5,maxDailyTrades:50,riskWarnings:!0,autoStopLoss:!0,requireConfirmation:!0,sessionTimeout:30,autoLock:!1,autoBackup:!0,backupFrequency:"daily",maxBackups:10,apiUrl:"http://localhost:3001"}),await ae()},K=()=>{const T=JSON.stringify(d,null,2),D=new Blob([T],{type:"application/json"}),B=URL.createObjectURL(D),X=document.createElement("a");X.href=B,X.download=`binomo-settings-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(X),X.click(),document.body.removeChild(X),URL.revokeObjectURL(B)},le=()=>{const T=document.createElement("input");T.type="file",T.accept=".json",T.onchange=D=>{var X;const B=(X=D.target.files)==null?void 0:X[0];if(B){const U=new FileReader;U.onload=ne=>{var re;try{const ge=JSON.parse((re=ne.target)==null?void 0:re.result);b(Le=>({...Le,...ge})),alert("Cài đặt đã được import thành công!")}catch{alert("File không hợp lệ!")}},U.readAsText(B)}},T.click()};return r.jsxs(bs,{theme:Sd,children:[r.jsx(vs,{}),r.jsxs(je,{sx:{maxWidth:1200,mx:"auto",p:3},children:[r.jsx(ie,{container:!0,spacing:3,sx:{mb:4},children:r.jsx(ie,{size:12,children:r.jsxs(Vt,{sx:{p:3,textAlign:"center",background:"linear-gradient(45deg, #007bff 30%, #0056b3 90%)"},children:[r.jsxs(q,{variant:"h4",sx:{color:"white",mb:1,display:"flex",alignItems:"center",justifyContent:"center",gap:2},children:[r.jsx(xs,{fontSize:"large"}),"🎯 Binomo Trading Assistant - Cài đặt"]}),r.jsx(q,{variant:"body1",sx:{color:"rgba(255,255,255,0.8)"},children:"Tùy chỉnh extension theo nhu cầu của bạn"})]})})}),$&&r.jsx(ie,{container:!0,spacing:3,sx:{mb:3},children:r.jsx(ie,{size:12,children:r.jsx(fn,{severity:"success",children:"Cài đặt đã được lưu thành công!"})})}),O&&r.jsx(ie,{container:!0,spacing:3,sx:{mb:3},children:r.jsx(ie,{size:12,children:r.jsx(fn,{severity:"error",children:O})})}),r.jsx(ie,{container:!0,spacing:3,children:r.jsx(ie,{size:12,children:r.jsx(Vt,{sx:{mb:3},children:r.jsxs(id,{value:e,onChange:(T,D)=>t(D),variant:"scrollable",scrollButtons:"auto",children:[r.jsx(Ot,{icon:r.jsx(gd,{}),label:"Giao diện"}),r.jsx(Ot,{icon:r.jsx(md,{}),label:"Thông báo"}),r.jsx(Ot,{icon:r.jsx(ys,{}),label:"Giao dịch"}),r.jsx(Ot,{icon:r.jsx(pd,{}),label:"Phương pháp"}),r.jsx(Ot,{icon:r.jsx(Ao,{}),label:"Thiền"}),r.jsx(Ot,{icon:r.jsx(vd,{}),label:"Bảo mật"}),r.jsx(Ot,{icon:r.jsx(xd,{}),label:"Dữ liệu"})]})})})}),r.jsx(ie,{container:!0,spacing:3,children:r.jsxs(ie,{size:12,children:[r.jsx($t,{value:e,index:0,children:r.jsxs(ie,{container:!0,spacing:3,children:[r.jsx(ie,{size:12,md:6,children:r.jsx(Ve,{children:r.jsxs(qe,{children:[r.jsx(q,{variant:"h6",sx:{mb:2},children:"🎨 Theme"}),r.jsxs(Co,{fullWidth:!0,sx:{mb:2},children:[r.jsx(Ue,{variant:d.theme==="light"?"contained":"outlined",onClick:()=>ee("theme","light"),children:"Light"}),r.jsx(Ue,{variant:d.theme==="dark"?"contained":"outlined",onClick:()=>ee("theme","dark"),children:"Dark"})]}),r.jsxs(q,{variant:"body2",sx:{mb:1},children:["Font Size: ",d.fontSize,"px"]}),r.jsx(cn,{value:d.fontSize,onChange:(T,D)=>ee("fontSize",D),min:12,max:20,step:1,marks:!0,valueLabelDisplay:"auto"})]})})}),r.jsx(ie,{size:12,md:6,children:r.jsx(Ve,{children:r.jsxs(qe,{children:[r.jsx(q,{variant:"h6",sx:{mb:2},children:"🌐 Ngôn ngữ"}),r.jsxs(Co,{fullWidth:!0,children:[r.jsx(Ue,{variant:d.language==="vi"?"contained":"outlined",onClick:()=>ee("language","vi"),children:"Tiếng Việt"}),r.jsx(Ue,{variant:d.language==="en"?"contained":"outlined",onClick:()=>ee("language","en"),children:"English"})]})]})})})]})}),r.jsx($t,{value:e,index:1,children:r.jsx(ie,{container:!0,spacing:3,children:r.jsx(ie,{size:12,md:6,children:r.jsx(Ve,{children:r.jsxs(qe,{children:[r.jsx(q,{variant:"h6",sx:{mb:2},children:"🔔 Thông báo"}),r.jsxs(Mt,{spacing:2,children:[r.jsx(xt,{control:r.jsx(yt,{checked:d.notifications,onChange:T=>ee("notifications",T.target.checked)}),label:"Bật thông báo"}),r.jsx(xt,{control:r.jsx(yt,{checked:d.soundAlerts,onChange:T=>ee("soundAlerts",T.target.checked)}),label:"Âm thanh cảnh báo"}),r.jsx(xt,{control:r.jsx(yt,{checked:d.desktopNotifications,onChange:T=>ee("desktopNotifications",T.target.checked)}),label:"Thông báo desktop"})]})]})})})})}),r.jsx($t,{value:e,index:2,children:r.jsxs(ie,{container:!0,spacing:3,children:[r.jsx(ie,{size:12,md:6,children:r.jsx(Ve,{children:r.jsxs(qe,{children:[r.jsx(q,{variant:"h6",sx:{mb:2},children:"💰 Giao dịch mặc định"}),r.jsxs(Mt,{spacing:3,children:[r.jsxs(je,{children:[r.jsx(q,{variant:"body2",sx:{mb:1},children:"Số tiền mặc định ($):"}),r.jsx(Co,{fullWidth:!0,children:[1,5,10,25,50].map(T=>r.jsxs(Ue,{variant:d.defaultTradeAmount===T?"contained":"outlined",onClick:()=>ee("defaultTradeAmount",T),children:["$",T]},T))})]}),r.jsxs(je,{children:[r.jsx(q,{variant:"body2",sx:{mb:1},children:"Thời gian mặc định:"}),r.jsx(Co,{fullWidth:!0,children:[1,5,15,30,60].map(T=>r.jsxs(Ue,{variant:d.defaultTradeDuration===T?"contained":"outlined",onClick:()=>ee("defaultTradeDuration",T),children:[T,"m"]},T))})]})]})]})})}),r.jsx(ie,{size:12,md:6,children:r.jsx(Ve,{children:r.jsxs(qe,{children:[r.jsx(q,{variant:"h6",sx:{mb:2},children:"⚠️ Quản lý rủi ro"}),r.jsxs(Mt,{spacing:2,children:[r.jsx(xt,{control:r.jsx(yt,{checked:d.riskWarnings,onChange:T=>ee("riskWarnings",T.target.checked)}),label:"Cảnh báo rủi ro"}),r.jsx(xt,{control:r.jsx(yt,{checked:d.autoStopLoss,onChange:T=>ee("autoStopLoss",T.target.checked)}),label:"Tự động dừng khi thua lỗ"}),r.jsxs(je,{children:[r.jsxs(q,{variant:"body2",sx:{mb:1},children:["Giới hạn lệnh/ngày: ",d.maxDailyTrades]}),r.jsx(cn,{value:d.maxDailyTrades,onChange:(T,D)=>ee("maxDailyTrades",D),min:10,max:100,step:5,marks:!0,valueLabelDisplay:"auto"})]})]})]})})})]})}),r.jsx($t,{value:e,index:3,children:r.jsxs(ie,{container:!0,spacing:3,children:[r.jsx(ie,{size:12,children:r.jsx(Ve,{children:r.jsxs(qe,{children:[r.jsxs(je,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[r.jsx(q,{variant:"h6",children:"📊 Phương pháp có sẵn"}),r.jsx(Ct,{label:`${R().length} phương pháp`,color:"primary"})]}),r.jsx(ie,{container:!0,spacing:2,children:R().map(T=>{var D;return r.jsx(ie,{size:12,md:6,lg:4,children:r.jsx(Ve,{variant:"outlined",sx:{height:"100%"},children:r.jsxs(qe,{children:[r.jsxs(je,{sx:{display:"flex",alignItems:"center",mb:1},children:[r.jsx(q,{variant:"h6",sx:{mr:1},children:T.icon}),r.jsx(q,{variant:"subtitle1",sx:{fontWeight:"bold"},children:T.name})]}),r.jsx(q,{variant:"body2",color:"text.secondary",sx:{mb:2},children:T.description}),r.jsxs(je,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[r.jsx(Ct,{label:`${((D=T.questions)==null?void 0:D.length)||5} câu hỏi`,size:"small",variant:"outlined"}),r.jsx(xt,{control:r.jsx(yt,{checked:M(T.id),onChange:B=>k(T.id,B.target.checked),size:"small"}),label:"",sx:{m:0}})]})]})})},T.id)})})]})})}),r.jsx(ie,{size:12,children:r.jsx(Ve,{children:r.jsxs(qe,{children:[r.jsxs(je,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[r.jsx(q,{variant:"h6",children:"⚙️ Phương pháp tùy chỉnh"}),r.jsxs(je,{sx:{display:"flex",gap:1},children:[r.jsx(Ct,{label:`${s.length} phương pháp`,color:"secondary"}),r.jsx(Ue,{variant:"contained",size:"small",startIcon:r.jsx(qo,{}),onClick:()=>h(!0),children:"Tạo mới"})]})]}),s.length===0?r.jsxs(je,{sx:{textAlign:"center",py:4},children:[r.jsx(q,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"Chưa có phương pháp tùy chỉnh nào"}),r.jsx(Ue,{variant:"outlined",startIcon:r.jsx(qo,{}),onClick:()=>h(!0),children:"Tạo phương pháp đầu tiên"})]}):r.jsx(ie,{container:!0,spacing:2,children:s.map(T=>{var D;return r.jsx(ie,{size:12,md:6,lg:4,children:r.jsx(Ve,{variant:"outlined",sx:{height:"100%",border:"2px solid #4caf50"},children:r.jsxs(qe,{children:[r.jsxs(je,{sx:{display:"flex",alignItems:"center",mb:1},children:[r.jsx(q,{variant:"h6",sx:{mr:1},children:T.icon}),r.jsx(q,{variant:"subtitle1",sx:{fontWeight:"bold",flex:1},children:T.name}),r.jsx(Ct,{label:"CUSTOM",size:"small",color:"success"})]}),r.jsx(q,{variant:"body2",color:"text.secondary",sx:{mb:2},children:T.description}),r.jsxs(je,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[r.jsx(Ct,{label:`${((D=T.questions)==null?void 0:D.length)||0} câu hỏi`,size:"small",variant:"outlined"}),r.jsx(Ct,{label:`${T.totalMaxScore||0} điểm`,size:"small",variant:"outlined"})]}),r.jsxs(je,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[r.jsxs(je,{sx:{display:"flex",gap:1},children:[r.jsx(Tr,{title:"Chỉnh sửa phương pháp này",children:r.jsx("span",{children:r.jsx(Ue,{size:"small",startIcon:r.jsx(hd,{}),onClick:()=>W(T),disabled:C,children:"Sửa"})})}),r.jsx(Tr,{title:"Xóa phương pháp này vĩnh viễn",children:r.jsx("span",{children:r.jsx(Ue,{size:"small",color:"error",startIcon:r.jsx(mo,{}),onClick:()=>H(T),disabled:C,children:"Xóa"})})})]}),r.jsx(xt,{control:r.jsx(yt,{checked:M(T.id),onChange:B=>k(T.id,B.target.checked),size:"small"}),label:"",sx:{m:0}})]})]})})},T.id)})})]})})})]})}),r.jsx($t,{value:e,index:4,children:r.jsx(ie,{container:!0,spacing:3,children:r.jsx(ie,{size:12,children:r.jsx(Ve,{children:r.jsxs(qe,{children:[r.jsxs(je,{sx:{textAlign:"center",mb:3},children:[r.jsx(Ao,{sx:{fontSize:64,color:"primary.main",mb:2}}),r.jsx(q,{variant:"h4",gutterBottom:!0,children:"🧘‍♂️ Thiền Chánh Niệm"}),r.jsx(q,{variant:"body1",color:"text.secondary",children:"Thời gian để tâm hồn nghỉ ngơi và tái tạo năng lượng tích cực"})]}),r.jsxs(ie,{container:!0,spacing:3,children:[r.jsx(ie,{size:12,md:4,children:r.jsx(Ve,{variant:"outlined",sx:{height:"100%",cursor:"pointer"},onClick:()=>{f("mindfulness"),S(!0)},children:r.jsxs(qe,{sx:{textAlign:"center"},children:[r.jsx(Ao,{sx:{fontSize:48,color:"primary.main",mb:2}}),r.jsx(q,{variant:"h6",gutterBottom:!0,children:"Thiền Chánh Niệm"}),r.jsx(q,{variant:"body2",color:"text.secondary",children:"Quan sát hơi thở, nhận biết cảm xúc và suy nghĩ mà không phán xét"})]})})}),r.jsx(ie,{size:12,md:4,children:r.jsx(Ve,{variant:"outlined",sx:{height:"100%",cursor:"pointer"},onClick:()=>{f("gratitude"),S(!0)},children:r.jsxs(qe,{sx:{textAlign:"center"},children:[r.jsx(Mr,{sx:{fontSize:48,color:"secondary.main",mb:2}}),r.jsx(q,{variant:"h6",gutterBottom:!0,children:"Thiền Biết Ơn"}),r.jsx(q,{variant:"body2",color:"text.secondary",children:"Cảm ơn những gì đã có, nuôi dưỡng lòng biết ơn và sự hài lòng"})]})})}),r.jsx(ie,{size:12,md:4,children:r.jsx(Ve,{variant:"outlined",sx:{height:"100%",cursor:"pointer"},onClick:()=>{f("compassion"),S(!0)},children:r.jsxs(qe,{sx:{textAlign:"center"},children:[r.jsx(Ir,{sx:{fontSize:48,color:"success.main",mb:2}}),r.jsx(q,{variant:"h6",gutterBottom:!0,children:"Thiền Từ Bi"}),r.jsx(q,{variant:"body2",color:"text.secondary",children:"Gửi tình yêu thương đến bản thân và mọi người xung quanh"})]})})})]}),r.jsx(je,{sx:{mt:3,p:2,bgcolor:"info.light",borderRadius:1},children:r.jsxs(q,{variant:"body2",color:"info.contrastText",children:["💡 ",r.jsx("strong",{children:"Lưu ý:"})," Khi tâm lý không phù hợp để giao dịch, hãy dành thời gian thiền để tái tạo năng lượng tích cực. Giao dịch với tâm hồn bình an sẽ mang lại kết quả tốt hơn."]})})]})})})})}),r.jsx($t,{value:e,index:5,children:r.jsx(ie,{container:!0,spacing:3,children:r.jsx(ie,{size:12,md:6,children:r.jsx(Ve,{children:r.jsxs(qe,{children:[r.jsx(q,{variant:"h6",sx:{mb:2},children:"🔒 Bảo mật"}),r.jsxs(Mt,{spacing:2,children:[r.jsx(xt,{control:r.jsx(yt,{checked:d.requireConfirmation,onChange:T=>ee("requireConfirmation",T.target.checked)}),label:"Yêu cầu xác nhận trước khi giao dịch"}),r.jsxs(je,{children:[r.jsxs(q,{variant:"body2",sx:{mb:1},children:["Session timeout: ",d.sessionTimeout," phút"]}),r.jsx(cn,{value:d.sessionTimeout,onChange:(T,D)=>ee("sessionTimeout",D),min:5,max:120,step:5,marks:!0,valueLabelDisplay:"auto"})]})]})]})})})})}),r.jsx($t,{value:e,index:6,children:r.jsxs(ie,{container:!0,spacing:3,children:[r.jsx(ie,{size:12,md:6,children:r.jsx(Ve,{children:r.jsxs(qe,{children:[r.jsx(q,{variant:"h6",sx:{mb:2},children:"💾 Backup & Storage"}),r.jsxs(Mt,{spacing:2,children:[r.jsx(xt,{control:r.jsx(yt,{checked:d.autoBackup,onChange:T=>ee("autoBackup",T.target.checked)}),label:"Tự động backup"}),r.jsx(At,{label:"API URL",value:d.apiUrl,onChange:T=>ee("apiUrl",T.target.value),fullWidth:!0,size:"small"})]})]})})}),r.jsx(ie,{size:12,md:6,children:r.jsx(Ve,{children:r.jsxs(qe,{children:[r.jsx(q,{variant:"h6",sx:{mb:2},children:"📤 Import/Export"}),r.jsxs(Mt,{spacing:2,children:[r.jsx(Ue,{variant:"outlined",startIcon:r.jsx(fd,{}),onClick:K,fullWidth:!0,children:"Xuất cài đặt"}),r.jsx(Ue,{variant:"outlined",startIcon:r.jsx(yd,{}),onClick:le,fullWidth:!0,children:"Nhập cài đặt"})]})]})})})]})})]})}),r.jsx(ie,{container:!0,spacing:3,sx:{mt:3},children:r.jsx(ie,{size:12,children:r.jsx(Vt,{sx:{p:3},children:r.jsxs(Mt,{direction:"row",spacing:2,justifyContent:"center",children:[r.jsx(Ue,{variant:"outlined",color:"secondary",startIcon:r.jsx(bd,{}),onClick:de,children:"Reset về mặc định"}),r.jsx(Ue,{variant:"contained",startIcon:r.jsx(is,{}),onClick:ae,disabled:C,size:"large",children:C?"Đang lưu...":"Lưu cài đặt"})]})})})}),r.jsx(wd,{open:c,onClose:()=>{h(!1),v(null)},onSave:_,editingMethod:p}),r.jsxs(xn,{open:y,onClose:()=>S(!1),maxWidth:"md",fullWidth:!0,children:[r.jsx(Sn,{children:r.jsxs(je,{sx:{display:"flex",alignItems:"center",gap:1},children:[m==="mindfulness"&&r.jsx(Ao,{color:"primary"}),m==="gratitude"&&r.jsx(Mr,{color:"secondary"}),m==="compassion"&&r.jsx(Ir,{color:"success"}),r.jsxs(q,{variant:"h6",children:[m==="mindfulness"&&"🧘‍♂️ Thiền Chánh Niệm",m==="gratitude"&&"🙏 Thiền Biết Ơn",m==="compassion"&&"💝 Thiền Từ Bi"]})]})}),r.jsxs(wn,{children:[m==="mindfulness"&&r.jsxs(je,{children:[r.jsx(q,{variant:"h6",gutterBottom:!0,children:"Hướng dẫn Thiền Chánh Niệm"}),r.jsxs(q,{paragraph:!0,children:["1. ",r.jsx("strong",{children:"Tìm tư thế thoải mái:"})," Ngồi thẳng lưng, thả lỏng vai, đặt tay trên đùi."]}),r.jsxs(q,{paragraph:!0,children:["2. ",r.jsx("strong",{children:"Quan sát hơi thở:"})," Tập trung vào cảm giác hơi thở vào ra tự nhiên."]}),r.jsxs(q,{paragraph:!0,children:["3. ",r.jsx("strong",{children:"Nhận biết suy nghĩ:"})," Khi tâm trí lang thang, nhẹ nhàng đưa về hơi thở."]}),r.jsxs(q,{paragraph:!0,children:["4. ",r.jsx("strong",{children:"Không phán xét:"})," Quan sát mọi cảm xúc, suy nghĩ mà không đánh giá."]}),r.jsxs(q,{paragraph:!0,children:["5. ",r.jsx("strong",{children:"Thực hành 10-15 phút:"})," Bắt đầu với thời gian ngắn, tăng dần."]}),r.jsx(je,{sx:{mt:3,p:2,bgcolor:"primary.light",borderRadius:1},children:r.jsxs(q,{variant:"body2",color:"primary.contrastText",children:["💡 ",r.jsx("strong",{children:"Lợi ích:"})," Giảm stress, tăng khả năng tập trung, cải thiện khả năng ra quyết định trong giao dịch."]})})]}),m==="gratitude"&&r.jsxs(je,{children:[r.jsx(q,{variant:"h6",gutterBottom:!0,children:"Hướng dẫn Thiền Biết Ơn"}),r.jsxs(q,{paragraph:!0,children:["1. ",r.jsx("strong",{children:"Ngồi yên tĩnh:"})," Tìm không gian thoải mái, thở sâu 3 lần."]}),r.jsxs(q,{paragraph:!0,children:["2. ",r.jsx("strong",{children:"Nghĩ về những điều tốt đẹp:"})," Gia đình, sức khỏe, cơ hội học hỏi từ thị trường."]}),r.jsxs(q,{paragraph:!0,children:["3. ",r.jsx("strong",{children:"Cảm ơn thị trường:"}),' "Tôi cảm ơn thị trường đã tạo ra nơi để tôi rèn luyện tâm và kiếm lợi nhuận đủ sống."']}),r.jsxs(q,{paragraph:!0,children:["4. ",r.jsx("strong",{children:"Cảm ơn bản thân:"}),' "Tôi cảm ơn bản thân đã kiên nhẫn học hỏi và rèn luyện."']}),r.jsxs(q,{paragraph:!0,children:["5. ",r.jsx("strong",{children:"Gửi lòng biết ơn:"})," Đến mọi người đã hỗ trợ hành trình của bạn."]}),r.jsx(je,{sx:{mt:3,p:2,bgcolor:"secondary.light",borderRadius:1},children:r.jsxs(q,{variant:"body2",color:"secondary.contrastText",children:["💝 ",r.jsx("strong",{children:"Lợi ích:"})," Tăng cảm giác hạnh phúc, giảm tham lam, tạo tâm thái tích cực trong giao dịch."]})})]}),m==="compassion"&&r.jsxs(je,{children:[r.jsx(q,{variant:"h6",gutterBottom:!0,children:"Hướng dẫn Thiền Từ Bi"}),r.jsxs(q,{paragraph:!0,children:["1. ",r.jsx("strong",{children:"Bắt đầu với bản thân:"}),' "Mong tôi được bình an, hạnh phúc và thành công."']}),r.jsxs(q,{paragraph:!0,children:["2. ",r.jsx("strong",{children:"Gửi đến người thân:"}),' "Mong gia đình tôi được khỏe mạnh và hạnh phúc."']}),r.jsxs(q,{paragraph:!0,children:["3. ",r.jsx("strong",{children:"Gửi đến trader khác:"}),' "Mong tất cả trader đều học hỏi và phát triển."']}),r.jsxs(q,{paragraph:!0,children:["4. ",r.jsx("strong",{children:"Tha thứ cho bản thân:"}),' "Tôi tha thứ cho những sai lầm trong giao dịch và sẽ học hỏi."']}),r.jsxs(q,{paragraph:!0,children:["5. ",r.jsx("strong",{children:"Tình yêu thương rộng lớn:"})," Gửi tình yêu thương đến tất cả chúng sinh."]}),r.jsx(je,{sx:{mt:3,p:2,bgcolor:"success.light",borderRadius:1},children:r.jsxs(q,{variant:"body2",color:"success.contrastText",children:["🌟 ",r.jsx("strong",{children:"Lợi ích:"})," Giảm tức giận khi thua lỗ, tăng khả năng tha thứ, tạo tâm thái bình an."]})})]})]}),r.jsx(yn,{children:r.jsx(Ue,{onClick:()=>S(!1),children:"Đóng"})})]}),r.jsxs(xn,{open:w,onClose:()=>{g(!1),j(null)},maxWidth:"sm",fullWidth:!0,children:[r.jsx(Sn,{children:r.jsxs(je,{sx:{display:"flex",alignItems:"center",gap:1},children:[r.jsx(mo,{color:"error"}),r.jsx(q,{variant:"h6",children:"Xác nhận xóa phương pháp"})]})}),r.jsxs(wn,{children:[r.jsxs(lr,{children:["Bạn có chắc chắn muốn xóa phương pháp"," ",r.jsxs("strong",{children:['"',x==null?void 0:x.name,'"']}),"?"]}),r.jsx(lr,{sx:{mt:2,color:"error.main"},children:"⚠️ Hành động này không thể hoàn tác. Tất cả dữ liệu liên quan đến phương pháp này sẽ bị xóa vĩnh viễn."}),x&&r.jsxs(je,{sx:{mt:2,p:2,bgcolor:"grey.100",borderRadius:1},children:[r.jsx(q,{variant:"body2",color:"text.secondary",children:r.jsx("strong",{children:"Thông tin phương pháp:"})}),r.jsxs(q,{variant:"body2",children:["• Tên: ",x.name]}),r.jsxs(q,{variant:"body2",children:["• Số câu hỏi: ",((ce=x.questions)==null?void 0:ce.length)||0]}),r.jsxs(q,{variant:"body2",children:["• Điểm tối đa: ",x.totalMaxScore||0]}),r.jsxs(q,{variant:"body2",children:["• Ngày tạo: ",x.createdAt?new Date(x.createdAt).toLocaleDateString("vi-VN"):"N/A"]})]})]}),r.jsxs(yn,{children:[r.jsx(Ue,{onClick:()=>{g(!1),j(null)},disabled:C,children:"Hủy"}),r.jsx(Ue,{onClick:A,color:"error",variant:"contained",disabled:C,startIcon:C?r.jsx(ws,{size:16}):r.jsx(mo,{}),children:C?"Đang xóa...":"Xóa phương pháp"})]})]})]})]})},Or=document.getElementById("root");Or&&Ss.createRoot(Or).render(r.jsx(Cd,{}));
