{"name": "vite-plugin-static-copy", "version": "3.0.2", "description": "rollup-plugin-copy for vite with dev server support.", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": "./dist/index.js", "files": ["dist"], "engines": {"node": "^18.0.0 || >=20.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/sapphi-red/vite-plugin-static-copy.git"}, "keywords": ["vite", "vite-plugin"], "author": "sapphi-red (https://github.com/sapphi-red)", "license": "MIT", "bugs": {"url": "https://github.com/sapphi-red/vite-plugin-static-copy/issues"}, "homepage": "https://github.com/sapphi-red/vite-plugin-static-copy#readme", "sideEffects": false, "peerDependencies": {"vite": "^5.0.0 || ^6.0.0"}, "devDependencies": {"@changesets/changelog-github": "^0.5.1", "@changesets/cli": "^2.29.4", "@eslint/js": "^9.28.0", "@polka/url": "^1.0.0-next.29", "@types/eslint-config-prettier": "^6.11.3", "@types/fs-extra": "^11.0.4", "@types/node": "^22.15.29", "@types/throttle-debounce": "^5.0.2", "@vitest/eslint-plugin": "^1.2.1", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "mrmime": "^2.0.1", "node-fetch": "^3.3.2", "prettier": "^3.5.3", "throttle-debounce": "^5.0.2", "tsdown": "^0.12.5", "typescript": "^5.8.3", "typescript-eslint": "^8.33.0", "vite": "^6.3.5", "vitest": "^3.1.4"}, "dependencies": {"chokidar": "^3.5.3", "fs-extra": "^11.3.0", "p-map": "^7.0.3", "picocolors": "^1.1.1", "tinyglobby": "^0.2.14"}, "scripts": {"dev": "tsdown --watch", "build": "tsdown", "lint": "eslint --cache .", "format": "prettier --write --cache .", "type-check": "tsc --noEmit", "test": "vitest", "release:version": "changeset version && pnpm install --no-frozen-lockfile", "release:publish": "changeset publish"}}