/* Binomo Trading Assistant - Content Styles */

/* Main overlay container */
.binomo-assistant-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 999999;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Modal container */
.binomo-assistant-modal {
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  position: relative;
}

/* Close button */
.binomo-assistant-close {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 4px;
  border-radius: 4px;
}

.binomo-assistant-close:hover {
  background: #f0f0f0;
  color: #333;
}

/* Typography */
.binomo-assistant-modal h1 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.binomo-assistant-modal h2 {
  margin: 20px 0 12px 0;
  color: #444;
  font-size: 18px;
  font-weight: 500;
}

.binomo-assistant-modal h3 {
  margin: 16px 0 8px 0;
  color: #555;
  font-size: 16px;
  font-weight: 500;
}

.binomo-assistant-modal p {
  margin: 8px 0;
  color: #666;
  line-height: 1.5;
}

/* Form elements */
.binomo-assistant-form-group {
  margin-bottom: 16px;
}

.binomo-assistant-label {
  display: block;
  margin-bottom: 6px;
  color: #333;
  font-weight: 500;
}

.binomo-assistant-input,
.binomo-assistant-textarea,
.binomo-assistant-select {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.binomo-assistant-input:focus,
.binomo-assistant-textarea:focus,
.binomo-assistant-select:focus {
  outline: none;
  border-color: #007bff;
}

.binomo-assistant-textarea {
  resize: vertical;
  min-height: 80px;
}

/* Buttons */
.binomo-assistant-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  margin-right: 8px;
  margin-bottom: 8px;
}

.binomo-assistant-btn-primary {
  background: #007bff;
  color: white;
}

.binomo-assistant-btn-primary:hover {
  background: #0056b3;
}

.binomo-assistant-btn-secondary {
  background: #6c757d;
  color: white;
}

.binomo-assistant-btn-secondary:hover {
  background: #545b62;
}

.binomo-assistant-btn-success {
  background: #28a745;
  color: white;
}

.binomo-assistant-btn-success:hover {
  background: #1e7e34;
}

.binomo-assistant-btn-danger {
  background: #dc3545;
  color: white;
}

.binomo-assistant-btn-danger:hover {
  background: #c82333;
}

.binomo-assistant-btn-warning {
  background: #ffc107;
  color: #212529;
}

.binomo-assistant-btn-warning:hover {
  background: #e0a800;
}

/* Checkbox and radio styles */
.binomo-assistant-checkbox,
.binomo-assistant-radio {
  margin-right: 8px;
}

.binomo-assistant-checkbox-group,
.binomo-assistant-radio-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.binomo-assistant-checkbox-item,
.binomo-assistant-radio-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.binomo-assistant-checkbox-item:hover,
.binomo-assistant-radio-item:hover {
  background: #f8f9fa;
}

/* Progress indicator */
.binomo-assistant-progress {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 0 20px;
}

.binomo-assistant-progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.binomo-assistant-progress-step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 15px;
  left: 50%;
  width: 100%;
  height: 2px;
  background: #e0e0e0;
  z-index: -1;
}

.binomo-assistant-progress-step.active::after {
  background: #007bff;
}

.binomo-assistant-progress-circle {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  margin-bottom: 8px;
}

.binomo-assistant-progress-step.active .binomo-assistant-progress-circle {
  background: #007bff;
}

.binomo-assistant-progress-step.completed .binomo-assistant-progress-circle {
  background: #28a745;
}

.binomo-assistant-progress-label {
  font-size: 12px;
  color: #666;
  text-align: center;
}

/* Alert styles */
.binomo-assistant-alert {
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 16px;
  border-left: 4px solid;
}

.binomo-assistant-alert-info {
  background: #d1ecf1;
  border-color: #17a2b8;
  color: #0c5460;
}

.binomo-assistant-alert-warning {
  background: #fff3cd;
  border-color: #ffc107;
  color: #856404;
}

.binomo-assistant-alert-danger {
  background: #f8d7da;
  border-color: #dc3545;
  color: #721c24;
}

.binomo-assistant-alert-success {
  background: #d4edda;
  border-color: #28a745;
  color: #155724;
}

/* Trading controls overlay */
.binomo-assistant-trading-overlay {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: 8px;
  padding: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 999998;
  width: 350px;
  max-height: calc(100vh - 40px);
  overflow-y: auto;
  border: 2px solid #007bff;
}

.binomo-assistant-trading-header {
  background: #007bff;
  color: white;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 6px 6px 0 0;
}

.binomo-assistant-trading-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.binomo-assistant-trading-header .binomo-assistant-close {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 18px;
}

.binomo-assistant-trading-header .binomo-assistant-close:hover {
  background: rgba(255, 255, 255, 0.3);
}

.binomo-assistant-trading-content {
  padding: 16px;
}

.binomo-assistant-trading-footer {
  padding: 12px 16px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  gap: 8px;
  justify-content: space-between;
}

/* Amount Controls */
.binomo-assistant-amount-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.binomo-assistant-amount-controls input {
  flex: 1;
  text-align: center;
  font-weight: bold;
}

.binomo-assistant-amount-controls button {
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.binomo-assistant-amount-presets {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.binomo-assistant-preset-btn {
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.binomo-assistant-preset-btn:hover {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

/* Payout Display */
.binomo-assistant-payout-display {
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.binomo-assistant-payout-rate {
  font-weight: bold;
  color: #28a745;
  font-size: 16px;
}

.binomo-assistant-payout-amount {
  color: #666;
  font-size: 14px;
}

/* Direction Buttons */
.binomo-assistant-direction-buttons {
  display: flex;
  gap: 8px;
  margin: 16px 0;
}

.binomo-assistant-direction-btn {
  flex: 1;
  padding: 12px 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  font-weight: bold;
  border-radius: 6px;
  transition: all 0.2s;
}

.binomo-assistant-direction-icon {
  font-size: 20px;
}

.binomo-assistant-direction-text {
  font-size: 14px;
}

.binomo-assistant-direction-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Current Trades */
.binomo-assistant-current-trades {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.binomo-assistant-current-trades h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #333;
}

.binomo-assistant-trade-item {
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.binomo-assistant-trade-info {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 12px;
}

.binomo-assistant-trade-direction {
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: bold;
  color: white;
  font-size: 10px;
}

.binomo-assistant-trade-direction.up {
  background: #28a745;
}

.binomo-assistant-trade-direction.down {
  background: #dc3545;
}

.binomo-assistant-trade-actions {
  display: flex;
  gap: 4px;
}

.binomo-assistant-btn-sm {
  padding: 4px 8px;
  font-size: 10px;
}

.binomo-assistant-no-trades {
  text-align: center;
  color: #666;
  font-style: italic;
  margin: 0;
  padding: 16px;
}

/* Session Stats */
.binomo-assistant-session-stats {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.binomo-assistant-session-stats h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #333;
}

.binomo-assistant-stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.binomo-assistant-stat-item {
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px;
  text-align: center;
}

.binomo-assistant-stat-label {
  display: block;
  font-size: 11px;
  color: #666;
  margin-bottom: 2px;
}

.binomo-assistant-stat-value {
  display: block;
  font-weight: bold;
  font-size: 14px;
  color: #333;
}

.binomo-assistant-stat-value.positive {
  color: #28a745;
}

.binomo-assistant-stat-value.negative {
  color: #dc3545;
}

/* Statistics Dashboard */
.binomo-assistant-stats-nav {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.binomo-assistant-stats-nav-btn.active {
  background: #007bff !important;
  color: white !important;
}

.binomo-assistant-stats-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.binomo-assistant-current-period {
  font-weight: bold;
  color: #333;
  font-size: 16px;
}

.binomo-assistant-stats-content {
  margin-bottom: 20px;
}

.binomo-assistant-stats-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.binomo-assistant-summary-card {
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  transition: all 0.2s;
}

.binomo-assistant-summary-card:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.binomo-assistant-summary-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.binomo-assistant-summary-label {
  font-size: 14px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.binomo-assistant-stats-charts {
  margin-bottom: 24px;
}

.binomo-assistant-chart-container {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
}

.binomo-assistant-chart-container h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 18px;
}

.binomo-assistant-simple-chart {
  display: flex;
  align-items: end;
  justify-content: center;
  height: 120px;
  border-bottom: 2px solid #e0e0e0;
  position: relative;
}

.binomo-assistant-chart-bar {
  width: 60px;
  background: #007bff;
  border-radius: 4px 4px 0 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 12px;
  min-height: 20px;
  position: relative;
}

.binomo-assistant-chart-bar.positive {
  background: #28a745;
}

.binomo-assistant-chart-bar.negative {
  background: #dc3545;
}

.binomo-assistant-details-container {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
}

.binomo-assistant-details-container h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 18px;
}

.binomo-assistant-details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.binomo-assistant-detail-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
}

.binomo-assistant-detail-label {
  color: #666;
  font-weight: 500;
}

.binomo-assistant-detail-value {
  color: #333;
  font-weight: bold;
}

.binomo-assistant-stats-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

/* Loading Overlay */
.binomo-assistant-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: 999999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.binomo-assistant-loading-content {
  background: white;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.binomo-assistant-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: binomo-assistant-spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes binomo-assistant-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.binomo-assistant-loading-message {
  color: #333;
  font-weight: 500;
}

/* Notifications */
.binomo-assistant-notifications-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 999997;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 400px;
}

.binomo-assistant-notification {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-left: 4px solid;
  padding: 16px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease;
}

.binomo-assistant-notification-show {
  transform: translateX(0);
  opacity: 1;
}

.binomo-assistant-notification-success {
  border-left-color: #28a745;
}

.binomo-assistant-notification-error {
  border-left-color: #dc3545;
}

.binomo-assistant-notification-warning {
  border-left-color: #ffc107;
}

.binomo-assistant-notification-info {
  border-left-color: #17a2b8;
}

.binomo-assistant-notification-content {
  flex: 1;
}

.binomo-assistant-notification-title {
  font-weight: bold;
  margin-bottom: 4px;
  color: #333;
}

.binomo-assistant-notification-message {
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.binomo-assistant-notification-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.binomo-assistant-notification-close {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  font-size: 18px;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.binomo-assistant-notification-close:hover {
  color: #666;
}

/* Confirm Dialog */
.binomo-assistant-confirm-dialog {
  max-width: 400px;
  padding: 0;
  overflow: hidden;
}

.binomo-assistant-confirm-header {
  padding: 16px 20px;
  color: white;
  margin: 0;
}

.binomo-assistant-confirm-header h3 {
  margin: 0;
  font-size: 18px;
}

.binomo-assistant-confirm-info {
  background: #17a2b8;
}

.binomo-assistant-confirm-warning {
  background: #ffc107;
  color: #333;
}

.binomo-assistant-confirm-danger {
  background: #dc3545;
}

.binomo-assistant-confirm-body {
  padding: 20px;
}

.binomo-assistant-confirm-body p {
  margin: 0;
  color: #333;
  line-height: 1.5;
}

.binomo-assistant-confirm-actions {
  padding: 16px 20px;
  background: #f8f9fa;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* Progress Bar */
.binomo-assistant-progress-container {
  margin: 12px 0;
}

.binomo-assistant-progress-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.binomo-assistant-progress-bar {
  width: 100%;
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.binomo-assistant-progress-fill {
  height: 100%;
  background: #007bff;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.binomo-assistant-progress-text {
  font-size: 12px;
  color: #666;
  text-align: right;
  margin-top: 4px;
}

/* Field Validation */
.binomo-assistant-field-error {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
}

.binomo-assistant-field-error-message {
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.binomo-assistant-field-error-message::before {
  content: '⚠️';
  font-size: 10px;
}

/* Tooltips */
.binomo-assistant-tooltip {
  position: absolute;
  background: #333;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 999999;
  opacity: 0;
  transition: opacity 0.2s;
  pointer-events: none;
  max-width: 200px;
  word-wrap: break-word;
}

.binomo-assistant-tooltip-show {
  opacity: 1;
}

.binomo-assistant-tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border: 4px solid transparent;
}

.binomo-assistant-tooltip-top .binomo-assistant-tooltip-arrow {
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  border-top-color: #333;
}

.binomo-assistant-tooltip-bottom .binomo-assistant-tooltip-arrow {
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  border-bottom-color: #333;
}

.binomo-assistant-tooltip-left .binomo-assistant-tooltip-arrow {
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  border-left-color: #333;
}

.binomo-assistant-tooltip-right .binomo-assistant-tooltip-arrow {
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  border-right-color: #333;
}

/* Responsive adjustments for stats */
@media (max-width: 768px) {
  .binomo-assistant-stats-summary {
    grid-template-columns: repeat(2, 1fr);
  }

  .binomo-assistant-stats-nav {
    grid-template-columns: repeat(2, 1fr);
  }

  .binomo-assistant-details-grid {
    grid-template-columns: 1fr;
  }

  .binomo-assistant-stats-actions {
    flex-direction: column;
  }

  .binomo-assistant-notifications-container {
    left: 10px;
    right: 10px;
    max-width: none;
  }

  .binomo-assistant-confirm-dialog {
    margin: 20px;
    max-width: calc(100% - 40px);
  }
}

/* Utility classes */
.binomo-assistant-hidden {
  display: none !important;
}

.binomo-assistant-text-center {
  text-align: center;
}

.binomo-assistant-text-right {
  text-align: right;
}

.binomo-assistant-mt-2 {
  margin-top: 16px;
}

.binomo-assistant-mb-2 {
  margin-bottom: 16px;
}

.binomo-assistant-flex {
  display: flex;
}

.binomo-assistant-flex-column {
  flex-direction: column;
}

.binomo-assistant-justify-between {
  justify-content: space-between;
}

.binomo-assistant-align-center {
  align-items: center;
}

.binomo-assistant-gap-2 {
  gap: 16px;
}

/* Trading Method Cards */
.binomo-assistant-methods-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 60vh;
  overflow-y: auto;
  padding: 8px;
}

.binomo-assistant-method-card {
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  background: #f8f9fa;
  transition: all 0.2s;
}

.binomo-assistant-method-card:hover {
  border-color: #007bff;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.binomo-assistant-method-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.binomo-assistant-method-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.binomo-assistant-method-description {
  color: #666;
  margin-bottom: 12px;
  font-style: italic;
}

.binomo-assistant-method-theory {
  margin-bottom: 12px;
}

.binomo-assistant-theory-content {
  background: white;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #007bff;
  margin-top: 8px;
  font-size: 14px;
  line-height: 1.5;
}

.binomo-assistant-method-questions ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.binomo-assistant-method-questions li {
  margin-bottom: 4px;
  color: #555;
}

/* Custom Method Form */
.binomo-assistant-question-item {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  align-items: center;
}

.binomo-assistant-question-item input {
  flex: 2;
}

.binomo-assistant-question-item select {
  flex: 1;
}

.binomo-assistant-question-item button {
  flex: none;
  padding: 8px 12px;
}

/* Psychology Assessment Styles */
.binomo-assistant-radio-item {
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.binomo-assistant-radio-item:hover {
  border-color: #007bff;
  background: #f8f9fa;
}

.binomo-assistant-radio-item input[type="radio"]:checked + label {
  font-weight: 600;
}

.binomo-assistant-radio-item input[type="radio"] {
  margin-right: 12px;
}

.binomo-assistant-radio-item label {
  cursor: pointer;
  display: block;
  margin: 0;
}

/* Progress Steps */
.binomo-assistant-progress {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  padding: 0 20px;
  position: relative;
}

.binomo-assistant-progress::before {
  content: '';
  position: absolute;
  top: 15px;
  left: 50px;
  right: 50px;
  height: 2px;
  background: #e0e0e0;
  z-index: 1;
}

.binomo-assistant-progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.binomo-assistant-progress-circle {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  margin-bottom: 8px;
  font-size: 14px;
}

.binomo-assistant-progress-step.active .binomo-assistant-progress-circle {
  background: #007bff;
}

.binomo-assistant-progress-step.completed .binomo-assistant-progress-circle {
  background: #28a745;
}

.binomo-assistant-progress-step.completed .binomo-assistant-progress-circle::before {
  content: '✓';
}

.binomo-assistant-progress-label {
  font-size: 12px;
  color: #666;
  text-align: center;
  max-width: 80px;
}

/* Trading Analysis Styles */
.binomo-assistant-questions-container {
  max-height: 50vh;
  overflow-y: auto;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: #f8f9fa;
}

.binomo-assistant-analysis-summary {
  background: white;
  border: 2px solid #007bff;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.binomo-assistant-score-display {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.binomo-assistant-score-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 18px;
  flex-shrink: 0;
}

.binomo-assistant-score-details {
  flex: 1;
  color: #333;
}

.binomo-assistant-summary-details {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #007bff;
  font-size: 14px;
  line-height: 1.5;
}

/* Question Styling */
.binomo-assistant-form-group[data-question-id] {
  background: white;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 12px;
  border-left: 4px solid #007bff;
}

.binomo-assistant-form-group[data-question-id]:hover {
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

/* Required field indicator */
.binomo-assistant-form-group[data-question-id] span[style*="color: red"] {
  color: #dc3545 !important;
  font-weight: bold;
}

/* Radio button styling for analysis */
.binomo-assistant-form-group[data-question-id] .binomo-assistant-radio-item {
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  margin-bottom: 4px;
  padding: 8px 12px;
}

.binomo-assistant-form-group[data-question-id] .binomo-assistant-radio-item:hover {
  background: #e9ecef;
  border-color: #007bff;
}

.binomo-assistant-form-group[data-question-id] .binomo-assistant-radio-item input:checked + label {
  color: #007bff;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .binomo-assistant-modal {
    margin: 20px;
    max-width: calc(100% - 40px);
    max-height: calc(100vh - 40px);
  }

  .binomo-assistant-method-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .binomo-assistant-question-item {
    flex-direction: column;
    align-items: stretch;
  }

  .binomo-assistant-progress {
    padding: 0 10px;
  }

  .binomo-assistant-progress-label {
    font-size: 10px;
    max-width: 60px;
  }

  .binomo-assistant-score-display {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .binomo-assistant-questions-container {
    max-height: 40vh;
  }
}
