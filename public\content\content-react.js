// Binomo Trading Assistant - React Components for Content Script
// This file provides Material UI components without Vite build conflicts

console.log('🎯 Loading Binomo React Components...');

try {
  // Simple React-like component system for content script
  class BinomoReactComponents {
    constructor() {
      console.log('🏗️ Constructing BinomoReactComponents...');
      this.containers = new Map();
      this.init();
    }

  init() {
    console.log('🔧 Starting init method...');

    try {
      console.log('✅ Binomo React Components initialized');

      // Make available globally with multiple references
      window.BinomoReactComponents = this;
      window.binomoReactComponents = this;

      // Also store in a more persistent way
      if (!window.BINOMO_EXTENSION) {
        window.BINOMO_EXTENSION = {};
      }
      window.BINOMO_EXTENSION.reactComponents = this;

      console.log('🔗 Global references set:', {
        BinomoReactComponents: !!window.BinomoReactComponents,
        binomoReactComponents: !!window.binomoReactComponents,
        BINOMO_EXTENSION: !!window.BINOMO_EXTENSION?.reactComponents
      });

      // Test if methods are available
      console.log('🧪 Testing methods:', {
        showDailyGoals: typeof this.showDailyGoals,
        showPsychologyAssessment: typeof this.showPsychologyAssessment,
        createModal: typeof this.createModal
      });

      // Notify main content script
      if (window.BinomoTradingAssistant) {
        console.log('🔄 Notifying main content script...');
      }

      // Dispatch custom event to notify content script
      const event = new CustomEvent('binomoReactComponentsReady', {
        detail: { components: this }
      });
      document.dispatchEvent(event);
      console.log('📡 Dispatched binomoReactComponentsReady event');

      console.log('✅ Init method completed successfully');
    } catch (error) {
      console.error('❌ Error in init method:', error);
      throw error;
    }
  }

  createModal(id, title, content, actions = []) {
    this.removeModal(id);

    const overlay = document.createElement('div');
    overlay.id = id;
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    const modal = document.createElement('div');
    modal.style.cssText = `
      background: white;
      border-radius: 12px;
      padding: 0;
      max-width: 600px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      animation: modalSlideIn 0.3s ease-out;
    `;

    // Add animation keyframes
    if (!document.getElementById('modal-animations')) {
      const style = document.createElement('style');
      style.id = 'modal-animations';
      style.textContent = `
        @keyframes modalSlideIn {
          from { opacity: 0; transform: scale(0.9) translateY(-20px); }
          to { opacity: 1; transform: scale(1) translateY(0); }
        }
      `;
      document.head.appendChild(style);
    }

    // Header
    const header = document.createElement('div');
    header.style.cssText = `
      padding: 24px 24px 16px;
      border-bottom: 1px solid #e0e0e0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    `;
    header.innerHTML = `
      <h2 style="margin: 0; color: #1976d2; font-size: 20px; font-weight: 600;">${title}</h2>
      <button onclick="document.getElementById('${id}').remove()" style="
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #666;
        padding: 4px;
        border-radius: 4px;
        transition: background-color 0.2s;
      " onmouseover="this.style.backgroundColor='#f5f5f5'" onmouseout="this.style.backgroundColor='transparent'">×</button>
    `;

    // Content
    const contentDiv = document.createElement('div');
    contentDiv.style.cssText = `
      padding: 24px;
    `;
    contentDiv.innerHTML = content;

    // Actions
    if (actions.length > 0) {
      const actionsDiv = document.createElement('div');
      actionsDiv.style.cssText = `
        padding: 16px 24px 24px;
        display: flex;
        gap: 12px;
        justify-content: flex-end;
        border-top: 1px solid #e0e0e0;
      `;
      
      actions.forEach(action => {
        const button = document.createElement('button');
        button.textContent = action.text;
        button.style.cssText = `
          padding: 10px 20px;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
          transition: all 0.2s;
          ${action.primary ? 
            'background: #1976d2; color: white;' : 
            'background: #f5f5f5; color: #666;'
          }
        `;
        
        if (action.primary) {
          button.onmouseover = () => button.style.backgroundColor = '#1565c0';
          button.onmouseout = () => button.style.backgroundColor = '#1976d2';
        } else {
          button.onmouseover = () => button.style.backgroundColor = '#e0e0e0';
          button.onmouseout = () => button.style.backgroundColor = '#f5f5f5';
        }
        
        button.onclick = action.onClick;
        actionsDiv.appendChild(button);
      });
      
      modal.appendChild(header);
      modal.appendChild(contentDiv);
      modal.appendChild(actionsDiv);
    } else {
      modal.appendChild(header);
      modal.appendChild(contentDiv);
    }

    overlay.appendChild(modal);
    document.body.appendChild(overlay);
    
    this.containers.set(id, overlay);
    return overlay;
  }

  removeModal(id) {
    const existing = this.containers.get(id);
    if (existing && existing.parentNode) {
      existing.parentNode.removeChild(existing);
    }
    this.containers.delete(id);
  }

  // Daily Goals Modal
  showDailyGoals() {
    const content = `
      <div style="margin-bottom: 20px;">
        <p style="color: #666; margin-bottom: 16px;">Thiết lập mục tiêu giao dịch hàng ngày để duy trì kỷ luật và quản lý rủi ro hiệu quả.</p>
      </div>
      
      <div style="display: grid; gap: 16px;">
        <div>
          <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #333;">💰 Mục tiêu lợi nhuận ($)</label>
          <input type="number" id="profit-target" placeholder="50" style="
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s;
          " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'">
        </div>
        
        <div>
          <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #333;">🛑 Giới hạn thua lỗ ($)</label>
          <input type="number" id="loss-limit" placeholder="20" style="
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s;
          " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'">
        </div>
        
        <div>
          <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #333;">📊 Số lệnh tối đa</label>
          <input type="number" id="max-trades" placeholder="20" style="
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s;
          " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'">
        </div>
        
        <div>
          <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #333;">📝 Mục tiêu học tập hôm nay</label>
          <textarea id="learning-goal" placeholder="Ví dụ: Thực hành phương pháp Bollinger Bands với kỷ luật..." style="
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            min-height: 80px;
            resize: vertical;
            transition: border-color 0.2s;
          " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'"></textarea>
        </div>
      </div>
    `;

    const actions = [
      {
        text: 'Hủy',
        onClick: () => this.removeModal('daily-goals-modal')
      },
      {
        text: 'Lưu mục tiêu',
        primary: true,
        onClick: () => this.saveDailyGoals()
      }
    ];

    this.createModal('daily-goals-modal', '🎯 Thiết lập Mục tiêu Hàng ngày', content, actions);
  }

  async saveDailyGoals() {
    const profitTarget = document.getElementById('profit-target').value;
    const lossLimit = document.getElementById('loss-limit').value;
    const maxTrades = document.getElementById('max-trades').value;
    const learningGoal = document.getElementById('learning-goal').value;

    if (!profitTarget || !lossLimit || !maxTrades) {
      alert('Vui lòng điền đầy đủ thông tin!');
      return;
    }

    const goals = {
      id: new Date().toISOString().split('T')[0],
      date: new Date().toISOString().split('T')[0],
      profitTarget: parseFloat(profitTarget),
      lossLimit: parseFloat(lossLimit),
      maxTrades: parseInt(maxTrades),
      tradingGoal: learningGoal || 'Giao dịch có kỷ luật',
      lessons: []
    };

    try {
      const response = await fetch('http://localhost:3001/dailyGoals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(goals)
      });

      if (response.ok) {
        console.log('✅ Daily goals saved:', goals);
        this.removeModal('daily-goals-modal');
        
        // Show success message
        this.showSuccessMessage('Mục tiêu hàng ngày đã được lưu!');
        
        // Continue to psychology assessment
        setTimeout(() => this.showPsychologyAssessment(), 1500);
      } else {
        throw new Error('Failed to save goals');
      }
    } catch (error) {
      console.error('❌ Error saving goals:', error);
      alert('Không thể lưu mục tiêu. Hãy kiểm tra JSON Server!');
    }
  }

  showSuccessMessage(message) {
    const toast = document.createElement('div');
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4caf50;
      color: white;
      padding: 16px 24px;
      border-radius: 8px;
      z-index: 1000000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      animation: slideInRight 0.3s ease-out;
    `;
    toast.textContent = message;

    document.body.appendChild(toast);
    
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 3000);
  }

  // Psychology Assessment
  showPsychologyAssessment() {
    const content = `
      <div style="margin-bottom: 20px;">
        <p style="color: #666; margin-bottom: 16px;">Đánh giá trạng thái tâm lý hiện tại để đưa ra quyết định giao dịch phù hợp.</p>
      </div>
      
      <div style="display: grid; gap: 12px;">
        <div>
          <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">Trạng thái tâm lý hiện tại:</label>
          <select id="psychology-state" style="
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
          ">
            <option value="">Chọn trạng thái...</option>
            <option value="balanced">😌 Cân bằng - Tâm trạng ổn định</option>
            <option value="greedy">🤑 Tham lam - Muốn kiếm nhanh</option>
            <option value="fearful">😰 Sợ hãi - Lo lắng mất tiền</option>
            <option value="impatient">⚡ Vội vàng - Không kiên nhẫn</option>
            <option value="overconfident">😎 Tự hào - Quá tự tin</option>
            <option value="angry">😡 Tức giận - Muốn revenge</option>
          </select>
        </div>
        
        <div>
          <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">Mô tả chi tiết cảm xúc:</label>
          <textarea id="emotion-description" placeholder="Mô tả cảm xúc và suy nghĩ hiện tại..." style="
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            min-height: 80px;
            resize: vertical;
          "></textarea>
        </div>
      </div>
    `;

    const actions = [
      {
        text: 'Quay lại',
        onClick: () => {
          this.removeModal('psychology-assessment');
          this.showDailyGoals();
        }
      },
      {
        text: 'Tiếp tục',
        primary: true,
        onClick: () => this.savePsychologyAssessment()
      }
    ];

    this.createModal('psychology-assessment', '🧠 Đánh giá Tâm lý', content, actions);
  }

  async savePsychologyAssessment() {
    const state = document.getElementById('psychology-state').value;
    const description = document.getElementById('emotion-description').value;

    if (!state || description.length < 10) {
      alert('Vui lòng chọn trạng thái và mô tả ít nhất 10 ký tự!');
      return;
    }

    const assessment = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      state: state,
      description: description,
      recommendation: this.getPsychologyRecommendation(state)
    };

    try {
      const response = await fetch('http://localhost:3001/psychologyStates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(assessment)
      });

      if (response.ok) {
        console.log('✅ Psychology assessment saved:', assessment);
        this.removeModal('psychology-assessment');
        
        // Show recommendation
        this.showPsychologyRecommendation(assessment);
      } else {
        throw new Error('Failed to save assessment');
      }
    } catch (error) {
      console.error('❌ Error saving assessment:', error);
      alert('Không thể lưu đánh giá. Hãy kiểm tra JSON Server!');
    }
  }

  getPsychologyRecommendation(state) {
    const recommendations = {
      balanced: { safe: true, message: 'Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.' },
      greedy: { safe: false, message: 'Cảnh báo: Tham lam có thể dẫn đến quyết định sai lầm. Hãy bình tĩnh.' },
      fearful: { safe: false, message: 'Sợ hãi có thể khiến bạn bỏ lỡ cơ hội. Hãy xem xét nghỉ ngơi.' },
      impatient: { safe: false, message: 'Vội vàng là kẻ thù của trader. Không nên giao dịch khi thiếu kiên nhẫn.' },
      overconfident: { safe: false, message: 'Quá tự tin có thể dẫn đến chủ quan. Hãy cẩn thận hơn.' },
      angry: { safe: false, message: 'Tuyệt đối không giao dịch khi tức giận. Hãy nghỉ ngơi và quay lại sau.' }
    };
    
    return recommendations[state] || { safe: false, message: 'Hãy đánh giá lại trạng thái tâm lý.' };
  }

  showPsychologyRecommendation(assessment) {
    const rec = assessment.recommendation;
    const content = `
      <div style="text-align: center; margin-bottom: 20px;">
        <div style="
          display: inline-block;
          padding: 20px;
          border-radius: 50%;
          background: ${rec.safe ? '#e8f5e8' : '#ffeaea'};
          margin-bottom: 16px;
        ">
          <span style="font-size: 48px;">${rec.safe ? '✅' : '⚠️'}</span>
        </div>
        <h3 style="margin: 0; color: ${rec.safe ? '#4caf50' : '#f44336'};">
          ${rec.safe ? 'An toàn để giao dịch' : 'Cần cẩn thận'}
        </h3>
      </div>
      
      <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; margin-bottom: 20px;">
        <p style="margin: 0; color: #666; line-height: 1.5;">${rec.message}</p>
      </div>
      
      <div style="font-size: 14px; color: #666;">
        <strong>Trạng thái:</strong> ${assessment.state}<br>
        <strong>Mô tả:</strong> ${assessment.description}
      </div>
    `;

    const actions = [
      {
        text: rec.safe ? 'Tiếp tục giao dịch' : 'Nghỉ ngơi',
        primary: true,
        onClick: () => {
          this.removeModal('psychology-recommendation');
          if (rec.safe) {
            this.showTradingMethodSelector();
          } else {
            this.showSuccessMessage('Hãy nghỉ ngơi và quay lại khi tâm lý ổn định hơn.');
          }
        }
      }
    ];

    this.createModal('psychology-recommendation', '🧠 Khuyến nghị Tâm lý', content, actions);
  }

  // Placeholder methods for other components
  showTradingMethodSelector() {
    this.createModal('trading-method-selector', '📊 Chọn Phương pháp Giao dịch', 
      '<p>Đang phát triển... Sẽ có các phương pháp như Bollinger Bands, RSI, Support/Resistance...</p>',
      [{ text: 'Đóng', onClick: () => this.removeModal('trading-method-selector') }]
    );
  }

  showTradingAnalysis() {
    this.createModal('trading-analysis', '🔍 Phân tích Thị trường', 
      '<p>Đang phát triển... Sẽ có các câu hỏi phân tích dựa trên phương pháp đã chọn...</p>',
      [{ text: 'Đóng', onClick: () => this.removeModal('trading-analysis') }]
    );
  }

  showTradingInterface() {
    this.createModal('trading-interface', '💹 Giao diện Giao dịch', 
      '<p>Đang phát triển... Sẽ có giao diện đặt lệnh và theo dõi thống kê...</p>',
      [{ text: 'Đóng', onClick: () => this.removeModal('trading-interface') }]
    );
  }

  showStatsDashboard() {
    this.createModal('stats-dashboard', '📈 Thống kê Giao dịch', 
      '<p>Đang phát triển... Sẽ hiển thị win rate, P&L, và các thống kê chi tiết...</p>',
      [{ text: 'Đóng', onClick: () => this.removeModal('stats-dashboard') }]
    );
  }
}

  // Initialize when script loads
  console.log('🚀 Initializing BinomoReactComponents...');
  const components = new BinomoReactComponents();
  console.log('✅ BinomoReactComponents created successfully:', components);

} catch (error) {
  console.error('❌ Error in content-react.js:', error);
  console.error('Stack trace:', error.stack);

  // Fallback - create a minimal version
  window.BinomoReactComponents = {
    showDailyGoals: function() {
      console.log('🔄 Fallback: showDailyGoals called');
      alert('React components failed to load. Please check console for errors.');
    }
  };

  console.log('🔄 Fallback BinomoReactComponents created');
}
