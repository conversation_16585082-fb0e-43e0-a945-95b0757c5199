// Binomo Trading Assistant - Enhanced Content Script with AI Psychology & Behavior Tracking

console.log('🎯 Binomo Trading Assistant - Enhanced Content Script Loaded');

// Inline AI Psychology Assessment interfaces and class
interface PsychologyAssessment {
  score: number;
  level: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
  shouldTrade: boolean;
  blockDuration: number;
  recommendation: string;
  aiAnalysis: string;
  factors: {
    emotional: number;
    financial: number;
    physical: number;
    mental: number;
  };
}

interface AssessmentInput {
  emotionalState: string;
  financialSituation: string;
  recentPerformance: string;
  sleepQuality: string;
  stressLevel: string;
  motivation: string;
  additionalNotes?: string;
}

class PsychologyAI {
  private apiKey: string;
  private baseURL = 'https://api.openai.com/v1/chat/completions';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async assessPsychology(input: AssessmentInput): Promise<PsychologyAssessment> {
    try {
      const prompt = this.createAssessmentPrompt(input);
      const aiResponse = await this.callOpenAI(prompt);
      const assessment = this.parseAIResponse(aiResponse, input);
      return assessment;
    } catch (error) {
      console.error('Error in AI psychology assessment:', error);
      return this.fallbackAssessment(input);
    }
  }

  private createAssessmentPrompt(input: AssessmentInput): string {
    return `
Bạn là một chuyên gia tâm lý giao dịch với 20 năm kinh nghiệm. Hãy đánh giá tâm lý giao dịch của người này:

THÔNG TIN ĐÁNH GIÁ:
- Trạng thái cảm xúc: ${input.emotionalState}
- Tình hình tài chính: ${input.financialSituation}
- Kết quả giao dịch gần đây: ${input.recentPerformance}
- Chất lượng giấc ngủ: ${input.sleepQuality}
- Mức độ căng thẳng: ${input.stressLevel}
- Động lực giao dịch: ${input.motivation}
${input.additionalNotes ? `- Ghi chú thêm: ${input.additionalNotes}` : ''}

Hãy trả về đánh giá theo format JSON chính xác sau:
{
  "score": [số điểm từ 0-100],
  "emotional_factor": [điểm cảm xúc 0-100],
  "financial_factor": [điểm tài chính 0-100],
  "physical_factor": [điểm thể chất 0-100],
  "mental_factor": [điểm tinh thần 0-100],
  "should_trade": [true/false],
  "recommendation": "[khuyến nghị ngắn gọn]",
  "analysis": "[phân tích chi tiết 2-3 câu]",
  "risk_factors": ["[yếu tố rủi ro 1]", "[yếu tố rủi ro 2]"],
  "positive_factors": ["[yếu tố tích cực 1]", "[yếu tố tích cực 2]"]
}

TIÊU CHÍ ĐÁNH GIÁ:
- 90-100: Tâm lý xuất sắc, sẵn sàng giao dịch
- 80-89: Tâm lý tốt, có thể giao dịch cẩn thận
- 60-79: Tâm lý trung bình, nên nghỉ 15-30 phút
- 30-59: Tâm lý kém, nên nghỉ 4-8 tiếng
- 0-29: Tâm lý rất kém, nên nghỉ 12-24 tiếng

Chỉ trả về JSON, không có text khác.
`;
  }

  private async callOpenAI(prompt: string): Promise<string> {
    const response = await fetch(this.baseURL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: 'Bạn là chuyên gia tâm lý giao dịch. Luôn trả về JSON hợp lệ.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  private parseAIResponse(aiResponse: string, input: AssessmentInput): PsychologyAssessment {
    try {
      const cleanResponse = aiResponse.replace(/```json\n?|\n?```/g, '').trim();
      const parsed = JSON.parse(cleanResponse);

      const level = this.getScoreLevel(parsed.score);
      const blockDuration = this.calculateBlockDuration(parsed.score, parsed);

      return {
        score: parsed.score,
        level,
        shouldTrade: parsed.should_trade && parsed.score >= 60,
        blockDuration,
        recommendation: parsed.recommendation,
        aiAnalysis: parsed.analysis,
        factors: {
          emotional: parsed.emotional_factor || 50,
          financial: parsed.financial_factor || 50,
          physical: parsed.physical_factor || 50,
          mental: parsed.mental_factor || 50
        }
      };
    } catch (error) {
      console.error('Error parsing AI response:', error);
      return this.fallbackAssessment(input);
    }
  }

  private getScoreLevel(score: number): PsychologyAssessment['level'] {
    if (score >= 90) return 'excellent';
    if (score >= 80) return 'good';
    if (score >= 60) return 'fair';
    if (score >= 30) return 'poor';
    return 'critical';
  }

  private calculateBlockDuration(score: number, aiData: any): number {
    let duration = 0;

    if (score >= 80) {
      duration = 0;
    } else if (score >= 60) {
      duration = 15;
    } else if (score >= 45) {
      duration = 60;
    } else if (score >= 30) {
      duration = 240;
    } else if (score >= 15) {
      duration = 720;
    } else {
      duration = 1440;
    }

    if (aiData.risk_factors && aiData.risk_factors.length > 2) {
      duration = Math.min(duration * 1.5, 1440);
    }

    if (aiData.positive_factors && aiData.positive_factors.length > 2) {
      duration = Math.max(duration * 0.7, 0);
    }

    return Math.round(duration);
  }

  private fallbackAssessment(input: AssessmentInput): PsychologyAssessment {
    let score = 50;

    if (input.emotionalState.includes('cân bằng') || input.emotionalState.includes('tích cực')) {
      score += 20;
    } else if (input.emotionalState.includes('căng thẳng') || input.emotionalState.includes('lo âu')) {
      score -= 20;
    }

    if (input.financialSituation.includes('ổn định')) {
      score += 15;
    } else if (input.financialSituation.includes('khó khăn')) {
      score -= 15;
    }

    if (input.recentPerformance.includes('tốt') || input.recentPerformance.includes('lãi')) {
      score += 10;
    } else if (input.recentPerformance.includes('thua') || input.recentPerformance.includes('lỗ')) {
      score -= 15;
    }

    score = Math.max(0, Math.min(100, score));
    const level = this.getScoreLevel(score);
    const blockDurations = { excellent: 0, good: 0, fair: 15, poor: 240, critical: 1440 };

    return {
      score,
      level,
      shouldTrade: score >= 60,
      blockDuration: blockDurations[level],
      recommendation: score >= 60 ? 'Có thể giao dịch cẩn thận' : 'Nên nghỉ ngơi và thiền định',
      aiAnalysis: 'Đánh giá dựa trên quy tắc cơ bản (AI không khả dụng)',
      factors: {
        emotional: score,
        financial: score,
        physical: score,
        mental: score
      }
    };
  }
}

class BinomoTradingAssistant {
  private isBlocked = false;
  private psychologyAI: PsychologyAI | null = null;
  private lastTradeAmount = 0;
  private tradeObserver: MutationObserver | null = null;
  private lastTradeReasonModalTime = 0;
  private lastMindfulnessModalTime = 0;
  private hasShownAIPsychologyModal = false;
  private currentTradeId: string | null = null;
  private lastTradeElementState = new Map<string, boolean>();
  private lastContentCheckTime = new Map<string, number>();
  private webSocketLoggingEnabled = true;

  constructor() {
    this.setupWebSocketInterceptor();
    this.init();

    // Add manual trigger for testing (remove in production)
    (window as any).testMindfulnessModal = (type: 'win' | 'loss') => {
      console.log(`🧪 Manual test trigger for ${type} modal`);
      this.lastMindfulnessModalTime = 0; // Reset cooldown
      this.showMindfulnessModal(type);
    };

    (window as any).testTradeReasonModal = () => {
      console.log('🧪 Manual test trigger for trade reason modal');
      this.lastTradeReasonModalTime = 0; // Reset cooldown
      this.showTradeReasonModal();
    };

    (window as any).scanForTradeElements = () => {
      console.log('🔍 Manual scan for trade elements');
      const containers = document.querySelectorAll('main.container');
      const progressItems = document.querySelectorAll('progress-bar-item');

      console.log(`Found ${containers.length} containers and ${progressItems.length} progress items`);

      containers.forEach((container, index) => {
        const isVisible = this.isElementVisible(container as HTMLElement);
        console.log(`Container ${index}: visible=${isVisible}, class=${container.className}`);
        if (isVisible) {
          this.checkForTradeExecution(container as HTMLElement);
        }
      });

      progressItems.forEach((item, index) => {
        const isVisible = this.isElementVisible(item as HTMLElement);
        console.log(`Progress item ${index}: visible=${isVisible}, class=${item.className}`);
        if (isVisible) {
          this.checkForTradeExecution(item as HTMLElement);
        }
      });
    };

    (window as any).resetTradeState = () => {
      console.log('🔄 Resetting trade state and cooldowns');
      this.lastTradeReasonModalTime = 0;
      this.lastMindfulnessModalTime = 0;
      this.currentTradeId = null;
      this.lastTradeElementState.clear();
      this.lastContentCheckTime.clear();
      console.log('✅ State reset complete');
    };

    (window as any).forceTradeModal = () => {
      console.log('🚀 Force showing trade reason modal');
      this.lastTradeReasonModalTime = 0;
      this.showTradeReasonModal();
    };

    (window as any).simulateTradeEnd = () => {
      console.log('🎭 Simulating trade end');
      this.handleTradeEnd();
    };

    (window as any).checkTradeContent = () => {
      console.log('🔍 Checking current trade content');
      const containers = document.querySelectorAll('main.container');
      const progressItems = document.querySelectorAll('progress-bar-item');

      containers.forEach((container, index) => {
        if (this.isElementVisible(container as HTMLElement)) {
          const content = this.getTradeContent(container as HTMLElement);
          console.log(`Container ${index} content:`, content);
        }
      });

      progressItems.forEach((item, index) => {
        if (this.isElementVisible(item as HTMLElement)) {
          const content = this.getTradeContent(item as HTMLElement);
          console.log(`Progress item ${index} content:`, content);
        }
      });
    };

    (window as any).testWebSocketTrade = () => {
      console.log('🧪 Testing WebSocket trade execution');
      this.handleTradeExecution();
    };

    (window as any).testWebSocketResult = (result: 'win' | 'loss') => {
      console.log(`🧪 Testing WebSocket trade result: ${result}`);
      this.handleTradeResult(result);
    };

    (window as any).toggleWebSocketLogging = () => {
      this.webSocketLoggingEnabled = !this.webSocketLoggingEnabled;
      console.log(`🔌 WebSocket logging ${this.webSocketLoggingEnabled ? 'enabled' : 'disabled'}`);
    };

    console.log('🧪 Test functions available:');
    console.log('  - testMindfulnessModal("win") or testMindfulnessModal("loss")');
    console.log('  - testTradeReasonModal()');
    console.log('  - scanForTradeElements()');
    console.log('  - resetTradeState() - Reset all cooldowns and state');
    console.log('  - forceTradeModal() - Force show trade reason modal');
    console.log('  - simulateTradeEnd() - Simulate trade end to reset state');
    console.log('  - checkTradeContent() - Check current trade content');
    console.log('  - testWebSocketTrade() - Test WebSocket trade execution');
    console.log('  - testWebSocketResult("win") or testWebSocketResult("loss")');
    console.log('  - toggleWebSocketLogging() - Toggle WebSocket message logging');
  }

  private setupWebSocketInterceptor() {
    console.log('🔌 Setting up WebSocket interceptor for Binomo');

    // Store original WebSocket constructor
    const OriginalWebSocket = window.WebSocket;

    // Override WebSocket constructor
    window.WebSocket = class extends OriginalWebSocket {
      constructor(url: string | URL, protocols?: string | string[]) {
        super(url, protocols);

        const urlString = url.toString();
        console.log('🔌 WebSocket connection detected:', urlString);

        // Check if this is a Binomo WebSocket
        if (urlString.includes('binomo') ||
            urlString.includes('as.binomo1.com') ||
            urlString.includes('ws.binomo1.com')) {
          console.log('🎯 Binomo WebSocket detected, setting up monitoring');
          this.setupBinomoWebSocketMonitoring();
        }
      }

      private setupBinomoWebSocketMonitoring() {
        // Monitor incoming messages
        this.addEventListener('message', (event) => {
          try {
            const data = JSON.parse(event.data);

            // Log all messages for debugging (can be toggled)
            const assistant = (window as any).binomoAssistant;
            if (assistant?.webSocketLoggingEnabled) {
              if (data && typeof data === 'object') {
                console.log('📨 Binomo WebSocket message (JSON):', data);
              } else {
                console.log('📨 Binomo WebSocket message (simple):', data);
              }
            }

            // Pass to our handler
            (window as any).binomoAssistant?.handleWebSocketMessage(data);
          } catch (error) {
            console.log('📨 Binomo WebSocket message (non-JSON):', event.data);
            (window as any).binomoAssistant?.handleWebSocketMessage(event.data);
          }
        });

        // Monitor connection events
        this.addEventListener('open', () => {
          console.log('🔌 Binomo WebSocket connected');
        });

        this.addEventListener('close', () => {
          console.log('🔌 Binomo WebSocket disconnected');
        });

        this.addEventListener('error', (error) => {
          console.log('❌ Binomo WebSocket error:', error);
        });
      }
    };

    // Make assistant available globally for WebSocket handler
    (window as any).binomoAssistant = this;

    console.log('✅ WebSocket interceptor setup complete');
  }

  private handleWebSocketMessage(data: any) {
    console.log('🔍 Processing WebSocket message:', data);

    // Check for trade-related messages
    if (typeof data === 'object' && data !== null) {
      // Look for trade execution indicators
      if (this.isTradeExecutionMessage(data)) {
        console.log('🎯 Trade execution detected via WebSocket');
        this.handleTradeExecution();
      }

      // Look for trade result indicators
      if (this.isTradeResultMessage(data)) {
        const result = this.extractTradeResult(data);
        console.log('📊 Trade result detected via WebSocket:', result);
        this.handleTradeResult(result);
      }
    }
  }

  private isTradeExecutionMessage(data: any): boolean {
    // Look for common trade execution patterns
    const messageStr = JSON.stringify(data).toLowerCase();

    // Common trade execution indicators
    const tradeIndicators = [
      'deal', 'trade', 'order', 'position',
      'buy', 'sell', 'call', 'put',
      'higher', 'lower', 'up', 'down'
    ];

    // Check if message contains trade-related keywords
    for (const indicator of tradeIndicators) {
      if (messageStr.includes(indicator)) {
        console.log('🎯 Trade indicator found:', indicator);
        return true;
      }
    }

    // Check for specific message types
    if (data.type && (
      data.type.includes('deal') ||
      data.type.includes('trade') ||
      data.type.includes('order')
    )) {
      return true;
    }

    return false;
  }

  private isTradeResultMessage(data: any): boolean {
    // Look for trade result patterns
    const messageStr = JSON.stringify(data).toLowerCase();

    // Common result indicators
    const resultIndicators = [
      'win', 'lose', 'loss', 'profit',
      'result', 'outcome', 'finished',
      'completed', 'closed'
    ];

    for (const indicator of resultIndicators) {
      if (messageStr.includes(indicator)) {
        console.log('📊 Result indicator found:', indicator);
        return true;
      }
    }

    return false;
  }

  private extractTradeResult(data: any): 'win' | 'loss' | 'unknown' {
    const messageStr = JSON.stringify(data).toLowerCase();

    if (messageStr.includes('win') || messageStr.includes('profit')) {
      return 'win';
    } else if (messageStr.includes('lose') || messageStr.includes('loss')) {
      return 'loss';
    }

    return 'unknown';
  }

  private handleTradeExecution() {
    const now = Date.now();

    if (now - this.lastTradeReasonModalTime > 5000) { // 5s cooldown
      console.log('🎯 Trade execution detected via WebSocket - showing modal');
      this.lastTradeReasonModalTime = now;
      setTimeout(() => this.showTradeReasonModal(), 1000);
    } else {
      console.log('⏰ Trade execution detected but modal on cooldown');
    }
  }

  private handleTradeResult(result: 'win' | 'loss' | 'unknown') {
    const now = Date.now();

    if (result !== 'unknown' && now - this.lastMindfulnessModalTime > 3000) {
      console.log(`📊 Trade result detected via WebSocket: ${result} - showing modal`);
      this.lastMindfulnessModalTime = now;
      setTimeout(() => this.showMindfulnessModal(result, true), 2000);
    } else if (result === 'unknown') {
      console.log('❓ Trade result detected but type unknown');
    } else {
      console.log('⏰ Trade result detected but modal on cooldown');
    }
  }

  private async init() {
    // Initialize OpenAI if available
    await this.initializeAI();
    
    // Check if trading is blocked
    await this.checkTradingBlock();
    
    if (this.isBlocked) {
      this.showBlockedMessage();
    } else {
      // Check if user needs psychology confirmation
      this.checkPsychologyConfirmation();

      // Show psychology state modal on page load
      this.showPsychologyStateModal();

      // Start behavior tracking
      this.startBehaviorTracking();
    }
  }

  private async initializeAI() {
    try {
      const result = await chrome.storage.local.get(['openaiApiKey']);
      if (result.openaiApiKey) {
        this.psychologyAI = new PsychologyAI(result.openaiApiKey);
      }
    } catch (error) {
      console.error('Error initializing AI:', error);
    }
  }

  private async checkTradingBlock(): Promise<void> {
    try {
      const result = await chrome.storage.local.get([
        'tradingBlocked', 
        'blockDate', 
        'blockUntil', 
        'blockDurationMinutes'
      ]);
      
      if (result.tradingBlocked) {
        const now = new Date();
        
        if (result.blockUntil) {
          const blockUntil = new Date(result.blockUntil);
          if (now >= blockUntil) {
            await chrome.storage.local.remove([
              'tradingBlocked', 
              'blockDate', 
              'blockUntil', 
              'blockDurationMinutes'
            ]);
            this.isBlocked = false;
            return;
          }
        }
        
        this.isBlocked = true;
      }
    } catch (error) {
      console.error('Error checking trading block:', error);
      this.isBlocked = false;
    }
  }

  private showPsychologyStateModal() {
    // Show psychology state modal on every page load
    setTimeout(() => {
      if (!this.isBlocked && !document.getElementById('psychology-state-modal')) {
        this.createPsychologyStateModal();
      }
    }, 2000); // Wait 2 seconds after page load
  }

  private createPsychologyStateModal() {
    const modal = document.createElement('div');
    modal.id = 'psychology-state-modal';
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    modal.innerHTML = `
      <div style="
        background: white;
        padding: 32px;
        border-radius: 16px;
        max-width: 500px;
        width: 90%;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="text-align: center; margin-bottom: 24px;">
          <div style="font-size: 48px; margin-bottom: 16px;">🧠</div>
          <h3 style="color: #1976d2; margin: 0 0 8px 0;">Trạng thái tâm lý hiện tại</h3>
          <p style="color: #666; margin: 0; font-size: 14px;">
            Hãy chia sẻ cảm xúc và trạng thái tâm lý của bạn lúc này
          </p>
        </div>

        <div style="margin-bottom: 24px;">
          <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">
            Hiện tại bạn đang cảm thấy như thế nào?
          </label>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 16px;">
            <button class="emotion-btn" data-emotion="tham" style="
              background: #ffebee;
              color: #c62828;
              border: 2px solid #ffcdd2;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">🤑 Tham lam</button>

            <button class="emotion-btn" data-emotion="gian" style="
              background: #fff3e0;
              color: #ef6c00;
              border: 2px solid #ffcc02;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">😡 Giận dữ</button>

            <button class="emotion-btn" data-emotion="lo_au" style="
              background: #f3e5f5;
              color: #7b1fa2;
              border: 2px solid #e1bee7;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">😰 Lo lắng</button>

            <button class="emotion-btn" data-emotion="so_hai" style="
              background: #e8f5e8;
              color: #388e3c;
              border: 2px solid #c8e6c9;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">😨 Sợ hãi</button>

            <button class="emotion-btn" data-emotion="si_me" style="
              background: #e3f2fd;
              color: #1976d2;
              border: 2px solid #bbdefb;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">😵‍💫 Si mê</button>

            <button class="emotion-btn" data-emotion="u_me" style="
              background: #fce4ec;
              color: #ad1457;
              border: 2px solid #f8bbd9;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">😴 U mê</button>

            <button class="emotion-btn" data-emotion="phan_khich" style="
              background: #fff8e1;
              color: #f57f17;
              border: 2px solid #fff176;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">🤩 Phấn khích</button>

            <button class="emotion-btn" data-emotion="hung_phan" style="
              background: #e0f2f1;
              color: #00695c;
              border: 2px solid #b2dfdb;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">😤 Hưng phấn</button>

            <button class="emotion-btn" data-emotion="binh_tinh" style="
              background: #e8f5e8;
              color: #2e7d32;
              border: 2px solid #c8e6c9;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">😌 Bình tĩnh</button>

            <button class="emotion-btn" data-emotion="tinh_thuc" style="
              background: #e3f2fd;
              color: #1565c0;
              border: 2px solid #90caf9;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">🧘‍♂️ Tỉnh thức</button>
          </div>

          <textarea
            id="psychology-notes"
            placeholder="Mô tả thêm về cảm xúc và suy nghĩ hiện tại của bạn..."
            style="
              width: 100%;
              height: 80px;
              padding: 12px;
              border: 2px solid #e0e0e0;
              border-radius: 8px;
              font-size: 14px;
              resize: vertical;
              font-family: inherit;
              margin-top: 8px;
            "
          ></textarea>
        </div>

        <div style="display: flex; gap: 12px;">
          <button id="skip-psychology" style="
            flex: 1;
            background: #666;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">Bỏ qua</button>
          <button id="analyze-psychology" style="
            flex: 2;
            background: #1976d2;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
          ">🧠 Phân tích tâm lý</button>
        </div>

        <div id="psychology-result" style="
          margin-top: 16px;
          padding: 16px;
          background: #f5f5f5;
          border-radius: 8px;
          display: none;
        "></div>
      </div>
    `;

    document.body.appendChild(modal);

    let selectedEmotion = '';

    // Add emotion button listeners
    modal.querySelectorAll('.emotion-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const target = e.target as HTMLElement;
        selectedEmotion = target.getAttribute('data-emotion') || '';

        // Reset all buttons
        modal.querySelectorAll('.emotion-btn').forEach(button => {
          (button as HTMLElement).style.borderColor = (button as HTMLElement).style.backgroundColor;
        });

        // Highlight selected
        target.style.borderColor = '#1976d2';
        target.style.borderWidth = '3px';
      });
    });

    // Add action listeners
    document.getElementById('skip-psychology')?.addEventListener('click', () => {
      modal.remove();
    });

    document.getElementById('analyze-psychology')?.addEventListener('click', () => {
      this.analyzePsychologyState(selectedEmotion);
    });
  }

  private createAIPsychologyModal() {
    const modal = document.createElement('div');
    modal.id = 'ai-psychology-modal';
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    modal.innerHTML = `
      <div style="
        background: white;
        padding: 32px;
        border-radius: 16px;
        max-width: 500px;
        width: 90%;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="text-align: center; margin-bottom: 24px;">
          <div style="font-size: 48px; margin-bottom: 16px;">🧠</div>
          <h3 style="color: #1976d2; margin: 0 0 8px 0;">Đánh giá tâm lý AI</h3>
          <p style="color: #666; margin: 0; font-size: 14px;">
            Hãy chia sẻ cảm xúc hiện tại để AI đánh giá tâm lý giao dịch
          </p>
        </div>

        <div style="margin-bottom: 24px;">
          <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">
            Hiện tại bạn đang cảm thấy như thế nào?
          </label>
          <textarea 
            id="psychology-diary" 
            placeholder="Ví dụ: Tôi cảm thấy hơi lo lắng vì thua lỗ hôm qua, nhưng cũng muốn gỡ lại. Tôi đã ngủ đủ giấc và cảm thấy tỉnh táo..."
            style="
              width: 100%;
              height: 120px;
              padding: 12px;
              border: 2px solid #e0e0e0;
              border-radius: 8px;
              font-size: 14px;
              resize: vertical;
              font-family: inherit;
            "
          ></textarea>
          <div style="margin-top: 8px; font-size: 12px; color: #666;">
            💡 Hãy thành thật về cảm xúc: tham lam, giận dữ, lo lắng, sợ hãi, phấn khích...
          </div>
        </div>

        <div style="display: flex; gap: 12px;">
          <button id="skip-assessment" style="
            flex: 1;
            background: #666;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">Bỏ qua</button>
          <button id="ai-analyze" style="
            flex: 2;
            background: #1976d2;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
          ">🤖 Phân tích AI</button>
        </div>

        <div id="ai-result" style="
          margin-top: 16px;
          padding: 16px;
          background: #f5f5f5;
          border-radius: 8px;
          display: none;
        "></div>
      </div>
    `;

    document.body.appendChild(modal);

    // Add event listeners
    document.getElementById('skip-assessment')?.addEventListener('click', () => {
      modal.remove();
    });

    document.getElementById('ai-analyze')?.addEventListener('click', () => {
      this.performAIAnalysis();
    });
  }

  private async analyzePsychologyState(emotion: string) {
    const notesText = (document.getElementById('psychology-notes') as HTMLTextAreaElement)?.value || '';
    const resultDiv = document.getElementById('psychology-result');
    const analyzeBtn = document.getElementById('analyze-psychology') as HTMLButtonElement;

    if (!emotion) {
      alert('Vui lòng chọn trạng thái cảm xúc hiện tại');
      return;
    }

    analyzeBtn.textContent = '🧠 Đang phân tích...';
    analyzeBtn.disabled = true;

    try {
      // Determine psychology score based on emotion
      const emotionScores = {
        'tham': 20,        // Tham lam - very bad
        'gian': 15,        // Giận dữ - very bad
        'lo_au': 30,       // Lo lắng - bad
        'so_hai': 25,      // Sợ hãi - bad
        'si_me': 35,       // Si mê - poor
        'u_me': 40,        // U mê - poor
        'phan_khich': 45,  // Phấn khích - risky
        'hung_phan': 50,   // Hưng phấn - neutral
        'binh_tinh': 85,   // Bình tĩnh - good
        'tinh_thuc': 95    // Tỉnh thức - excellent
      };

      const score = emotionScores[emotion as keyof typeof emotionScores] || 50;
      const blockDuration = this.calculateBlockDurationFromScore(score);
      const shouldTrade = score >= 70;

      // Get emotion description
      const emotionDescriptions = {
        'tham': 'Tâm tham lam có thể dẫn đến quyết định liều lĩnh và mất kiểm soát',
        'gian': 'Tâm giận dữ làm mờ khả năng phán đoán và dễ dẫn đến sai lầm',
        'lo_au': 'Tâm lo lắng tạo ra căng thẳng và ảnh hưởng đến quyết định',
        'so_hai': 'Tâm sợ hãi có thể khiến bạn bỏ lỡ cơ hội hoặc thoát quá sớm',
        'si_me': 'Tâm si mê làm mất tỉnh táo và khả năng nhìn nhận thực tế',
        'u_me': 'Tâm u mê thiếu sự tỉnh thức cần thiết cho giao dịch',
        'phan_khich': 'Tâm phấn khích có thể dẫn đến quyết định vội vàng',
        'hung_phan': 'Tâm hưng phấn cần được kiểm soát để tránh rủi ro',
        'binh_tinh': 'Tâm bình tĩnh là nền tảng tốt cho giao dịch có ý thức',
        'tinh_thuc': 'Tâm tỉnh thức là trạng thái lý tưởng cho giao dịch'
      };

      const analysis = emotionDescriptions[emotion as keyof typeof emotionDescriptions] || 'Cần đánh giá thêm';

      // Show result
      if (resultDiv) {
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = `
          <div style="text-align: center; margin-bottom: 16px;">
            <div style="font-size: 32px; margin-bottom: 8px;">
              ${score >= 80 ? '😊' : score >= 60 ? '😐' : '😰'}
            </div>
            <div style="font-size: 24px; font-weight: bold; color: ${score >= 80 ? '#4caf50' : score >= 60 ? '#ff9800' : '#f44336'};">
              ${score}/100 điểm
            </div>
          </div>

          <div style="margin-bottom: 16px;">
            <strong>Phân tích tâm lý:</strong>
            <p style="margin: 8px 0; font-size: 14px; line-height: 1.4;">${analysis}</p>
            ${notesText ? `<p style="margin: 8px 0; font-size: 13px; color: #666; font-style: italic;">"${notesText}"</p>` : ''}
          </div>

          ${!shouldTrade ? `
            <div style="background: #fff3cd; padding: 12px; border-radius: 8px; border-left: 4px solid #ffc107; margin-bottom: 16px;">
              <strong>⚠️ Khuyến nghị nghỉ ngơi:</strong> ${this.formatDuration(blockDuration)}
              <br><small>Hãy dành thời gian thiền định và rèn luyện tâm để chuẩn bị tốt hơn.</small>
            </div>
          ` : `
            <div style="background: #d4edda; padding: 12px; border-radius: 8px; border-left: 4px solid #28a745; margin-bottom: 16px;">
              <strong>✅ Có thể giao dịch:</strong> Tâm lý ổn định
              <br><small>Hãy duy trì sự tỉnh thức và tuân thủ nguyên tắc giao dịch.</small>
            </div>
          `}

          <div style="display: flex; gap: 8px;">
            ${shouldTrade ? `
              <button onclick="document.getElementById('psychology-state-modal').remove()" style="
                flex: 1; background: #4caf50; color: white; border: none; padding: 10px; border-radius: 6px; cursor: pointer;
              ">✅ Tiếp tục giao dịch</button>
            ` : `
              <button onclick="window.open('${chrome.runtime.getURL('options.html#meditation')}', '_blank')" style="
                flex: 1; background: #9c27b0; color: white; border: none; padding: 10px; border-radius: 6px; cursor: pointer;
              ">🧘‍♂️ Đi thiền</button>
            `}
            <button onclick="document.getElementById('psychology-state-modal').remove()" style="
              background: #666; color: white; border: none; padding: 10px 16px; border-radius: 6px; cursor: pointer;
            ">Đóng</button>
          </div>
        `;
      }

      // Save assessment result
      await chrome.storage.local.set({
        lastPsychologyState: {
          timestamp: Date.now(),
          emotion,
          notes: notesText,
          score,
          shouldTrade,
          blockDuration
        }
      });

      // Block trading if recommended
      if (!shouldTrade && blockDuration > 0) {
        await this.blockTradingWithDuration(blockDuration);
      }

    } catch (error) {
      console.error('Psychology analysis error:', error);
      if (resultDiv) {
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = `
          <div style="background: #f8d7da; padding: 12px; border-radius: 8px; color: #721c24;">
            <strong>❌ Lỗi phân tích:</strong> Không thể phân tích tâm lý. Vui lòng thử lại.
          </div>
        `;
      }
    }

    analyzeBtn.textContent = '🧠 Phân tích tâm lý';
    analyzeBtn.disabled = false;
  }

  private calculateBlockDurationFromScore(score: number): number {
    if (score >= 80) return 0;      // No block
    if (score >= 60) return 15;     // 15 minutes
    if (score >= 45) return 60;     // 1 hour
    if (score >= 30) return 240;    // 4 hours
    if (score >= 15) return 720;    // 12 hours
    return 1440;                    // 24 hours
  }

  private async performAIAnalysis() {
    const diaryText = (document.getElementById('psychology-diary') as HTMLTextAreaElement)?.value;
    const resultDiv = document.getElementById('ai-result');
    const analyzeBtn = document.getElementById('ai-analyze') as HTMLButtonElement;

    if (!diaryText.trim()) {
      alert('Vui lòng chia sẻ cảm xúc hiện tại của bạn');
      return;
    }

    if (!this.psychologyAI) {
      alert('AI chưa được cấu hình. Vui lòng thêm OpenAI API key trong Settings.');
      return;
    }

    analyzeBtn.textContent = '🤖 Đang phân tích...';
    analyzeBtn.disabled = true;

    try {
      // Parse diary text into assessment input
      const assessmentInput = {
        emotionalState: diaryText,
        financialSituation: 'Không rõ',
        recentPerformance: 'Không rõ',
        sleepQuality: 'Không rõ',
        stressLevel: 'Không rõ',
        motivation: diaryText,
        additionalNotes: 'Đánh giá từ nhật ký tâm trạng'
      };

      const assessment = await this.psychologyAI.assessPsychology(assessmentInput);

      // Show result
      if (resultDiv) {
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = `
          <div style="text-align: center; margin-bottom: 16px;">
            <div style="font-size: 32px; margin-bottom: 8px;">
              ${assessment.score >= 80 ? '😊' : assessment.score >= 60 ? '😐' : '😰'}
            </div>
            <div style="font-size: 24px; font-weight: bold; color: ${assessment.score >= 80 ? '#4caf50' : assessment.score >= 60 ? '#ff9800' : '#f44336'};">
              ${assessment.score}/100 điểm
            </div>
          </div>
          
          <div style="margin-bottom: 16px;">
            <strong>Phân tích AI:</strong>
            <p style="margin: 8px 0; font-size: 14px; line-height: 1.4;">${assessment.aiAnalysis}</p>
          </div>
          
          <div style="margin-bottom: 16px;">
            <strong>Khuyến nghị:</strong>
            <p style="margin: 8px 0; font-size: 14px; line-height: 1.4;">${assessment.recommendation}</p>
          </div>
          
          ${assessment.blockDuration > 0 ? `
            <div style="background: #fff3cd; padding: 12px; border-radius: 8px; border-left: 4px solid #ffc107;">
              <strong>⚠️ Khuyến nghị nghỉ ngơi:</strong> ${this.formatDuration(assessment.blockDuration)}
            </div>
          ` : `
            <div style="background: #d4edda; padding: 12px; border-radius: 8px; border-left: 4px solid #28a745;">
              <strong>✅ Có thể giao dịch:</strong> Tâm lý ổn định
            </div>
          `}
          
          <div style="margin-top: 16px; display: flex; gap: 8px;">
            ${assessment.shouldTrade ? `
              <button onclick="document.getElementById('ai-psychology-modal').remove()" style="
                flex: 1; background: #4caf50; color: white; border: none; padding: 10px; border-radius: 6px; cursor: pointer;
              ">✅ Tiếp tục giao dịch</button>
            ` : `
              <button onclick="window.open('${chrome.runtime.getURL('options.html#meditation')}', '_blank')" style="
                flex: 1; background: #9c27b0; color: white; border: none; padding: 10px; border-radius: 6px; cursor: pointer;
              ">🧘‍♂️ Đi thiền</button>
            `}
            <button onclick="document.getElementById('ai-psychology-modal').remove()" style="
              background: #666; color: white; border: none; padding: 10px 16px; border-radius: 6px; cursor: pointer;
            ">Đóng</button>
          </div>
        `;
      }

      // Save assessment result
      await chrome.storage.local.set({
        lastAIAssessment: {
          timestamp: Date.now(),
          score: assessment.score,
          shouldTrade: assessment.shouldTrade,
          blockDuration: assessment.blockDuration
        }
      });

      // Block trading if recommended
      if (!assessment.shouldTrade && assessment.blockDuration > 0) {
        await this.blockTradingWithDuration(assessment.blockDuration);
      }

    } catch (error) {
      console.error('AI analysis error:', error);
      if (resultDiv) {
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = `
          <div style="background: #f8d7da; padding: 12px; border-radius: 8px; color: #721c24;">
            <strong>❌ Lỗi phân tích AI:</strong> ${error.message || 'Không thể kết nối với AI'}
          </div>
        `;
      }
    }

    analyzeBtn.textContent = '🤖 Phân tích AI';
    analyzeBtn.disabled = false;
  }

  private formatDuration(minutes: number): string {
    if (minutes < 60) return `${minutes} phút`;
    if (minutes < 1440) return `${Math.round(minutes / 60)} tiếng`;
    return `${Math.round(minutes / 1440)} ngày`;
  }

  private async blockTradingWithDuration(durationMinutes: number) {
    try {
      const now = new Date();
      const blockUntil = new Date(now.getTime() + durationMinutes * 60 * 1000);
      
      await chrome.storage.local.set({
        tradingBlocked: true,
        blockDate: now.toISOString(),
        blockUntil: blockUntil.toISOString(),
        blockDurationMinutes: durationMinutes,
        needsPsychologyConfirmation: true
      });
      
      // Reload page to show block message
      setTimeout(() => window.location.reload(), 2000);
    } catch (error) {
      console.error('Error blocking trading:', error);
    }
  }

  private startBehaviorTracking() {
    // Only start tracking if not blocked and after page is fully loaded
    setTimeout(() => {
      // Track deal button clicks and amounts
      this.observeTradeActions();

      // Track trade results
      this.observeTradeResults();
    }, 3000); // Wait 3 seconds for page to fully load
  }

  private observeTradeActions() {
    console.log('🎯 Starting trade action observation...');

    // Specific selectors based on actual Binomo HTML structure
    const dealButtonSelectors = [
      // Look for trade execution buttons
      'button[class*="deal"]',
      'button[class*="trade"]',
      'button[class*="up"]',
      'button[class*="down"]',
      'button[class*="higher"]',
      'button[class*="lower"]',
      'button[class*="call"]',
      'button[class*="put"]',
      // Generic trading buttons
      '.deal-button',
      '.trade-button',
      '.option-button',
      '[data-direction="up"]',
      '[data-direction="down"]',
      // Look for buttons in trading interface
      '.trading-panel button',
      '.deal-panel button'
    ];

    const amountSelectors = [
      'input[data-test-id*="amount"]',
      'input[class*="amount"]',
      'input[type="number"]',
      '.amount-input',
      '[data-field="amount"]'
    ];

    // Monitor amount changes
    const findAndMonitorAmountInputs = () => {
      amountSelectors.forEach(selector => {
        const inputs = document.querySelectorAll(selector);
        inputs.forEach(input => {
          if (!input.hasAttribute('data-monitored')) {
            input.setAttribute('data-monitored', 'true');
            input.addEventListener('change', (e) => {
              const target = e.target as HTMLInputElement;
              this.lastTradeAmount = parseFloat(target.value) || 0;
              console.log('💰 Amount changed:', this.lastTradeAmount);
            });
          }
        });
      });
    };

    // Monitor deal button clicks with more specific detection
    const findAndMonitorDealButtons = () => {
      dealButtonSelectors.forEach(selector => {
        const buttons = document.querySelectorAll(selector);
        buttons.forEach(button => {
          if (!button.hasAttribute('data-monitored')) {
            button.setAttribute('data-monitored', 'true');
            button.addEventListener('click', (e) => {
              console.log('🎯 Deal button clicked:', button);
              // Only show modal if this looks like a real trade button
              if (this.isValidTradeButton(button as HTMLElement)) {
                // Don't show trade reason modal here - wait for trade execution detection
                console.log('✅ Valid trade button clicked, waiting for trade execution...');
              }
            });
          }
        });
      });
    };

    // Initial scan
    findAndMonitorAmountInputs();
    findAndMonitorDealButtons();

    // Use MutationObserver for dynamic content
    this.tradeObserver = new MutationObserver((mutations) => {
      let shouldRescan = false;

      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;

            // Check if new trade-related elements were added
            dealButtonSelectors.forEach(selector => {
              if (element.matches && element.matches(selector)) {
                shouldRescan = true;
              }
              if (element.querySelectorAll && element.querySelectorAll(selector).length > 0) {
                shouldRescan = true;
              }
            });
          }
        });
      });

      if (shouldRescan) {
        setTimeout(() => {
          findAndMonitorAmountInputs();
          findAndMonitorDealButtons();
        }, 500);
      }
    });

    this.tradeObserver.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  private isValidTradeButton(button: HTMLElement): boolean {
    // Check if this is actually a trade button and not just any button
    const buttonText = button.textContent?.toLowerCase() || '';
    const buttonClass = button.className.toLowerCase();
    const buttonId = button.id.toLowerCase();

    // Valid trade button indicators
    const validIndicators = [
      'up', 'down', 'higher', 'lower', 'call', 'put',
      'deal', 'trade', 'buy', 'sell'
    ];

    // Invalid button indicators (avoid false positives)
    const invalidIndicators = [
      'close', 'cancel', 'menu', 'settings', 'help',
      'login', 'register', 'deposit', 'withdraw'
    ];

    // Check for invalid indicators first
    for (const invalid of invalidIndicators) {
      if (buttonText.includes(invalid) || buttonClass.includes(invalid) || buttonId.includes(invalid)) {
        return false;
      }
    }

    // Check for valid indicators
    for (const valid of validIndicators) {
      if (buttonText.includes(valid) || buttonClass.includes(valid) || buttonId.includes(valid)) {
        return true;
      }
    }

    // Additional checks for button context
    const parent = button.parentElement;
    if (parent) {
      const parentClass = parent.className.toLowerCase();
      if (parentClass.includes('deal') || parentClass.includes('trade') || parentClass.includes('trading')) {
        return true;
      }
    }

    return false;
  }

  private showTradeReasonModal() {
    const now = Date.now();
    const modalCooldown = 10000; // 10 seconds cooldown

    console.log('🎯 Attempting to show trade reason modal');
    console.log(`⏰ Time since last trade modal: ${now - this.lastTradeReasonModalTime}ms (cooldown: ${modalCooldown}ms)`);

    if (now - this.lastTradeReasonModalTime < modalCooldown) {
      console.log('⏰ Trade reason modal on cooldown');
      return;
    }

    // Check if modal already exists
    const existingModal = document.getElementById('trade-reason-modal');
    if (existingModal) {
      console.log('📝 Removing existing trade reason modal');
      existingModal.remove();
    }

    this.lastTradeReasonModalTime = now;
    console.log('🤔 Creating trade reason modal');

    const modal = document.createElement('div');
    modal.id = 'trade-reason-modal';
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    modal.innerHTML = `
      <div style="
        background: white;
        padding: 24px;
        border-radius: 16px;
        max-width: 400px;
        width: 90%;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        text-align: center;
      ">
        <div style="font-size: 32px; margin-bottom: 16px;">🤔</div>
        <h3 style="color: #1976d2; margin: 0 0 16px 0;">Lý do vào lệnh</h3>
        <p style="color: #666; margin: 0 0 20px 0; font-size: 14px;">
          Lệnh này bạn vào vì điều gì?
        </p>

        <div style="display: flex; flex-direction: column; gap: 8px; margin-bottom: 20px;">
          <button class="reason-btn" data-reason="method" style="
            background: #4caf50;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">✅ Theo nguyên tắc phương pháp</button>
          
          <button class="reason-btn" data-reason="greed" style="
            background: #ff9800;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🤑 Vì tâm tham lam</button>
          
          <button class="reason-btn" data-reason="anger" style="
            background: #f44336;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">😡 Vì tâm giận dữ (gỡ lại)</button>
          
          <button class="reason-btn" data-reason="guess" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🎲 Vì suy đoán tầm bậy</button>
        </div>

        <button onclick="document.getElementById('trade-reason-modal').remove()" style="
          background: #666;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 12px;
        ">Bỏ qua</button>
      </div>
    `;

    document.body.appendChild(modal);

    // Add event listeners
    modal.querySelectorAll('.reason-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const reason = (e.target as HTMLElement).getAttribute('data-reason');
        this.handleTradeReason(reason);
        modal.remove();
      });
    });
  }

  private async handleTradeReason(reason: string | null) {
    if (reason === 'method') {
      // Continue trading - good reason
      console.log('✅ Trade based on method principles');
    } else {
      // Show warning and suggest meditation
      const warningModal = document.createElement('div');
      warningModal.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #fff3cd;
        border: 1px solid #ffc107;
        padding: 16px;
        border-radius: 8px;
        z-index: 999999;
        max-width: 300px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      `;

      warningModal.innerHTML = `
        <div style="color: #856404;">
          <strong>⚠️ Cảnh báo tâm lý</strong>
          <p style="margin: 8px 0; font-size: 14px;">
            Giao dịch không theo nguyên tắc có thể gây thua lỗ. Hãy nghỉ ngơi và rèn tâm.
          </p>
          <button onclick="window.open('${chrome.runtime.getURL('options.html#meditation')}', '_blank')" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 8px;
          ">🧘‍♂️ Thiền định</button>
          <button onclick="this.parentElement.parentElement.remove()" style="
            background: #666;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
          ">Đóng</button>
        </div>
      `;

      document.body.appendChild(warningModal);

      // Auto remove after 10 seconds
      setTimeout(() => {
        if (warningModal.parentElement) {
          warningModal.remove();
        }
      }, 10000);
    }

    // Save trade reason
    await chrome.storage.local.set({
      lastTradeReason: {
        timestamp: Date.now(),
        reason,
        amount: this.lastTradeAmount
      }
    });
  }

  private observeTradeResults() {
    console.log('📊 Starting trade result observation...');

    // Track existing elements to avoid false triggers
    const existingElements = new Set<Element>();

    // Initial scan to mark existing elements
    const initialScan = () => {
      const selectors = [
        'progress-bar-item',        // Trade execution (unique when appears)
        'progress-bar-timeline',    // Trade timeline (unique when appears)
        '.option.win',              // Win results
        '.option:not(.win)',        // Loss results (option without win)
        'lottie-player.lose',       // Loss animation
        'option-animation'          // Animation wrapper
      ];

      let existingCount = 0;
      selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
          existingElements.add(el);
          existingCount++;
        });
        console.log(`📊 Found ${elements.length} existing elements for selector: ${selector}`);
      });

      console.log(`📊 Initial scan: marked ${existingCount} existing elements total`);
    };

    // Run initial scan after a delay
    setTimeout(initialScan, 1000);

    // Monitor for NEW elements being added to DOM
    const resultObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.addedNodes.length > 0) {
          console.log('🔄 DOM mutation detected, added nodes:', mutation.addedNodes.length);
        }

        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;
            const tagName = element.tagName?.toLowerCase() || '';
            const className = element.className || '';

            console.log('➕ New element added:', tagName, className);

            // Only process NEW elements that weren't in initial scan
            if (!existingElements.has(element)) {
              console.log('🆕 Processing truly new element:', tagName);
              this.processNewElement(element);
            } else {
              console.log('⏭️ Skipping existing element:', tagName);
            }

            // Also check child elements of new nodes
            const children = element.querySelectorAll('*');
            if (children.length > 0) {
              console.log('👶 Checking', children.length, 'child elements');
              children.forEach(child => {
                if (!existingElements.has(child)) {
                  const childTag = child.tagName?.toLowerCase() || '';
                  const childClass = child.className || '';
                  console.log('👶 Processing new child:', childTag, childClass);
                  this.processNewElement(child);
                }
              });
            }
          }
        });

        // Monitor attribute changes on existing elements
        if (mutation.type === 'attributes') {
          const target = mutation.target as Element;
          const now = Date.now();
          const tagName = target.tagName?.toLowerCase() || '';
          const className = target.className || '';
          const attributeName = mutation.attributeName;

          console.log('🎨 Attribute change detected:', attributeName, tagName, className);

          // Check for trade end indicators (profit lost, trade completion)
          if (attributeName === 'class' &&
              (className.includes('profit lost') ||
               className.includes('lose') ||
               className.includes('win'))) {
            console.log('🔚 Trade end detected via class change:', className);
            this.handleTradeEnd();
          }

          // Check for Angular animation classes (trade execution)
          if (attributeName === 'class' &&
              (className.includes('ng-trigger-progressBarItemInOut') ||
               className.includes('ng-star-inserted') ||
               className.includes('ng-trigger'))) {

            if ((tagName === 'progress-bar-item' || tagName === 'main') &&
                this.isElementVisible(target as HTMLElement)) {
              console.log('🎯 Angular animation detected for trade element:', tagName);
              this.checkForTradeExecution(target as HTMLElement);
            }
          }

          // Check for lottie-player class changes (lose animation)
          if (attributeName === 'class' && tagName === 'lottie-player' && className.includes('lose')) {
            if (now - this.lastMindfulnessModalTime > 3000) {
              console.log('😔 Loss result detected via class change');
              this.lastMindfulnessModalTime = now;
              setTimeout(() => this.showMindfulnessModal('loss', true), 2000);
            } else {
              console.log('⏰ Loss modal on cooldown (class change)');
            }
          }

          // Check for style changes that might indicate visibility
          if (attributeName === 'style') {
            if ((tagName === 'progress-bar-item' ||
                 (tagName === 'main' && className.includes('container'))) &&
                this.isElementVisible(target as HTMLElement)) {
              console.log('👁️ Style change detected for trade element:', tagName);
              this.checkForTradeExecution(target as HTMLElement);
            }
          }
        }
      });
    });

    resultObserver.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'style', 'data-state']
    });

    // Additional observer for visibility changes
    this.startVisibilityObserver();
  }

  private startVisibilityObserver() {
    console.log('👁️ Starting visibility observer for trade execution detection');

    // Check for existing elements that might become visible
    const checkExistingElements = () => {
      // Look for main.container elements
      const containers = document.querySelectorAll('main.container');
      containers.forEach(container => {
        if (this.isElementVisible(container as HTMLElement)) {
          console.log('👁️ Found visible container:', container.className);
          this.checkForTradeExecution(container as HTMLElement);
        }
      });

      // Look for progress-bar-item elements
      const progressItems = document.querySelectorAll('progress-bar-item');
      progressItems.forEach(item => {
        if (this.isElementVisible(item as HTMLElement)) {
          console.log('👁️ Found visible progress-bar-item:', item.className);
          this.checkForTradeExecution(item as HTMLElement);
        }
      });
    };

    // Check every 1 second for visibility changes (more responsive)
    setInterval(checkExistingElements, 1000);

    // Also check immediately
    setTimeout(checkExistingElements, 500);
  }

  private isElementVisible(element: HTMLElement): boolean {
    const style = window.getComputedStyle(element);
    const rect = element.getBoundingClientRect();

    const isVisible = style.display !== 'none' &&
                     style.visibility !== 'hidden' &&
                     style.opacity !== '0' &&
                     rect.width > 0 &&
                     rect.height > 0;

    return isVisible;
  }

  private checkForTradeExecution(element: HTMLElement) {
    const now = Date.now();
    const tagName = element.tagName?.toLowerCase() || '';
    const className = element.className || '';

    // Create unique identifier for this element
    const elementId = `${tagName}-${className.replace(/\s+/g, '-')}`;
    const isCurrentlyValid = this.isValidTradeExecution(element);
    const wasValidBefore = this.lastTradeElementState.get(elementId) || false;

    console.log('🔍 Checking trade execution:', tagName, `valid=${isCurrentlyValid}, wasBefore=${wasValidBefore}`);

    // Only trigger if element became valid (state change from invalid to valid)
    if (isCurrentlyValid && !wasValidBefore) {
      console.log('🆕 NEW trade execution state detected (became valid)');
      console.log('🎯 Triggering trade reason modal for new trade');
      this.lastTradeReasonModalTime = now;
      this.currentTradeId = elementId;
      setTimeout(() => this.showTradeReasonModal(), 1000);
    } else if (isCurrentlyValid && wasValidBefore) {
      // Still valid - check if this is a different trade by looking at content
      const lastContentCheck = this.lastContentCheckTime.get(elementId) || 0;

      // Only check content every 3 seconds to avoid spam
      if (now - lastContentCheck > 3000) {
        const currentContent = this.getTradeContent(element);
        const lastContent = this.lastTradeElementState.get(`${elementId}-content`);

        console.log('🔍 Content check:', { current: currentContent, last: lastContent });

        if (currentContent !== lastContent && lastContent !== undefined) {
          console.log('🔄 Trade content changed - new trade detected');
          console.log('🎯 Triggering trade reason modal for content change');
          this.lastTradeReasonModalTime = now;
          this.currentTradeId = elementId;
          setTimeout(() => this.showTradeReasonModal(), 1000);
        } else {
          console.log('⏭️ Trade content unchanged (same trade)');
        }

        this.lastTradeElementState.set(`${elementId}-content`, currentContent);
        this.lastContentCheckTime.set(elementId, now);
      } else {
        console.log('⏭️ Trade execution still valid (content check on cooldown)');
      }
    } else if (!isCurrentlyValid && wasValidBefore) {
      console.log('🔚 Trade execution ended (became invalid)');
      this.handleTradeEnd();
    }

    // Update state
    this.lastTradeElementState.set(elementId, isCurrentlyValid);
  }

  private getTradeContent(element: HTMLElement): string {
    // Get stable content that only changes between different trades
    const profitText = element.querySelector('.profit')?.textContent || '';
    const amountText = element.querySelector('.offset-left-sm-2xs')?.textContent || '';
    const arrowText = element.querySelector('.arrow')?.textContent || '';
    const iconSrc = element.querySelector('.asset-icon')?.getAttribute('src') || '';

    // Don't include progress width or time as they change during the same trade
    const stableContent = `${profitText}-${amountText}-${arrowText}-${iconSrc}`;

    console.log('📊 Trade content:', stableContent);
    return stableContent;
  }

  private handleTradeEnd() {
    console.log('🔚 Trade ended - resetting trade execution state');

    // Reset all trade execution states
    this.lastTradeElementState.clear();
    this.lastContentCheckTime.clear();
    this.currentTradeId = null;

    console.log('✅ Trade state reset - ready for new trade detection');
  }

  private processNewElement(element: Element) {
    const now = Date.now();
    const tagName = element.tagName?.toLowerCase() || '';
    const className = element.className || '';

    console.log('🔍 Processing new element:', tagName, className);

    // Check for trade execution (multiple possible indicators)
    if (tagName === 'progress-bar-item' ||
        tagName === 'progress-bar-timeline' ||
        (tagName === 'main' && className.includes('container'))) {
      console.log('🎯 Found potential trade execution element:', tagName, className);

      if (this.isValidTradeExecution(element as HTMLElement)) {
        console.log('✅ Valid trade execution confirmed');
        console.log('🎯 Forcing trade reason modal (ignoring cooldown for real trade)');
        this.lastTradeReasonModalTime = now;
        setTimeout(() => this.showTradeReasonModal(), 1000);
      } else {
        console.log('❌ Trade execution validation failed');
      }
    }

    // Check for win results (option.win is reliable)
    if (element.matches && element.matches('.option.win')) {
      if (this.isValidWinResult(element as HTMLElement)) {
        console.log('🎉 NEW win result detected:', className);
        console.log('🎯 Forcing win modal (ignoring cooldown for real trade result)');
        this.lastMindfulnessModalTime = now;
        setTimeout(() => this.showMindfulnessModal('win', true), 2000);
      }
    }

    // Check for loss results with priority system
    let lossDetected = false;

    // Priority 1: lottie-player.lose (most reliable, trigger immediately)
    if (tagName === 'lottie-player' && className.includes('lose')) {
      if (this.isValidLossResult(element as HTMLElement)) {
        console.log('😔 NEW loss result detected (animation):', tagName, className);
        console.log('🎯 Forcing loss modal (ignoring cooldown for real trade result)');
        this.lastMindfulnessModalTime = now;
        setTimeout(() => this.showMindfulnessModal('loss', true), 2000);
        lossDetected = true;
      }
    }

    // Priority 2: div.option without win (only if animation not detected)
    if (!lossDetected &&
        tagName === 'div' &&
        className.includes('option') &&
        !className.includes('win') &&
        !className.includes('analytics')) {
      if (this.isValidLossResult(element as HTMLElement)) {
        console.log('😔 NEW loss result detected (option):', tagName, className);
        console.log('🎯 Forcing loss modal (ignoring cooldown for real trade result)');
        this.lastMindfulnessModalTime = now;
        setTimeout(() => this.showMindfulnessModal('loss', true), 2000);
      }
    }
  }

  private isValidTradeExecution(element: HTMLElement): boolean {
    const tagName = element.tagName?.toLowerCase() || '';
    const elementClass = element.className || '';

    console.log('🔍 Validating trade execution:', tagName, elementClass);

    // main.container with progress-bar-item indicates trade execution
    if (tagName === 'main' && elementClass.includes('container')) {
      const hasProgressBar = element.querySelector('progress-bar-item');
      const hasProgressTimeline = element.querySelector('progress-bar-timeline');
      const hasProfit = element.querySelector('.profit');

      console.log('📊 Container validation:', {
        hasProgressBar: !!hasProgressBar,
        hasProgressTimeline: !!hasProgressTimeline,
        hasProfit: !!hasProfit
      });

      const isValid = !!(hasProgressBar || hasProgressTimeline);
      console.log('📊 Container valid:', isValid);
      return isValid;
    }

    // progress-bar-item is unique and only appears when trade starts
    if (tagName === 'progress-bar-item') {
      const hasProfit = element.querySelector('.profit');
      const hasArrow = element.querySelector('.arrow');
      const hasTimeline = element.querySelector('progress-bar-timeline');
      const hasAssetIcon = element.querySelector('.asset-icon');

      console.log('📈 Progress bar validation:', {
        hasProfit: !!hasProfit,
        hasArrow: !!hasArrow,
        hasTimeline: !!hasTimeline,
        hasAssetIcon: !!hasAssetIcon
      });

      if (hasProfit) {
        console.log('💰 Profit text:', hasProfit.textContent);
      }
      if (hasArrow) {
        console.log('➡️ Arrow text:', hasArrow.textContent);
      }

      // Simplified validation - just need profit OR arrow (trade indicators)
      const isValid = !!(hasProfit || hasArrow || hasAssetIcon);
      console.log('📈 Trade execution valid:', isValid);
      return isValid;
    }

    // progress-bar-timeline also indicates active trade
    if (tagName === 'progress-bar-timeline') {
      const hasProgress = element.querySelector('.progress');
      const hasText = element.querySelector('.text');

      console.log('⏱️ Timeline validation:', {
        hasProgress: !!hasProgress,
        hasText: !!hasText
      });

      if (hasText) {
        console.log('⏰ Timeline text:', hasText.textContent);
      }

      const isValid = !!(hasProgress && hasText);
      console.log('⏱️ Timeline valid:', isValid);
      return isValid;
    }

    console.log('❌ Not a recognized trade execution element');
    return false;
  }

  private isValidWinResult(element: HTMLElement): boolean {
    const elementClass = element.className || '';
    const tagName = element.tagName?.toLowerCase() || '';

    console.log('🏆 Validating win result:', tagName, elementClass);

    // Only check for option.win (most reliable)
    if (elementClass.includes('option') && elementClass.includes('win')) {
      const currencyElement = element.querySelector('.currency');
      if (currencyElement) {
        const currencyText = currencyElement.textContent || '';
        console.log('💰 Win currency text:', currencyText);

        // Must have profit amount (not 0,00)
        const hasProfit = !currencyText.includes('0,00') &&
                         (currencyText.includes('$') || currencyText.includes('₫'));

        console.log('💰 Has profit:', hasProfit);
        return hasProfit;
      }

      // If no currency element, assume it's valid win
      console.log('✅ Win class detected without currency check');
      return true;
    }

    return false;
  }

  private isValidLossResult(element: HTMLElement): boolean {
    const elementClass = element.className || '';
    const tagName = element.tagName?.toLowerCase() || '';

    console.log('😔 Validating loss result:', tagName, elementClass);

    // Priority 1: lottie-player.lose (most reliable)
    if (tagName === 'lottie-player' && elementClass.includes('lose')) {
      console.log('❌ Loss animation detected (priority)');
      return true;
    }

    // Priority 2: div.option without win class (but exclude analytics and other non-result elements)
    if (tagName === 'div' &&
        elementClass.includes('option') &&
        !elementClass.includes('win') &&
        !elementClass.includes('analytics') &&  // Exclude analytics elements
        !elementClass.includes('time') &&       // Exclude time elements
        !elementClass.includes('button') &&     // Exclude button elements
        !elementClass.includes('close')) {      // Exclude close elements

      const currencyElement = element.querySelector('.currency');
      if (currencyElement) {
        const currencyText = currencyElement.textContent || '';
        console.log('💸 Loss currency text:', currencyText);

        // Must show 0,00 for loss
        const isLoss = currencyText.includes('0,00');
        console.log('💸 Is loss:', isLoss);

        if (isLoss) {
          // Additional check: must have badge and assets (real trade result)
          const hasBadge = element.querySelector('.badge');
          const hasAssets = element.querySelector('.assets');
          console.log('🔍 Trade result structure:', { hasBadge: !!hasBadge, hasAssets: !!hasAssets });

          return !!(hasBadge && hasAssets);
        }
      }
    }

    return false;
  }

  private showMindfulnessModal(result: 'win' | 'loss', forceShow: boolean = false) {
    const now = Date.now();
    const modalCooldown = 3000; // Reduced to 3s for testing

    console.log(`🎯 Attempting to show mindfulness modal for ${result} (force: ${forceShow})`);
    console.log(`⏰ Time since last modal: ${now - this.lastMindfulnessModalTime}ms (cooldown: ${modalCooldown}ms)`);

    if (!forceShow && now - this.lastMindfulnessModalTime < modalCooldown) {
      console.log('⏰ Mindfulness modal on cooldown');
      return;
    }

    // Check if modal already exists
    const existingModal = document.querySelector('[id*="mindfulness-modal"]');
    if (existingModal) {
      console.log('🧘‍♂️ Removing existing modal:', existingModal.id);
      existingModal.remove();
    }

    console.log(`🙏 Creating mindfulness modal for ${result}`);

    const isWin = result === 'win';
    const suggestions = isWin ? [
      'Tôi biết ơn vì kết quả tốt này và sẽ không để nó làm tôi kiêu ngạo',
      'Thắng lợi này là nhờ sự chuẩn bị kỹ lưỡng và tâm tỉnh thức',
      'Tôi sẽ giữ tâm bình thản và tiếp tục theo nguyên tắc',
      'Mọi thắng lợi đều vô thường, tôi không bám víu vào nó'
    ] : [
      'Thua lỗ là bài học quý báu để tôi trưởng thành hơn',
      'Tôi chấp nhận kết quả này với tâm bình thản và không giận dữ',
      'Mọi thua lỗ đều vô thường, tôi sẽ học hỏi và tiến bước',
      'Tôi buông bỏ sự thất vọng và tập trung vào cải thiện bản thân'
    ];

    const modal = document.createElement('div');
    modal.id = `mindfulness-modal-${result}-${Date.now()}`;
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    modal.innerHTML = `
      <div style="
        background: white;
        padding: 24px;
        border-radius: 16px;
        max-width: 400px;
        width: 90%;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        text-align: center;
      ">
        <div style="font-size: 32px; margin-bottom: 16px;">${isWin ? '🙏' : '🧘‍♂️'}</div>
        <h3 style="color: ${isWin ? '#4caf50' : '#ff9800'}; margin: 0 0 16px 0;">
          ${isWin ? 'Lời buông xả cho thắng lợi' : 'Lời buông xả cho thua lỗ'}
        </h3>
        
        <div style="margin-bottom: 20px;">
          <p style="color: #666; margin: 0 0 16px 0; font-size: 14px;">
            Hãy chọn một câu để thực hành buông xả:
          </p>
          
          <div style="display: flex; flex-direction: column; gap: 8px;">
            ${suggestions.map((suggestion, index) => `
              <button class="mindfulness-btn" data-text="${suggestion}" style="
                background: ${isWin ? '#e8f5e8' : '#fff3e0'};
                color: ${isWin ? '#2e7d32' : '#f57c00'};
                border: 1px solid ${isWin ? '#c8e6c9' : '#ffcc02'};
                padding: 12px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 13px;
                text-align: left;
                line-height: 1.4;
              ">${suggestion}</button>
            `).join('')}
          </div>
        </div>

        <button onclick="this.parentElement.parentElement.remove()" style="
          background: #666;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 12px;
        ">Đóng</button>
      </div>
    `;

    document.body.appendChild(modal);

    // Add event listeners
    modal.querySelectorAll('.mindfulness-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const text = (e.target as HTMLElement).getAttribute('data-text');
        this.showMindfulnessConfirmation(text || '');
        modal.remove();
      });
    });

    // Auto remove after 30 seconds
    setTimeout(() => {
      if (modal.parentElement) {
        modal.remove();
      }
    }, 30000);
  }

  private showMindfulnessConfirmation(text: string) {
    const toast = document.createElement('div');
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4caf50;
      color: white;
      padding: 16px;
      border-radius: 8px;
      z-index: 999999;
      max-width: 300px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      line-height: 1.4;
    `;

    toast.innerHTML = `
      <div>
        <strong>🙏 Thực hành buông xả</strong>
        <p style="margin: 8px 0 0 0;">"${text}"</p>
      </div>
    `;

    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
      if (toast.parentElement) {
        toast.remove();
      }
    }, 5000);
  }

  private async showBlockedMessage() {
    const result = await chrome.storage.local.get([
      'blockUntil',
      'blockDurationMinutes',
      'blockDate'
    ]);

    const overlay = document.createElement('div');
    overlay.id = 'trading-block-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.9);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    const timeRemaining = this.calculateTimeRemaining(result);
    const durationText = this.formatDuration(result.blockDurationMinutes || 1440);

    overlay.innerHTML = `
      <div style="
        background: white;
        padding: 40px;
        border-radius: 16px;
        text-align: center;
        max-width: 500px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="font-size: 64px; margin-bottom: 20px;">🧘‍♂️</div>
        <h2 style="color: #d32f2f; margin: 0 0 16px 0;">Giao dịch bị khóa</h2>
        <p style="color: #666; margin: 0 0 16px 0; line-height: 1.5;">
          Hệ thống đã khóa giao dịch trong <strong>${durationText}</strong> để bảo vệ tâm lý của bạn.<br>
          Hãy sử dụng thời gian này để nghỉ ngơi và rèn luyện tâm.
        </p>
        <div id="countdown" style="
          background: #f5f5f5;
          padding: 16px;
          border-radius: 8px;
          margin: 16px 0;
          font-size: 18px;
          font-weight: bold;
          color: #d32f2f;
        ">
          Còn lại: ${timeRemaining}
        </div>
        <div style="display: flex; gap: 12px; justify-content: center;">
          <button id="meditation-btn" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🧘‍♂️ Thiền định</button>
          <button id="close-tab-btn" style="
            background: #666;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">❌ Đóng tab</button>
        </div>
        <p style="color: #999; font-size: 12px; margin: 16px 0 0 0;">
          Thời gian khóa dựa trên đánh giá tâm lý của bạn
        </p>
      </div>
    `;

    document.body.appendChild(overlay);
    this.startCountdown(result);

    document.getElementById('meditation-btn')?.addEventListener('click', () => {
      this.openMeditationOptions();
    });

    document.getElementById('close-tab-btn')?.addEventListener('click', () => {
      window.close();
    });
  }

  private calculateTimeRemaining(blockInfo: any): string {
    const now = new Date();

    if (blockInfo.blockUntil) {
      const blockUntil = new Date(blockInfo.blockUntil);
      const diffMs = blockUntil.getTime() - now.getTime();

      if (diffMs <= 0) return 'Đã hết hạn';

      const hours = Math.floor(diffMs / (1000 * 60 * 60));
      const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

      if (hours > 0) {
        return `${hours} tiếng ${minutes} phút`;
      } else {
        return `${minutes} phút`;
      }
    }

    return 'Không xác định';
  }

  private startCountdown(blockInfo: any) {
    const countdownElement = document.getElementById('countdown');
    if (!countdownElement) return;

    const updateCountdown = () => {
      const timeRemaining = this.calculateTimeRemaining(blockInfo);
      countdownElement.textContent = `Còn lại: ${timeRemaining}`;

      if (timeRemaining === 'Đã hết hạn') {
        window.location.reload();
      }
    };

    const interval = setInterval(updateCountdown, 60000);

    window.addEventListener('beforeunload', () => {
      clearInterval(interval);
    });
  }

  private async checkPsychologyConfirmation() {
    try {
      const result = await chrome.storage.local.get(['needsPsychologyConfirmation']);

      if (result.needsPsychologyConfirmation) {
        this.showPsychologyConfirmation();
      }
    } catch (error) {
      console.error('Error checking psychology confirmation:', error);
    }
  }

  private showPsychologyConfirmation() {
    const modal = document.createElement('div');
    modal.id = 'psychology-confirmation-modal';
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    modal.innerHTML = `
      <div style="
        background: white;
        padding: 32px;
        border-radius: 16px;
        text-align: center;
        max-width: 450px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="font-size: 48px; margin-bottom: 16px;">🧠</div>
        <h3 style="color: #1976d2; margin: 0 0 16px 0;">Xác nhận tâm lý</h3>
        <p style="color: #666; margin: 0 0 24px 0; line-height: 1.5;">
          Bạn đã hoàn thành thiền định hoặc nghỉ ngơi?<br>
          Tâm lý hiện tại có ổn định và sẵn sàng giao dịch không?
        </p>

        <div style="margin-bottom: 24px;">
          <p style="color: #333; font-weight: bold; margin: 0 0 12px 0;">
            Trạng thái tâm lý hiện tại:
          </p>
          <div style="display: flex; flex-direction: column; gap: 8px;">
            <button class="psychology-option" data-state="balanced" style="
              background: #e8f5e8;
              color: #2e7d32;
              border: 2px solid #e8f5e8;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😌 Cân bằng và tỉnh táo</button>
            <button class="psychology-option" data-state="confident" style="
              background: #e3f2fd;
              color: #1976d2;
              border: 2px solid #e3f2fd;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😊 Tự tin và kiên nhẫn</button>
            <button class="psychology-option" data-state="not_ready" style="
              background: #fff3e0;
              color: #f57c00;
              border: 2px solid #fff3e0;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😐 Chưa sẵn sàng, cần thêm thời gian</button>
          </div>
        </div>

        <div style="display: flex; gap: 12px; justify-content: center;">
          <button id="continue-trading-btn" style="
            background: #4caf50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            opacity: 0.5;
          " disabled>✅ Tiếp tục giao dịch</button>
          <button id="more-meditation-btn" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🧘‍♂️ Thiền thêm</button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    let selectedState = '';

    modal.querySelectorAll('.psychology-option').forEach(button => {
      button.addEventListener('click', (e) => {
        const target = e.target as HTMLElement;
        selectedState = target.getAttribute('data-state') || '';

        modal.querySelectorAll('.psychology-option').forEach(btn => {
          (btn as HTMLElement).style.borderColor = (btn as HTMLElement).style.backgroundColor;
        });

        target.style.borderColor = '#1976d2';

        const continueBtn = document.getElementById('continue-trading-btn') as HTMLButtonElement;
        if (selectedState === 'balanced' || selectedState === 'confident') {
          continueBtn.disabled = false;
          continueBtn.style.opacity = '1';
        } else {
          continueBtn.disabled = true;
          continueBtn.style.opacity = '0.5';
        }
      });
    });

    document.getElementById('continue-trading-btn')?.addEventListener('click', () => {
      if (selectedState === 'balanced' || selectedState === 'confident') {
        this.confirmPsychologyAndContinue(selectedState);
      }
    });

    document.getElementById('more-meditation-btn')?.addEventListener('click', () => {
      this.openMeditationOptions();
    });
  }

  private async confirmPsychologyAndContinue(state: string) {
    try {
      await chrome.storage.local.set({
        needsPsychologyConfirmation: false,
        lastConfirmationTime: Date.now(),
        confirmedPsychologyState: state
      });

      const modal = document.getElementById('psychology-confirmation-modal');
      if (modal) {
        modal.remove();
      }

      this.showSuccessMessage('✅ Tâm lý đã được xác nhận! Bạn có thể giao dịch an toàn.');

    } catch (error) {
      console.error('Error confirming psychology:', error);
    }
  }

  private openMeditationOptions() {
    try {
      chrome.runtime.sendMessage({
        action: 'openOptions',
        tab: 'meditation'
      });
    } catch (error) {
      console.error('Error opening meditation options:', error);
      window.open(chrome.runtime.getURL('options.html#meditation'), '_blank');
    }
  }

  private showSuccessMessage(message: string) {
    const toast = document.createElement('div');
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4caf50;
      color: white;
      padding: 16px 24px;
      border-radius: 8px;
      z-index: 999999;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      animation: slideIn 0.3s ease-out;
    `;

    toast.textContent = message;

    const style = document.createElement('style');
    style.textContent = `
      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    `;
    document.head.appendChild(style);

    document.body.appendChild(toast);

    setTimeout(() => {
      toast.remove();
      style.remove();
    }, 3000);
  }
}

// Initialize when page loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => new BinomoTradingAssistant());
} else {
  new BinomoTradingAssistant();
}
