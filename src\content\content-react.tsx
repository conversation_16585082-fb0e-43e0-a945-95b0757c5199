// Binomo Trading Assistant - Content Script with React Components

import React from 'react';
import { createRoot } from 'react-dom/client';
import { ThemeProvider, createTheme, CssBaseline } from '@mui/material';
import './content.css';

// Import components
import { DailyGoalsModal } from '../components/DailyGoalsModal';
import { PsychologyAssessment } from '../components/PsychologyAssessment';
import { TradingMethodSelector } from '../components/TradingMethodSelector';
import { TradingAnalysis } from '../components/TradingAnalysis';
import { TradingInterface } from '../components/TradingInterface';
import { StatsDashboard } from '../components/StatsDashboard';
import { storage } from '../utils/storage';

// Material UI Theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#007bff',
    },
    secondary: {
      main: '#6c757d',
    },
  },
});

// Main Content Script Class
class BinomoTradingAssistant {
  private currentFlow: 'goals' | 'psychology' | 'method' | 'analysis' | 'trading' | null = null;
  private currentSession: any = null;
  private containers: Map<string, HTMLElement> = new Map();
  private roots: Map<string, any> = new Map();

  constructor() {
    this.init();
  }

  private async init() {
    console.log('🎯 Binomo Trading Assistant - Content Script Loaded');

    // Check if we're on the correct page
    if (!window.location.href.includes('binomo1.com/trading')) {
      console.log('Not on Binomo trading page, exiting...');
      return;
    }

    // Wait for page to be ready
    await this.waitForPageReady();
    
    // Add some delay to ensure page is fully loaded
    setTimeout(() => {
      this.startTradingFlow();
    }, 2000);

    // Setup message listener
    this.setupMessageListener();
  }

  private waitForPageReady(): Promise<void> {
    return new Promise((resolve) => {
      if (document.readyState === 'complete') {
        resolve();
      } else {
        window.addEventListener('load', () => resolve());
      }
    });
  }

  private createContainer(id: string): HTMLElement {
    // Remove existing container if any
    this.removeContainer(id);

    const container = document.createElement('div');
    container.id = id;
    container.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
      pointer-events: none;
    `;
    
    document.body.appendChild(container);
    this.containers.set(id, container);
    
    return container;
  }

  private removeContainer(id: string) {
    const existingContainer = this.containers.get(id);
    if (existingContainer && existingContainer.parentNode) {
      existingContainer.parentNode.removeChild(existingContainer);
    }
    
    const existingRoot = this.roots.get(id);
    if (existingRoot) {
      existingRoot.unmount();
      this.roots.delete(id);
    }
    
    this.containers.delete(id);
  }

  private renderComponent(containerId: string, component: React.ReactElement) {
    const container = this.createContainer(containerId);
    const root = createRoot(container);
    
    root.render(
      <ThemeProvider theme={theme}>
        <CssBaseline />
        {component}
      </ThemeProvider>
    );
    
    this.roots.set(containerId, root);
  }

  // Show daily goals modal
  public showDailyGoals() {
    console.log('Showing daily goals modal...');
    this.currentFlow = 'goals';
    
    this.renderComponent('daily-goals-modal', 
      <DailyGoalsModal
        open={true}
        onClose={() => this.removeContainer('daily-goals-modal')}
        onComplete={(goals) => {
          console.log('Daily goals completed:', goals);
          this.removeContainer('daily-goals-modal');
          this.showPsychologyAssessment();
        }}
      />
    );
  }

  // Show psychology assessment
  public showPsychologyAssessment() {
    console.log('Showing psychology assessment...');
    this.currentFlow = 'psychology';
    
    this.renderComponent('psychology-assessment',
      <PsychologyAssessment
        open={true}
        onClose={() => this.removeContainer('psychology-assessment')}
        onComplete={(state) => {
          console.log('Psychology assessment completed:', state);
          this.removeContainer('psychology-assessment');
          this.showTradingMethodSelector();
        }}
      />
    );
  }

  // Show trading method selector
  public showTradingMethodSelector() {
    console.log('Showing trading method selector...');
    this.currentFlow = 'method';
    
    this.renderComponent('trading-method-selector',
      <TradingMethodSelector
        open={true}
        onClose={() => this.removeContainer('trading-method-selector')}
        onComplete={(method) => {
          console.log('Trading method selected:', method);
          this.removeContainer('trading-method-selector');
          this.showTradingAnalysis(method);
        }}
      />
    );
  }

  // Show trading analysis
  public showTradingAnalysis(method: any) {
    console.log('Showing trading analysis...');
    this.currentFlow = 'analysis';
    
    // Get current psychology state
    storage.getCurrentPsychologyState().then(psychologyState => {
      if (!psychologyState) {
        console.error('No psychology state found');
        return;
      }

      this.renderComponent('trading-analysis',
        <TradingAnalysis
          open={true}
          onClose={() => this.removeContainer('trading-analysis')}
          onComplete={(sessionId) => {
            console.log('Trading analysis completed:', sessionId);
            this.removeContainer('trading-analysis');
            this.showTradingInterface(sessionId);
          }}
          onBack={() => {
            this.removeContainer('trading-analysis');
            this.showTradingMethodSelector();
          }}
          method={method}
          psychologyState={psychologyState}
        />
      );
    });
  }

  // Show trading interface
  public showTradingInterface(sessionId: string) {
    console.log('Showing trading interface...');
    this.currentFlow = 'trading';
    this.currentSession = sessionId;
    
    this.renderComponent('trading-interface',
      <TradingInterface
        sessionId={sessionId}
        onClose={() => this.removeContainer('trading-interface')}
      />
    );
  }

  // Show stats dashboard
  public showStatsDashboard() {
    console.log('Showing stats dashboard...');
    
    this.renderComponent('stats-dashboard',
      <StatsDashboard
        open={true}
        onClose={() => this.removeContainer('stats-dashboard')}
      />
    );
  }

  // Start the trading flow
  public async startTradingFlow() {
    try {
      console.log('🚀 Starting trading flow...');
      
      // Check if goals are set for today
      const todayGoals = await storage.getTodayGoals();
      
      if (!todayGoals) {
        this.showDailyGoals();
      } else {
        this.showPsychologyAssessment();
      }
    } catch (error) {
      console.error('Error starting trading flow:', error);
      alert('Không thể kết nối với JSON Server. Hãy chạy: npm run server');
    }
  }

  // Restart flow
  public restartFlow() {
    // Remove all existing modals
    this.containers.forEach((_, id) => {
      this.removeContainer(id);
    });
    
    this.currentFlow = null;
    this.currentSession = null;
    
    // Start fresh
    this.startTradingFlow();
  }

  // Setup message listener for popup commands
  private setupMessageListener() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      console.log('Content script received message:', request);
      
      switch (request.action) {
        case 'showStats':
          this.showStatsDashboard();
          break;
        case 'restartFlow':
          this.restartFlow();
          break;
        case 'startFlow':
          this.startTradingFlow();
          break;
        default:
          console.log('Unknown action:', request.action);
      }
      
      sendResponse({ success: true });
    });
  }
}

// Initialize when page is ready
document.addEventListener('DOMContentLoaded', () => {
  const assistant = new BinomoTradingAssistant();
  
  // Add global functions for testing
  (window as any).BinomoTradingAssistant = assistant;
});

// Also initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    const assistant = new BinomoTradingAssistant();
    (window as any).BinomoTradingAssistant = assistant;
  });
} else {
  const assistant = new BinomoTradingAssistant();
  (window as any).BinomoTradingAssistant = assistant;
}
