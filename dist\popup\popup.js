import{g as M,a as W,r as j,u as w,j as s,s as P,c as R,b as L,d as m,e as V,T as E,C as D,B as h,G as n,f as t,I as k,S as T,A as I,h as p,i as y,k as G,l as c,P as H,m as z,n as v,o as b,R as O}from"../assets/TrendingUp-BecRsdBW.js";function U(i){return M("MuiCardActions",i)}W("MuiCardActions",["root","spacing"]);const N=i=>{const{classes:e,disableSpacing:o}=i;return L({root:["root",!o&&"spacing"]},U,e)},F=P("div",{name:"MuiCardActions",slot:"Root",overridesResolver:(i,e)=>{const{ownerState:o}=i;return[e.root,!o.disableSpacing&&e.spacing]}})({display:"flex",alignItems:"center",padding:8,variants:[{props:{disableSpacing:!1},style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),C=j.forwardRef(function(e,o){const d=w({props:e,name:"MuiCardActions"}),{disableSpacing:x=!1,className:u,...f}=d,a={...d,disableSpacing:x},l=N(a);return s.jsx(F,{className:R(l.root,u),ownerState:a,ref:o,...f})}),J=m(s.jsx("path",{d:"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20z"})),q=m(s.jsx("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2M9 17H7v-7h2zm4 0h-2V7h2zm4 0h-2v-4h2z"})),K=m(s.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z"})),X=m(s.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m1 15h-2v-2h2zm0-4h-2V7h2z"})),$=m(s.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m1 15h-2v-6h2zm0-8h-2V7h2z"})),_=m(s.jsx("path",{d:"M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-7h-2zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3z"})),S=m(s.jsx("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4z"})),Q=V({palette:{primary:{main:"#007bff"},secondary:{main:"#6c757d"},success:{main:"#28a745"},error:{main:"#dc3545"},warning:{main:"#ffc107"}},typography:{h6:{fontWeight:600},body2:{fontSize:"0.875rem"}}}),Y=()=>{const[i,e]=j.useState("checking"),[o,d]=j.useState("main");j.useEffect(()=>{x()},[]);const x=async()=>{try{const l=await fetch("http://localhost:3001/dailyGoals?_limit=1");e(l.ok?"online":"offline")}catch{e("offline")}},u=()=>{chrome.tabs.query({active:!0,currentWindow:!0},l=>{var g;(g=l[0])!=null&&g.id&&chrome.tabs.sendMessage(l[0].id,{action:"showStats"})})},f=()=>{chrome.tabs.query({active:!0,currentWindow:!0},l=>{var g;(g=l[0])!=null&&g.id&&chrome.tabs.sendMessage(l[0].id,{action:"restartFlow"})})},a=()=>{chrome.tabs.create({url:"https://binomo1.com/trading"})};return s.jsxs(E,{theme:Q,children:[s.jsx(D,{}),o==="main"?s.jsx(Z,{apiStatus:i,onOpenTradingPage:a,onOpenStatsPage:u,onRestartFlow:f,onOpenSettings:()=>d("settings"),onRefreshApi:x}):s.jsx(ss,{onBack:()=>d("main")})]})},Z=({apiStatus:i,onOpenTradingPage:e,onOpenStatsPage:o,onRestartFlow:d,onOpenSettings:x,onRefreshApi:u})=>s.jsxs(h,{sx:{width:380,p:2},children:[s.jsxs(n,{container:!0,spacing:2,sx:{mb:2},children:[s.jsx(n,{size:10,children:s.jsxs(h,{sx:{textAlign:"center"},children:[s.jsx(t,{variant:"h6",sx:{display:"flex",alignItems:"center",justifyContent:"center",gap:1},children:"🎯 Binomo Trading Assistant"}),s.jsx(t,{variant:"body2",color:"text.secondary",sx:{fontStyle:"italic"},children:"Công cụ hỗ trợ trading có kỷ luật"})]})}),s.jsx(n,{size:2,children:s.jsx(k,{onClick:x,size:"small",children:s.jsx(T,{})})})]}),s.jsx(n,{container:!0,spacing:2,sx:{mb:2},children:s.jsx(n,{size:12,children:s.jsxs(I,{severity:i==="online"?"success":i==="offline"?"error":"info",icon:i==="online"?s.jsx(K,{}):i==="offline"?s.jsx(X,{}):s.jsx($,{}),sx:{fontSize:"0.75rem"},action:s.jsx(k,{size:"small",onClick:u,children:s.jsx(S,{fontSize:"small"})}),children:[i==="checking"&&"Đang kiểm tra kết nối...",i==="online"&&"JSON Server đang hoạt động",i==="offline"&&"JSON Server không hoạt động - Hãy chạy npm run server"]})})}),s.jsx(n,{container:!0,spacing:2,sx:{mb:2},children:s.jsx(n,{size:12,children:s.jsxs(p,{children:[s.jsxs(y,{sx:{pb:1},children:[s.jsxs(t,{variant:"subtitle2",sx:{mb:1,display:"flex",alignItems:"center",gap:1},children:[s.jsx(G,{color:"primary"}),"Giao dịch"]}),s.jsx(t,{variant:"body2",color:"text.secondary",children:"Mở trang Binomo để bắt đầu giao dịch"})]}),s.jsx(C,{children:s.jsx(c,{fullWidth:!0,variant:"contained",onClick:e,startIcon:s.jsx(_,{}),disabled:i==="offline",children:"Mở trang giao dịch"})})]})})}),s.jsxs(n,{container:!0,spacing:2,sx:{mb:2},children:[s.jsx(n,{size:6,children:s.jsxs(p,{sx:{height:"100%"},children:[s.jsxs(y,{sx:{pb:1},children:[s.jsxs(t,{variant:"subtitle2",sx:{mb:1,display:"flex",alignItems:"center",gap:1},children:[s.jsx(q,{color:"success"}),"Thống kê"]}),s.jsx(t,{variant:"body2",color:"text.secondary",sx:{fontSize:"0.75rem"},children:"Xem báo cáo chi tiết"})]}),s.jsx(C,{children:s.jsx(c,{fullWidth:!0,variant:"outlined",color:"success",onClick:o,size:"small",disabled:i==="offline",children:"Xem thống kê"})})]})}),s.jsx(n,{size:6,children:s.jsxs(p,{sx:{height:"100%"},children:[s.jsxs(y,{sx:{pb:1},children:[s.jsxs(t,{variant:"subtitle2",sx:{mb:1,display:"flex",alignItems:"center",gap:1},children:[s.jsx(S,{color:"secondary"}),"Khởi động lại"]}),s.jsx(t,{variant:"body2",color:"text.secondary",sx:{fontSize:"0.75rem"},children:"Reset flow giao dịch"})]}),s.jsx(C,{children:s.jsx(c,{fullWidth:!0,variant:"outlined",color:"secondary",onClick:d,size:"small",disabled:i==="offline",children:"Khởi động lại"})})]})})]}),s.jsx(n,{container:!0,spacing:2,children:s.jsx(n,{size:12,children:s.jsxs(H,{sx:{p:1.5,backgroundColor:"#f8f9fa"},children:[s.jsxs(t,{variant:"body2",color:"text.secondary",sx:{textAlign:"center",fontSize:"0.75rem"},children:[s.jsx("strong",{children:"Lưu ý:"})," Extension chỉ hoạt động trên trang binomo1.com/trading"]}),s.jsxs(h,{sx:{display:"flex",justifyContent:"center",gap:1,mt:1},children:[s.jsx(z,{label:"Material UI v7+",size:"small",color:"primary",variant:"outlined"}),s.jsx(z,{label:"JSON Server",size:"small",color:"secondary",variant:"outlined"})]})]})})})]}),ss=({onBack:i})=>{const[e,o]=j.useState({theme:"light",language:"vi",notifications:!0,autoBackup:!0,riskWarnings:!0,defaultTradeAmount:10,defaultTradeDuration:5,apiUrl:"http://localhost:3001"}),[d,x]=j.useState(!1),[u,f]=j.useState(!1),a=(r,B)=>{o(A=>({...A,[r]:B}))},l=async()=>{x(!0);try{await chrome.storage.local.set({userSettings:e}),f(!0),setTimeout(()=>f(!1),2e3)}catch(r){console.error("Error saving settings:",r)}finally{x(!1)}},g=()=>{o({theme:"light",language:"vi",notifications:!0,autoBackup:!0,riskWarnings:!0,defaultTradeAmount:10,defaultTradeDuration:5,apiUrl:"http://localhost:3001"})};return s.jsxs(h,{sx:{width:380,p:2},children:[s.jsxs(n,{container:!0,spacing:2,sx:{mb:2},children:[s.jsx(n,{size:2,children:s.jsx(k,{onClick:i,size:"small",children:s.jsx(J,{})})}),s.jsx(n,{size:10,children:s.jsxs(t,{variant:"h6",sx:{display:"flex",alignItems:"center",gap:1},children:[s.jsx(T,{color:"primary"}),"Cài đặt Extension"]})})]}),u&&s.jsx(n,{container:!0,spacing:2,sx:{mb:2},children:s.jsx(n,{size:12,children:s.jsx(I,{severity:"success",sx:{fontSize:"0.75rem"},children:"Cài đặt đã được lưu thành công!"})})}),s.jsx(n,{container:!0,spacing:2,sx:{mb:2},children:s.jsx(n,{size:12,children:s.jsx(p,{children:s.jsxs(y,{children:[s.jsx(t,{variant:"subtitle2",sx:{mb:2},children:"🎨 Giao diện"}),s.jsxs(v,{spacing:2,children:[s.jsxs(h,{children:[s.jsx(t,{variant:"body2",sx:{mb:1},children:"Theme:"}),s.jsxs(b,{size:"small",fullWidth:!0,children:[s.jsx(c,{variant:e.theme==="light"?"contained":"outlined",onClick:()=>a("theme","light"),children:"Light"}),s.jsx(c,{variant:e.theme==="dark"?"contained":"outlined",onClick:()=>a("theme","dark"),children:"Dark"})]})]}),s.jsxs(h,{children:[s.jsx(t,{variant:"body2",sx:{mb:1},children:"Ngôn ngữ:"}),s.jsxs(b,{size:"small",fullWidth:!0,children:[s.jsx(c,{variant:e.language==="vi"?"contained":"outlined",onClick:()=>a("language","vi"),children:"Tiếng Việt"}),s.jsx(c,{variant:e.language==="en"?"contained":"outlined",onClick:()=>a("language","en"),children:"English"})]})]})]})]})})})}),s.jsx(n,{container:!0,spacing:2,sx:{mb:2},children:s.jsx(n,{size:12,children:s.jsx(p,{children:s.jsxs(y,{children:[s.jsx(t,{variant:"subtitle2",sx:{mb:2},children:"⚙️ Chức năng"}),s.jsxs(v,{spacing:2,children:[s.jsxs(h,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[s.jsx(t,{variant:"body2",children:"Thông báo"}),s.jsx(c,{size:"small",variant:e.notifications?"contained":"outlined",color:e.notifications?"success":"secondary",onClick:()=>a("notifications",!e.notifications),children:e.notifications?"BẬT":"TẮT"})]}),s.jsxs(h,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[s.jsx(t,{variant:"body2",children:"Tự động backup"}),s.jsx(c,{size:"small",variant:e.autoBackup?"contained":"outlined",color:e.autoBackup?"success":"secondary",onClick:()=>a("autoBackup",!e.autoBackup),children:e.autoBackup?"BẬT":"TẮT"})]}),s.jsxs(h,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[s.jsx(t,{variant:"body2",children:"Cảnh báo rủi ro"}),s.jsx(c,{size:"small",variant:e.riskWarnings?"contained":"outlined",color:e.riskWarnings?"success":"secondary",onClick:()=>a("riskWarnings",!e.riskWarnings),children:e.riskWarnings?"BẬT":"TẮT"})]})]})]})})})}),s.jsx(n,{container:!0,spacing:2,sx:{mb:2},children:s.jsx(n,{size:12,children:s.jsx(p,{children:s.jsxs(y,{children:[s.jsx(t,{variant:"subtitle2",sx:{mb:2},children:"💰 Giao dịch mặc định"}),s.jsxs(v,{spacing:2,children:[s.jsxs(h,{children:[s.jsx(t,{variant:"body2",sx:{mb:1},children:"Số tiền mặc định ($):"}),s.jsx(b,{size:"small",fullWidth:!0,children:[1,5,10,25].map(r=>s.jsxs(c,{variant:e.defaultTradeAmount===r?"contained":"outlined",onClick:()=>a("defaultTradeAmount",r),children:["$",r]},r))})]}),s.jsxs(h,{children:[s.jsx(t,{variant:"body2",sx:{mb:1},children:"Thời gian mặc định:"}),s.jsx(b,{size:"small",fullWidth:!0,children:[1,5,15,30].map(r=>s.jsxs(c,{variant:e.defaultTradeDuration===r?"contained":"outlined",onClick:()=>a("defaultTradeDuration",r),children:[r,"m"]},r))})]})]})]})})})}),s.jsxs(n,{container:!0,spacing:2,children:[s.jsx(n,{size:6,children:s.jsx(c,{fullWidth:!0,variant:"outlined",color:"secondary",onClick:g,size:"small",children:"Reset"})}),s.jsx(n,{size:6,children:s.jsx(c,{fullWidth:!0,variant:"contained",onClick:l,disabled:d,size:"small",children:d?"Đang lưu...":"Lưu cài đặt"})})]})]})},es=O.createRoot(document.getElementById("root"));es.render(s.jsx(Y,{}));
