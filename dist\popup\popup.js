import{g as A,a as M,r as j,u as W,j as e,s as P,c as R,b as E,d as y,e as V,T as H,C as L,B as h,f as r,I as C,A as I,h as p,i as f,k as O,l as o,P as U,m as w,R as D}from"../assets/TrendingUp-BITtWk55.js";import{G as n,S as B,a as k,B as v}from"../assets/Settings-C6j4aHPK.js";import{R as T,C as G}from"../assets/Refresh-CKSyo3uB.js";function N(t){return A("MuiCardActions",t)}M("MuiCardActions",["root","spacing"]);const q=t=>{const{classes:s,disableSpacing:l}=t;return E({root:["root",!l&&"spacing"]},N,s)},F=P("div",{name:"MuiCardActions",slot:"Root",overridesResolver:(t,s)=>{const{ownerState:l}=t;return[s.root,!l.disableSpacing&&s.spacing]}})({display:"flex",alignItems:"center",padding:8,variants:[{props:{disableSpacing:!1},style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),S=j.forwardRef(function(s,l){const d=W({props:s,name:"MuiCardActions"}),{disableSpacing:x=!1,className:g,...u}=d,c={...d,disableSpacing:x},b=q(c);return e.jsx(F,{className:R(b.root,g),ownerState:c,ref:l,...u})}),J=y(e.jsx("path",{d:"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20z"})),K=y(e.jsx("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2M9 17H7v-7h2zm4 0h-2V7h2zm4 0h-2v-4h2z"})),X=y(e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m1 15h-2v-2h2zm0-4h-2V7h2z"})),$=y(e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m1 15h-2v-6h2zm0-8h-2V7h2z"})),_=y(e.jsx("path",{d:"M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-7h-2zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3z"})),Q=y(e.jsx("path",{d:"M16 20H2V4h14zm2-12h4V4h-4zm0 12h4v-4h-4zm0-6h4v-4h-4z"})),Y=V({palette:{primary:{main:"#007bff"},secondary:{main:"#6c757d"},success:{main:"#28a745"},error:{main:"#dc3545"},warning:{main:"#ffc107"}},typography:{h6:{fontWeight:600},body2:{fontSize:"0.875rem"}}}),Z=()=>{const[t,s]=j.useState("checking"),[l,d]=j.useState("main");j.useEffect(()=>{x()},[]);const x=async()=>{try{const a=await fetch("http://localhost:3001/dailyGoals?_limit=1");s(a.ok?"online":"offline")}catch{s("offline")}},g=()=>{chrome.tabs.query({active:!0,currentWindow:!0},a=>{var i;(i=a[0])!=null&&i.id&&chrome.tabs.sendMessage(a[0].id,{action:"showStats"})})},u=()=>{chrome.tabs.query({active:!0,currentWindow:!0},a=>{var i;(i=a[0])!=null&&i.id&&chrome.tabs.sendMessage(a[0].id,{action:"restartFlow"})})},c=()=>{chrome.tabs.create({url:"https://binomo1.com/trading"})},b=async()=>{try{const[a]=await chrome.tabs.query({active:!0,currentWindow:!0});if(a!=null&&a.windowId)await chrome.sidePanel.open({windowId:a.windowId});else{const i=await chrome.windows.getAll({populate:!1}),m=i.find(z=>z.focused)||i[0];if(m!=null&&m.id)await chrome.sidePanel.open({windowId:m.id});else throw new Error("No valid window found")}}catch(a){console.error("Error opening sidebar:",a),alert('Không thể mở sidebar. Hãy thử right-click vào extension icon và chọn "Open side panel".')}};return e.jsxs(H,{theme:Y,children:[e.jsx(L,{}),l==="main"?e.jsx(ee,{apiStatus:t,onOpenTradingPage:c,onOpenStatsPage:g,onRestartFlow:u,onOpenSettings:()=>d("settings"),onOpenSidebar:b,onRefreshApi:x}):e.jsx(se,{onBack:()=>d("main")})]})},ee=({apiStatus:t,onOpenTradingPage:s,onOpenStatsPage:l,onRestartFlow:d,onOpenSettings:x,onOpenSidebar:g,onRefreshApi:u})=>e.jsxs(h,{sx:{width:380,p:2},children:[e.jsxs(n,{container:!0,spacing:1,sx:{mb:2},children:[e.jsx(n,{size:8,children:e.jsxs(h,{sx:{textAlign:"center"},children:[e.jsx(r,{variant:"h6",sx:{display:"flex",alignItems:"center",justifyContent:"center",gap:1},children:"🎯 Binomo Trading Assistant"}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{fontStyle:"italic"},children:"Công cụ hỗ trợ trading có kỷ luật"})]})}),e.jsx(n,{size:2,children:e.jsx(C,{onClick:g,size:"small",title:"Mở Sidebar",children:e.jsx(Q,{})})}),e.jsx(n,{size:2,children:e.jsx(C,{onClick:x,size:"small",title:"Cài đặt",children:e.jsx(B,{})})})]}),e.jsx(n,{container:!0,spacing:2,sx:{mb:2},children:e.jsx(n,{size:12,children:e.jsxs(I,{severity:t==="online"?"success":t==="offline"?"error":"info",icon:t==="online"?e.jsx(G,{}):t==="offline"?e.jsx(X,{}):e.jsx($,{}),sx:{fontSize:"0.75rem"},action:e.jsx(C,{size:"small",onClick:u,children:e.jsx(T,{fontSize:"small"})}),children:[t==="checking"&&"Đang kiểm tra kết nối...",t==="online"&&"JSON Server đang hoạt động",t==="offline"&&"JSON Server không hoạt động - Hãy chạy npm run server"]})})}),e.jsx(n,{container:!0,spacing:2,sx:{mb:2},children:e.jsx(n,{size:12,children:e.jsxs(p,{children:[e.jsxs(f,{sx:{pb:1},children:[e.jsxs(r,{variant:"subtitle2",sx:{mb:1,display:"flex",alignItems:"center",gap:1},children:[e.jsx(O,{color:"primary"}),"Giao dịch"]}),e.jsx(r,{variant:"body2",color:"text.secondary",children:"Mở trang Binomo để bắt đầu giao dịch"})]}),e.jsx(S,{children:e.jsx(o,{fullWidth:!0,variant:"contained",onClick:s,startIcon:e.jsx(_,{}),disabled:t==="offline",children:"Mở trang giao dịch"})})]})})}),e.jsxs(n,{container:!0,spacing:2,sx:{mb:2},children:[e.jsx(n,{size:6,children:e.jsxs(p,{sx:{height:"100%"},children:[e.jsxs(f,{sx:{pb:1},children:[e.jsxs(r,{variant:"subtitle2",sx:{mb:1,display:"flex",alignItems:"center",gap:1},children:[e.jsx(K,{color:"success"}),"Thống kê"]}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{fontSize:"0.75rem"},children:"Xem báo cáo chi tiết"})]}),e.jsx(S,{children:e.jsx(o,{fullWidth:!0,variant:"outlined",color:"success",onClick:l,size:"small",disabled:t==="offline",children:"Xem thống kê"})})]})}),e.jsx(n,{size:6,children:e.jsxs(p,{sx:{height:"100%"},children:[e.jsxs(f,{sx:{pb:1},children:[e.jsxs(r,{variant:"subtitle2",sx:{mb:1,display:"flex",alignItems:"center",gap:1},children:[e.jsx(T,{color:"secondary"}),"Khởi động lại"]}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{fontSize:"0.75rem"},children:"Reset flow giao dịch"})]}),e.jsx(S,{children:e.jsx(o,{fullWidth:!0,variant:"outlined",color:"secondary",onClick:d,size:"small",disabled:t==="offline",children:"Khởi động lại"})})]})})]}),e.jsx(n,{container:!0,spacing:2,children:e.jsx(n,{size:12,children:e.jsxs(U,{sx:{p:1.5,backgroundColor:"#f8f9fa"},children:[e.jsxs(r,{variant:"body2",color:"text.secondary",sx:{textAlign:"center",fontSize:"0.75rem"},children:[e.jsx("strong",{children:"Lưu ý:"})," Extension chỉ hoạt động trên trang binomo1.com/trading"]}),e.jsxs(h,{sx:{display:"flex",justifyContent:"center",gap:1,mt:1},children:[e.jsx(w,{label:"Material UI v7+",size:"small",color:"primary",variant:"outlined"}),e.jsx(w,{label:"JSON Server",size:"small",color:"secondary",variant:"outlined"})]})]})})})]}),se=({onBack:t})=>{const[s,l]=j.useState({theme:"light",language:"vi",notifications:!0,autoBackup:!0,riskWarnings:!0,defaultTradeAmount:10,defaultTradeDuration:5,apiUrl:"http://localhost:3001"}),[d,x]=j.useState(!1),[g,u]=j.useState(!1),c=(i,m)=>{l(z=>({...z,[i]:m}))},b=async()=>{x(!0);try{await chrome.storage.local.set({userSettings:s}),u(!0),setTimeout(()=>u(!1),2e3)}catch(i){console.error("Error saving settings:",i)}finally{x(!1)}},a=()=>{l({theme:"light",language:"vi",notifications:!0,autoBackup:!0,riskWarnings:!0,defaultTradeAmount:10,defaultTradeDuration:5,apiUrl:"http://localhost:3001"})};return e.jsxs(h,{sx:{width:380,p:2},children:[e.jsxs(n,{container:!0,spacing:2,sx:{mb:2},children:[e.jsx(n,{size:2,children:e.jsx(C,{onClick:t,size:"small",children:e.jsx(J,{})})}),e.jsx(n,{size:10,children:e.jsxs(r,{variant:"h6",sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(B,{color:"primary"}),"Cài đặt Extension"]})})]}),g&&e.jsx(n,{container:!0,spacing:2,sx:{mb:2},children:e.jsx(n,{size:12,children:e.jsx(I,{severity:"success",sx:{fontSize:"0.75rem"},children:"Cài đặt đã được lưu thành công!"})})}),e.jsx(n,{container:!0,spacing:2,sx:{mb:2},children:e.jsx(n,{size:12,children:e.jsx(p,{children:e.jsxs(f,{children:[e.jsx(r,{variant:"subtitle2",sx:{mb:2},children:"🎨 Giao diện"}),e.jsxs(k,{spacing:2,children:[e.jsxs(h,{children:[e.jsx(r,{variant:"body2",sx:{mb:1},children:"Theme:"}),e.jsxs(v,{size:"small",fullWidth:!0,children:[e.jsx(o,{variant:s.theme==="light"?"contained":"outlined",onClick:()=>c("theme","light"),children:"Light"}),e.jsx(o,{variant:s.theme==="dark"?"contained":"outlined",onClick:()=>c("theme","dark"),children:"Dark"})]})]}),e.jsxs(h,{children:[e.jsx(r,{variant:"body2",sx:{mb:1},children:"Ngôn ngữ:"}),e.jsxs(v,{size:"small",fullWidth:!0,children:[e.jsx(o,{variant:s.language==="vi"?"contained":"outlined",onClick:()=>c("language","vi"),children:"Tiếng Việt"}),e.jsx(o,{variant:s.language==="en"?"contained":"outlined",onClick:()=>c("language","en"),children:"English"})]})]})]})]})})})}),e.jsx(n,{container:!0,spacing:2,sx:{mb:2},children:e.jsx(n,{size:12,children:e.jsx(p,{children:e.jsxs(f,{children:[e.jsx(r,{variant:"subtitle2",sx:{mb:2},children:"⚙️ Chức năng"}),e.jsxs(k,{spacing:2,children:[e.jsxs(h,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx(r,{variant:"body2",children:"Thông báo"}),e.jsx(o,{size:"small",variant:s.notifications?"contained":"outlined",color:s.notifications?"success":"secondary",onClick:()=>c("notifications",!s.notifications),children:s.notifications?"BẬT":"TẮT"})]}),e.jsxs(h,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx(r,{variant:"body2",children:"Tự động backup"}),e.jsx(o,{size:"small",variant:s.autoBackup?"contained":"outlined",color:s.autoBackup?"success":"secondary",onClick:()=>c("autoBackup",!s.autoBackup),children:s.autoBackup?"BẬT":"TẮT"})]}),e.jsxs(h,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx(r,{variant:"body2",children:"Cảnh báo rủi ro"}),e.jsx(o,{size:"small",variant:s.riskWarnings?"contained":"outlined",color:s.riskWarnings?"success":"secondary",onClick:()=>c("riskWarnings",!s.riskWarnings),children:s.riskWarnings?"BẬT":"TẮT"})]})]})]})})})}),e.jsx(n,{container:!0,spacing:2,sx:{mb:2},children:e.jsx(n,{size:12,children:e.jsx(p,{children:e.jsxs(f,{children:[e.jsx(r,{variant:"subtitle2",sx:{mb:2},children:"💰 Giao dịch mặc định"}),e.jsxs(k,{spacing:2,children:[e.jsxs(h,{children:[e.jsx(r,{variant:"body2",sx:{mb:1},children:"Số tiền mặc định ($):"}),e.jsx(v,{size:"small",fullWidth:!0,children:[1,5,10,25].map(i=>e.jsxs(o,{variant:s.defaultTradeAmount===i?"contained":"outlined",onClick:()=>c("defaultTradeAmount",i),children:["$",i]},i))})]}),e.jsxs(h,{children:[e.jsx(r,{variant:"body2",sx:{mb:1},children:"Thời gian mặc định:"}),e.jsx(v,{size:"small",fullWidth:!0,children:[1,5,15,30].map(i=>e.jsxs(o,{variant:s.defaultTradeDuration===i?"contained":"outlined",onClick:()=>c("defaultTradeDuration",i),children:[i,"m"]},i))})]})]})]})})})}),e.jsxs(n,{container:!0,spacing:2,children:[e.jsx(n,{size:6,children:e.jsx(o,{fullWidth:!0,variant:"outlined",color:"secondary",onClick:a,size:"small",children:"Reset"})}),e.jsx(n,{size:6,children:e.jsx(o,{fullWidth:!0,variant:"contained",onClick:b,disabled:d,size:"small",children:d?"Đang lưu...":"Lưu cài đặt"})})]})]})},ne=D.createRoot(document.getElementById("root"));ne.render(e.jsx(Z,{}));
