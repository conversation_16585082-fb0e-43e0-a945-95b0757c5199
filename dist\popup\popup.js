import{g as W,a as A,r as j,u as M,j as e,s as R,c as P,b as V,d as f,e as E,T as D,C as L,B as d,f as r,I as v,A as T,h as m,i as p,k as O,l as c,P as U,m as k,R as H}from"../assets/TrendingUp-BITtWk55.js";import{G as n,S as I,a as C,B as y}from"../assets/Settings-C6j4aHPK.js";import{R as S,C as N}from"../assets/Refresh-CKSyo3uB.js";function G(i){return W("MuiCardActions",i)}A("MuiCardActions",["root","spacing"]);const F=i=>{const{classes:s,disableSpacing:o}=i;return V({root:["root",!o&&"spacing"]},G,s)},J=R("div",{name:"MuiCardActions",slot:"Root",overridesResolver:(i,s)=>{const{ownerState:o}=i;return[s.root,!o.disableSpacing&&s.spacing]}})({display:"flex",alignItems:"center",padding:8,variants:[{props:{disableSpacing:!1},style:{"& > :not(style) ~ :not(style)":{marginLeft:8}}}]}),z=j.forwardRef(function(s,o){const l=M({props:s,name:"MuiCardActions"}),{disableSpacing:x=!1,className:g,...u}=l,a={...l,disableSpacing:x},b=F(a);return e.jsx(J,{className:P(b.root,g),ownerState:a,ref:o,...u})}),_=f(e.jsx("path",{d:"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20z"})),q=f(e.jsx("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2M9 17H7v-7h2zm4 0h-2V7h2zm4 0h-2v-4h2z"})),K=f(e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m1 15h-2v-2h2zm0-4h-2V7h2z"})),X=f(e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m1 15h-2v-6h2zm0-8h-2V7h2z"})),$=f(e.jsx("path",{d:"M19 19H5V5h7V3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2v-7h-2zM14 3v2h3.59l-9.83 9.83 1.41 1.41L19 6.41V10h2V3z"})),Q=f(e.jsx("path",{d:"M16 20H2V4h14zm2-12h4V4h-4zm0 12h4v-4h-4zm0-6h4v-4h-4z"})),Y=E({palette:{primary:{main:"#007bff"},secondary:{main:"#6c757d"},success:{main:"#28a745"},error:{main:"#dc3545"},warning:{main:"#ffc107"}},typography:{h6:{fontWeight:600},body2:{fontSize:"0.875rem"}}}),Z=()=>{const[i,s]=j.useState("checking"),[o,l]=j.useState("main");j.useEffect(()=>{x()},[]);const x=async()=>{try{const h=await fetch("http://localhost:3001/dailyGoals?_limit=1");s(h.ok?"online":"offline")}catch{s("offline")}},g=()=>{chrome.tabs.query({active:!0,currentWindow:!0},h=>{var t;(t=h[0])!=null&&t.id&&chrome.tabs.sendMessage(h[0].id,{action:"showStats"})})},u=()=>{chrome.tabs.query({active:!0,currentWindow:!0},h=>{var t;(t=h[0])!=null&&t.id&&chrome.tabs.sendMessage(h[0].id,{action:"restartFlow"})})},a=()=>{chrome.tabs.create({url:"https://binomo1.com/trading"})},b=()=>{chrome.sidePanel.open({windowId:chrome.windows.WINDOW_ID_CURRENT})};return e.jsxs(D,{theme:Y,children:[e.jsx(L,{}),o==="main"?e.jsx(ee,{apiStatus:i,onOpenTradingPage:a,onOpenStatsPage:g,onRestartFlow:u,onOpenSettings:()=>l("settings"),onOpenSidebar:b,onRefreshApi:x}):e.jsx(se,{onBack:()=>l("main")})]})},ee=({apiStatus:i,onOpenTradingPage:s,onOpenStatsPage:o,onRestartFlow:l,onOpenSettings:x,onOpenSidebar:g,onRefreshApi:u})=>e.jsxs(d,{sx:{width:380,p:2},children:[e.jsxs(n,{container:!0,spacing:1,sx:{mb:2},children:[e.jsx(n,{size:8,children:e.jsxs(d,{sx:{textAlign:"center"},children:[e.jsx(r,{variant:"h6",sx:{display:"flex",alignItems:"center",justifyContent:"center",gap:1},children:"🎯 Binomo Trading Assistant"}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{fontStyle:"italic"},children:"Công cụ hỗ trợ trading có kỷ luật"})]})}),e.jsx(n,{size:2,children:e.jsx(v,{onClick:g,size:"small",title:"Mở Sidebar",children:e.jsx(Q,{})})}),e.jsx(n,{size:2,children:e.jsx(v,{onClick:x,size:"small",title:"Cài đặt",children:e.jsx(I,{})})})]}),e.jsx(n,{container:!0,spacing:2,sx:{mb:2},children:e.jsx(n,{size:12,children:e.jsxs(T,{severity:i==="online"?"success":i==="offline"?"error":"info",icon:i==="online"?e.jsx(N,{}):i==="offline"?e.jsx(K,{}):e.jsx(X,{}),sx:{fontSize:"0.75rem"},action:e.jsx(v,{size:"small",onClick:u,children:e.jsx(S,{fontSize:"small"})}),children:[i==="checking"&&"Đang kiểm tra kết nối...",i==="online"&&"JSON Server đang hoạt động",i==="offline"&&"JSON Server không hoạt động - Hãy chạy npm run server"]})})}),e.jsx(n,{container:!0,spacing:2,sx:{mb:2},children:e.jsx(n,{size:12,children:e.jsxs(m,{children:[e.jsxs(p,{sx:{pb:1},children:[e.jsxs(r,{variant:"subtitle2",sx:{mb:1,display:"flex",alignItems:"center",gap:1},children:[e.jsx(O,{color:"primary"}),"Giao dịch"]}),e.jsx(r,{variant:"body2",color:"text.secondary",children:"Mở trang Binomo để bắt đầu giao dịch"})]}),e.jsx(z,{children:e.jsx(c,{fullWidth:!0,variant:"contained",onClick:s,startIcon:e.jsx($,{}),disabled:i==="offline",children:"Mở trang giao dịch"})})]})})}),e.jsxs(n,{container:!0,spacing:2,sx:{mb:2},children:[e.jsx(n,{size:6,children:e.jsxs(m,{sx:{height:"100%"},children:[e.jsxs(p,{sx:{pb:1},children:[e.jsxs(r,{variant:"subtitle2",sx:{mb:1,display:"flex",alignItems:"center",gap:1},children:[e.jsx(q,{color:"success"}),"Thống kê"]}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{fontSize:"0.75rem"},children:"Xem báo cáo chi tiết"})]}),e.jsx(z,{children:e.jsx(c,{fullWidth:!0,variant:"outlined",color:"success",onClick:o,size:"small",disabled:i==="offline",children:"Xem thống kê"})})]})}),e.jsx(n,{size:6,children:e.jsxs(m,{sx:{height:"100%"},children:[e.jsxs(p,{sx:{pb:1},children:[e.jsxs(r,{variant:"subtitle2",sx:{mb:1,display:"flex",alignItems:"center",gap:1},children:[e.jsx(S,{color:"secondary"}),"Khởi động lại"]}),e.jsx(r,{variant:"body2",color:"text.secondary",sx:{fontSize:"0.75rem"},children:"Reset flow giao dịch"})]}),e.jsx(z,{children:e.jsx(c,{fullWidth:!0,variant:"outlined",color:"secondary",onClick:l,size:"small",disabled:i==="offline",children:"Khởi động lại"})})]})})]}),e.jsx(n,{container:!0,spacing:2,children:e.jsx(n,{size:12,children:e.jsxs(U,{sx:{p:1.5,backgroundColor:"#f8f9fa"},children:[e.jsxs(r,{variant:"body2",color:"text.secondary",sx:{textAlign:"center",fontSize:"0.75rem"},children:[e.jsx("strong",{children:"Lưu ý:"})," Extension chỉ hoạt động trên trang binomo1.com/trading"]}),e.jsxs(d,{sx:{display:"flex",justifyContent:"center",gap:1,mt:1},children:[e.jsx(k,{label:"Material UI v7+",size:"small",color:"primary",variant:"outlined"}),e.jsx(k,{label:"JSON Server",size:"small",color:"secondary",variant:"outlined"})]})]})})})]}),se=({onBack:i})=>{const[s,o]=j.useState({theme:"light",language:"vi",notifications:!0,autoBackup:!0,riskWarnings:!0,defaultTradeAmount:10,defaultTradeDuration:5,apiUrl:"http://localhost:3001"}),[l,x]=j.useState(!1),[g,u]=j.useState(!1),a=(t,w)=>{o(B=>({...B,[t]:w}))},b=async()=>{x(!0);try{await chrome.storage.local.set({userSettings:s}),u(!0),setTimeout(()=>u(!1),2e3)}catch(t){console.error("Error saving settings:",t)}finally{x(!1)}},h=()=>{o({theme:"light",language:"vi",notifications:!0,autoBackup:!0,riskWarnings:!0,defaultTradeAmount:10,defaultTradeDuration:5,apiUrl:"http://localhost:3001"})};return e.jsxs(d,{sx:{width:380,p:2},children:[e.jsxs(n,{container:!0,spacing:2,sx:{mb:2},children:[e.jsx(n,{size:2,children:e.jsx(v,{onClick:i,size:"small",children:e.jsx(_,{})})}),e.jsx(n,{size:10,children:e.jsxs(r,{variant:"h6",sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(I,{color:"primary"}),"Cài đặt Extension"]})})]}),g&&e.jsx(n,{container:!0,spacing:2,sx:{mb:2},children:e.jsx(n,{size:12,children:e.jsx(T,{severity:"success",sx:{fontSize:"0.75rem"},children:"Cài đặt đã được lưu thành công!"})})}),e.jsx(n,{container:!0,spacing:2,sx:{mb:2},children:e.jsx(n,{size:12,children:e.jsx(m,{children:e.jsxs(p,{children:[e.jsx(r,{variant:"subtitle2",sx:{mb:2},children:"🎨 Giao diện"}),e.jsxs(C,{spacing:2,children:[e.jsxs(d,{children:[e.jsx(r,{variant:"body2",sx:{mb:1},children:"Theme:"}),e.jsxs(y,{size:"small",fullWidth:!0,children:[e.jsx(c,{variant:s.theme==="light"?"contained":"outlined",onClick:()=>a("theme","light"),children:"Light"}),e.jsx(c,{variant:s.theme==="dark"?"contained":"outlined",onClick:()=>a("theme","dark"),children:"Dark"})]})]}),e.jsxs(d,{children:[e.jsx(r,{variant:"body2",sx:{mb:1},children:"Ngôn ngữ:"}),e.jsxs(y,{size:"small",fullWidth:!0,children:[e.jsx(c,{variant:s.language==="vi"?"contained":"outlined",onClick:()=>a("language","vi"),children:"Tiếng Việt"}),e.jsx(c,{variant:s.language==="en"?"contained":"outlined",onClick:()=>a("language","en"),children:"English"})]})]})]})]})})})}),e.jsx(n,{container:!0,spacing:2,sx:{mb:2},children:e.jsx(n,{size:12,children:e.jsx(m,{children:e.jsxs(p,{children:[e.jsx(r,{variant:"subtitle2",sx:{mb:2},children:"⚙️ Chức năng"}),e.jsxs(C,{spacing:2,children:[e.jsxs(d,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx(r,{variant:"body2",children:"Thông báo"}),e.jsx(c,{size:"small",variant:s.notifications?"contained":"outlined",color:s.notifications?"success":"secondary",onClick:()=>a("notifications",!s.notifications),children:s.notifications?"BẬT":"TẮT"})]}),e.jsxs(d,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx(r,{variant:"body2",children:"Tự động backup"}),e.jsx(c,{size:"small",variant:s.autoBackup?"contained":"outlined",color:s.autoBackup?"success":"secondary",onClick:()=>a("autoBackup",!s.autoBackup),children:s.autoBackup?"BẬT":"TẮT"})]}),e.jsxs(d,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx(r,{variant:"body2",children:"Cảnh báo rủi ro"}),e.jsx(c,{size:"small",variant:s.riskWarnings?"contained":"outlined",color:s.riskWarnings?"success":"secondary",onClick:()=>a("riskWarnings",!s.riskWarnings),children:s.riskWarnings?"BẬT":"TẮT"})]})]})]})})})}),e.jsx(n,{container:!0,spacing:2,sx:{mb:2},children:e.jsx(n,{size:12,children:e.jsx(m,{children:e.jsxs(p,{children:[e.jsx(r,{variant:"subtitle2",sx:{mb:2},children:"💰 Giao dịch mặc định"}),e.jsxs(C,{spacing:2,children:[e.jsxs(d,{children:[e.jsx(r,{variant:"body2",sx:{mb:1},children:"Số tiền mặc định ($):"}),e.jsx(y,{size:"small",fullWidth:!0,children:[1,5,10,25].map(t=>e.jsxs(c,{variant:s.defaultTradeAmount===t?"contained":"outlined",onClick:()=>a("defaultTradeAmount",t),children:["$",t]},t))})]}),e.jsxs(d,{children:[e.jsx(r,{variant:"body2",sx:{mb:1},children:"Thời gian mặc định:"}),e.jsx(y,{size:"small",fullWidth:!0,children:[1,5,15,30].map(t=>e.jsxs(c,{variant:s.defaultTradeDuration===t?"contained":"outlined",onClick:()=>a("defaultTradeDuration",t),children:[t,"m"]},t))})]})]})]})})})}),e.jsxs(n,{container:!0,spacing:2,children:[e.jsx(n,{size:6,children:e.jsx(c,{fullWidth:!0,variant:"outlined",color:"secondary",onClick:h,size:"small",children:"Reset"})}),e.jsx(n,{size:6,children:e.jsx(c,{fullWidth:!0,variant:"contained",onClick:b,disabled:l,size:"small",children:l?"Đang lưu...":"Lưu cài đặt"})})]})]})},ne=H.createRoot(document.getElementById("root"));ne.render(e.jsx(Z,{}));
