var nv=Object.defineProperty;var av=(a,r,u)=>r in a?nv(a,r,{enumerable:!0,configurable:!0,writable:!0,value:u}):a[r]=u;var Li=(a,r,u)=>av(a,typeof r!="symbol"?r+"":r,u);function lv(a,r){for(var u=0;u<r.length;u++){const o=r[u];if(typeof o!="string"&&!Array.isArray(o)){for(const s in o)if(s!=="default"&&!(s in a)){const f=Object.getOwnPropertyDescriptor(o,s);f&&Object.defineProperty(a,s,f.get?f:{enumerable:!0,get:()=>o[s]})}}}return Object.freeze(Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}))}function Km(a){return a&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,"default")?a.default:a}var rs={exports:{}},qi={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jh;function iv(){if(Jh)return qi;Jh=1;var a=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function u(o,s,f){var p=null;if(f!==void 0&&(p=""+f),s.key!==void 0&&(p=""+s.key),"key"in s){f={};for(var m in s)m!=="key"&&(f[m]=s[m])}else f=s;return s=f.ref,{$$typeof:a,type:o,key:p,ref:s!==void 0?s:null,props:f}}return qi.Fragment=r,qi.jsx=u,qi.jsxs=u,qi}var Wh;function rv(){return Wh||(Wh=1,rs.exports=iv()),rs.exports}var tt=rv(),us={exports:{}},Yi={},os={exports:{}},cs={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ph;function uv(){return Ph||(Ph=1,function(a){function r(N,Z){var nt=N.length;N.push(Z);t:for(;0<nt;){var at=nt-1>>>1,C=N[at];if(0<s(C,Z))N[at]=Z,N[nt]=C,nt=at;else break t}}function u(N){return N.length===0?null:N[0]}function o(N){if(N.length===0)return null;var Z=N[0],nt=N.pop();if(nt!==Z){N[0]=nt;t:for(var at=0,C=N.length,q=C>>>1;at<q;){var K=2*(at+1)-1,P=N[K],ot=K+1,vt=N[ot];if(0>s(P,nt))ot<C&&0>s(vt,P)?(N[at]=vt,N[ot]=nt,at=ot):(N[at]=P,N[K]=nt,at=K);else if(ot<C&&0>s(vt,nt))N[at]=vt,N[ot]=nt,at=ot;else break t}}return Z}function s(N,Z){var nt=N.sortIndex-Z.sortIndex;return nt!==0?nt:N.id-Z.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;a.unstable_now=function(){return f.now()}}else{var p=Date,m=p.now();a.unstable_now=function(){return p.now()-m}}var v=[],y=[],S=1,E=null,A=3,U=!1,M=!1,T=!1,G=!1,X=typeof setTimeout=="function"?setTimeout:null,W=typeof clearTimeout=="function"?clearTimeout:null,V=typeof setImmediate<"u"?setImmediate:null;function j(N){for(var Z=u(y);Z!==null;){if(Z.callback===null)o(y);else if(Z.startTime<=N)o(y),Z.sortIndex=Z.expirationTime,r(v,Z);else break;Z=u(y)}}function R(N){if(T=!1,j(N),!M)if(u(v)!==null)M=!0,Q||(Q=!0,J());else{var Z=u(y);Z!==null&&mt(R,Z.startTime-N)}}var Q=!1,F=-1,I=5,et=-1;function g(){return G?!0:!(a.unstable_now()-et<I)}function Y(){if(G=!1,Q){var N=a.unstable_now();et=N;var Z=!0;try{t:{M=!1,T&&(T=!1,W(F),F=-1),U=!0;var nt=A;try{e:{for(j(N),E=u(v);E!==null&&!(E.expirationTime>N&&g());){var at=E.callback;if(typeof at=="function"){E.callback=null,A=E.priorityLevel;var C=at(E.expirationTime<=N);if(N=a.unstable_now(),typeof C=="function"){E.callback=C,j(N),Z=!0;break e}E===u(v)&&o(v),j(N)}else o(v);E=u(v)}if(E!==null)Z=!0;else{var q=u(y);q!==null&&mt(R,q.startTime-N),Z=!1}}break t}finally{E=null,A=nt,U=!1}Z=void 0}}finally{Z?J():Q=!1}}}var J;if(typeof V=="function")J=function(){V(Y)};else if(typeof MessageChannel<"u"){var it=new MessageChannel,pt=it.port2;it.port1.onmessage=Y,J=function(){pt.postMessage(null)}}else J=function(){X(Y,0)};function mt(N,Z){F=X(function(){N(a.unstable_now())},Z)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(N){N.callback=null},a.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):I=0<N?Math.floor(1e3/N):5},a.unstable_getCurrentPriorityLevel=function(){return A},a.unstable_next=function(N){switch(A){case 1:case 2:case 3:var Z=3;break;default:Z=A}var nt=A;A=Z;try{return N()}finally{A=nt}},a.unstable_requestPaint=function(){G=!0},a.unstable_runWithPriority=function(N,Z){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var nt=A;A=N;try{return Z()}finally{A=nt}},a.unstable_scheduleCallback=function(N,Z,nt){var at=a.unstable_now();switch(typeof nt=="object"&&nt!==null?(nt=nt.delay,nt=typeof nt=="number"&&0<nt?at+nt:at):nt=at,N){case 1:var C=-1;break;case 2:C=250;break;case 5:C=1073741823;break;case 4:C=1e4;break;default:C=5e3}return C=nt+C,N={id:S++,callback:Z,priorityLevel:N,startTime:nt,expirationTime:C,sortIndex:-1},nt>at?(N.sortIndex=nt,r(y,N),u(v)===null&&N===u(y)&&(T?(W(F),F=-1):T=!0,mt(R,nt-at))):(N.sortIndex=C,r(v,N),M||U||(M=!0,Q||(Q=!0,J()))),N},a.unstable_shouldYield=g,a.unstable_wrapCallback=function(N){var Z=A;return function(){var nt=A;A=Z;try{return N.apply(this,arguments)}finally{A=nt}}}}(cs)),cs}var Fh;function ov(){return Fh||(Fh=1,os.exports=uv()),os.exports}var ss={exports:{}},yt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ih;function cv(){if(Ih)return yt;Ih=1;var a=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),p=Symbol.for("react.context"),m=Symbol.for("react.forward_ref"),v=Symbol.for("react.suspense"),y=Symbol.for("react.memo"),S=Symbol.for("react.lazy"),E=Symbol.iterator;function A(C){return C===null||typeof C!="object"?null:(C=E&&C[E]||C["@@iterator"],typeof C=="function"?C:null)}var U={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},M=Object.assign,T={};function G(C,q,K){this.props=C,this.context=q,this.refs=T,this.updater=K||U}G.prototype.isReactComponent={},G.prototype.setState=function(C,q){if(typeof C!="object"&&typeof C!="function"&&C!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,C,q,"setState")},G.prototype.forceUpdate=function(C){this.updater.enqueueForceUpdate(this,C,"forceUpdate")};function X(){}X.prototype=G.prototype;function W(C,q,K){this.props=C,this.context=q,this.refs=T,this.updater=K||U}var V=W.prototype=new X;V.constructor=W,M(V,G.prototype),V.isPureReactComponent=!0;var j=Array.isArray,R={H:null,A:null,T:null,S:null,V:null},Q=Object.prototype.hasOwnProperty;function F(C,q,K,P,ot,vt){return K=vt.ref,{$$typeof:a,type:C,key:q,ref:K!==void 0?K:null,props:vt}}function I(C,q){return F(C.type,q,void 0,void 0,void 0,C.props)}function et(C){return typeof C=="object"&&C!==null&&C.$$typeof===a}function g(C){var q={"=":"=0",":":"=2"};return"$"+C.replace(/[=:]/g,function(K){return q[K]})}var Y=/\/+/g;function J(C,q){return typeof C=="object"&&C!==null&&C.key!=null?g(""+C.key):q.toString(36)}function it(){}function pt(C){switch(C.status){case"fulfilled":return C.value;case"rejected":throw C.reason;default:switch(typeof C.status=="string"?C.then(it,it):(C.status="pending",C.then(function(q){C.status==="pending"&&(C.status="fulfilled",C.value=q)},function(q){C.status==="pending"&&(C.status="rejected",C.reason=q)})),C.status){case"fulfilled":return C.value;case"rejected":throw C.reason}}throw C}function mt(C,q,K,P,ot){var vt=typeof C;(vt==="undefined"||vt==="boolean")&&(C=null);var ft=!1;if(C===null)ft=!0;else switch(vt){case"bigint":case"string":case"number":ft=!0;break;case"object":switch(C.$$typeof){case a:case r:ft=!0;break;case S:return ft=C._init,mt(ft(C._payload),q,K,P,ot)}}if(ft)return ot=ot(C),ft=P===""?"."+J(C,0):P,j(ot)?(K="",ft!=null&&(K=ft.replace(Y,"$&/")+"/"),mt(ot,q,K,"",function(Ne){return Ne})):ot!=null&&(et(ot)&&(ot=I(ot,K+(ot.key==null||C&&C.key===ot.key?"":(""+ot.key).replace(Y,"$&/")+"/")+ft)),q.push(ot)),1;ft=0;var Qt=P===""?".":P+":";if(j(C))for(var zt=0;zt<C.length;zt++)P=C[zt],vt=Qt+J(P,zt),ft+=mt(P,q,K,vt,ot);else if(zt=A(C),typeof zt=="function")for(C=zt.call(C),zt=0;!(P=C.next()).done;)P=P.value,vt=Qt+J(P,zt++),ft+=mt(P,q,K,vt,ot);else if(vt==="object"){if(typeof C.then=="function")return mt(pt(C),q,K,P,ot);throw q=String(C),Error("Objects are not valid as a React child (found: "+(q==="[object Object]"?"object with keys {"+Object.keys(C).join(", ")+"}":q)+"). If you meant to render a collection of children, use an array instead.")}return ft}function N(C,q,K){if(C==null)return C;var P=[],ot=0;return mt(C,P,"","",function(vt){return q.call(K,vt,ot++)}),P}function Z(C){if(C._status===-1){var q=C._result;q=q(),q.then(function(K){(C._status===0||C._status===-1)&&(C._status=1,C._result=K)},function(K){(C._status===0||C._status===-1)&&(C._status=2,C._result=K)}),C._status===-1&&(C._status=0,C._result=q)}if(C._status===1)return C._result.default;throw C._result}var nt=typeof reportError=="function"?reportError:function(C){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var q=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof C=="object"&&C!==null&&typeof C.message=="string"?String(C.message):String(C),error:C});if(!window.dispatchEvent(q))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",C);return}console.error(C)};function at(){}return yt.Children={map:N,forEach:function(C,q,K){N(C,function(){q.apply(this,arguments)},K)},count:function(C){var q=0;return N(C,function(){q++}),q},toArray:function(C){return N(C,function(q){return q})||[]},only:function(C){if(!et(C))throw Error("React.Children.only expected to receive a single React element child.");return C}},yt.Component=G,yt.Fragment=u,yt.Profiler=s,yt.PureComponent=W,yt.StrictMode=o,yt.Suspense=v,yt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=R,yt.__COMPILER_RUNTIME={__proto__:null,c:function(C){return R.H.useMemoCache(C)}},yt.cache=function(C){return function(){return C.apply(null,arguments)}},yt.cloneElement=function(C,q,K){if(C==null)throw Error("The argument must be a React element, but you passed "+C+".");var P=M({},C.props),ot=C.key,vt=void 0;if(q!=null)for(ft in q.ref!==void 0&&(vt=void 0),q.key!==void 0&&(ot=""+q.key),q)!Q.call(q,ft)||ft==="key"||ft==="__self"||ft==="__source"||ft==="ref"&&q.ref===void 0||(P[ft]=q[ft]);var ft=arguments.length-2;if(ft===1)P.children=K;else if(1<ft){for(var Qt=Array(ft),zt=0;zt<ft;zt++)Qt[zt]=arguments[zt+2];P.children=Qt}return F(C.type,ot,void 0,void 0,vt,P)},yt.createContext=function(C){return C={$$typeof:p,_currentValue:C,_currentValue2:C,_threadCount:0,Provider:null,Consumer:null},C.Provider=C,C.Consumer={$$typeof:f,_context:C},C},yt.createElement=function(C,q,K){var P,ot={},vt=null;if(q!=null)for(P in q.key!==void 0&&(vt=""+q.key),q)Q.call(q,P)&&P!=="key"&&P!=="__self"&&P!=="__source"&&(ot[P]=q[P]);var ft=arguments.length-2;if(ft===1)ot.children=K;else if(1<ft){for(var Qt=Array(ft),zt=0;zt<ft;zt++)Qt[zt]=arguments[zt+2];ot.children=Qt}if(C&&C.defaultProps)for(P in ft=C.defaultProps,ft)ot[P]===void 0&&(ot[P]=ft[P]);return F(C,vt,void 0,void 0,null,ot)},yt.createRef=function(){return{current:null}},yt.forwardRef=function(C){return{$$typeof:m,render:C}},yt.isValidElement=et,yt.lazy=function(C){return{$$typeof:S,_payload:{_status:-1,_result:C},_init:Z}},yt.memo=function(C,q){return{$$typeof:y,type:C,compare:q===void 0?null:q}},yt.startTransition=function(C){var q=R.T,K={};R.T=K;try{var P=C(),ot=R.S;ot!==null&&ot(K,P),typeof P=="object"&&P!==null&&typeof P.then=="function"&&P.then(at,nt)}catch(vt){nt(vt)}finally{R.T=q}},yt.unstable_useCacheRefresh=function(){return R.H.useCacheRefresh()},yt.use=function(C){return R.H.use(C)},yt.useActionState=function(C,q,K){return R.H.useActionState(C,q,K)},yt.useCallback=function(C,q){return R.H.useCallback(C,q)},yt.useContext=function(C){return R.H.useContext(C)},yt.useDebugValue=function(){},yt.useDeferredValue=function(C,q){return R.H.useDeferredValue(C,q)},yt.useEffect=function(C,q,K){var P=R.H;if(typeof K=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return P.useEffect(C,q)},yt.useId=function(){return R.H.useId()},yt.useImperativeHandle=function(C,q,K){return R.H.useImperativeHandle(C,q,K)},yt.useInsertionEffect=function(C,q){return R.H.useInsertionEffect(C,q)},yt.useLayoutEffect=function(C,q){return R.H.useLayoutEffect(C,q)},yt.useMemo=function(C,q){return R.H.useMemo(C,q)},yt.useOptimistic=function(C,q){return R.H.useOptimistic(C,q)},yt.useReducer=function(C,q,K){return R.H.useReducer(C,q,K)},yt.useRef=function(C){return R.H.useRef(C)},yt.useState=function(C){return R.H.useState(C)},yt.useSyncExternalStore=function(C,q,K){return R.H.useSyncExternalStore(C,q,K)},yt.useTransition=function(){return R.H.useTransition()},yt.version="19.1.0",yt}var tm;function Us(){return tm||(tm=1,ss.exports=cv()),ss.exports}var fs={exports:{}},Te={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var em;function sv(){if(em)return Te;em=1;var a=Us();function r(v){var y="https://react.dev/errors/"+v;if(1<arguments.length){y+="?args[]="+encodeURIComponent(arguments[1]);for(var S=2;S<arguments.length;S++)y+="&args[]="+encodeURIComponent(arguments[S])}return"Minified React error #"+v+"; visit "+y+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(){}var o={d:{f:u,r:function(){throw Error(r(522))},D:u,C:u,L:u,m:u,X:u,S:u,M:u},p:0,findDOMNode:null},s=Symbol.for("react.portal");function f(v,y,S){var E=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:s,key:E==null?null:""+E,children:v,containerInfo:y,implementation:S}}var p=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function m(v,y){if(v==="font")return"";if(typeof y=="string")return y==="use-credentials"?y:""}return Te.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,Te.createPortal=function(v,y){var S=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!y||y.nodeType!==1&&y.nodeType!==9&&y.nodeType!==11)throw Error(r(299));return f(v,y,null,S)},Te.flushSync=function(v){var y=p.T,S=o.p;try{if(p.T=null,o.p=2,v)return v()}finally{p.T=y,o.p=S,o.d.f()}},Te.preconnect=function(v,y){typeof v=="string"&&(y?(y=y.crossOrigin,y=typeof y=="string"?y==="use-credentials"?y:"":void 0):y=null,o.d.C(v,y))},Te.prefetchDNS=function(v){typeof v=="string"&&o.d.D(v)},Te.preinit=function(v,y){if(typeof v=="string"&&y&&typeof y.as=="string"){var S=y.as,E=m(S,y.crossOrigin),A=typeof y.integrity=="string"?y.integrity:void 0,U=typeof y.fetchPriority=="string"?y.fetchPriority:void 0;S==="style"?o.d.S(v,typeof y.precedence=="string"?y.precedence:void 0,{crossOrigin:E,integrity:A,fetchPriority:U}):S==="script"&&o.d.X(v,{crossOrigin:E,integrity:A,fetchPriority:U,nonce:typeof y.nonce=="string"?y.nonce:void 0})}},Te.preinitModule=function(v,y){if(typeof v=="string")if(typeof y=="object"&&y!==null){if(y.as==null||y.as==="script"){var S=m(y.as,y.crossOrigin);o.d.M(v,{crossOrigin:S,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0})}}else y==null&&o.d.M(v)},Te.preload=function(v,y){if(typeof v=="string"&&typeof y=="object"&&y!==null&&typeof y.as=="string"){var S=y.as,E=m(S,y.crossOrigin);o.d.L(v,S,{crossOrigin:E,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0,type:typeof y.type=="string"?y.type:void 0,fetchPriority:typeof y.fetchPriority=="string"?y.fetchPriority:void 0,referrerPolicy:typeof y.referrerPolicy=="string"?y.referrerPolicy:void 0,imageSrcSet:typeof y.imageSrcSet=="string"?y.imageSrcSet:void 0,imageSizes:typeof y.imageSizes=="string"?y.imageSizes:void 0,media:typeof y.media=="string"?y.media:void 0})}},Te.preloadModule=function(v,y){if(typeof v=="string")if(y){var S=m(y.as,y.crossOrigin);o.d.m(v,{as:typeof y.as=="string"&&y.as!=="script"?y.as:void 0,crossOrigin:S,integrity:typeof y.integrity=="string"?y.integrity:void 0})}else o.d.m(v)},Te.requestFormReset=function(v){o.d.r(v)},Te.unstable_batchedUpdates=function(v,y){return v(y)},Te.useFormState=function(v,y,S){return p.H.useFormState(v,y,S)},Te.useFormStatus=function(){return p.H.useHostTransitionStatus()},Te.version="19.1.0",Te}var nm;function fv(){if(nm)return fs.exports;nm=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(r){console.error(r)}}return a(),fs.exports=sv(),fs.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var am;function dv(){if(am)return Yi;am=1;var a=ov(),r=Us(),u=fv();function o(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function f(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function p(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function m(t){if(f(t)!==t)throw Error(o(188))}function v(t){var e=t.alternate;if(!e){if(e=f(t),e===null)throw Error(o(188));return e!==t?null:t}for(var n=t,l=e;;){var i=n.return;if(i===null)break;var c=i.alternate;if(c===null){if(l=i.return,l!==null){n=l;continue}break}if(i.child===c.child){for(c=i.child;c;){if(c===n)return m(i),t;if(c===l)return m(i),e;c=c.sibling}throw Error(o(188))}if(n.return!==l.return)n=i,l=c;else{for(var d=!1,h=i.child;h;){if(h===n){d=!0,n=i,l=c;break}if(h===l){d=!0,l=i,n=c;break}h=h.sibling}if(!d){for(h=c.child;h;){if(h===n){d=!0,n=c,l=i;break}if(h===l){d=!0,l=c,n=i;break}h=h.sibling}if(!d)throw Error(o(189))}}if(n.alternate!==l)throw Error(o(190))}if(n.tag!==3)throw Error(o(188));return n.stateNode.current===n?t:e}function y(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=y(t),e!==null)return e;t=t.sibling}return null}var S=Object.assign,E=Symbol.for("react.element"),A=Symbol.for("react.transitional.element"),U=Symbol.for("react.portal"),M=Symbol.for("react.fragment"),T=Symbol.for("react.strict_mode"),G=Symbol.for("react.profiler"),X=Symbol.for("react.provider"),W=Symbol.for("react.consumer"),V=Symbol.for("react.context"),j=Symbol.for("react.forward_ref"),R=Symbol.for("react.suspense"),Q=Symbol.for("react.suspense_list"),F=Symbol.for("react.memo"),I=Symbol.for("react.lazy"),et=Symbol.for("react.activity"),g=Symbol.for("react.memo_cache_sentinel"),Y=Symbol.iterator;function J(t){return t===null||typeof t!="object"?null:(t=Y&&t[Y]||t["@@iterator"],typeof t=="function"?t:null)}var it=Symbol.for("react.client.reference");function pt(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===it?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case M:return"Fragment";case G:return"Profiler";case T:return"StrictMode";case R:return"Suspense";case Q:return"SuspenseList";case et:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case U:return"Portal";case V:return(t.displayName||"Context")+".Provider";case W:return(t._context.displayName||"Context")+".Consumer";case j:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case F:return e=t.displayName||null,e!==null?e:pt(t.type)||"Memo";case I:e=t._payload,t=t._init;try{return pt(t(e))}catch{}}return null}var mt=Array.isArray,N=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Z=u.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,nt={pending:!1,data:null,method:null,action:null},at=[],C=-1;function q(t){return{current:t}}function K(t){0>C||(t.current=at[C],at[C]=null,C--)}function P(t,e){C++,at[C]=t.current,t.current=e}var ot=q(null),vt=q(null),ft=q(null),Qt=q(null);function zt(t,e){switch(P(ft,e),P(vt,t),P(ot,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?xh(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=xh(e),t=Eh(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}K(ot),P(ot,t)}function Ne(){K(ot),K(vt),K(ft)}function xa(t){t.memoizedState!==null&&P(Qt,t);var e=ot.current,n=Eh(e,t.type);e!==n&&(P(vt,t),P(ot,n))}function Zn(t){vt.current===t&&(K(ot),K(vt)),Qt.current===t&&(K(Qt),Ui._currentValue=nt)}var Kn=Object.prototype.hasOwnProperty,Jn=a.unstable_scheduleCallback,pn=a.unstable_cancelCallback,Xa=a.unstable_shouldYield,kl=a.unstable_requestPaint,re=a.unstable_now,un=a.unstable_getCurrentPriorityLevel,me=a.unstable_ImmediatePriority,hn=a.unstable_UserBlockingPriority,Xe=a.unstable_NormalPriority,ht=a.unstable_LowPriority,rr=a.unstable_IdlePriority,ur=a.log,or=a.unstable_setDisableYieldValue,Wn=null,ye=null;function on(t){if(typeof ur=="function"&&or(t),ye&&typeof ye.setStrictMode=="function")try{ye.setStrictMode(Wn,t)}catch{}}var ge=Math.clz32?Math.clz32:Mn,ve=Math.log,Vt=Math.LN2;function Mn(t){return t>>>=0,t===0?32:31-(ve(t)/Vt|0)|0}var Ee=256,be=4194304;function Rn(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function cr(t,e,n){var l=t.pendingLanes;if(l===0)return 0;var i=0,c=t.suspendedLanes,d=t.pingedLanes;t=t.warmLanes;var h=l&134217727;return h!==0?(l=h&~c,l!==0?i=Rn(l):(d&=h,d!==0?i=Rn(d):n||(n=h&~t,n!==0&&(i=Rn(n))))):(h=l&~c,h!==0?i=Rn(h):d!==0?i=Rn(d):n||(n=l&~t,n!==0&&(i=Rn(n)))),i===0?0:e!==0&&e!==i&&(e&c)===0&&(c=i&-i,n=e&-e,c>=n||c===32&&(n&4194048)!==0)?e:i}function Gl(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function G0(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function rf(){var t=Ee;return Ee<<=1,(Ee&4194048)===0&&(Ee=256),t}function uf(){var t=be;return be<<=1,(be&62914560)===0&&(be=4194304),t}function Ku(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function Vl(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function V0(t,e,n,l,i,c){var d=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var h=t.entanglements,b=t.expirationTimes,_=t.hiddenUpdates;for(n=d&~n;0<n;){var H=31-ge(n),k=1<<H;h[H]=0,b[H]=-1;var D=_[H];if(D!==null)for(_[H]=null,H=0;H<D.length;H++){var B=D[H];B!==null&&(B.lane&=-536870913)}n&=~k}l!==0&&of(t,l,0),c!==0&&i===0&&t.tag!==0&&(t.suspendedLanes|=c&~(d&~e))}function of(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var l=31-ge(e);t.entangledLanes|=e,t.entanglements[l]=t.entanglements[l]|1073741824|n&4194090}function cf(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var l=31-ge(n),i=1<<l;i&e|t[l]&e&&(t[l]|=e),n&=~i}}function Ju(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function Wu(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function sf(){var t=Z.p;return t!==0?t:(t=window.event,t===void 0?32:Gh(t.type))}function X0(t,e){var n=Z.p;try{return Z.p=t,e()}finally{Z.p=n}}var Pn=Math.random().toString(36).slice(2),Se="__reactFiber$"+Pn,Oe="__reactProps$"+Pn,Qa="__reactContainer$"+Pn,Pu="__reactEvents$"+Pn,Q0="__reactListeners$"+Pn,Z0="__reactHandles$"+Pn,ff="__reactResources$"+Pn,Xl="__reactMarker$"+Pn;function Fu(t){delete t[Se],delete t[Oe],delete t[Pu],delete t[Q0],delete t[Z0]}function Za(t){var e=t[Se];if(e)return e;for(var n=t.parentNode;n;){if(e=n[Qa]||n[Se]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=Rh(t);t!==null;){if(n=t[Se])return n;t=Rh(t)}return e}t=n,n=t.parentNode}return null}function Ka(t){if(t=t[Se]||t[Qa]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Ql(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(o(33))}function Ja(t){var e=t[ff];return e||(e=t[ff]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function ue(t){t[Xl]=!0}var df=new Set,pf={};function Ea(t,e){Wa(t,e),Wa(t+"Capture",e)}function Wa(t,e){for(pf[t]=e,t=0;t<e.length;t++)df.add(e[t])}var K0=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),hf={},mf={};function J0(t){return Kn.call(mf,t)?!0:Kn.call(hf,t)?!1:K0.test(t)?mf[t]=!0:(hf[t]=!0,!1)}function sr(t,e,n){if(J0(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var l=e.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function fr(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function zn(t,e,n,l){if(l===null)t.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+l)}}var Iu,yf;function Pa(t){if(Iu===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);Iu=e&&e[1]||"",yf=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Iu+t+yf}var to=!1;function eo(t,e){if(!t||to)return"";to=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(e){var k=function(){throw Error()};if(Object.defineProperty(k.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(k,[])}catch(B){var D=B}Reflect.construct(t,[],k)}else{try{k.call()}catch(B){D=B}t.call(k.prototype)}}else{try{throw Error()}catch(B){D=B}(k=t())&&typeof k.catch=="function"&&k.catch(function(){})}}catch(B){if(B&&D&&typeof B.stack=="string")return[B.stack,D.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=l.DetermineComponentFrameRoot(),d=c[0],h=c[1];if(d&&h){var b=d.split(`
`),_=h.split(`
`);for(i=l=0;l<b.length&&!b[l].includes("DetermineComponentFrameRoot");)l++;for(;i<_.length&&!_[i].includes("DetermineComponentFrameRoot");)i++;if(l===b.length||i===_.length)for(l=b.length-1,i=_.length-1;1<=l&&0<=i&&b[l]!==_[i];)i--;for(;1<=l&&0<=i;l--,i--)if(b[l]!==_[i]){if(l!==1||i!==1)do if(l--,i--,0>i||b[l]!==_[i]){var H=`
`+b[l].replace(" at new "," at ");return t.displayName&&H.includes("<anonymous>")&&(H=H.replace("<anonymous>",t.displayName)),H}while(1<=l&&0<=i);break}}}finally{to=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?Pa(n):""}function W0(t){switch(t.tag){case 26:case 27:case 5:return Pa(t.type);case 16:return Pa("Lazy");case 13:return Pa("Suspense");case 19:return Pa("SuspenseList");case 0:case 15:return eo(t.type,!1);case 11:return eo(t.type.render,!1);case 1:return eo(t.type,!0);case 31:return Pa("Activity");default:return""}}function gf(t){try{var e="";do e+=W0(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Qe(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function vf(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function P0(t){var e=vf(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),l=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,c=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return i.call(this)},set:function(d){l=""+d,c.call(this,d)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(d){l=""+d},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function dr(t){t._valueTracker||(t._valueTracker=P0(t))}function bf(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),l="";return t&&(l=vf(t)?t.checked?"true":"false":t.value),t=l,t!==n?(e.setValue(t),!0):!1}function pr(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var F0=/[\n"\\]/g;function Ze(t){return t.replace(F0,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function no(t,e,n,l,i,c,d,h){t.name="",d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?t.type=d:t.removeAttribute("type"),e!=null?d==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Qe(e)):t.value!==""+Qe(e)&&(t.value=""+Qe(e)):d!=="submit"&&d!=="reset"||t.removeAttribute("value"),e!=null?ao(t,d,Qe(e)):n!=null?ao(t,d,Qe(n)):l!=null&&t.removeAttribute("value"),i==null&&c!=null&&(t.defaultChecked=!!c),i!=null&&(t.checked=i&&typeof i!="function"&&typeof i!="symbol"),h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?t.name=""+Qe(h):t.removeAttribute("name")}function Sf(t,e,n,l,i,c,d,h){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(t.type=c),e!=null||n!=null){if(!(c!=="submit"&&c!=="reset"||e!=null))return;n=n!=null?""+Qe(n):"",e=e!=null?""+Qe(e):n,h||e===t.value||(t.value=e),t.defaultValue=e}l=l??i,l=typeof l!="function"&&typeof l!="symbol"&&!!l,t.checked=h?t.checked:!!l,t.defaultChecked=!!l,d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"&&(t.name=d)}function ao(t,e,n){e==="number"&&pr(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function Fa(t,e,n,l){if(t=t.options,e){e={};for(var i=0;i<n.length;i++)e["$"+n[i]]=!0;for(n=0;n<t.length;n++)i=e.hasOwnProperty("$"+t[n].value),t[n].selected!==i&&(t[n].selected=i),i&&l&&(t[n].defaultSelected=!0)}else{for(n=""+Qe(n),e=null,i=0;i<t.length;i++){if(t[i].value===n){t[i].selected=!0,l&&(t[i].defaultSelected=!0);return}e!==null||t[i].disabled||(e=t[i])}e!==null&&(e.selected=!0)}}function Cf(t,e,n){if(e!=null&&(e=""+Qe(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+Qe(n):""}function Tf(t,e,n,l){if(e==null){if(l!=null){if(n!=null)throw Error(o(92));if(mt(l)){if(1<l.length)throw Error(o(93));l=l[0]}n=l}n==null&&(n=""),e=n}n=Qe(e),t.defaultValue=n,l=t.textContent,l===n&&l!==""&&l!==null&&(t.value=l)}function Ia(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var I0=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function xf(t,e,n){var l=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":l?t.setProperty(e,n):typeof n!="number"||n===0||I0.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function Ef(t,e,n){if(e!=null&&typeof e!="object")throw Error(o(62));if(t=t.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||e!=null&&e.hasOwnProperty(l)||(l.indexOf("--")===0?t.setProperty(l,""):l==="float"?t.cssFloat="":t[l]="");for(var i in e)l=e[i],e.hasOwnProperty(i)&&n[i]!==l&&xf(t,i,l)}else for(var c in e)e.hasOwnProperty(c)&&xf(t,c,e[c])}function lo(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ty=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),ey=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function hr(t){return ey.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var io=null;function ro(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var tl=null,el=null;function Af(t){var e=Ka(t);if(e&&(t=e.stateNode)){var n=t[Oe]||null;t:switch(t=e.stateNode,e.type){case"input":if(no(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Ze(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var l=n[e];if(l!==t&&l.form===t.form){var i=l[Oe]||null;if(!i)throw Error(o(90));no(l,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(e=0;e<n.length;e++)l=n[e],l.form===t.form&&bf(l)}break t;case"textarea":Cf(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&Fa(t,!!n.multiple,e,!1)}}}var uo=!1;function Of(t,e,n){if(uo)return t(e,n);uo=!0;try{var l=t(e);return l}finally{if(uo=!1,(tl!==null||el!==null)&&(Ir(),tl&&(e=tl,t=el,el=tl=null,Af(e),t)))for(e=0;e<t.length;e++)Af(t[e])}}function Zl(t,e){var n=t.stateNode;if(n===null)return null;var l=n[Oe]||null;if(l===null)return null;n=l[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(t=t.type,l=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!l;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(o(231,e,typeof n));return n}var _n=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),oo=!1;if(_n)try{var Kl={};Object.defineProperty(Kl,"passive",{get:function(){oo=!0}}),window.addEventListener("test",Kl,Kl),window.removeEventListener("test",Kl,Kl)}catch{oo=!1}var Fn=null,co=null,mr=null;function Mf(){if(mr)return mr;var t,e=co,n=e.length,l,i="value"in Fn?Fn.value:Fn.textContent,c=i.length;for(t=0;t<n&&e[t]===i[t];t++);var d=n-t;for(l=1;l<=d&&e[n-l]===i[c-l];l++);return mr=i.slice(t,1<l?1-l:void 0)}function yr(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function gr(){return!0}function Rf(){return!1}function Me(t){function e(n,l,i,c,d){this._reactName=n,this._targetInst=i,this.type=l,this.nativeEvent=c,this.target=d,this.currentTarget=null;for(var h in t)t.hasOwnProperty(h)&&(n=t[h],this[h]=n?n(c):c[h]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?gr:Rf,this.isPropagationStopped=Rf,this}return S(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=gr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=gr)},persist:function(){},isPersistent:gr}),e}var Aa={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},vr=Me(Aa),Jl=S({},Aa,{view:0,detail:0}),ny=Me(Jl),so,fo,Wl,br=S({},Jl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ho,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Wl&&(Wl&&t.type==="mousemove"?(so=t.screenX-Wl.screenX,fo=t.screenY-Wl.screenY):fo=so=0,Wl=t),so)},movementY:function(t){return"movementY"in t?t.movementY:fo}}),zf=Me(br),ay=S({},br,{dataTransfer:0}),ly=Me(ay),iy=S({},Jl,{relatedTarget:0}),po=Me(iy),ry=S({},Aa,{animationName:0,elapsedTime:0,pseudoElement:0}),uy=Me(ry),oy=S({},Aa,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),cy=Me(oy),sy=S({},Aa,{data:0}),_f=Me(sy),fy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},py={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function hy(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=py[t])?!!e[t]:!1}function ho(){return hy}var my=S({},Jl,{key:function(t){if(t.key){var e=fy[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=yr(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?dy[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ho,charCode:function(t){return t.type==="keypress"?yr(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?yr(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),yy=Me(my),gy=S({},br,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Df=Me(gy),vy=S({},Jl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ho}),by=Me(vy),Sy=S({},Aa,{propertyName:0,elapsedTime:0,pseudoElement:0}),Cy=Me(Sy),Ty=S({},br,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),xy=Me(Ty),Ey=S({},Aa,{newState:0,oldState:0}),Ay=Me(Ey),Oy=[9,13,27,32],mo=_n&&"CompositionEvent"in window,Pl=null;_n&&"documentMode"in document&&(Pl=document.documentMode);var My=_n&&"TextEvent"in window&&!Pl,Bf=_n&&(!mo||Pl&&8<Pl&&11>=Pl),Nf=" ",Uf=!1;function wf(t,e){switch(t){case"keyup":return Oy.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function $f(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var nl=!1;function Ry(t,e){switch(t){case"compositionend":return $f(e);case"keypress":return e.which!==32?null:(Uf=!0,Nf);case"textInput":return t=e.data,t===Nf&&Uf?null:t;default:return null}}function zy(t,e){if(nl)return t==="compositionend"||!mo&&wf(t,e)?(t=Mf(),mr=co=Fn=null,nl=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Bf&&e.locale!=="ko"?null:e.data;default:return null}}var _y={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Hf(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!_y[t.type]:e==="textarea"}function jf(t,e,n,l){tl?el?el.push(l):el=[l]:tl=l,e=iu(e,"onChange"),0<e.length&&(n=new vr("onChange","change",null,n,l),t.push({event:n,listeners:e}))}var Fl=null,Il=null;function Dy(t){vh(t,0)}function Sr(t){var e=Ql(t);if(bf(e))return t}function Lf(t,e){if(t==="change")return e}var qf=!1;if(_n){var yo;if(_n){var go="oninput"in document;if(!go){var Yf=document.createElement("div");Yf.setAttribute("oninput","return;"),go=typeof Yf.oninput=="function"}yo=go}else yo=!1;qf=yo&&(!document.documentMode||9<document.documentMode)}function kf(){Fl&&(Fl.detachEvent("onpropertychange",Gf),Il=Fl=null)}function Gf(t){if(t.propertyName==="value"&&Sr(Il)){var e=[];jf(e,Il,t,ro(t)),Of(Dy,e)}}function By(t,e,n){t==="focusin"?(kf(),Fl=e,Il=n,Fl.attachEvent("onpropertychange",Gf)):t==="focusout"&&kf()}function Ny(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Sr(Il)}function Uy(t,e){if(t==="click")return Sr(e)}function wy(t,e){if(t==="input"||t==="change")return Sr(e)}function $y(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Ue=typeof Object.is=="function"?Object.is:$y;function ti(t,e){if(Ue(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),l=Object.keys(e);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var i=n[l];if(!Kn.call(e,i)||!Ue(t[i],e[i]))return!1}return!0}function Vf(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Xf(t,e){var n=Vf(t);t=0;for(var l;n;){if(n.nodeType===3){if(l=t+n.textContent.length,t<=e&&l>=e)return{node:n,offset:e-t};t=l}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=Vf(n)}}function Qf(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Qf(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Zf(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=pr(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=pr(t.document)}return e}function vo(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Hy=_n&&"documentMode"in document&&11>=document.documentMode,al=null,bo=null,ei=null,So=!1;function Kf(t,e,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;So||al==null||al!==pr(l)||(l=al,"selectionStart"in l&&vo(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),ei&&ti(ei,l)||(ei=l,l=iu(bo,"onSelect"),0<l.length&&(e=new vr("onSelect","select",null,e,n),t.push({event:e,listeners:l}),e.target=al)))}function Oa(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var ll={animationend:Oa("Animation","AnimationEnd"),animationiteration:Oa("Animation","AnimationIteration"),animationstart:Oa("Animation","AnimationStart"),transitionrun:Oa("Transition","TransitionRun"),transitionstart:Oa("Transition","TransitionStart"),transitioncancel:Oa("Transition","TransitionCancel"),transitionend:Oa("Transition","TransitionEnd")},Co={},Jf={};_n&&(Jf=document.createElement("div").style,"AnimationEvent"in window||(delete ll.animationend.animation,delete ll.animationiteration.animation,delete ll.animationstart.animation),"TransitionEvent"in window||delete ll.transitionend.transition);function Ma(t){if(Co[t])return Co[t];if(!ll[t])return t;var e=ll[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in Jf)return Co[t]=e[n];return t}var Wf=Ma("animationend"),Pf=Ma("animationiteration"),Ff=Ma("animationstart"),jy=Ma("transitionrun"),Ly=Ma("transitionstart"),qy=Ma("transitioncancel"),If=Ma("transitionend"),td=new Map,To="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");To.push("scrollEnd");function cn(t,e){td.set(t,e),Ea(e,[t])}var ed=new WeakMap;function Ke(t,e){if(typeof t=="object"&&t!==null){var n=ed.get(t);return n!==void 0?n:(e={value:t,source:e,stack:gf(e)},ed.set(t,e),e)}return{value:t,source:e,stack:gf(e)}}var Je=[],il=0,xo=0;function Cr(){for(var t=il,e=xo=il=0;e<t;){var n=Je[e];Je[e++]=null;var l=Je[e];Je[e++]=null;var i=Je[e];Je[e++]=null;var c=Je[e];if(Je[e++]=null,l!==null&&i!==null){var d=l.pending;d===null?i.next=i:(i.next=d.next,d.next=i),l.pending=i}c!==0&&nd(n,i,c)}}function Tr(t,e,n,l){Je[il++]=t,Je[il++]=e,Je[il++]=n,Je[il++]=l,xo|=l,t.lanes|=l,t=t.alternate,t!==null&&(t.lanes|=l)}function Eo(t,e,n,l){return Tr(t,e,n,l),xr(t)}function rl(t,e){return Tr(t,null,null,e),xr(t)}function nd(t,e,n){t.lanes|=n;var l=t.alternate;l!==null&&(l.lanes|=n);for(var i=!1,c=t.return;c!==null;)c.childLanes|=n,l=c.alternate,l!==null&&(l.childLanes|=n),c.tag===22&&(t=c.stateNode,t===null||t._visibility&1||(i=!0)),t=c,c=c.return;return t.tag===3?(c=t.stateNode,i&&e!==null&&(i=31-ge(n),t=c.hiddenUpdates,l=t[i],l===null?t[i]=[e]:l.push(e),e.lane=n|536870912),c):null}function xr(t){if(50<Oi)throw Oi=0,_c=null,Error(o(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var ul={};function Yy(t,e,n,l){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function we(t,e,n,l){return new Yy(t,e,n,l)}function Ao(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Dn(t,e){var n=t.alternate;return n===null?(n=we(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function ad(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Er(t,e,n,l,i,c){var d=0;if(l=t,typeof t=="function")Ao(t)&&(d=1);else if(typeof t=="string")d=Gg(t,n,ot.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case et:return t=we(31,n,e,i),t.elementType=et,t.lanes=c,t;case M:return Ra(n.children,i,c,e);case T:d=8,i|=24;break;case G:return t=we(12,n,e,i|2),t.elementType=G,t.lanes=c,t;case R:return t=we(13,n,e,i),t.elementType=R,t.lanes=c,t;case Q:return t=we(19,n,e,i),t.elementType=Q,t.lanes=c,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case X:case V:d=10;break t;case W:d=9;break t;case j:d=11;break t;case F:d=14;break t;case I:d=16,l=null;break t}d=29,n=Error(o(130,t===null?"null":typeof t,"")),l=null}return e=we(d,n,e,i),e.elementType=t,e.type=l,e.lanes=c,e}function Ra(t,e,n,l){return t=we(7,t,l,e),t.lanes=n,t}function Oo(t,e,n){return t=we(6,t,null,e),t.lanes=n,t}function Mo(t,e,n){return e=we(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var ol=[],cl=0,Ar=null,Or=0,We=[],Pe=0,za=null,Bn=1,Nn="";function _a(t,e){ol[cl++]=Or,ol[cl++]=Ar,Ar=t,Or=e}function ld(t,e,n){We[Pe++]=Bn,We[Pe++]=Nn,We[Pe++]=za,za=t;var l=Bn;t=Nn;var i=32-ge(l)-1;l&=~(1<<i),n+=1;var c=32-ge(e)+i;if(30<c){var d=i-i%5;c=(l&(1<<d)-1).toString(32),l>>=d,i-=d,Bn=1<<32-ge(e)+i|n<<i|l,Nn=c+t}else Bn=1<<c|n<<i|l,Nn=t}function Ro(t){t.return!==null&&(_a(t,1),ld(t,1,0))}function zo(t){for(;t===Ar;)Ar=ol[--cl],ol[cl]=null,Or=ol[--cl],ol[cl]=null;for(;t===za;)za=We[--Pe],We[Pe]=null,Nn=We[--Pe],We[Pe]=null,Bn=We[--Pe],We[Pe]=null}var Ae=null,Zt=null,At=!1,Da=null,mn=!1,_o=Error(o(519));function Ba(t){var e=Error(o(418,""));throw li(Ke(e,t)),_o}function id(t){var e=t.stateNode,n=t.type,l=t.memoizedProps;switch(e[Se]=t,e[Oe]=l,n){case"dialog":Ct("cancel",e),Ct("close",e);break;case"iframe":case"object":case"embed":Ct("load",e);break;case"video":case"audio":for(n=0;n<Ri.length;n++)Ct(Ri[n],e);break;case"source":Ct("error",e);break;case"img":case"image":case"link":Ct("error",e),Ct("load",e);break;case"details":Ct("toggle",e);break;case"input":Ct("invalid",e),Sf(e,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),dr(e);break;case"select":Ct("invalid",e);break;case"textarea":Ct("invalid",e),Tf(e,l.value,l.defaultValue,l.children),dr(e)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||l.suppressHydrationWarning===!0||Th(e.textContent,n)?(l.popover!=null&&(Ct("beforetoggle",e),Ct("toggle",e)),l.onScroll!=null&&Ct("scroll",e),l.onScrollEnd!=null&&Ct("scrollend",e),l.onClick!=null&&(e.onclick=ru),e=!0):e=!1,e||Ba(t)}function rd(t){for(Ae=t.return;Ae;)switch(Ae.tag){case 5:case 13:mn=!1;return;case 27:case 3:mn=!0;return;default:Ae=Ae.return}}function ni(t){if(t!==Ae)return!1;if(!At)return rd(t),At=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||Qc(t.type,t.memoizedProps)),n=!n),n&&Zt&&Ba(t),rd(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(o(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){Zt=fn(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}Zt=null}}else e===27?(e=Zt,ha(t.type)?(t=Wc,Wc=null,Zt=t):Zt=e):Zt=Ae?fn(t.stateNode.nextSibling):null;return!0}function ai(){Zt=Ae=null,At=!1}function ud(){var t=Da;return t!==null&&(_e===null?_e=t:_e.push.apply(_e,t),Da=null),t}function li(t){Da===null?Da=[t]:Da.push(t)}var Do=q(null),Na=null,Un=null;function In(t,e,n){P(Do,e._currentValue),e._currentValue=n}function wn(t){t._currentValue=Do.current,K(Do)}function Bo(t,e,n){for(;t!==null;){var l=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,l!==null&&(l.childLanes|=e)):l!==null&&(l.childLanes&e)!==e&&(l.childLanes|=e),t===n)break;t=t.return}}function No(t,e,n,l){var i=t.child;for(i!==null&&(i.return=t);i!==null;){var c=i.dependencies;if(c!==null){var d=i.child;c=c.firstContext;t:for(;c!==null;){var h=c;c=i;for(var b=0;b<e.length;b++)if(h.context===e[b]){c.lanes|=n,h=c.alternate,h!==null&&(h.lanes|=n),Bo(c.return,n,t),l||(d=null);break t}c=h.next}}else if(i.tag===18){if(d=i.return,d===null)throw Error(o(341));d.lanes|=n,c=d.alternate,c!==null&&(c.lanes|=n),Bo(d,n,t),d=null}else d=i.child;if(d!==null)d.return=i;else for(d=i;d!==null;){if(d===t){d=null;break}if(i=d.sibling,i!==null){i.return=d.return,d=i;break}d=d.return}i=d}}function ii(t,e,n,l){t=null;for(var i=e,c=!1;i!==null;){if(!c){if((i.flags&524288)!==0)c=!0;else if((i.flags&262144)!==0)break}if(i.tag===10){var d=i.alternate;if(d===null)throw Error(o(387));if(d=d.memoizedProps,d!==null){var h=i.type;Ue(i.pendingProps.value,d.value)||(t!==null?t.push(h):t=[h])}}else if(i===Qt.current){if(d=i.alternate,d===null)throw Error(o(387));d.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(t!==null?t.push(Ui):t=[Ui])}i=i.return}t!==null&&No(e,t,n,l),e.flags|=262144}function Mr(t){for(t=t.firstContext;t!==null;){if(!Ue(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Ua(t){Na=t,Un=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Ce(t){return od(Na,t)}function Rr(t,e){return Na===null&&Ua(t),od(t,e)}function od(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},Un===null){if(t===null)throw Error(o(308));Un=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Un=Un.next=e;return n}var ky=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,l){t.push(l)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},Gy=a.unstable_scheduleCallback,Vy=a.unstable_NormalPriority,le={$$typeof:V,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Uo(){return{controller:new ky,data:new Map,refCount:0}}function ri(t){t.refCount--,t.refCount===0&&Gy(Vy,function(){t.controller.abort()})}var ui=null,wo=0,sl=0,fl=null;function Xy(t,e){if(ui===null){var n=ui=[];wo=0,sl=Hc(),fl={status:"pending",value:void 0,then:function(l){n.push(l)}}}return wo++,e.then(cd,cd),e}function cd(){if(--wo===0&&ui!==null){fl!==null&&(fl.status="fulfilled");var t=ui;ui=null,sl=0,fl=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Qy(t,e){var n=[],l={status:"pending",value:null,reason:null,then:function(i){n.push(i)}};return t.then(function(){l.status="fulfilled",l.value=e;for(var i=0;i<n.length;i++)(0,n[i])(e)},function(i){for(l.status="rejected",l.reason=i,i=0;i<n.length;i++)(0,n[i])(void 0)}),l}var sd=N.S;N.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&Xy(t,e),sd!==null&&sd(t,e)};var wa=q(null);function $o(){var t=wa.current;return t!==null?t:qt.pooledCache}function zr(t,e){e===null?P(wa,wa.current):P(wa,e.pool)}function fd(){var t=$o();return t===null?null:{parent:le._currentValue,pool:t}}var oi=Error(o(460)),dd=Error(o(474)),_r=Error(o(542)),Ho={then:function(){}};function pd(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Dr(){}function hd(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(Dr,Dr),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,yd(t),t;default:if(typeof e.status=="string")e.then(Dr,Dr);else{if(t=qt,t!==null&&100<t.shellSuspendCounter)throw Error(o(482));t=e,t.status="pending",t.then(function(l){if(e.status==="pending"){var i=e;i.status="fulfilled",i.value=l}},function(l){if(e.status==="pending"){var i=e;i.status="rejected",i.reason=l}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,yd(t),t}throw ci=e,oi}}var ci=null;function md(){if(ci===null)throw Error(o(459));var t=ci;return ci=null,t}function yd(t){if(t===oi||t===_r)throw Error(o(483))}var ta=!1;function jo(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Lo(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function ea(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function na(t,e,n){var l=t.updateQueue;if(l===null)return null;if(l=l.shared,(_t&2)!==0){var i=l.pending;return i===null?e.next=e:(e.next=i.next,i.next=e),l.pending=e,e=xr(t),nd(t,null,n),e}return Tr(t,l,e,n),xr(t)}function si(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,cf(t,n)}}function qo(t,e){var n=t.updateQueue,l=t.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var i=null,c=null;if(n=n.firstBaseUpdate,n!==null){do{var d={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};c===null?i=c=d:c=c.next=d,n=n.next}while(n!==null);c===null?i=c=e:c=c.next=e}else i=c=e;n={baseState:l.baseState,firstBaseUpdate:i,lastBaseUpdate:c,shared:l.shared,callbacks:l.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var Yo=!1;function fi(){if(Yo){var t=fl;if(t!==null)throw t}}function di(t,e,n,l){Yo=!1;var i=t.updateQueue;ta=!1;var c=i.firstBaseUpdate,d=i.lastBaseUpdate,h=i.shared.pending;if(h!==null){i.shared.pending=null;var b=h,_=b.next;b.next=null,d===null?c=_:d.next=_,d=b;var H=t.alternate;H!==null&&(H=H.updateQueue,h=H.lastBaseUpdate,h!==d&&(h===null?H.firstBaseUpdate=_:h.next=_,H.lastBaseUpdate=b))}if(c!==null){var k=i.baseState;d=0,H=_=b=null,h=c;do{var D=h.lane&-536870913,B=D!==h.lane;if(B?(xt&D)===D:(l&D)===D){D!==0&&D===sl&&(Yo=!0),H!==null&&(H=H.next={lane:0,tag:h.tag,payload:h.payload,callback:null,next:null});t:{var dt=t,ct=h;D=e;var Ut=n;switch(ct.tag){case 1:if(dt=ct.payload,typeof dt=="function"){k=dt.call(Ut,k,D);break t}k=dt;break t;case 3:dt.flags=dt.flags&-65537|128;case 0:if(dt=ct.payload,D=typeof dt=="function"?dt.call(Ut,k,D):dt,D==null)break t;k=S({},k,D);break t;case 2:ta=!0}}D=h.callback,D!==null&&(t.flags|=64,B&&(t.flags|=8192),B=i.callbacks,B===null?i.callbacks=[D]:B.push(D))}else B={lane:D,tag:h.tag,payload:h.payload,callback:h.callback,next:null},H===null?(_=H=B,b=k):H=H.next=B,d|=D;if(h=h.next,h===null){if(h=i.shared.pending,h===null)break;B=h,h=B.next,B.next=null,i.lastBaseUpdate=B,i.shared.pending=null}}while(!0);H===null&&(b=k),i.baseState=b,i.firstBaseUpdate=_,i.lastBaseUpdate=H,c===null&&(i.shared.lanes=0),sa|=d,t.lanes=d,t.memoizedState=k}}function gd(t,e){if(typeof t!="function")throw Error(o(191,t));t.call(e)}function vd(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)gd(n[t],e)}var dl=q(null),Br=q(0);function bd(t,e){t=kn,P(Br,t),P(dl,e),kn=t|e.baseLanes}function ko(){P(Br,kn),P(dl,dl.current)}function Go(){kn=Br.current,K(dl),K(Br)}var aa=0,gt=null,Bt=null,te=null,Nr=!1,pl=!1,$a=!1,Ur=0,pi=0,hl=null,Zy=0;function Pt(){throw Error(o(321))}function Vo(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!Ue(t[n],e[n]))return!1;return!0}function Xo(t,e,n,l,i,c){return aa=c,gt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,N.H=t===null||t.memoizedState===null?np:ap,$a=!1,c=n(l,i),$a=!1,pl&&(c=Cd(e,n,l,i)),Sd(t),c}function Sd(t){N.H=qr;var e=Bt!==null&&Bt.next!==null;if(aa=0,te=Bt=gt=null,Nr=!1,pi=0,hl=null,e)throw Error(o(300));t===null||oe||(t=t.dependencies,t!==null&&Mr(t)&&(oe=!0))}function Cd(t,e,n,l){gt=t;var i=0;do{if(pl&&(hl=null),pi=0,pl=!1,25<=i)throw Error(o(301));if(i+=1,te=Bt=null,t.updateQueue!=null){var c=t.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}N.H=tg,c=e(n,l)}while(pl);return c}function Ky(){var t=N.H,e=t.useState()[0];return e=typeof e.then=="function"?hi(e):e,t=t.useState()[0],(Bt!==null?Bt.memoizedState:null)!==t&&(gt.flags|=1024),e}function Qo(){var t=Ur!==0;return Ur=0,t}function Zo(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function Ko(t){if(Nr){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Nr=!1}aa=0,te=Bt=gt=null,pl=!1,pi=Ur=0,hl=null}function Re(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return te===null?gt.memoizedState=te=t:te=te.next=t,te}function ee(){if(Bt===null){var t=gt.alternate;t=t!==null?t.memoizedState:null}else t=Bt.next;var e=te===null?gt.memoizedState:te.next;if(e!==null)te=e,Bt=t;else{if(t===null)throw gt.alternate===null?Error(o(467)):Error(o(310));Bt=t,t={memoizedState:Bt.memoizedState,baseState:Bt.baseState,baseQueue:Bt.baseQueue,queue:Bt.queue,next:null},te===null?gt.memoizedState=te=t:te=te.next=t}return te}function Jo(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function hi(t){var e=pi;return pi+=1,hl===null&&(hl=[]),t=hd(hl,t,e),e=gt,(te===null?e.memoizedState:te.next)===null&&(e=e.alternate,N.H=e===null||e.memoizedState===null?np:ap),t}function wr(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return hi(t);if(t.$$typeof===V)return Ce(t)}throw Error(o(438,String(t)))}function Wo(t){var e=null,n=gt.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var l=gt.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(e={data:l.data.map(function(i){return i.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=Jo(),gt.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),l=0;l<t;l++)n[l]=g;return e.index++,n}function $n(t,e){return typeof e=="function"?e(t):e}function $r(t){var e=ee();return Po(e,Bt,t)}function Po(t,e,n){var l=t.queue;if(l===null)throw Error(o(311));l.lastRenderedReducer=n;var i=t.baseQueue,c=l.pending;if(c!==null){if(i!==null){var d=i.next;i.next=c.next,c.next=d}e.baseQueue=i=c,l.pending=null}if(c=t.baseState,i===null)t.memoizedState=c;else{e=i.next;var h=d=null,b=null,_=e,H=!1;do{var k=_.lane&-536870913;if(k!==_.lane?(xt&k)===k:(aa&k)===k){var D=_.revertLane;if(D===0)b!==null&&(b=b.next={lane:0,revertLane:0,action:_.action,hasEagerState:_.hasEagerState,eagerState:_.eagerState,next:null}),k===sl&&(H=!0);else if((aa&D)===D){_=_.next,D===sl&&(H=!0);continue}else k={lane:0,revertLane:_.revertLane,action:_.action,hasEagerState:_.hasEagerState,eagerState:_.eagerState,next:null},b===null?(h=b=k,d=c):b=b.next=k,gt.lanes|=D,sa|=D;k=_.action,$a&&n(c,k),c=_.hasEagerState?_.eagerState:n(c,k)}else D={lane:k,revertLane:_.revertLane,action:_.action,hasEagerState:_.hasEagerState,eagerState:_.eagerState,next:null},b===null?(h=b=D,d=c):b=b.next=D,gt.lanes|=k,sa|=k;_=_.next}while(_!==null&&_!==e);if(b===null?d=c:b.next=h,!Ue(c,t.memoizedState)&&(oe=!0,H&&(n=fl,n!==null)))throw n;t.memoizedState=c,t.baseState=d,t.baseQueue=b,l.lastRenderedState=c}return i===null&&(l.lanes=0),[t.memoizedState,l.dispatch]}function Fo(t){var e=ee(),n=e.queue;if(n===null)throw Error(o(311));n.lastRenderedReducer=t;var l=n.dispatch,i=n.pending,c=e.memoizedState;if(i!==null){n.pending=null;var d=i=i.next;do c=t(c,d.action),d=d.next;while(d!==i);Ue(c,e.memoizedState)||(oe=!0),e.memoizedState=c,e.baseQueue===null&&(e.baseState=c),n.lastRenderedState=c}return[c,l]}function Td(t,e,n){var l=gt,i=ee(),c=At;if(c){if(n===void 0)throw Error(o(407));n=n()}else n=e();var d=!Ue((Bt||i).memoizedState,n);d&&(i.memoizedState=n,oe=!0),i=i.queue;var h=Ad.bind(null,l,i,t);if(mi(2048,8,h,[t]),i.getSnapshot!==e||d||te!==null&&te.memoizedState.tag&1){if(l.flags|=2048,ml(9,Hr(),Ed.bind(null,l,i,n,e),null),qt===null)throw Error(o(349));c||(aa&124)!==0||xd(l,e,n)}return n}function xd(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=gt.updateQueue,e===null?(e=Jo(),gt.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function Ed(t,e,n,l){e.value=n,e.getSnapshot=l,Od(e)&&Md(t)}function Ad(t,e,n){return n(function(){Od(e)&&Md(t)})}function Od(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!Ue(t,n)}catch{return!0}}function Md(t){var e=rl(t,2);e!==null&&qe(e,t,2)}function Io(t){var e=Re();if(typeof t=="function"){var n=t;if(t=n(),$a){on(!0);try{n()}finally{on(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:$n,lastRenderedState:t},e}function Rd(t,e,n,l){return t.baseState=n,Po(t,Bt,typeof l=="function"?l:$n)}function Jy(t,e,n,l,i){if(Lr(t))throw Error(o(485));if(t=e.action,t!==null){var c={payload:i,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(d){c.listeners.push(d)}};N.T!==null?n(!0):c.isTransition=!1,l(c),n=e.pending,n===null?(c.next=e.pending=c,zd(e,c)):(c.next=n.next,e.pending=n.next=c)}}function zd(t,e){var n=e.action,l=e.payload,i=t.state;if(e.isTransition){var c=N.T,d={};N.T=d;try{var h=n(i,l),b=N.S;b!==null&&b(d,h),_d(t,e,h)}catch(_){tc(t,e,_)}finally{N.T=c}}else try{c=n(i,l),_d(t,e,c)}catch(_){tc(t,e,_)}}function _d(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){Dd(t,e,l)},function(l){return tc(t,e,l)}):Dd(t,e,n)}function Dd(t,e,n){e.status="fulfilled",e.value=n,Bd(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,zd(t,n)))}function tc(t,e,n){var l=t.pending;if(t.pending=null,l!==null){l=l.next;do e.status="rejected",e.reason=n,Bd(e),e=e.next;while(e!==l)}t.action=null}function Bd(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Nd(t,e){return e}function Ud(t,e){if(At){var n=qt.formState;if(n!==null){t:{var l=gt;if(At){if(Zt){e:{for(var i=Zt,c=mn;i.nodeType!==8;){if(!c){i=null;break e}if(i=fn(i.nextSibling),i===null){i=null;break e}}c=i.data,i=c==="F!"||c==="F"?i:null}if(i){Zt=fn(i.nextSibling),l=i.data==="F!";break t}}Ba(l)}l=!1}l&&(e=n[0])}}return n=Re(),n.memoizedState=n.baseState=e,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Nd,lastRenderedState:e},n.queue=l,n=Id.bind(null,gt,l),l.dispatch=n,l=Io(!1),c=ic.bind(null,gt,!1,l.queue),l=Re(),i={state:e,dispatch:null,action:t,pending:null},l.queue=i,n=Jy.bind(null,gt,i,c,n),i.dispatch=n,l.memoizedState=t,[e,n,!1]}function wd(t){var e=ee();return $d(e,Bt,t)}function $d(t,e,n){if(e=Po(t,e,Nd)[0],t=$r($n)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var l=hi(e)}catch(d){throw d===oi?_r:d}else l=e;e=ee();var i=e.queue,c=i.dispatch;return n!==e.memoizedState&&(gt.flags|=2048,ml(9,Hr(),Wy.bind(null,i,n),null)),[l,c,t]}function Wy(t,e){t.action=e}function Hd(t){var e=ee(),n=Bt;if(n!==null)return $d(e,n,t);ee(),e=e.memoizedState,n=ee();var l=n.queue.dispatch;return n.memoizedState=t,[e,l,!1]}function ml(t,e,n,l){return t={tag:t,create:n,deps:l,inst:e,next:null},e=gt.updateQueue,e===null&&(e=Jo(),gt.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(l=n.next,n.next=t,t.next=l,e.lastEffect=t),t}function Hr(){return{destroy:void 0,resource:void 0}}function jd(){return ee().memoizedState}function jr(t,e,n,l){var i=Re();l=l===void 0?null:l,gt.flags|=t,i.memoizedState=ml(1|e,Hr(),n,l)}function mi(t,e,n,l){var i=ee();l=l===void 0?null:l;var c=i.memoizedState.inst;Bt!==null&&l!==null&&Vo(l,Bt.memoizedState.deps)?i.memoizedState=ml(e,c,n,l):(gt.flags|=t,i.memoizedState=ml(1|e,c,n,l))}function Ld(t,e){jr(8390656,8,t,e)}function qd(t,e){mi(2048,8,t,e)}function Yd(t,e){return mi(4,2,t,e)}function kd(t,e){return mi(4,4,t,e)}function Gd(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Vd(t,e,n){n=n!=null?n.concat([t]):null,mi(4,4,Gd.bind(null,e,t),n)}function ec(){}function Xd(t,e){var n=ee();e=e===void 0?null:e;var l=n.memoizedState;return e!==null&&Vo(e,l[1])?l[0]:(n.memoizedState=[t,e],t)}function Qd(t,e){var n=ee();e=e===void 0?null:e;var l=n.memoizedState;if(e!==null&&Vo(e,l[1]))return l[0];if(l=t(),$a){on(!0);try{t()}finally{on(!1)}}return n.memoizedState=[l,e],l}function nc(t,e,n){return n===void 0||(aa&1073741824)!==0?t.memoizedState=e:(t.memoizedState=n,t=Jp(),gt.lanes|=t,sa|=t,n)}function Zd(t,e,n,l){return Ue(n,e)?n:dl.current!==null?(t=nc(t,n,l),Ue(t,e)||(oe=!0),t):(aa&42)===0?(oe=!0,t.memoizedState=n):(t=Jp(),gt.lanes|=t,sa|=t,e)}function Kd(t,e,n,l,i){var c=Z.p;Z.p=c!==0&&8>c?c:8;var d=N.T,h={};N.T=h,ic(t,!1,e,n);try{var b=i(),_=N.S;if(_!==null&&_(h,b),b!==null&&typeof b=="object"&&typeof b.then=="function"){var H=Qy(b,l);yi(t,e,H,Le(t))}else yi(t,e,l,Le(t))}catch(k){yi(t,e,{then:function(){},status:"rejected",reason:k},Le())}finally{Z.p=c,N.T=d}}function Py(){}function ac(t,e,n,l){if(t.tag!==5)throw Error(o(476));var i=Jd(t).queue;Kd(t,i,e,nt,n===null?Py:function(){return Wd(t),n(l)})}function Jd(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:nt,baseState:nt,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:$n,lastRenderedState:nt},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:$n,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Wd(t){var e=Jd(t).next.queue;yi(t,e,{},Le())}function lc(){return Ce(Ui)}function Pd(){return ee().memoizedState}function Fd(){return ee().memoizedState}function Fy(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=Le();t=ea(n);var l=na(e,t,n);l!==null&&(qe(l,e,n),si(l,e,n)),e={cache:Uo()},t.payload=e;return}e=e.return}}function Iy(t,e,n){var l=Le();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Lr(t)?tp(e,n):(n=Eo(t,e,n,l),n!==null&&(qe(n,t,l),ep(n,e,l)))}function Id(t,e,n){var l=Le();yi(t,e,n,l)}function yi(t,e,n,l){var i={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Lr(t))tp(e,i);else{var c=t.alternate;if(t.lanes===0&&(c===null||c.lanes===0)&&(c=e.lastRenderedReducer,c!==null))try{var d=e.lastRenderedState,h=c(d,n);if(i.hasEagerState=!0,i.eagerState=h,Ue(h,d))return Tr(t,e,i,0),qt===null&&Cr(),!1}catch{}finally{}if(n=Eo(t,e,i,l),n!==null)return qe(n,t,l),ep(n,e,l),!0}return!1}function ic(t,e,n,l){if(l={lane:2,revertLane:Hc(),action:l,hasEagerState:!1,eagerState:null,next:null},Lr(t)){if(e)throw Error(o(479))}else e=Eo(t,n,l,2),e!==null&&qe(e,t,2)}function Lr(t){var e=t.alternate;return t===gt||e!==null&&e===gt}function tp(t,e){pl=Nr=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function ep(t,e,n){if((n&4194048)!==0){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,cf(t,n)}}var qr={readContext:Ce,use:wr,useCallback:Pt,useContext:Pt,useEffect:Pt,useImperativeHandle:Pt,useLayoutEffect:Pt,useInsertionEffect:Pt,useMemo:Pt,useReducer:Pt,useRef:Pt,useState:Pt,useDebugValue:Pt,useDeferredValue:Pt,useTransition:Pt,useSyncExternalStore:Pt,useId:Pt,useHostTransitionStatus:Pt,useFormState:Pt,useActionState:Pt,useOptimistic:Pt,useMemoCache:Pt,useCacheRefresh:Pt},np={readContext:Ce,use:wr,useCallback:function(t,e){return Re().memoizedState=[t,e===void 0?null:e],t},useContext:Ce,useEffect:Ld,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,jr(4194308,4,Gd.bind(null,e,t),n)},useLayoutEffect:function(t,e){return jr(4194308,4,t,e)},useInsertionEffect:function(t,e){jr(4,2,t,e)},useMemo:function(t,e){var n=Re();e=e===void 0?null:e;var l=t();if($a){on(!0);try{t()}finally{on(!1)}}return n.memoizedState=[l,e],l},useReducer:function(t,e,n){var l=Re();if(n!==void 0){var i=n(e);if($a){on(!0);try{n(e)}finally{on(!1)}}}else i=e;return l.memoizedState=l.baseState=i,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:i},l.queue=t,t=t.dispatch=Iy.bind(null,gt,t),[l.memoizedState,t]},useRef:function(t){var e=Re();return t={current:t},e.memoizedState=t},useState:function(t){t=Io(t);var e=t.queue,n=Id.bind(null,gt,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:ec,useDeferredValue:function(t,e){var n=Re();return nc(n,t,e)},useTransition:function(){var t=Io(!1);return t=Kd.bind(null,gt,t.queue,!0,!1),Re().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var l=gt,i=Re();if(At){if(n===void 0)throw Error(o(407));n=n()}else{if(n=e(),qt===null)throw Error(o(349));(xt&124)!==0||xd(l,e,n)}i.memoizedState=n;var c={value:n,getSnapshot:e};return i.queue=c,Ld(Ad.bind(null,l,c,t),[t]),l.flags|=2048,ml(9,Hr(),Ed.bind(null,l,c,n,e),null),n},useId:function(){var t=Re(),e=qt.identifierPrefix;if(At){var n=Nn,l=Bn;n=(l&~(1<<32-ge(l)-1)).toString(32)+n,e="«"+e+"R"+n,n=Ur++,0<n&&(e+="H"+n.toString(32)),e+="»"}else n=Zy++,e="«"+e+"r"+n.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:lc,useFormState:Ud,useActionState:Ud,useOptimistic:function(t){var e=Re();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=ic.bind(null,gt,!0,n),n.dispatch=e,[t,e]},useMemoCache:Wo,useCacheRefresh:function(){return Re().memoizedState=Fy.bind(null,gt)}},ap={readContext:Ce,use:wr,useCallback:Xd,useContext:Ce,useEffect:qd,useImperativeHandle:Vd,useInsertionEffect:Yd,useLayoutEffect:kd,useMemo:Qd,useReducer:$r,useRef:jd,useState:function(){return $r($n)},useDebugValue:ec,useDeferredValue:function(t,e){var n=ee();return Zd(n,Bt.memoizedState,t,e)},useTransition:function(){var t=$r($n)[0],e=ee().memoizedState;return[typeof t=="boolean"?t:hi(t),e]},useSyncExternalStore:Td,useId:Pd,useHostTransitionStatus:lc,useFormState:wd,useActionState:wd,useOptimistic:function(t,e){var n=ee();return Rd(n,Bt,t,e)},useMemoCache:Wo,useCacheRefresh:Fd},tg={readContext:Ce,use:wr,useCallback:Xd,useContext:Ce,useEffect:qd,useImperativeHandle:Vd,useInsertionEffect:Yd,useLayoutEffect:kd,useMemo:Qd,useReducer:Fo,useRef:jd,useState:function(){return Fo($n)},useDebugValue:ec,useDeferredValue:function(t,e){var n=ee();return Bt===null?nc(n,t,e):Zd(n,Bt.memoizedState,t,e)},useTransition:function(){var t=Fo($n)[0],e=ee().memoizedState;return[typeof t=="boolean"?t:hi(t),e]},useSyncExternalStore:Td,useId:Pd,useHostTransitionStatus:lc,useFormState:Hd,useActionState:Hd,useOptimistic:function(t,e){var n=ee();return Bt!==null?Rd(n,Bt,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:Wo,useCacheRefresh:Fd},yl=null,gi=0;function Yr(t){var e=gi;return gi+=1,yl===null&&(yl=[]),hd(yl,t,e)}function vi(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function kr(t,e){throw e.$$typeof===E?Error(o(525)):(t=Object.prototype.toString.call(e),Error(o(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function lp(t){var e=t._init;return e(t._payload)}function ip(t){function e(O,x){if(t){var z=O.deletions;z===null?(O.deletions=[x],O.flags|=16):z.push(x)}}function n(O,x){if(!t)return null;for(;x!==null;)e(O,x),x=x.sibling;return null}function l(O){for(var x=new Map;O!==null;)O.key!==null?x.set(O.key,O):x.set(O.index,O),O=O.sibling;return x}function i(O,x){return O=Dn(O,x),O.index=0,O.sibling=null,O}function c(O,x,z){return O.index=z,t?(z=O.alternate,z!==null?(z=z.index,z<x?(O.flags|=67108866,x):z):(O.flags|=67108866,x)):(O.flags|=1048576,x)}function d(O){return t&&O.alternate===null&&(O.flags|=67108866),O}function h(O,x,z,L){return x===null||x.tag!==6?(x=Oo(z,O.mode,L),x.return=O,x):(x=i(x,z),x.return=O,x)}function b(O,x,z,L){var lt=z.type;return lt===M?H(O,x,z.props.children,L,z.key):x!==null&&(x.elementType===lt||typeof lt=="object"&&lt!==null&&lt.$$typeof===I&&lp(lt)===x.type)?(x=i(x,z.props),vi(x,z),x.return=O,x):(x=Er(z.type,z.key,z.props,null,O.mode,L),vi(x,z),x.return=O,x)}function _(O,x,z,L){return x===null||x.tag!==4||x.stateNode.containerInfo!==z.containerInfo||x.stateNode.implementation!==z.implementation?(x=Mo(z,O.mode,L),x.return=O,x):(x=i(x,z.children||[]),x.return=O,x)}function H(O,x,z,L,lt){return x===null||x.tag!==7?(x=Ra(z,O.mode,L,lt),x.return=O,x):(x=i(x,z),x.return=O,x)}function k(O,x,z){if(typeof x=="string"&&x!==""||typeof x=="number"||typeof x=="bigint")return x=Oo(""+x,O.mode,z),x.return=O,x;if(typeof x=="object"&&x!==null){switch(x.$$typeof){case A:return z=Er(x.type,x.key,x.props,null,O.mode,z),vi(z,x),z.return=O,z;case U:return x=Mo(x,O.mode,z),x.return=O,x;case I:var L=x._init;return x=L(x._payload),k(O,x,z)}if(mt(x)||J(x))return x=Ra(x,O.mode,z,null),x.return=O,x;if(typeof x.then=="function")return k(O,Yr(x),z);if(x.$$typeof===V)return k(O,Rr(O,x),z);kr(O,x)}return null}function D(O,x,z,L){var lt=x!==null?x.key:null;if(typeof z=="string"&&z!==""||typeof z=="number"||typeof z=="bigint")return lt!==null?null:h(O,x,""+z,L);if(typeof z=="object"&&z!==null){switch(z.$$typeof){case A:return z.key===lt?b(O,x,z,L):null;case U:return z.key===lt?_(O,x,z,L):null;case I:return lt=z._init,z=lt(z._payload),D(O,x,z,L)}if(mt(z)||J(z))return lt!==null?null:H(O,x,z,L,null);if(typeof z.then=="function")return D(O,x,Yr(z),L);if(z.$$typeof===V)return D(O,x,Rr(O,z),L);kr(O,z)}return null}function B(O,x,z,L,lt){if(typeof L=="string"&&L!==""||typeof L=="number"||typeof L=="bigint")return O=O.get(z)||null,h(x,O,""+L,lt);if(typeof L=="object"&&L!==null){switch(L.$$typeof){case A:return O=O.get(L.key===null?z:L.key)||null,b(x,O,L,lt);case U:return O=O.get(L.key===null?z:L.key)||null,_(x,O,L,lt);case I:var bt=L._init;return L=bt(L._payload),B(O,x,z,L,lt)}if(mt(L)||J(L))return O=O.get(z)||null,H(x,O,L,lt,null);if(typeof L.then=="function")return B(O,x,z,Yr(L),lt);if(L.$$typeof===V)return B(O,x,z,Rr(x,L),lt);kr(x,L)}return null}function dt(O,x,z,L){for(var lt=null,bt=null,ut=x,st=x=0,se=null;ut!==null&&st<z.length;st++){ut.index>st?(se=ut,ut=null):se=ut.sibling;var Et=D(O,ut,z[st],L);if(Et===null){ut===null&&(ut=se);break}t&&ut&&Et.alternate===null&&e(O,ut),x=c(Et,x,st),bt===null?lt=Et:bt.sibling=Et,bt=Et,ut=se}if(st===z.length)return n(O,ut),At&&_a(O,st),lt;if(ut===null){for(;st<z.length;st++)ut=k(O,z[st],L),ut!==null&&(x=c(ut,x,st),bt===null?lt=ut:bt.sibling=ut,bt=ut);return At&&_a(O,st),lt}for(ut=l(ut);st<z.length;st++)se=B(ut,O,st,z[st],L),se!==null&&(t&&se.alternate!==null&&ut.delete(se.key===null?st:se.key),x=c(se,x,st),bt===null?lt=se:bt.sibling=se,bt=se);return t&&ut.forEach(function(ba){return e(O,ba)}),At&&_a(O,st),lt}function ct(O,x,z,L){if(z==null)throw Error(o(151));for(var lt=null,bt=null,ut=x,st=x=0,se=null,Et=z.next();ut!==null&&!Et.done;st++,Et=z.next()){ut.index>st?(se=ut,ut=null):se=ut.sibling;var ba=D(O,ut,Et.value,L);if(ba===null){ut===null&&(ut=se);break}t&&ut&&ba.alternate===null&&e(O,ut),x=c(ba,x,st),bt===null?lt=ba:bt.sibling=ba,bt=ba,ut=se}if(Et.done)return n(O,ut),At&&_a(O,st),lt;if(ut===null){for(;!Et.done;st++,Et=z.next())Et=k(O,Et.value,L),Et!==null&&(x=c(Et,x,st),bt===null?lt=Et:bt.sibling=Et,bt=Et);return At&&_a(O,st),lt}for(ut=l(ut);!Et.done;st++,Et=z.next())Et=B(ut,O,st,Et.value,L),Et!==null&&(t&&Et.alternate!==null&&ut.delete(Et.key===null?st:Et.key),x=c(Et,x,st),bt===null?lt=Et:bt.sibling=Et,bt=Et);return t&&ut.forEach(function(ev){return e(O,ev)}),At&&_a(O,st),lt}function Ut(O,x,z,L){if(typeof z=="object"&&z!==null&&z.type===M&&z.key===null&&(z=z.props.children),typeof z=="object"&&z!==null){switch(z.$$typeof){case A:t:{for(var lt=z.key;x!==null;){if(x.key===lt){if(lt=z.type,lt===M){if(x.tag===7){n(O,x.sibling),L=i(x,z.props.children),L.return=O,O=L;break t}}else if(x.elementType===lt||typeof lt=="object"&&lt!==null&&lt.$$typeof===I&&lp(lt)===x.type){n(O,x.sibling),L=i(x,z.props),vi(L,z),L.return=O,O=L;break t}n(O,x);break}else e(O,x);x=x.sibling}z.type===M?(L=Ra(z.props.children,O.mode,L,z.key),L.return=O,O=L):(L=Er(z.type,z.key,z.props,null,O.mode,L),vi(L,z),L.return=O,O=L)}return d(O);case U:t:{for(lt=z.key;x!==null;){if(x.key===lt)if(x.tag===4&&x.stateNode.containerInfo===z.containerInfo&&x.stateNode.implementation===z.implementation){n(O,x.sibling),L=i(x,z.children||[]),L.return=O,O=L;break t}else{n(O,x);break}else e(O,x);x=x.sibling}L=Mo(z,O.mode,L),L.return=O,O=L}return d(O);case I:return lt=z._init,z=lt(z._payload),Ut(O,x,z,L)}if(mt(z))return dt(O,x,z,L);if(J(z)){if(lt=J(z),typeof lt!="function")throw Error(o(150));return z=lt.call(z),ct(O,x,z,L)}if(typeof z.then=="function")return Ut(O,x,Yr(z),L);if(z.$$typeof===V)return Ut(O,x,Rr(O,z),L);kr(O,z)}return typeof z=="string"&&z!==""||typeof z=="number"||typeof z=="bigint"?(z=""+z,x!==null&&x.tag===6?(n(O,x.sibling),L=i(x,z),L.return=O,O=L):(n(O,x),L=Oo(z,O.mode,L),L.return=O,O=L),d(O)):n(O,x)}return function(O,x,z,L){try{gi=0;var lt=Ut(O,x,z,L);return yl=null,lt}catch(ut){if(ut===oi||ut===_r)throw ut;var bt=we(29,ut,null,O.mode);return bt.lanes=L,bt.return=O,bt}finally{}}}var gl=ip(!0),rp=ip(!1),Fe=q(null),yn=null;function la(t){var e=t.alternate;P(ie,ie.current&1),P(Fe,t),yn===null&&(e===null||dl.current!==null||e.memoizedState!==null)&&(yn=t)}function up(t){if(t.tag===22){if(P(ie,ie.current),P(Fe,t),yn===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(yn=t)}}else ia()}function ia(){P(ie,ie.current),P(Fe,Fe.current)}function Hn(t){K(Fe),yn===t&&(yn=null),K(ie)}var ie=q(0);function Gr(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Jc(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function rc(t,e,n,l){e=t.memoizedState,n=n(l,e),n=n==null?e:S({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var uc={enqueueSetState:function(t,e,n){t=t._reactInternals;var l=Le(),i=ea(l);i.payload=e,n!=null&&(i.callback=n),e=na(t,i,l),e!==null&&(qe(e,t,l),si(e,t,l))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var l=Le(),i=ea(l);i.tag=1,i.payload=e,n!=null&&(i.callback=n),e=na(t,i,l),e!==null&&(qe(e,t,l),si(e,t,l))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=Le(),l=ea(n);l.tag=2,e!=null&&(l.callback=e),e=na(t,l,n),e!==null&&(qe(e,t,n),si(e,t,n))}};function op(t,e,n,l,i,c,d){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(l,c,d):e.prototype&&e.prototype.isPureReactComponent?!ti(n,l)||!ti(i,c):!0}function cp(t,e,n,l){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,l),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,l),e.state!==t&&uc.enqueueReplaceState(e,e.state,null)}function Ha(t,e){var n=e;if("ref"in e){n={};for(var l in e)l!=="ref"&&(n[l]=e[l])}if(t=t.defaultProps){n===e&&(n=S({},n));for(var i in t)n[i]===void 0&&(n[i]=t[i])}return n}var Vr=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function sp(t){Vr(t)}function fp(t){console.error(t)}function dp(t){Vr(t)}function Xr(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(l){setTimeout(function(){throw l})}}function pp(t,e,n){try{var l=t.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function oc(t,e,n){return n=ea(n),n.tag=3,n.payload={element:null},n.callback=function(){Xr(t,e)},n}function hp(t){return t=ea(t),t.tag=3,t}function mp(t,e,n,l){var i=n.type.getDerivedStateFromError;if(typeof i=="function"){var c=l.value;t.payload=function(){return i(c)},t.callback=function(){pp(e,n,l)}}var d=n.stateNode;d!==null&&typeof d.componentDidCatch=="function"&&(t.callback=function(){pp(e,n,l),typeof i!="function"&&(fa===null?fa=new Set([this]):fa.add(this));var h=l.stack;this.componentDidCatch(l.value,{componentStack:h!==null?h:""})})}function eg(t,e,n,l,i){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(e=n.alternate,e!==null&&ii(e,n,i,!0),n=Fe.current,n!==null){switch(n.tag){case 13:return yn===null?Bc():n.alternate===null&&Kt===0&&(Kt=3),n.flags&=-257,n.flags|=65536,n.lanes=i,l===Ho?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([l]):e.add(l),Uc(t,l,i)),!1;case 22:return n.flags|=65536,l===Ho?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([l]):n.add(l)),Uc(t,l,i)),!1}throw Error(o(435,n.tag))}return Uc(t,l,i),Bc(),!1}if(At)return e=Fe.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=i,l!==_o&&(t=Error(o(422),{cause:l}),li(Ke(t,n)))):(l!==_o&&(e=Error(o(423),{cause:l}),li(Ke(e,n))),t=t.current.alternate,t.flags|=65536,i&=-i,t.lanes|=i,l=Ke(l,n),i=oc(t.stateNode,l,i),qo(t,i),Kt!==4&&(Kt=2)),!1;var c=Error(o(520),{cause:l});if(c=Ke(c,n),Ai===null?Ai=[c]:Ai.push(c),Kt!==4&&(Kt=2),e===null)return!0;l=Ke(l,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=i&-i,n.lanes|=t,t=oc(n.stateNode,l,t),qo(n,t),!1;case 1:if(e=n.type,c=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(fa===null||!fa.has(c))))return n.flags|=65536,i&=-i,n.lanes|=i,i=hp(i),mp(i,t,n,l),qo(n,i),!1}n=n.return}while(n!==null);return!1}var yp=Error(o(461)),oe=!1;function fe(t,e,n,l){e.child=t===null?rp(e,null,n,l):gl(e,t.child,n,l)}function gp(t,e,n,l,i){n=n.render;var c=e.ref;if("ref"in l){var d={};for(var h in l)h!=="ref"&&(d[h]=l[h])}else d=l;return Ua(e),l=Xo(t,e,n,d,c,i),h=Qo(),t!==null&&!oe?(Zo(t,e,i),jn(t,e,i)):(At&&h&&Ro(e),e.flags|=1,fe(t,e,l,i),e.child)}function vp(t,e,n,l,i){if(t===null){var c=n.type;return typeof c=="function"&&!Ao(c)&&c.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=c,bp(t,e,c,l,i)):(t=Er(n.type,null,l,e,e.mode,i),t.ref=e.ref,t.return=e,e.child=t)}if(c=t.child,!yc(t,i)){var d=c.memoizedProps;if(n=n.compare,n=n!==null?n:ti,n(d,l)&&t.ref===e.ref)return jn(t,e,i)}return e.flags|=1,t=Dn(c,l),t.ref=e.ref,t.return=e,e.child=t}function bp(t,e,n,l,i){if(t!==null){var c=t.memoizedProps;if(ti(c,l)&&t.ref===e.ref)if(oe=!1,e.pendingProps=l=c,yc(t,i))(t.flags&131072)!==0&&(oe=!0);else return e.lanes=t.lanes,jn(t,e,i)}return cc(t,e,n,l,i)}function Sp(t,e,n){var l=e.pendingProps,i=l.children,c=t!==null?t.memoizedState:null;if(l.mode==="hidden"){if((e.flags&128)!==0){if(l=c!==null?c.baseLanes|n:n,t!==null){for(i=e.child=t.child,c=0;i!==null;)c=c|i.lanes|i.childLanes,i=i.sibling;e.childLanes=c&~l}else e.childLanes=0,e.child=null;return Cp(t,e,l,n)}if((n&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&zr(e,c!==null?c.cachePool:null),c!==null?bd(e,c):ko(),up(e);else return e.lanes=e.childLanes=536870912,Cp(t,e,c!==null?c.baseLanes|n:n,n)}else c!==null?(zr(e,c.cachePool),bd(e,c),ia(),e.memoizedState=null):(t!==null&&zr(e,null),ko(),ia());return fe(t,e,i,n),e.child}function Cp(t,e,n,l){var i=$o();return i=i===null?null:{parent:le._currentValue,pool:i},e.memoizedState={baseLanes:n,cachePool:i},t!==null&&zr(e,null),ko(),up(e),t!==null&&ii(t,e,l,!0),null}function Qr(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(o(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function cc(t,e,n,l,i){return Ua(e),n=Xo(t,e,n,l,void 0,i),l=Qo(),t!==null&&!oe?(Zo(t,e,i),jn(t,e,i)):(At&&l&&Ro(e),e.flags|=1,fe(t,e,n,i),e.child)}function Tp(t,e,n,l,i,c){return Ua(e),e.updateQueue=null,n=Cd(e,l,n,i),Sd(t),l=Qo(),t!==null&&!oe?(Zo(t,e,c),jn(t,e,c)):(At&&l&&Ro(e),e.flags|=1,fe(t,e,n,c),e.child)}function xp(t,e,n,l,i){if(Ua(e),e.stateNode===null){var c=ul,d=n.contextType;typeof d=="object"&&d!==null&&(c=Ce(d)),c=new n(l,c),e.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=uc,e.stateNode=c,c._reactInternals=e,c=e.stateNode,c.props=l,c.state=e.memoizedState,c.refs={},jo(e),d=n.contextType,c.context=typeof d=="object"&&d!==null?Ce(d):ul,c.state=e.memoizedState,d=n.getDerivedStateFromProps,typeof d=="function"&&(rc(e,n,d,l),c.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(d=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),d!==c.state&&uc.enqueueReplaceState(c,c.state,null),di(e,l,c,i),fi(),c.state=e.memoizedState),typeof c.componentDidMount=="function"&&(e.flags|=4194308),l=!0}else if(t===null){c=e.stateNode;var h=e.memoizedProps,b=Ha(n,h);c.props=b;var _=c.context,H=n.contextType;d=ul,typeof H=="object"&&H!==null&&(d=Ce(H));var k=n.getDerivedStateFromProps;H=typeof k=="function"||typeof c.getSnapshotBeforeUpdate=="function",h=e.pendingProps!==h,H||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(h||_!==d)&&cp(e,c,l,d),ta=!1;var D=e.memoizedState;c.state=D,di(e,l,c,i),fi(),_=e.memoizedState,h||D!==_||ta?(typeof k=="function"&&(rc(e,n,k,l),_=e.memoizedState),(b=ta||op(e,n,b,l,D,_,d))?(H||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(e.flags|=4194308)):(typeof c.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=l,e.memoizedState=_),c.props=l,c.state=_,c.context=d,l=b):(typeof c.componentDidMount=="function"&&(e.flags|=4194308),l=!1)}else{c=e.stateNode,Lo(t,e),d=e.memoizedProps,H=Ha(n,d),c.props=H,k=e.pendingProps,D=c.context,_=n.contextType,b=ul,typeof _=="object"&&_!==null&&(b=Ce(_)),h=n.getDerivedStateFromProps,(_=typeof h=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(d!==k||D!==b)&&cp(e,c,l,b),ta=!1,D=e.memoizedState,c.state=D,di(e,l,c,i),fi();var B=e.memoizedState;d!==k||D!==B||ta||t!==null&&t.dependencies!==null&&Mr(t.dependencies)?(typeof h=="function"&&(rc(e,n,h,l),B=e.memoizedState),(H=ta||op(e,n,H,l,D,B,b)||t!==null&&t.dependencies!==null&&Mr(t.dependencies))?(_||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(l,B,b),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(l,B,b)),typeof c.componentDidUpdate=="function"&&(e.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof c.componentDidUpdate!="function"||d===t.memoizedProps&&D===t.memoizedState||(e.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||d===t.memoizedProps&&D===t.memoizedState||(e.flags|=1024),e.memoizedProps=l,e.memoizedState=B),c.props=l,c.state=B,c.context=b,l=H):(typeof c.componentDidUpdate!="function"||d===t.memoizedProps&&D===t.memoizedState||(e.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||d===t.memoizedProps&&D===t.memoizedState||(e.flags|=1024),l=!1)}return c=l,Qr(t,e),l=(e.flags&128)!==0,c||l?(c=e.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:c.render(),e.flags|=1,t!==null&&l?(e.child=gl(e,t.child,null,i),e.child=gl(e,null,n,i)):fe(t,e,n,i),e.memoizedState=c.state,t=e.child):t=jn(t,e,i),t}function Ep(t,e,n,l){return ai(),e.flags|=256,fe(t,e,n,l),e.child}var sc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function fc(t){return{baseLanes:t,cachePool:fd()}}function dc(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=Ie),t}function Ap(t,e,n){var l=e.pendingProps,i=!1,c=(e.flags&128)!==0,d;if((d=c)||(d=t!==null&&t.memoizedState===null?!1:(ie.current&2)!==0),d&&(i=!0,e.flags&=-129),d=(e.flags&32)!==0,e.flags&=-33,t===null){if(At){if(i?la(e):ia(),At){var h=Zt,b;if(b=h){t:{for(b=h,h=mn;b.nodeType!==8;){if(!h){h=null;break t}if(b=fn(b.nextSibling),b===null){h=null;break t}}h=b}h!==null?(e.memoizedState={dehydrated:h,treeContext:za!==null?{id:Bn,overflow:Nn}:null,retryLane:536870912,hydrationErrors:null},b=we(18,null,null,0),b.stateNode=h,b.return=e,e.child=b,Ae=e,Zt=null,b=!0):b=!1}b||Ba(e)}if(h=e.memoizedState,h!==null&&(h=h.dehydrated,h!==null))return Jc(h)?e.lanes=32:e.lanes=536870912,null;Hn(e)}return h=l.children,l=l.fallback,i?(ia(),i=e.mode,h=Zr({mode:"hidden",children:h},i),l=Ra(l,i,n,null),h.return=e,l.return=e,h.sibling=l,e.child=h,i=e.child,i.memoizedState=fc(n),i.childLanes=dc(t,d,n),e.memoizedState=sc,l):(la(e),pc(e,h))}if(b=t.memoizedState,b!==null&&(h=b.dehydrated,h!==null)){if(c)e.flags&256?(la(e),e.flags&=-257,e=hc(t,e,n)):e.memoizedState!==null?(ia(),e.child=t.child,e.flags|=128,e=null):(ia(),i=l.fallback,h=e.mode,l=Zr({mode:"visible",children:l.children},h),i=Ra(i,h,n,null),i.flags|=2,l.return=e,i.return=e,l.sibling=i,e.child=l,gl(e,t.child,null,n),l=e.child,l.memoizedState=fc(n),l.childLanes=dc(t,d,n),e.memoizedState=sc,e=i);else if(la(e),Jc(h)){if(d=h.nextSibling&&h.nextSibling.dataset,d)var _=d.dgst;d=_,l=Error(o(419)),l.stack="",l.digest=d,li({value:l,source:null,stack:null}),e=hc(t,e,n)}else if(oe||ii(t,e,n,!1),d=(n&t.childLanes)!==0,oe||d){if(d=qt,d!==null&&(l=n&-n,l=(l&42)!==0?1:Ju(l),l=(l&(d.suspendedLanes|n))!==0?0:l,l!==0&&l!==b.retryLane))throw b.retryLane=l,rl(t,l),qe(d,t,l),yp;h.data==="$?"||Bc(),e=hc(t,e,n)}else h.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=b.treeContext,Zt=fn(h.nextSibling),Ae=e,At=!0,Da=null,mn=!1,t!==null&&(We[Pe++]=Bn,We[Pe++]=Nn,We[Pe++]=za,Bn=t.id,Nn=t.overflow,za=e),e=pc(e,l.children),e.flags|=4096);return e}return i?(ia(),i=l.fallback,h=e.mode,b=t.child,_=b.sibling,l=Dn(b,{mode:"hidden",children:l.children}),l.subtreeFlags=b.subtreeFlags&65011712,_!==null?i=Dn(_,i):(i=Ra(i,h,n,null),i.flags|=2),i.return=e,l.return=e,l.sibling=i,e.child=l,l=i,i=e.child,h=t.child.memoizedState,h===null?h=fc(n):(b=h.cachePool,b!==null?(_=le._currentValue,b=b.parent!==_?{parent:_,pool:_}:b):b=fd(),h={baseLanes:h.baseLanes|n,cachePool:b}),i.memoizedState=h,i.childLanes=dc(t,d,n),e.memoizedState=sc,l):(la(e),n=t.child,t=n.sibling,n=Dn(n,{mode:"visible",children:l.children}),n.return=e,n.sibling=null,t!==null&&(d=e.deletions,d===null?(e.deletions=[t],e.flags|=16):d.push(t)),e.child=n,e.memoizedState=null,n)}function pc(t,e){return e=Zr({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Zr(t,e){return t=we(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function hc(t,e,n){return gl(e,t.child,null,n),t=pc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Op(t,e,n){t.lanes|=e;var l=t.alternate;l!==null&&(l.lanes|=e),Bo(t.return,e,n)}function mc(t,e,n,l,i){var c=t.memoizedState;c===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:i}:(c.isBackwards=e,c.rendering=null,c.renderingStartTime=0,c.last=l,c.tail=n,c.tailMode=i)}function Mp(t,e,n){var l=e.pendingProps,i=l.revealOrder,c=l.tail;if(fe(t,e,l.children,n),l=ie.current,(l&2)!==0)l=l&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Op(t,n,e);else if(t.tag===19)Op(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}l&=1}switch(P(ie,l),i){case"forwards":for(n=e.child,i=null;n!==null;)t=n.alternate,t!==null&&Gr(t)===null&&(i=n),n=n.sibling;n=i,n===null?(i=e.child,e.child=null):(i=n.sibling,n.sibling=null),mc(e,!1,i,n,c);break;case"backwards":for(n=null,i=e.child,e.child=null;i!==null;){if(t=i.alternate,t!==null&&Gr(t)===null){e.child=i;break}t=i.sibling,i.sibling=n,n=i,i=t}mc(e,!0,n,null,c);break;case"together":mc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function jn(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),sa|=e.lanes,(n&e.childLanes)===0)if(t!==null){if(ii(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(o(153));if(e.child!==null){for(t=e.child,n=Dn(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=Dn(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function yc(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Mr(t)))}function ng(t,e,n){switch(e.tag){case 3:zt(e,e.stateNode.containerInfo),In(e,le,t.memoizedState.cache),ai();break;case 27:case 5:xa(e);break;case 4:zt(e,e.stateNode.containerInfo);break;case 10:In(e,e.type,e.memoizedProps.value);break;case 13:var l=e.memoizedState;if(l!==null)return l.dehydrated!==null?(la(e),e.flags|=128,null):(n&e.child.childLanes)!==0?Ap(t,e,n):(la(e),t=jn(t,e,n),t!==null?t.sibling:null);la(e);break;case 19:var i=(t.flags&128)!==0;if(l=(n&e.childLanes)!==0,l||(ii(t,e,n,!1),l=(n&e.childLanes)!==0),i){if(l)return Mp(t,e,n);e.flags|=128}if(i=e.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),P(ie,ie.current),l)break;return null;case 22:case 23:return e.lanes=0,Sp(t,e,n);case 24:In(e,le,t.memoizedState.cache)}return jn(t,e,n)}function Rp(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)oe=!0;else{if(!yc(t,n)&&(e.flags&128)===0)return oe=!1,ng(t,e,n);oe=(t.flags&131072)!==0}else oe=!1,At&&(e.flags&1048576)!==0&&ld(e,Or,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var l=e.elementType,i=l._init;if(l=i(l._payload),e.type=l,typeof l=="function")Ao(l)?(t=Ha(l,t),e.tag=1,e=xp(null,e,l,t,n)):(e.tag=0,e=cc(null,e,l,t,n));else{if(l!=null){if(i=l.$$typeof,i===j){e.tag=11,e=gp(null,e,l,t,n);break t}else if(i===F){e.tag=14,e=vp(null,e,l,t,n);break t}}throw e=pt(l)||l,Error(o(306,e,""))}}return e;case 0:return cc(t,e,e.type,e.pendingProps,n);case 1:return l=e.type,i=Ha(l,e.pendingProps),xp(t,e,l,i,n);case 3:t:{if(zt(e,e.stateNode.containerInfo),t===null)throw Error(o(387));l=e.pendingProps;var c=e.memoizedState;i=c.element,Lo(t,e),di(e,l,null,n);var d=e.memoizedState;if(l=d.cache,In(e,le,l),l!==c.cache&&No(e,[le],n,!0),fi(),l=d.element,c.isDehydrated)if(c={element:l,isDehydrated:!1,cache:d.cache},e.updateQueue.baseState=c,e.memoizedState=c,e.flags&256){e=Ep(t,e,l,n);break t}else if(l!==i){i=Ke(Error(o(424)),e),li(i),e=Ep(t,e,l,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Zt=fn(t.firstChild),Ae=e,At=!0,Da=null,mn=!0,n=rp(e,null,l,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(ai(),l===i){e=jn(t,e,n);break t}fe(t,e,l,n)}e=e.child}return e;case 26:return Qr(t,e),t===null?(n=Bh(e.type,null,e.pendingProps,null))?e.memoizedState=n:At||(n=e.type,t=e.pendingProps,l=uu(ft.current).createElement(n),l[Se]=e,l[Oe]=t,pe(l,n,t),ue(l),e.stateNode=l):e.memoizedState=Bh(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return xa(e),t===null&&At&&(l=e.stateNode=zh(e.type,e.pendingProps,ft.current),Ae=e,mn=!0,i=Zt,ha(e.type)?(Wc=i,Zt=fn(l.firstChild)):Zt=i),fe(t,e,e.pendingProps.children,n),Qr(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&At&&((i=l=Zt)&&(l=_g(l,e.type,e.pendingProps,mn),l!==null?(e.stateNode=l,Ae=e,Zt=fn(l.firstChild),mn=!1,i=!0):i=!1),i||Ba(e)),xa(e),i=e.type,c=e.pendingProps,d=t!==null?t.memoizedProps:null,l=c.children,Qc(i,c)?l=null:d!==null&&Qc(i,d)&&(e.flags|=32),e.memoizedState!==null&&(i=Xo(t,e,Ky,null,null,n),Ui._currentValue=i),Qr(t,e),fe(t,e,l,n),e.child;case 6:return t===null&&At&&((t=n=Zt)&&(n=Dg(n,e.pendingProps,mn),n!==null?(e.stateNode=n,Ae=e,Zt=null,t=!0):t=!1),t||Ba(e)),null;case 13:return Ap(t,e,n);case 4:return zt(e,e.stateNode.containerInfo),l=e.pendingProps,t===null?e.child=gl(e,null,l,n):fe(t,e,l,n),e.child;case 11:return gp(t,e,e.type,e.pendingProps,n);case 7:return fe(t,e,e.pendingProps,n),e.child;case 8:return fe(t,e,e.pendingProps.children,n),e.child;case 12:return fe(t,e,e.pendingProps.children,n),e.child;case 10:return l=e.pendingProps,In(e,e.type,l.value),fe(t,e,l.children,n),e.child;case 9:return i=e.type._context,l=e.pendingProps.children,Ua(e),i=Ce(i),l=l(i),e.flags|=1,fe(t,e,l,n),e.child;case 14:return vp(t,e,e.type,e.pendingProps,n);case 15:return bp(t,e,e.type,e.pendingProps,n);case 19:return Mp(t,e,n);case 31:return l=e.pendingProps,n=e.mode,l={mode:l.mode,children:l.children},t===null?(n=Zr(l,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=Dn(t.child,l),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return Sp(t,e,n);case 24:return Ua(e),l=Ce(le),t===null?(i=$o(),i===null&&(i=qt,c=Uo(),i.pooledCache=c,c.refCount++,c!==null&&(i.pooledCacheLanes|=n),i=c),e.memoizedState={parent:l,cache:i},jo(e),In(e,le,i)):((t.lanes&n)!==0&&(Lo(t,e),di(e,null,null,n),fi()),i=t.memoizedState,c=e.memoizedState,i.parent!==l?(i={parent:l,cache:l},e.memoizedState=i,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=i),In(e,le,l)):(l=c.cache,In(e,le,l),l!==i.cache&&No(e,[le],n,!0))),fe(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(o(156,e.tag))}function Ln(t){t.flags|=4}function zp(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Hh(e)){if(e=Fe.current,e!==null&&((xt&4194048)===xt?yn!==null:(xt&62914560)!==xt&&(xt&536870912)===0||e!==yn))throw ci=Ho,dd;t.flags|=8192}}function Kr(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?uf():536870912,t.lanes|=e,Cl|=e)}function bi(t,e){if(!At)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:l.sibling=null}}function Xt(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,l=0;if(e)for(var i=t.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags&65011712,l|=i.flags&65011712,i.return=t,i=i.sibling;else for(i=t.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags,l|=i.flags,i.return=t,i=i.sibling;return t.subtreeFlags|=l,t.childLanes=n,e}function ag(t,e,n){var l=e.pendingProps;switch(zo(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Xt(e),null;case 1:return Xt(e),null;case 3:return n=e.stateNode,l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),wn(le),Ne(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(ni(e)?Ln(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,ud())),Xt(e),null;case 26:return n=e.memoizedState,t===null?(Ln(e),n!==null?(Xt(e),zp(e,n)):(Xt(e),e.flags&=-16777217)):n?n!==t.memoizedState?(Ln(e),Xt(e),zp(e,n)):(Xt(e),e.flags&=-16777217):(t.memoizedProps!==l&&Ln(e),Xt(e),e.flags&=-16777217),null;case 27:Zn(e),n=ft.current;var i=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==l&&Ln(e);else{if(!l){if(e.stateNode===null)throw Error(o(166));return Xt(e),null}t=ot.current,ni(e)?id(e):(t=zh(i,l,n),e.stateNode=t,Ln(e))}return Xt(e),null;case 5:if(Zn(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==l&&Ln(e);else{if(!l){if(e.stateNode===null)throw Error(o(166));return Xt(e),null}if(t=ot.current,ni(e))id(e);else{switch(i=uu(ft.current),t){case 1:t=i.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=i.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=i.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof l.is=="string"?i.createElement("select",{is:l.is}):i.createElement("select"),l.multiple?t.multiple=!0:l.size&&(t.size=l.size);break;default:t=typeof l.is=="string"?i.createElement(n,{is:l.is}):i.createElement(n)}}t[Se]=e,t[Oe]=l;t:for(i=e.child;i!==null;){if(i.tag===5||i.tag===6)t.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===e)break t;for(;i.sibling===null;){if(i.return===null||i.return===e)break t;i=i.return}i.sibling.return=i.return,i=i.sibling}e.stateNode=t;t:switch(pe(t,n,l),n){case"button":case"input":case"select":case"textarea":t=!!l.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&Ln(e)}}return Xt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==l&&Ln(e);else{if(typeof l!="string"&&e.stateNode===null)throw Error(o(166));if(t=ft.current,ni(e)){if(t=e.stateNode,n=e.memoizedProps,l=null,i=Ae,i!==null)switch(i.tag){case 27:case 5:l=i.memoizedProps}t[Se]=e,t=!!(t.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||Th(t.nodeValue,n)),t||Ba(e)}else t=uu(t).createTextNode(l),t[Se]=e,e.stateNode=t}return Xt(e),null;case 13:if(l=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(i=ni(e),l!==null&&l.dehydrated!==null){if(t===null){if(!i)throw Error(o(318));if(i=e.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(o(317));i[Se]=e}else ai(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Xt(e),i=!1}else i=ud(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=i),i=!0;if(!i)return e.flags&256?(Hn(e),e):(Hn(e),null)}if(Hn(e),(e.flags&128)!==0)return e.lanes=n,e;if(n=l!==null,t=t!==null&&t.memoizedState!==null,n){l=e.child,i=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(i=l.alternate.memoizedState.cachePool.pool);var c=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(c=l.memoizedState.cachePool.pool),c!==i&&(l.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),Kr(e,e.updateQueue),Xt(e),null;case 4:return Ne(),t===null&&Yc(e.stateNode.containerInfo),Xt(e),null;case 10:return wn(e.type),Xt(e),null;case 19:if(K(ie),i=e.memoizedState,i===null)return Xt(e),null;if(l=(e.flags&128)!==0,c=i.rendering,c===null)if(l)bi(i,!1);else{if(Kt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(c=Gr(t),c!==null){for(e.flags|=128,bi(i,!1),t=c.updateQueue,e.updateQueue=t,Kr(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)ad(n,t),n=n.sibling;return P(ie,ie.current&1|2),e.child}t=t.sibling}i.tail!==null&&re()>Pr&&(e.flags|=128,l=!0,bi(i,!1),e.lanes=4194304)}else{if(!l)if(t=Gr(c),t!==null){if(e.flags|=128,l=!0,t=t.updateQueue,e.updateQueue=t,Kr(e,t),bi(i,!0),i.tail===null&&i.tailMode==="hidden"&&!c.alternate&&!At)return Xt(e),null}else 2*re()-i.renderingStartTime>Pr&&n!==536870912&&(e.flags|=128,l=!0,bi(i,!1),e.lanes=4194304);i.isBackwards?(c.sibling=e.child,e.child=c):(t=i.last,t!==null?t.sibling=c:e.child=c,i.last=c)}return i.tail!==null?(e=i.tail,i.rendering=e,i.tail=e.sibling,i.renderingStartTime=re(),e.sibling=null,t=ie.current,P(ie,l?t&1|2:t&1),e):(Xt(e),null);case 22:case 23:return Hn(e),Go(),l=e.memoizedState!==null,t!==null?t.memoizedState!==null!==l&&(e.flags|=8192):l&&(e.flags|=8192),l?(n&536870912)!==0&&(e.flags&128)===0&&(Xt(e),e.subtreeFlags&6&&(e.flags|=8192)):Xt(e),n=e.updateQueue,n!==null&&Kr(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),l=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),l!==n&&(e.flags|=2048),t!==null&&K(wa),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),wn(le),Xt(e),null;case 25:return null;case 30:return null}throw Error(o(156,e.tag))}function lg(t,e){switch(zo(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return wn(le),Ne(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return Zn(e),null;case 13:if(Hn(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(o(340));ai()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return K(ie),null;case 4:return Ne(),null;case 10:return wn(e.type),null;case 22:case 23:return Hn(e),Go(),t!==null&&K(wa),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return wn(le),null;case 25:return null;default:return null}}function _p(t,e){switch(zo(e),e.tag){case 3:wn(le),Ne();break;case 26:case 27:case 5:Zn(e);break;case 4:Ne();break;case 13:Hn(e);break;case 19:K(ie);break;case 10:wn(e.type);break;case 22:case 23:Hn(e),Go(),t!==null&&K(wa);break;case 24:wn(le)}}function Si(t,e){try{var n=e.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var i=l.next;n=i;do{if((n.tag&t)===t){l=void 0;var c=n.create,d=n.inst;l=c(),d.destroy=l}n=n.next}while(n!==i)}}catch(h){$t(e,e.return,h)}}function ra(t,e,n){try{var l=e.updateQueue,i=l!==null?l.lastEffect:null;if(i!==null){var c=i.next;l=c;do{if((l.tag&t)===t){var d=l.inst,h=d.destroy;if(h!==void 0){d.destroy=void 0,i=e;var b=n,_=h;try{_()}catch(H){$t(i,b,H)}}}l=l.next}while(l!==c)}}catch(H){$t(e,e.return,H)}}function Dp(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{vd(e,n)}catch(l){$t(t,t.return,l)}}}function Bp(t,e,n){n.props=Ha(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(l){$t(t,e,l)}}function Ci(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var l=t.stateNode;break;case 30:l=t.stateNode;break;default:l=t.stateNode}typeof n=="function"?t.refCleanup=n(l):n.current=l}}catch(i){$t(t,e,i)}}function gn(t,e){var n=t.ref,l=t.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(i){$t(t,e,i)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(i){$t(t,e,i)}else n.current=null}function Np(t){var e=t.type,n=t.memoizedProps,l=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break t;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(i){$t(t,t.return,i)}}function gc(t,e,n){try{var l=t.stateNode;Ag(l,t.type,n,e),l[Oe]=e}catch(i){$t(t,t.return,i)}}function Up(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&ha(t.type)||t.tag===4}function vc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Up(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&ha(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function bc(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=ru));else if(l!==4&&(l===27&&ha(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(bc(t,e,n),t=t.sibling;t!==null;)bc(t,e,n),t=t.sibling}function Jr(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(l!==4&&(l===27&&ha(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(Jr(t,e,n),t=t.sibling;t!==null;)Jr(t,e,n),t=t.sibling}function wp(t){var e=t.stateNode,n=t.memoizedProps;try{for(var l=t.type,i=e.attributes;i.length;)e.removeAttributeNode(i[0]);pe(e,l,n),e[Se]=t,e[Oe]=n}catch(c){$t(t,t.return,c)}}var qn=!1,Ft=!1,Sc=!1,$p=typeof WeakSet=="function"?WeakSet:Set,ce=null;function ig(t,e){if(t=t.containerInfo,Vc=pu,t=Zf(t),vo(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var i=l.anchorOffset,c=l.focusNode;l=l.focusOffset;try{n.nodeType,c.nodeType}catch{n=null;break t}var d=0,h=-1,b=-1,_=0,H=0,k=t,D=null;e:for(;;){for(var B;k!==n||i!==0&&k.nodeType!==3||(h=d+i),k!==c||l!==0&&k.nodeType!==3||(b=d+l),k.nodeType===3&&(d+=k.nodeValue.length),(B=k.firstChild)!==null;)D=k,k=B;for(;;){if(k===t)break e;if(D===n&&++_===i&&(h=d),D===c&&++H===l&&(b=d),(B=k.nextSibling)!==null)break;k=D,D=k.parentNode}k=B}n=h===-1||b===-1?null:{start:h,end:b}}else n=null}n=n||{start:0,end:0}}else n=null;for(Xc={focusedElem:t,selectionRange:n},pu=!1,ce=e;ce!==null;)if(e=ce,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,ce=t;else for(;ce!==null;){switch(e=ce,c=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&c!==null){t=void 0,n=e,i=c.memoizedProps,c=c.memoizedState,l=n.stateNode;try{var dt=Ha(n.type,i,n.elementType===n.type);t=l.getSnapshotBeforeUpdate(dt,c),l.__reactInternalSnapshotBeforeUpdate=t}catch(ct){$t(n,n.return,ct)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)Kc(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":Kc(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(o(163))}if(t=e.sibling,t!==null){t.return=e.return,ce=t;break}ce=e.return}}function Hp(t,e,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:ua(t,n),l&4&&Si(5,n);break;case 1:if(ua(t,n),l&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(d){$t(n,n.return,d)}else{var i=Ha(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(i,e,t.__reactInternalSnapshotBeforeUpdate)}catch(d){$t(n,n.return,d)}}l&64&&Dp(n),l&512&&Ci(n,n.return);break;case 3:if(ua(t,n),l&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{vd(t,e)}catch(d){$t(n,n.return,d)}}break;case 27:e===null&&l&4&&wp(n);case 26:case 5:ua(t,n),e===null&&l&4&&Np(n),l&512&&Ci(n,n.return);break;case 12:ua(t,n);break;case 13:ua(t,n),l&4&&qp(t,n),l&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=hg.bind(null,n),Bg(t,n))));break;case 22:if(l=n.memoizedState!==null||qn,!l){e=e!==null&&e.memoizedState!==null||Ft,i=qn;var c=Ft;qn=l,(Ft=e)&&!c?oa(t,n,(n.subtreeFlags&8772)!==0):ua(t,n),qn=i,Ft=c}break;case 30:break;default:ua(t,n)}}function jp(t){var e=t.alternate;e!==null&&(t.alternate=null,jp(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Fu(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Yt=null,ze=!1;function Yn(t,e,n){for(n=n.child;n!==null;)Lp(t,e,n),n=n.sibling}function Lp(t,e,n){if(ye&&typeof ye.onCommitFiberUnmount=="function")try{ye.onCommitFiberUnmount(Wn,n)}catch{}switch(n.tag){case 26:Ft||gn(n,e),Yn(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Ft||gn(n,e);var l=Yt,i=ze;ha(n.type)&&(Yt=n.stateNode,ze=!1),Yn(t,e,n),_i(n.stateNode),Yt=l,ze=i;break;case 5:Ft||gn(n,e);case 6:if(l=Yt,i=ze,Yt=null,Yn(t,e,n),Yt=l,ze=i,Yt!==null)if(ze)try{(Yt.nodeType===9?Yt.body:Yt.nodeName==="HTML"?Yt.ownerDocument.body:Yt).removeChild(n.stateNode)}catch(c){$t(n,e,c)}else try{Yt.removeChild(n.stateNode)}catch(c){$t(n,e,c)}break;case 18:Yt!==null&&(ze?(t=Yt,Mh(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),ji(t)):Mh(Yt,n.stateNode));break;case 4:l=Yt,i=ze,Yt=n.stateNode.containerInfo,ze=!0,Yn(t,e,n),Yt=l,ze=i;break;case 0:case 11:case 14:case 15:Ft||ra(2,n,e),Ft||ra(4,n,e),Yn(t,e,n);break;case 1:Ft||(gn(n,e),l=n.stateNode,typeof l.componentWillUnmount=="function"&&Bp(n,e,l)),Yn(t,e,n);break;case 21:Yn(t,e,n);break;case 22:Ft=(l=Ft)||n.memoizedState!==null,Yn(t,e,n),Ft=l;break;default:Yn(t,e,n)}}function qp(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{ji(t)}catch(n){$t(e,e.return,n)}}function rg(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new $p),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new $p),e;default:throw Error(o(435,t.tag))}}function Cc(t,e){var n=rg(t);e.forEach(function(l){var i=mg.bind(null,t,l);n.has(l)||(n.add(l),l.then(i,i))})}function $e(t,e){var n=e.deletions;if(n!==null)for(var l=0;l<n.length;l++){var i=n[l],c=t,d=e,h=d;t:for(;h!==null;){switch(h.tag){case 27:if(ha(h.type)){Yt=h.stateNode,ze=!1;break t}break;case 5:Yt=h.stateNode,ze=!1;break t;case 3:case 4:Yt=h.stateNode.containerInfo,ze=!0;break t}h=h.return}if(Yt===null)throw Error(o(160));Lp(c,d,i),Yt=null,ze=!1,c=i.alternate,c!==null&&(c.return=null),i.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Yp(e,t),e=e.sibling}var sn=null;function Yp(t,e){var n=t.alternate,l=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:$e(e,t),He(t),l&4&&(ra(3,t,t.return),Si(3,t),ra(5,t,t.return));break;case 1:$e(e,t),He(t),l&512&&(Ft||n===null||gn(n,n.return)),l&64&&qn&&(t=t.updateQueue,t!==null&&(l=t.callbacks,l!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var i=sn;if($e(e,t),He(t),l&512&&(Ft||n===null||gn(n,n.return)),l&4){var c=n!==null?n.memoizedState:null;if(l=t.memoizedState,n===null)if(l===null)if(t.stateNode===null){t:{l=t.type,n=t.memoizedProps,i=i.ownerDocument||i;e:switch(l){case"title":c=i.getElementsByTagName("title")[0],(!c||c[Xl]||c[Se]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=i.createElement(l),i.head.insertBefore(c,i.querySelector("head > title"))),pe(c,l,n),c[Se]=t,ue(c),l=c;break t;case"link":var d=wh("link","href",i).get(l+(n.href||""));if(d){for(var h=0;h<d.length;h++)if(c=d[h],c.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&c.getAttribute("rel")===(n.rel==null?null:n.rel)&&c.getAttribute("title")===(n.title==null?null:n.title)&&c.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){d.splice(h,1);break e}}c=i.createElement(l),pe(c,l,n),i.head.appendChild(c);break;case"meta":if(d=wh("meta","content",i).get(l+(n.content||""))){for(h=0;h<d.length;h++)if(c=d[h],c.getAttribute("content")===(n.content==null?null:""+n.content)&&c.getAttribute("name")===(n.name==null?null:n.name)&&c.getAttribute("property")===(n.property==null?null:n.property)&&c.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&c.getAttribute("charset")===(n.charSet==null?null:n.charSet)){d.splice(h,1);break e}}c=i.createElement(l),pe(c,l,n),i.head.appendChild(c);break;default:throw Error(o(468,l))}c[Se]=t,ue(c),l=c}t.stateNode=l}else $h(i,t.type,t.stateNode);else t.stateNode=Uh(i,l,t.memoizedProps);else c!==l?(c===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):c.count--,l===null?$h(i,t.type,t.stateNode):Uh(i,l,t.memoizedProps)):l===null&&t.stateNode!==null&&gc(t,t.memoizedProps,n.memoizedProps)}break;case 27:$e(e,t),He(t),l&512&&(Ft||n===null||gn(n,n.return)),n!==null&&l&4&&gc(t,t.memoizedProps,n.memoizedProps);break;case 5:if($e(e,t),He(t),l&512&&(Ft||n===null||gn(n,n.return)),t.flags&32){i=t.stateNode;try{Ia(i,"")}catch(B){$t(t,t.return,B)}}l&4&&t.stateNode!=null&&(i=t.memoizedProps,gc(t,i,n!==null?n.memoizedProps:i)),l&1024&&(Sc=!0);break;case 6:if($e(e,t),He(t),l&4){if(t.stateNode===null)throw Error(o(162));l=t.memoizedProps,n=t.stateNode;try{n.nodeValue=l}catch(B){$t(t,t.return,B)}}break;case 3:if(su=null,i=sn,sn=ou(e.containerInfo),$e(e,t),sn=i,He(t),l&4&&n!==null&&n.memoizedState.isDehydrated)try{ji(e.containerInfo)}catch(B){$t(t,t.return,B)}Sc&&(Sc=!1,kp(t));break;case 4:l=sn,sn=ou(t.stateNode.containerInfo),$e(e,t),He(t),sn=l;break;case 12:$e(e,t),He(t);break;case 13:$e(e,t),He(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Mc=re()),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,Cc(t,l)));break;case 22:i=t.memoizedState!==null;var b=n!==null&&n.memoizedState!==null,_=qn,H=Ft;if(qn=_||i,Ft=H||b,$e(e,t),Ft=H,qn=_,He(t),l&8192)t:for(e=t.stateNode,e._visibility=i?e._visibility&-2:e._visibility|1,i&&(n===null||b||qn||Ft||ja(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){b=n=e;try{if(c=b.stateNode,i)d=c.style,typeof d.setProperty=="function"?d.setProperty("display","none","important"):d.display="none";else{h=b.stateNode;var k=b.memoizedProps.style,D=k!=null&&k.hasOwnProperty("display")?k.display:null;h.style.display=D==null||typeof D=="boolean"?"":(""+D).trim()}}catch(B){$t(b,b.return,B)}}}else if(e.tag===6){if(n===null){b=e;try{b.stateNode.nodeValue=i?"":b.memoizedProps}catch(B){$t(b,b.return,B)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}l&4&&(l=t.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,Cc(t,n))));break;case 19:$e(e,t),He(t),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,Cc(t,l)));break;case 30:break;case 21:break;default:$e(e,t),He(t)}}function He(t){var e=t.flags;if(e&2){try{for(var n,l=t.return;l!==null;){if(Up(l)){n=l;break}l=l.return}if(n==null)throw Error(o(160));switch(n.tag){case 27:var i=n.stateNode,c=vc(t);Jr(t,c,i);break;case 5:var d=n.stateNode;n.flags&32&&(Ia(d,""),n.flags&=-33);var h=vc(t);Jr(t,h,d);break;case 3:case 4:var b=n.stateNode.containerInfo,_=vc(t);bc(t,_,b);break;default:throw Error(o(161))}}catch(H){$t(t,t.return,H)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function kp(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;kp(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function ua(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Hp(t,e.alternate,e),e=e.sibling}function ja(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:ra(4,e,e.return),ja(e);break;case 1:gn(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&Bp(e,e.return,n),ja(e);break;case 27:_i(e.stateNode);case 26:case 5:gn(e,e.return),ja(e);break;case 22:e.memoizedState===null&&ja(e);break;case 30:ja(e);break;default:ja(e)}t=t.sibling}}function oa(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var l=e.alternate,i=t,c=e,d=c.flags;switch(c.tag){case 0:case 11:case 15:oa(i,c,n),Si(4,c);break;case 1:if(oa(i,c,n),l=c,i=l.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(_){$t(l,l.return,_)}if(l=c,i=l.updateQueue,i!==null){var h=l.stateNode;try{var b=i.shared.hiddenCallbacks;if(b!==null)for(i.shared.hiddenCallbacks=null,i=0;i<b.length;i++)gd(b[i],h)}catch(_){$t(l,l.return,_)}}n&&d&64&&Dp(c),Ci(c,c.return);break;case 27:wp(c);case 26:case 5:oa(i,c,n),n&&l===null&&d&4&&Np(c),Ci(c,c.return);break;case 12:oa(i,c,n);break;case 13:oa(i,c,n),n&&d&4&&qp(i,c);break;case 22:c.memoizedState===null&&oa(i,c,n),Ci(c,c.return);break;case 30:break;default:oa(i,c,n)}e=e.sibling}}function Tc(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&ri(n))}function xc(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&ri(t))}function vn(t,e,n,l){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Gp(t,e,n,l),e=e.sibling}function Gp(t,e,n,l){var i=e.flags;switch(e.tag){case 0:case 11:case 15:vn(t,e,n,l),i&2048&&Si(9,e);break;case 1:vn(t,e,n,l);break;case 3:vn(t,e,n,l),i&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&ri(t)));break;case 12:if(i&2048){vn(t,e,n,l),t=e.stateNode;try{var c=e.memoizedProps,d=c.id,h=c.onPostCommit;typeof h=="function"&&h(d,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(b){$t(e,e.return,b)}}else vn(t,e,n,l);break;case 13:vn(t,e,n,l);break;case 23:break;case 22:c=e.stateNode,d=e.alternate,e.memoizedState!==null?c._visibility&2?vn(t,e,n,l):Ti(t,e):c._visibility&2?vn(t,e,n,l):(c._visibility|=2,vl(t,e,n,l,(e.subtreeFlags&10256)!==0)),i&2048&&Tc(d,e);break;case 24:vn(t,e,n,l),i&2048&&xc(e.alternate,e);break;default:vn(t,e,n,l)}}function vl(t,e,n,l,i){for(i=i&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var c=t,d=e,h=n,b=l,_=d.flags;switch(d.tag){case 0:case 11:case 15:vl(c,d,h,b,i),Si(8,d);break;case 23:break;case 22:var H=d.stateNode;d.memoizedState!==null?H._visibility&2?vl(c,d,h,b,i):Ti(c,d):(H._visibility|=2,vl(c,d,h,b,i)),i&&_&2048&&Tc(d.alternate,d);break;case 24:vl(c,d,h,b,i),i&&_&2048&&xc(d.alternate,d);break;default:vl(c,d,h,b,i)}e=e.sibling}}function Ti(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,l=e,i=l.flags;switch(l.tag){case 22:Ti(n,l),i&2048&&Tc(l.alternate,l);break;case 24:Ti(n,l),i&2048&&xc(l.alternate,l);break;default:Ti(n,l)}e=e.sibling}}var xi=8192;function bl(t){if(t.subtreeFlags&xi)for(t=t.child;t!==null;)Vp(t),t=t.sibling}function Vp(t){switch(t.tag){case 26:bl(t),t.flags&xi&&t.memoizedState!==null&&Xg(sn,t.memoizedState,t.memoizedProps);break;case 5:bl(t);break;case 3:case 4:var e=sn;sn=ou(t.stateNode.containerInfo),bl(t),sn=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=xi,xi=16777216,bl(t),xi=e):bl(t));break;default:bl(t)}}function Xp(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Ei(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];ce=l,Zp(l,t)}Xp(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Qp(t),t=t.sibling}function Qp(t){switch(t.tag){case 0:case 11:case 15:Ei(t),t.flags&2048&&ra(9,t,t.return);break;case 3:Ei(t);break;case 12:Ei(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Wr(t)):Ei(t);break;default:Ei(t)}}function Wr(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];ce=l,Zp(l,t)}Xp(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:ra(8,e,e.return),Wr(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,Wr(e));break;default:Wr(e)}t=t.sibling}}function Zp(t,e){for(;ce!==null;){var n=ce;switch(n.tag){case 0:case 11:case 15:ra(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:ri(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,ce=l;else t:for(n=t;ce!==null;){l=ce;var i=l.sibling,c=l.return;if(jp(l),l===n){ce=null;break t}if(i!==null){i.return=c,ce=i;break t}ce=c}}}var ug={getCacheForType:function(t){var e=Ce(le),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},og=typeof WeakMap=="function"?WeakMap:Map,_t=0,qt=null,St=null,xt=0,Dt=0,je=null,ca=!1,Sl=!1,Ec=!1,kn=0,Kt=0,sa=0,La=0,Ac=0,Ie=0,Cl=0,Ai=null,_e=null,Oc=!1,Mc=0,Pr=1/0,Fr=null,fa=null,de=0,da=null,Tl=null,xl=0,Rc=0,zc=null,Kp=null,Oi=0,_c=null;function Le(){if((_t&2)!==0&&xt!==0)return xt&-xt;if(N.T!==null){var t=sl;return t!==0?t:Hc()}return sf()}function Jp(){Ie===0&&(Ie=(xt&536870912)===0||At?rf():536870912);var t=Fe.current;return t!==null&&(t.flags|=32),Ie}function qe(t,e,n){(t===qt&&(Dt===2||Dt===9)||t.cancelPendingCommit!==null)&&(El(t,0),pa(t,xt,Ie,!1)),Vl(t,n),((_t&2)===0||t!==qt)&&(t===qt&&((_t&2)===0&&(La|=n),Kt===4&&pa(t,xt,Ie,!1)),bn(t))}function Wp(t,e,n){if((_t&6)!==0)throw Error(o(327));var l=!n&&(e&124)===0&&(e&t.expiredLanes)===0||Gl(t,e),i=l?fg(t,e):Nc(t,e,!0),c=l;do{if(i===0){Sl&&!l&&pa(t,e,0,!1);break}else{if(n=t.current.alternate,c&&!cg(n)){i=Nc(t,e,!1),c=!1;continue}if(i===2){if(c=e,t.errorRecoveryDisabledLanes&c)var d=0;else d=t.pendingLanes&-536870913,d=d!==0?d:d&536870912?536870912:0;if(d!==0){e=d;t:{var h=t;i=Ai;var b=h.current.memoizedState.isDehydrated;if(b&&(El(h,d).flags|=256),d=Nc(h,d,!1),d!==2){if(Ec&&!b){h.errorRecoveryDisabledLanes|=c,La|=c,i=4;break t}c=_e,_e=i,c!==null&&(_e===null?_e=c:_e.push.apply(_e,c))}i=d}if(c=!1,i!==2)continue}}if(i===1){El(t,0),pa(t,e,0,!0);break}t:{switch(l=t,c=i,c){case 0:case 1:throw Error(o(345));case 4:if((e&4194048)!==e)break;case 6:pa(l,e,Ie,!ca);break t;case 2:_e=null;break;case 3:case 5:break;default:throw Error(o(329))}if((e&62914560)===e&&(i=Mc+300-re(),10<i)){if(pa(l,e,Ie,!ca),cr(l,0,!0)!==0)break t;l.timeoutHandle=Ah(Pp.bind(null,l,n,_e,Fr,Oc,e,Ie,La,Cl,ca,c,2,-0,0),i);break t}Pp(l,n,_e,Fr,Oc,e,Ie,La,Cl,ca,c,0,-0,0)}}break}while(!0);bn(t)}function Pp(t,e,n,l,i,c,d,h,b,_,H,k,D,B){if(t.timeoutHandle=-1,k=e.subtreeFlags,(k&8192||(k&16785408)===16785408)&&(Ni={stylesheets:null,count:0,unsuspend:Vg},Vp(e),k=Qg(),k!==null)){t.cancelPendingCommit=k(lh.bind(null,t,e,c,n,l,i,d,h,b,H,1,D,B)),pa(t,c,d,!_);return}lh(t,e,c,n,l,i,d,h,b)}function cg(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var i=n[l],c=i.getSnapshot;i=i.value;try{if(!Ue(c(),i))return!1}catch{return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function pa(t,e,n,l){e&=~Ac,e&=~La,t.suspendedLanes|=e,t.pingedLanes&=~e,l&&(t.warmLanes|=e),l=t.expirationTimes;for(var i=e;0<i;){var c=31-ge(i),d=1<<c;l[c]=-1,i&=~d}n!==0&&of(t,n,e)}function Ir(){return(_t&6)===0?(Mi(0),!1):!0}function Dc(){if(St!==null){if(Dt===0)var t=St.return;else t=St,Un=Na=null,Ko(t),yl=null,gi=0,t=St;for(;t!==null;)_p(t.alternate,t),t=t.return;St=null}}function El(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,Mg(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),Dc(),qt=t,St=n=Dn(t.current,null),xt=e,Dt=0,je=null,ca=!1,Sl=Gl(t,e),Ec=!1,Cl=Ie=Ac=La=sa=Kt=0,_e=Ai=null,Oc=!1,(e&8)!==0&&(e|=e&32);var l=t.entangledLanes;if(l!==0)for(t=t.entanglements,l&=e;0<l;){var i=31-ge(l),c=1<<i;e|=t[i],l&=~c}return kn=e,Cr(),n}function Fp(t,e){gt=null,N.H=qr,e===oi||e===_r?(e=md(),Dt=3):e===dd?(e=md(),Dt=4):Dt=e===yp?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,je=e,St===null&&(Kt=1,Xr(t,Ke(e,t.current)))}function Ip(){var t=N.H;return N.H=qr,t===null?qr:t}function th(){var t=N.A;return N.A=ug,t}function Bc(){Kt=4,ca||(xt&4194048)!==xt&&Fe.current!==null||(Sl=!0),(sa&134217727)===0&&(La&134217727)===0||qt===null||pa(qt,xt,Ie,!1)}function Nc(t,e,n){var l=_t;_t|=2;var i=Ip(),c=th();(qt!==t||xt!==e)&&(Fr=null,El(t,e)),e=!1;var d=Kt;t:do try{if(Dt!==0&&St!==null){var h=St,b=je;switch(Dt){case 8:Dc(),d=6;break t;case 3:case 2:case 9:case 6:Fe.current===null&&(e=!0);var _=Dt;if(Dt=0,je=null,Al(t,h,b,_),n&&Sl){d=0;break t}break;default:_=Dt,Dt=0,je=null,Al(t,h,b,_)}}sg(),d=Kt;break}catch(H){Fp(t,H)}while(!0);return e&&t.shellSuspendCounter++,Un=Na=null,_t=l,N.H=i,N.A=c,St===null&&(qt=null,xt=0,Cr()),d}function sg(){for(;St!==null;)eh(St)}function fg(t,e){var n=_t;_t|=2;var l=Ip(),i=th();qt!==t||xt!==e?(Fr=null,Pr=re()+500,El(t,e)):Sl=Gl(t,e);t:do try{if(Dt!==0&&St!==null){e=St;var c=je;e:switch(Dt){case 1:Dt=0,je=null,Al(t,e,c,1);break;case 2:case 9:if(pd(c)){Dt=0,je=null,nh(e);break}e=function(){Dt!==2&&Dt!==9||qt!==t||(Dt=7),bn(t)},c.then(e,e);break t;case 3:Dt=7;break t;case 4:Dt=5;break t;case 7:pd(c)?(Dt=0,je=null,nh(e)):(Dt=0,je=null,Al(t,e,c,7));break;case 5:var d=null;switch(St.tag){case 26:d=St.memoizedState;case 5:case 27:var h=St;if(!d||Hh(d)){Dt=0,je=null;var b=h.sibling;if(b!==null)St=b;else{var _=h.return;_!==null?(St=_,tu(_)):St=null}break e}}Dt=0,je=null,Al(t,e,c,5);break;case 6:Dt=0,je=null,Al(t,e,c,6);break;case 8:Dc(),Kt=6;break t;default:throw Error(o(462))}}dg();break}catch(H){Fp(t,H)}while(!0);return Un=Na=null,N.H=l,N.A=i,_t=n,St!==null?0:(qt=null,xt=0,Cr(),Kt)}function dg(){for(;St!==null&&!Xa();)eh(St)}function eh(t){var e=Rp(t.alternate,t,kn);t.memoizedProps=t.pendingProps,e===null?tu(t):St=e}function nh(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=Tp(n,e,e.pendingProps,e.type,void 0,xt);break;case 11:e=Tp(n,e,e.pendingProps,e.type.render,e.ref,xt);break;case 5:Ko(e);default:_p(n,e),e=St=ad(e,kn),e=Rp(n,e,kn)}t.memoizedProps=t.pendingProps,e===null?tu(t):St=e}function Al(t,e,n,l){Un=Na=null,Ko(e),yl=null,gi=0;var i=e.return;try{if(eg(t,i,e,n,xt)){Kt=1,Xr(t,Ke(n,t.current)),St=null;return}}catch(c){if(i!==null)throw St=i,c;Kt=1,Xr(t,Ke(n,t.current)),St=null;return}e.flags&32768?(At||l===1?t=!0:Sl||(xt&536870912)!==0?t=!1:(ca=t=!0,(l===2||l===9||l===3||l===6)&&(l=Fe.current,l!==null&&l.tag===13&&(l.flags|=16384))),ah(e,t)):tu(e)}function tu(t){var e=t;do{if((e.flags&32768)!==0){ah(e,ca);return}t=e.return;var n=ag(e.alternate,e,kn);if(n!==null){St=n;return}if(e=e.sibling,e!==null){St=e;return}St=e=t}while(e!==null);Kt===0&&(Kt=5)}function ah(t,e){do{var n=lg(t.alternate,t);if(n!==null){n.flags&=32767,St=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){St=t;return}St=t=n}while(t!==null);Kt=6,St=null}function lh(t,e,n,l,i,c,d,h,b){t.cancelPendingCommit=null;do eu();while(de!==0);if((_t&6)!==0)throw Error(o(327));if(e!==null){if(e===t.current)throw Error(o(177));if(c=e.lanes|e.childLanes,c|=xo,V0(t,n,c,d,h,b),t===qt&&(St=qt=null,xt=0),Tl=e,da=t,xl=n,Rc=c,zc=i,Kp=l,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,yg(Xe,function(){return ch(),null})):(t.callbackNode=null,t.callbackPriority=0),l=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||l){l=N.T,N.T=null,i=Z.p,Z.p=2,d=_t,_t|=4;try{ig(t,e,n)}finally{_t=d,Z.p=i,N.T=l}}de=1,ih(),rh(),uh()}}function ih(){if(de===1){de=0;var t=da,e=Tl,n=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||n){n=N.T,N.T=null;var l=Z.p;Z.p=2;var i=_t;_t|=4;try{Yp(e,t);var c=Xc,d=Zf(t.containerInfo),h=c.focusedElem,b=c.selectionRange;if(d!==h&&h&&h.ownerDocument&&Qf(h.ownerDocument.documentElement,h)){if(b!==null&&vo(h)){var _=b.start,H=b.end;if(H===void 0&&(H=_),"selectionStart"in h)h.selectionStart=_,h.selectionEnd=Math.min(H,h.value.length);else{var k=h.ownerDocument||document,D=k&&k.defaultView||window;if(D.getSelection){var B=D.getSelection(),dt=h.textContent.length,ct=Math.min(b.start,dt),Ut=b.end===void 0?ct:Math.min(b.end,dt);!B.extend&&ct>Ut&&(d=Ut,Ut=ct,ct=d);var O=Xf(h,ct),x=Xf(h,Ut);if(O&&x&&(B.rangeCount!==1||B.anchorNode!==O.node||B.anchorOffset!==O.offset||B.focusNode!==x.node||B.focusOffset!==x.offset)){var z=k.createRange();z.setStart(O.node,O.offset),B.removeAllRanges(),ct>Ut?(B.addRange(z),B.extend(x.node,x.offset)):(z.setEnd(x.node,x.offset),B.addRange(z))}}}}for(k=[],B=h;B=B.parentNode;)B.nodeType===1&&k.push({element:B,left:B.scrollLeft,top:B.scrollTop});for(typeof h.focus=="function"&&h.focus(),h=0;h<k.length;h++){var L=k[h];L.element.scrollLeft=L.left,L.element.scrollTop=L.top}}pu=!!Vc,Xc=Vc=null}finally{_t=i,Z.p=l,N.T=n}}t.current=e,de=2}}function rh(){if(de===2){de=0;var t=da,e=Tl,n=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||n){n=N.T,N.T=null;var l=Z.p;Z.p=2;var i=_t;_t|=4;try{Hp(t,e.alternate,e)}finally{_t=i,Z.p=l,N.T=n}}de=3}}function uh(){if(de===4||de===3){de=0,kl();var t=da,e=Tl,n=xl,l=Kp;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?de=5:(de=0,Tl=da=null,oh(t,t.pendingLanes));var i=t.pendingLanes;if(i===0&&(fa=null),Wu(n),e=e.stateNode,ye&&typeof ye.onCommitFiberRoot=="function")try{ye.onCommitFiberRoot(Wn,e,void 0,(e.current.flags&128)===128)}catch{}if(l!==null){e=N.T,i=Z.p,Z.p=2,N.T=null;try{for(var c=t.onRecoverableError,d=0;d<l.length;d++){var h=l[d];c(h.value,{componentStack:h.stack})}}finally{N.T=e,Z.p=i}}(xl&3)!==0&&eu(),bn(t),i=t.pendingLanes,(n&4194090)!==0&&(i&42)!==0?t===_c?Oi++:(Oi=0,_c=t):Oi=0,Mi(0)}}function oh(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,ri(e)))}function eu(t){return ih(),rh(),uh(),ch()}function ch(){if(de!==5)return!1;var t=da,e=Rc;Rc=0;var n=Wu(xl),l=N.T,i=Z.p;try{Z.p=32>n?32:n,N.T=null,n=zc,zc=null;var c=da,d=xl;if(de=0,Tl=da=null,xl=0,(_t&6)!==0)throw Error(o(331));var h=_t;if(_t|=4,Qp(c.current),Gp(c,c.current,d,n),_t=h,Mi(0,!1),ye&&typeof ye.onPostCommitFiberRoot=="function")try{ye.onPostCommitFiberRoot(Wn,c)}catch{}return!0}finally{Z.p=i,N.T=l,oh(t,e)}}function sh(t,e,n){e=Ke(n,e),e=oc(t.stateNode,e,2),t=na(t,e,2),t!==null&&(Vl(t,2),bn(t))}function $t(t,e,n){if(t.tag===3)sh(t,t,n);else for(;e!==null;){if(e.tag===3){sh(e,t,n);break}else if(e.tag===1){var l=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(fa===null||!fa.has(l))){t=Ke(n,t),n=hp(2),l=na(e,n,2),l!==null&&(mp(n,l,e,t),Vl(l,2),bn(l));break}}e=e.return}}function Uc(t,e,n){var l=t.pingCache;if(l===null){l=t.pingCache=new og;var i=new Set;l.set(e,i)}else i=l.get(e),i===void 0&&(i=new Set,l.set(e,i));i.has(n)||(Ec=!0,i.add(n),t=pg.bind(null,t,e,n),e.then(t,t))}function pg(t,e,n){var l=t.pingCache;l!==null&&l.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,qt===t&&(xt&n)===n&&(Kt===4||Kt===3&&(xt&62914560)===xt&&300>re()-Mc?(_t&2)===0&&El(t,0):Ac|=n,Cl===xt&&(Cl=0)),bn(t)}function fh(t,e){e===0&&(e=uf()),t=rl(t,e),t!==null&&(Vl(t,e),bn(t))}function hg(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),fh(t,n)}function mg(t,e){var n=0;switch(t.tag){case 13:var l=t.stateNode,i=t.memoizedState;i!==null&&(n=i.retryLane);break;case 19:l=t.stateNode;break;case 22:l=t.stateNode._retryCache;break;default:throw Error(o(314))}l!==null&&l.delete(e),fh(t,n)}function yg(t,e){return Jn(t,e)}var nu=null,Ol=null,wc=!1,au=!1,$c=!1,qa=0;function bn(t){t!==Ol&&t.next===null&&(Ol===null?nu=Ol=t:Ol=Ol.next=t),au=!0,wc||(wc=!0,vg())}function Mi(t,e){if(!$c&&au){$c=!0;do for(var n=!1,l=nu;l!==null;){if(t!==0){var i=l.pendingLanes;if(i===0)var c=0;else{var d=l.suspendedLanes,h=l.pingedLanes;c=(1<<31-ge(42|t)+1)-1,c&=i&~(d&~h),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(n=!0,mh(l,c))}else c=xt,c=cr(l,l===qt?c:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(c&3)===0||Gl(l,c)||(n=!0,mh(l,c));l=l.next}while(n);$c=!1}}function gg(){dh()}function dh(){au=wc=!1;var t=0;qa!==0&&(Og()&&(t=qa),qa=0);for(var e=re(),n=null,l=nu;l!==null;){var i=l.next,c=ph(l,e);c===0?(l.next=null,n===null?nu=i:n.next=i,i===null&&(Ol=n)):(n=l,(t!==0||(c&3)!==0)&&(au=!0)),l=i}Mi(t)}function ph(t,e){for(var n=t.suspendedLanes,l=t.pingedLanes,i=t.expirationTimes,c=t.pendingLanes&-62914561;0<c;){var d=31-ge(c),h=1<<d,b=i[d];b===-1?((h&n)===0||(h&l)!==0)&&(i[d]=G0(h,e)):b<=e&&(t.expiredLanes|=h),c&=~h}if(e=qt,n=xt,n=cr(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l=t.callbackNode,n===0||t===e&&(Dt===2||Dt===9)||t.cancelPendingCommit!==null)return l!==null&&l!==null&&pn(l),t.callbackNode=null,t.callbackPriority=0;if((n&3)===0||Gl(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(l!==null&&pn(l),Wu(n)){case 2:case 8:n=hn;break;case 32:n=Xe;break;case 268435456:n=rr;break;default:n=Xe}return l=hh.bind(null,t),n=Jn(n,l),t.callbackPriority=e,t.callbackNode=n,e}return l!==null&&l!==null&&pn(l),t.callbackPriority=2,t.callbackNode=null,2}function hh(t,e){if(de!==0&&de!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(eu()&&t.callbackNode!==n)return null;var l=xt;return l=cr(t,t===qt?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l===0?null:(Wp(t,l,e),ph(t,re()),t.callbackNode!=null&&t.callbackNode===n?hh.bind(null,t):null)}function mh(t,e){if(eu())return null;Wp(t,e,!0)}function vg(){Rg(function(){(_t&6)!==0?Jn(me,gg):dh()})}function Hc(){return qa===0&&(qa=rf()),qa}function yh(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:hr(""+t)}function gh(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function bg(t,e,n,l,i){if(e==="submit"&&n&&n.stateNode===i){var c=yh((i[Oe]||null).action),d=l.submitter;d&&(e=(e=d[Oe]||null)?yh(e.formAction):d.getAttribute("formAction"),e!==null&&(c=e,d=null));var h=new vr("action","action",null,l,i);t.push({event:h,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(qa!==0){var b=d?gh(i,d):new FormData(i);ac(n,{pending:!0,data:b,method:i.method,action:c},null,b)}}else typeof c=="function"&&(h.preventDefault(),b=d?gh(i,d):new FormData(i),ac(n,{pending:!0,data:b,method:i.method,action:c},c,b))},currentTarget:i}]})}}for(var jc=0;jc<To.length;jc++){var Lc=To[jc],Sg=Lc.toLowerCase(),Cg=Lc[0].toUpperCase()+Lc.slice(1);cn(Sg,"on"+Cg)}cn(Wf,"onAnimationEnd"),cn(Pf,"onAnimationIteration"),cn(Ff,"onAnimationStart"),cn("dblclick","onDoubleClick"),cn("focusin","onFocus"),cn("focusout","onBlur"),cn(jy,"onTransitionRun"),cn(Ly,"onTransitionStart"),cn(qy,"onTransitionCancel"),cn(If,"onTransitionEnd"),Wa("onMouseEnter",["mouseout","mouseover"]),Wa("onMouseLeave",["mouseout","mouseover"]),Wa("onPointerEnter",["pointerout","pointerover"]),Wa("onPointerLeave",["pointerout","pointerover"]),Ea("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ea("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ea("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ea("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ea("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ea("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ri="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Tg=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ri));function vh(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var l=t[n],i=l.event;l=l.listeners;t:{var c=void 0;if(e)for(var d=l.length-1;0<=d;d--){var h=l[d],b=h.instance,_=h.currentTarget;if(h=h.listener,b!==c&&i.isPropagationStopped())break t;c=h,i.currentTarget=_;try{c(i)}catch(H){Vr(H)}i.currentTarget=null,c=b}else for(d=0;d<l.length;d++){if(h=l[d],b=h.instance,_=h.currentTarget,h=h.listener,b!==c&&i.isPropagationStopped())break t;c=h,i.currentTarget=_;try{c(i)}catch(H){Vr(H)}i.currentTarget=null,c=b}}}}function Ct(t,e){var n=e[Pu];n===void 0&&(n=e[Pu]=new Set);var l=t+"__bubble";n.has(l)||(bh(e,t,2,!1),n.add(l))}function qc(t,e,n){var l=0;e&&(l|=4),bh(n,t,l,e)}var lu="_reactListening"+Math.random().toString(36).slice(2);function Yc(t){if(!t[lu]){t[lu]=!0,df.forEach(function(n){n!=="selectionchange"&&(Tg.has(n)||qc(n,!1,t),qc(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[lu]||(e[lu]=!0,qc("selectionchange",!1,e))}}function bh(t,e,n,l){switch(Gh(e)){case 2:var i=Jg;break;case 8:i=Wg;break;default:i=es}n=i.bind(null,e,n,t),i=void 0,!oo||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(i=!0),l?i!==void 0?t.addEventListener(e,n,{capture:!0,passive:i}):t.addEventListener(e,n,!0):i!==void 0?t.addEventListener(e,n,{passive:i}):t.addEventListener(e,n,!1)}function kc(t,e,n,l,i){var c=l;if((e&1)===0&&(e&2)===0&&l!==null)t:for(;;){if(l===null)return;var d=l.tag;if(d===3||d===4){var h=l.stateNode.containerInfo;if(h===i)break;if(d===4)for(d=l.return;d!==null;){var b=d.tag;if((b===3||b===4)&&d.stateNode.containerInfo===i)return;d=d.return}for(;h!==null;){if(d=Za(h),d===null)return;if(b=d.tag,b===5||b===6||b===26||b===27){l=c=d;continue t}h=h.parentNode}}l=l.return}Of(function(){var _=c,H=ro(n),k=[];t:{var D=td.get(t);if(D!==void 0){var B=vr,dt=t;switch(t){case"keypress":if(yr(n)===0)break t;case"keydown":case"keyup":B=yy;break;case"focusin":dt="focus",B=po;break;case"focusout":dt="blur",B=po;break;case"beforeblur":case"afterblur":B=po;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":B=zf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":B=ly;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":B=by;break;case Wf:case Pf:case Ff:B=uy;break;case If:B=Cy;break;case"scroll":case"scrollend":B=ny;break;case"wheel":B=xy;break;case"copy":case"cut":case"paste":B=cy;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":B=Df;break;case"toggle":case"beforetoggle":B=Ay}var ct=(e&4)!==0,Ut=!ct&&(t==="scroll"||t==="scrollend"),O=ct?D!==null?D+"Capture":null:D;ct=[];for(var x=_,z;x!==null;){var L=x;if(z=L.stateNode,L=L.tag,L!==5&&L!==26&&L!==27||z===null||O===null||(L=Zl(x,O),L!=null&&ct.push(zi(x,L,z))),Ut)break;x=x.return}0<ct.length&&(D=new B(D,dt,null,n,H),k.push({event:D,listeners:ct}))}}if((e&7)===0){t:{if(D=t==="mouseover"||t==="pointerover",B=t==="mouseout"||t==="pointerout",D&&n!==io&&(dt=n.relatedTarget||n.fromElement)&&(Za(dt)||dt[Qa]))break t;if((B||D)&&(D=H.window===H?H:(D=H.ownerDocument)?D.defaultView||D.parentWindow:window,B?(dt=n.relatedTarget||n.toElement,B=_,dt=dt?Za(dt):null,dt!==null&&(Ut=f(dt),ct=dt.tag,dt!==Ut||ct!==5&&ct!==27&&ct!==6)&&(dt=null)):(B=null,dt=_),B!==dt)){if(ct=zf,L="onMouseLeave",O="onMouseEnter",x="mouse",(t==="pointerout"||t==="pointerover")&&(ct=Df,L="onPointerLeave",O="onPointerEnter",x="pointer"),Ut=B==null?D:Ql(B),z=dt==null?D:Ql(dt),D=new ct(L,x+"leave",B,n,H),D.target=Ut,D.relatedTarget=z,L=null,Za(H)===_&&(ct=new ct(O,x+"enter",dt,n,H),ct.target=z,ct.relatedTarget=Ut,L=ct),Ut=L,B&&dt)e:{for(ct=B,O=dt,x=0,z=ct;z;z=Ml(z))x++;for(z=0,L=O;L;L=Ml(L))z++;for(;0<x-z;)ct=Ml(ct),x--;for(;0<z-x;)O=Ml(O),z--;for(;x--;){if(ct===O||O!==null&&ct===O.alternate)break e;ct=Ml(ct),O=Ml(O)}ct=null}else ct=null;B!==null&&Sh(k,D,B,ct,!1),dt!==null&&Ut!==null&&Sh(k,Ut,dt,ct,!0)}}t:{if(D=_?Ql(_):window,B=D.nodeName&&D.nodeName.toLowerCase(),B==="select"||B==="input"&&D.type==="file")var lt=Lf;else if(Hf(D))if(qf)lt=wy;else{lt=Ny;var bt=By}else B=D.nodeName,!B||B.toLowerCase()!=="input"||D.type!=="checkbox"&&D.type!=="radio"?_&&lo(_.elementType)&&(lt=Lf):lt=Uy;if(lt&&(lt=lt(t,_))){jf(k,lt,n,H);break t}bt&&bt(t,D,_),t==="focusout"&&_&&D.type==="number"&&_.memoizedProps.value!=null&&ao(D,"number",D.value)}switch(bt=_?Ql(_):window,t){case"focusin":(Hf(bt)||bt.contentEditable==="true")&&(al=bt,bo=_,ei=null);break;case"focusout":ei=bo=al=null;break;case"mousedown":So=!0;break;case"contextmenu":case"mouseup":case"dragend":So=!1,Kf(k,n,H);break;case"selectionchange":if(Hy)break;case"keydown":case"keyup":Kf(k,n,H)}var ut;if(mo)t:{switch(t){case"compositionstart":var st="onCompositionStart";break t;case"compositionend":st="onCompositionEnd";break t;case"compositionupdate":st="onCompositionUpdate";break t}st=void 0}else nl?wf(t,n)&&(st="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(st="onCompositionStart");st&&(Bf&&n.locale!=="ko"&&(nl||st!=="onCompositionStart"?st==="onCompositionEnd"&&nl&&(ut=Mf()):(Fn=H,co="value"in Fn?Fn.value:Fn.textContent,nl=!0)),bt=iu(_,st),0<bt.length&&(st=new _f(st,t,null,n,H),k.push({event:st,listeners:bt}),ut?st.data=ut:(ut=$f(n),ut!==null&&(st.data=ut)))),(ut=My?Ry(t,n):zy(t,n))&&(st=iu(_,"onBeforeInput"),0<st.length&&(bt=new _f("onBeforeInput","beforeinput",null,n,H),k.push({event:bt,listeners:st}),bt.data=ut)),bg(k,t,_,n,H)}vh(k,e)})}function zi(t,e,n){return{instance:t,listener:e,currentTarget:n}}function iu(t,e){for(var n=e+"Capture",l=[];t!==null;){var i=t,c=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||c===null||(i=Zl(t,n),i!=null&&l.unshift(zi(t,i,c)),i=Zl(t,e),i!=null&&l.push(zi(t,i,c))),t.tag===3)return l;t=t.return}return[]}function Ml(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Sh(t,e,n,l,i){for(var c=e._reactName,d=[];n!==null&&n!==l;){var h=n,b=h.alternate,_=h.stateNode;if(h=h.tag,b!==null&&b===l)break;h!==5&&h!==26&&h!==27||_===null||(b=_,i?(_=Zl(n,c),_!=null&&d.unshift(zi(n,_,b))):i||(_=Zl(n,c),_!=null&&d.push(zi(n,_,b)))),n=n.return}d.length!==0&&t.push({event:e,listeners:d})}var xg=/\r\n?/g,Eg=/\u0000|\uFFFD/g;function Ch(t){return(typeof t=="string"?t:""+t).replace(xg,`
`).replace(Eg,"")}function Th(t,e){return e=Ch(e),Ch(t)===e}function ru(){}function Nt(t,e,n,l,i,c){switch(n){case"children":typeof l=="string"?e==="body"||e==="textarea"&&l===""||Ia(t,l):(typeof l=="number"||typeof l=="bigint")&&e!=="body"&&Ia(t,""+l);break;case"className":fr(t,"class",l);break;case"tabIndex":fr(t,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":fr(t,n,l);break;case"style":Ef(t,l,c);break;case"data":if(e!=="object"){fr(t,"data",l);break}case"src":case"href":if(l===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=hr(""+l),t.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(n==="formAction"?(e!=="input"&&Nt(t,e,"name",i.name,i,null),Nt(t,e,"formEncType",i.formEncType,i,null),Nt(t,e,"formMethod",i.formMethod,i,null),Nt(t,e,"formTarget",i.formTarget,i,null)):(Nt(t,e,"encType",i.encType,i,null),Nt(t,e,"method",i.method,i,null),Nt(t,e,"target",i.target,i,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=hr(""+l),t.setAttribute(n,l);break;case"onClick":l!=null&&(t.onclick=ru);break;case"onScroll":l!=null&&Ct("scroll",t);break;case"onScrollEnd":l!=null&&Ct("scrollend",t);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(o(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(o(60));t.innerHTML=n}}break;case"multiple":t.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":t.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){t.removeAttribute("xlink:href");break}n=hr(""+l),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""+l):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":l===!0?t.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,l):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?t.setAttribute(n,l):t.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?t.removeAttribute(n):t.setAttribute(n,l);break;case"popover":Ct("beforetoggle",t),Ct("toggle",t),sr(t,"popover",l);break;case"xlinkActuate":zn(t,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":zn(t,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":zn(t,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":zn(t,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":zn(t,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":zn(t,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":zn(t,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":zn(t,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":zn(t,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":sr(t,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=ty.get(n)||n,sr(t,n,l))}}function Gc(t,e,n,l,i,c){switch(n){case"style":Ef(t,l,c);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(o(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(o(60));t.innerHTML=n}}break;case"children":typeof l=="string"?Ia(t,l):(typeof l=="number"||typeof l=="bigint")&&Ia(t,""+l);break;case"onScroll":l!=null&&Ct("scroll",t);break;case"onScrollEnd":l!=null&&Ct("scrollend",t);break;case"onClick":l!=null&&(t.onclick=ru);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!pf.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(i=n.endsWith("Capture"),e=n.slice(2,i?n.length-7:void 0),c=t[Oe]||null,c=c!=null?c[n]:null,typeof c=="function"&&t.removeEventListener(e,c,i),typeof l=="function")){typeof c!="function"&&c!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,l,i);break t}n in t?t[n]=l:l===!0?t.setAttribute(n,""):sr(t,n,l)}}}function pe(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Ct("error",t),Ct("load",t);var l=!1,i=!1,c;for(c in n)if(n.hasOwnProperty(c)){var d=n[c];if(d!=null)switch(c){case"src":l=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:Nt(t,e,c,d,n,null)}}i&&Nt(t,e,"srcSet",n.srcSet,n,null),l&&Nt(t,e,"src",n.src,n,null);return;case"input":Ct("invalid",t);var h=c=d=i=null,b=null,_=null;for(l in n)if(n.hasOwnProperty(l)){var H=n[l];if(H!=null)switch(l){case"name":i=H;break;case"type":d=H;break;case"checked":b=H;break;case"defaultChecked":_=H;break;case"value":c=H;break;case"defaultValue":h=H;break;case"children":case"dangerouslySetInnerHTML":if(H!=null)throw Error(o(137,e));break;default:Nt(t,e,l,H,n,null)}}Sf(t,c,h,b,_,d,i,!1),dr(t);return;case"select":Ct("invalid",t),l=d=c=null;for(i in n)if(n.hasOwnProperty(i)&&(h=n[i],h!=null))switch(i){case"value":c=h;break;case"defaultValue":d=h;break;case"multiple":l=h;default:Nt(t,e,i,h,n,null)}e=c,n=d,t.multiple=!!l,e!=null?Fa(t,!!l,e,!1):n!=null&&Fa(t,!!l,n,!0);return;case"textarea":Ct("invalid",t),c=i=l=null;for(d in n)if(n.hasOwnProperty(d)&&(h=n[d],h!=null))switch(d){case"value":l=h;break;case"defaultValue":i=h;break;case"children":c=h;break;case"dangerouslySetInnerHTML":if(h!=null)throw Error(o(91));break;default:Nt(t,e,d,h,n,null)}Tf(t,l,i,c),dr(t);return;case"option":for(b in n)if(n.hasOwnProperty(b)&&(l=n[b],l!=null))switch(b){case"selected":t.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Nt(t,e,b,l,n,null)}return;case"dialog":Ct("beforetoggle",t),Ct("toggle",t),Ct("cancel",t),Ct("close",t);break;case"iframe":case"object":Ct("load",t);break;case"video":case"audio":for(l=0;l<Ri.length;l++)Ct(Ri[l],t);break;case"image":Ct("error",t),Ct("load",t);break;case"details":Ct("toggle",t);break;case"embed":case"source":case"link":Ct("error",t),Ct("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(_ in n)if(n.hasOwnProperty(_)&&(l=n[_],l!=null))switch(_){case"children":case"dangerouslySetInnerHTML":throw Error(o(137,e));default:Nt(t,e,_,l,n,null)}return;default:if(lo(e)){for(H in n)n.hasOwnProperty(H)&&(l=n[H],l!==void 0&&Gc(t,e,H,l,n,void 0));return}}for(h in n)n.hasOwnProperty(h)&&(l=n[h],l!=null&&Nt(t,e,h,l,n,null))}function Ag(t,e,n,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,c=null,d=null,h=null,b=null,_=null,H=null;for(B in n){var k=n[B];if(n.hasOwnProperty(B)&&k!=null)switch(B){case"checked":break;case"value":break;case"defaultValue":b=k;default:l.hasOwnProperty(B)||Nt(t,e,B,null,l,k)}}for(var D in l){var B=l[D];if(k=n[D],l.hasOwnProperty(D)&&(B!=null||k!=null))switch(D){case"type":c=B;break;case"name":i=B;break;case"checked":_=B;break;case"defaultChecked":H=B;break;case"value":d=B;break;case"defaultValue":h=B;break;case"children":case"dangerouslySetInnerHTML":if(B!=null)throw Error(o(137,e));break;default:B!==k&&Nt(t,e,D,B,l,k)}}no(t,d,h,b,_,H,c,i);return;case"select":B=d=h=D=null;for(c in n)if(b=n[c],n.hasOwnProperty(c)&&b!=null)switch(c){case"value":break;case"multiple":B=b;default:l.hasOwnProperty(c)||Nt(t,e,c,null,l,b)}for(i in l)if(c=l[i],b=n[i],l.hasOwnProperty(i)&&(c!=null||b!=null))switch(i){case"value":D=c;break;case"defaultValue":h=c;break;case"multiple":d=c;default:c!==b&&Nt(t,e,i,c,l,b)}e=h,n=d,l=B,D!=null?Fa(t,!!n,D,!1):!!l!=!!n&&(e!=null?Fa(t,!!n,e,!0):Fa(t,!!n,n?[]:"",!1));return;case"textarea":B=D=null;for(h in n)if(i=n[h],n.hasOwnProperty(h)&&i!=null&&!l.hasOwnProperty(h))switch(h){case"value":break;case"children":break;default:Nt(t,e,h,null,l,i)}for(d in l)if(i=l[d],c=n[d],l.hasOwnProperty(d)&&(i!=null||c!=null))switch(d){case"value":D=i;break;case"defaultValue":B=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(o(91));break;default:i!==c&&Nt(t,e,d,i,l,c)}Cf(t,D,B);return;case"option":for(var dt in n)if(D=n[dt],n.hasOwnProperty(dt)&&D!=null&&!l.hasOwnProperty(dt))switch(dt){case"selected":t.selected=!1;break;default:Nt(t,e,dt,null,l,D)}for(b in l)if(D=l[b],B=n[b],l.hasOwnProperty(b)&&D!==B&&(D!=null||B!=null))switch(b){case"selected":t.selected=D&&typeof D!="function"&&typeof D!="symbol";break;default:Nt(t,e,b,D,l,B)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ct in n)D=n[ct],n.hasOwnProperty(ct)&&D!=null&&!l.hasOwnProperty(ct)&&Nt(t,e,ct,null,l,D);for(_ in l)if(D=l[_],B=n[_],l.hasOwnProperty(_)&&D!==B&&(D!=null||B!=null))switch(_){case"children":case"dangerouslySetInnerHTML":if(D!=null)throw Error(o(137,e));break;default:Nt(t,e,_,D,l,B)}return;default:if(lo(e)){for(var Ut in n)D=n[Ut],n.hasOwnProperty(Ut)&&D!==void 0&&!l.hasOwnProperty(Ut)&&Gc(t,e,Ut,void 0,l,D);for(H in l)D=l[H],B=n[H],!l.hasOwnProperty(H)||D===B||D===void 0&&B===void 0||Gc(t,e,H,D,l,B);return}}for(var O in n)D=n[O],n.hasOwnProperty(O)&&D!=null&&!l.hasOwnProperty(O)&&Nt(t,e,O,null,l,D);for(k in l)D=l[k],B=n[k],!l.hasOwnProperty(k)||D===B||D==null&&B==null||Nt(t,e,k,D,l,B)}var Vc=null,Xc=null;function uu(t){return t.nodeType===9?t:t.ownerDocument}function xh(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Eh(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function Qc(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var Zc=null;function Og(){var t=window.event;return t&&t.type==="popstate"?t===Zc?!1:(Zc=t,!0):(Zc=null,!1)}var Ah=typeof setTimeout=="function"?setTimeout:void 0,Mg=typeof clearTimeout=="function"?clearTimeout:void 0,Oh=typeof Promise=="function"?Promise:void 0,Rg=typeof queueMicrotask=="function"?queueMicrotask:typeof Oh<"u"?function(t){return Oh.resolve(null).then(t).catch(zg)}:Ah;function zg(t){setTimeout(function(){throw t})}function ha(t){return t==="head"}function Mh(t,e){var n=e,l=0,i=0;do{var c=n.nextSibling;if(t.removeChild(n),c&&c.nodeType===8)if(n=c.data,n==="/$"){if(0<l&&8>l){n=l;var d=t.ownerDocument;if(n&1&&_i(d.documentElement),n&2&&_i(d.body),n&4)for(n=d.head,_i(n),d=n.firstChild;d;){var h=d.nextSibling,b=d.nodeName;d[Xl]||b==="SCRIPT"||b==="STYLE"||b==="LINK"&&d.rel.toLowerCase()==="stylesheet"||n.removeChild(d),d=h}}if(i===0){t.removeChild(c),ji(e);return}i--}else n==="$"||n==="$?"||n==="$!"?i++:l=n.charCodeAt(0)-48;else l=0;n=c}while(n);ji(e)}function Kc(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Kc(n),Fu(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function _g(t,e,n,l){for(;t.nodeType===1;){var i=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!l&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(l){if(!t[Xl])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(c=t.getAttribute("rel"),c==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(c!==i.rel||t.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||t.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||t.getAttribute("title")!==(i.title==null?null:i.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(c=t.getAttribute("src"),(c!==(i.src==null?null:i.src)||t.getAttribute("type")!==(i.type==null?null:i.type)||t.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&c&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var c=i.name==null?null:""+i.name;if(i.type==="hidden"&&t.getAttribute("name")===c)return t}else return t;if(t=fn(t.nextSibling),t===null)break}return null}function Dg(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=fn(t.nextSibling),t===null))return null;return t}function Jc(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function Bg(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var l=function(){e(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),t._reactRetry=l}}function fn(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var Wc=null;function Rh(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function zh(t,e,n){switch(e=uu(n),t){case"html":if(t=e.documentElement,!t)throw Error(o(452));return t;case"head":if(t=e.head,!t)throw Error(o(453));return t;case"body":if(t=e.body,!t)throw Error(o(454));return t;default:throw Error(o(451))}}function _i(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Fu(t)}var tn=new Map,_h=new Set;function ou(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Gn=Z.d;Z.d={f:Ng,r:Ug,D:wg,C:$g,L:Hg,m:jg,X:qg,S:Lg,M:Yg};function Ng(){var t=Gn.f(),e=Ir();return t||e}function Ug(t){var e=Ka(t);e!==null&&e.tag===5&&e.type==="form"?Wd(e):Gn.r(t)}var Rl=typeof document>"u"?null:document;function Dh(t,e,n){var l=Rl;if(l&&typeof e=="string"&&e){var i=Ze(e);i='link[rel="'+t+'"][href="'+i+'"]',typeof n=="string"&&(i+='[crossorigin="'+n+'"]'),_h.has(i)||(_h.add(i),t={rel:t,crossOrigin:n,href:e},l.querySelector(i)===null&&(e=l.createElement("link"),pe(e,"link",t),ue(e),l.head.appendChild(e)))}}function wg(t){Gn.D(t),Dh("dns-prefetch",t,null)}function $g(t,e){Gn.C(t,e),Dh("preconnect",t,e)}function Hg(t,e,n){Gn.L(t,e,n);var l=Rl;if(l&&t&&e){var i='link[rel="preload"][as="'+Ze(e)+'"]';e==="image"&&n&&n.imageSrcSet?(i+='[imagesrcset="'+Ze(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(i+='[imagesizes="'+Ze(n.imageSizes)+'"]')):i+='[href="'+Ze(t)+'"]';var c=i;switch(e){case"style":c=zl(t);break;case"script":c=_l(t)}tn.has(c)||(t=S({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),tn.set(c,t),l.querySelector(i)!==null||e==="style"&&l.querySelector(Di(c))||e==="script"&&l.querySelector(Bi(c))||(e=l.createElement("link"),pe(e,"link",t),ue(e),l.head.appendChild(e)))}}function jg(t,e){Gn.m(t,e);var n=Rl;if(n&&t){var l=e&&typeof e.as=="string"?e.as:"script",i='link[rel="modulepreload"][as="'+Ze(l)+'"][href="'+Ze(t)+'"]',c=i;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=_l(t)}if(!tn.has(c)&&(t=S({rel:"modulepreload",href:t},e),tn.set(c,t),n.querySelector(i)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Bi(c)))return}l=n.createElement("link"),pe(l,"link",t),ue(l),n.head.appendChild(l)}}}function Lg(t,e,n){Gn.S(t,e,n);var l=Rl;if(l&&t){var i=Ja(l).hoistableStyles,c=zl(t);e=e||"default";var d=i.get(c);if(!d){var h={loading:0,preload:null};if(d=l.querySelector(Di(c)))h.loading=5;else{t=S({rel:"stylesheet",href:t,"data-precedence":e},n),(n=tn.get(c))&&Pc(t,n);var b=d=l.createElement("link");ue(b),pe(b,"link",t),b._p=new Promise(function(_,H){b.onload=_,b.onerror=H}),b.addEventListener("load",function(){h.loading|=1}),b.addEventListener("error",function(){h.loading|=2}),h.loading|=4,cu(d,e,l)}d={type:"stylesheet",instance:d,count:1,state:h},i.set(c,d)}}}function qg(t,e){Gn.X(t,e);var n=Rl;if(n&&t){var l=Ja(n).hoistableScripts,i=_l(t),c=l.get(i);c||(c=n.querySelector(Bi(i)),c||(t=S({src:t,async:!0},e),(e=tn.get(i))&&Fc(t,e),c=n.createElement("script"),ue(c),pe(c,"link",t),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},l.set(i,c))}}function Yg(t,e){Gn.M(t,e);var n=Rl;if(n&&t){var l=Ja(n).hoistableScripts,i=_l(t),c=l.get(i);c||(c=n.querySelector(Bi(i)),c||(t=S({src:t,async:!0,type:"module"},e),(e=tn.get(i))&&Fc(t,e),c=n.createElement("script"),ue(c),pe(c,"link",t),n.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},l.set(i,c))}}function Bh(t,e,n,l){var i=(i=ft.current)?ou(i):null;if(!i)throw Error(o(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=zl(n.href),n=Ja(i).hoistableStyles,l=n.get(e),l||(l={type:"style",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=zl(n.href);var c=Ja(i).hoistableStyles,d=c.get(t);if(d||(i=i.ownerDocument||i,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(t,d),(c=i.querySelector(Di(t)))&&!c._p&&(d.instance=c,d.state.loading=5),tn.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},tn.set(t,n),c||kg(i,t,n,d.state))),e&&l===null)throw Error(o(528,""));return d}if(e&&l!==null)throw Error(o(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=_l(n),n=Ja(i).hoistableScripts,l=n.get(e),l||(l={type:"script",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(o(444,t))}}function zl(t){return'href="'+Ze(t)+'"'}function Di(t){return'link[rel="stylesheet"]['+t+"]"}function Nh(t){return S({},t,{"data-precedence":t.precedence,precedence:null})}function kg(t,e,n,l){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?l.loading=1:(e=t.createElement("link"),l.preload=e,e.addEventListener("load",function(){return l.loading|=1}),e.addEventListener("error",function(){return l.loading|=2}),pe(e,"link",n),ue(e),t.head.appendChild(e))}function _l(t){return'[src="'+Ze(t)+'"]'}function Bi(t){return"script[async]"+t}function Uh(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var l=t.querySelector('style[data-href~="'+Ze(n.href)+'"]');if(l)return e.instance=l,ue(l),l;var i=S({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(t.ownerDocument||t).createElement("style"),ue(l),pe(l,"style",i),cu(l,n.precedence,t),e.instance=l;case"stylesheet":i=zl(n.href);var c=t.querySelector(Di(i));if(c)return e.state.loading|=4,e.instance=c,ue(c),c;l=Nh(n),(i=tn.get(i))&&Pc(l,i),c=(t.ownerDocument||t).createElement("link"),ue(c);var d=c;return d._p=new Promise(function(h,b){d.onload=h,d.onerror=b}),pe(c,"link",l),e.state.loading|=4,cu(c,n.precedence,t),e.instance=c;case"script":return c=_l(n.src),(i=t.querySelector(Bi(c)))?(e.instance=i,ue(i),i):(l=n,(i=tn.get(c))&&(l=S({},n),Fc(l,i)),t=t.ownerDocument||t,i=t.createElement("script"),ue(i),pe(i,"link",l),t.head.appendChild(i),e.instance=i);case"void":return null;default:throw Error(o(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(l=e.instance,e.state.loading|=4,cu(l,n.precedence,t));return e.instance}function cu(t,e,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=l.length?l[l.length-1]:null,c=i,d=0;d<l.length;d++){var h=l[d];if(h.dataset.precedence===e)c=h;else if(c!==i)break}c?c.parentNode.insertBefore(t,c.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function Pc(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function Fc(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var su=null;function wh(t,e,n){if(su===null){var l=new Map,i=su=new Map;i.set(n,l)}else i=su,l=i.get(n),l||(l=new Map,i.set(n,l));if(l.has(t))return l;for(l.set(t,null),n=n.getElementsByTagName(t),i=0;i<n.length;i++){var c=n[i];if(!(c[Xl]||c[Se]||t==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var d=c.getAttribute(e)||"";d=t+d;var h=l.get(d);h?h.push(c):l.set(d,[c])}}return l}function $h(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function Gg(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Hh(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Ni=null;function Vg(){}function Xg(t,e,n){if(Ni===null)throw Error(o(475));var l=Ni;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var i=zl(n.href),c=t.querySelector(Di(i));if(c){t=c._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(l.count++,l=fu.bind(l),t.then(l,l)),e.state.loading|=4,e.instance=c,ue(c);return}c=t.ownerDocument||t,n=Nh(n),(i=tn.get(i))&&Pc(n,i),c=c.createElement("link"),ue(c);var d=c;d._p=new Promise(function(h,b){d.onload=h,d.onerror=b}),pe(c,"link",n),e.instance=c}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(l.count++,e=fu.bind(l),t.addEventListener("load",e),t.addEventListener("error",e))}}function Qg(){if(Ni===null)throw Error(o(475));var t=Ni;return t.stylesheets&&t.count===0&&Ic(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&Ic(t,t.stylesheets),t.unsuspend){var l=t.unsuspend;t.unsuspend=null,l()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function fu(){if(this.count--,this.count===0){if(this.stylesheets)Ic(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var du=null;function Ic(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,du=new Map,e.forEach(Zg,t),du=null,fu.call(t))}function Zg(t,e){if(!(e.state.loading&4)){var n=du.get(t);if(n)var l=n.get(null);else{n=new Map,du.set(t,n);for(var i=t.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<i.length;c++){var d=i[c];(d.nodeName==="LINK"||d.getAttribute("media")!=="not all")&&(n.set(d.dataset.precedence,d),l=d)}l&&n.set(null,l)}i=e.instance,d=i.getAttribute("data-precedence"),c=n.get(d)||l,c===l&&n.set(null,i),n.set(d,i),this.count++,l=fu.bind(this),i.addEventListener("load",l),i.addEventListener("error",l),c?c.parentNode.insertBefore(i,c.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(i,t.firstChild)),e.state.loading|=4}}var Ui={$$typeof:V,Provider:null,Consumer:null,_currentValue:nt,_currentValue2:nt,_threadCount:0};function Kg(t,e,n,l,i,c,d,h){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Ku(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ku(0),this.hiddenUpdates=Ku(null),this.identifierPrefix=l,this.onUncaughtError=i,this.onCaughtError=c,this.onRecoverableError=d,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=h,this.incompleteTransitions=new Map}function jh(t,e,n,l,i,c,d,h,b,_,H,k){return t=new Kg(t,e,n,d,h,b,_,k),e=1,c===!0&&(e|=24),c=we(3,null,null,e),t.current=c,c.stateNode=t,e=Uo(),e.refCount++,t.pooledCache=e,e.refCount++,c.memoizedState={element:l,isDehydrated:n,cache:e},jo(c),t}function Lh(t){return t?(t=ul,t):ul}function qh(t,e,n,l,i,c){i=Lh(i),l.context===null?l.context=i:l.pendingContext=i,l=ea(e),l.payload={element:n},c=c===void 0?null:c,c!==null&&(l.callback=c),n=na(t,l,e),n!==null&&(qe(n,t,e),si(n,t,e))}function Yh(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function ts(t,e){Yh(t,e),(t=t.alternate)&&Yh(t,e)}function kh(t){if(t.tag===13){var e=rl(t,67108864);e!==null&&qe(e,t,67108864),ts(t,67108864)}}var pu=!0;function Jg(t,e,n,l){var i=N.T;N.T=null;var c=Z.p;try{Z.p=2,es(t,e,n,l)}finally{Z.p=c,N.T=i}}function Wg(t,e,n,l){var i=N.T;N.T=null;var c=Z.p;try{Z.p=8,es(t,e,n,l)}finally{Z.p=c,N.T=i}}function es(t,e,n,l){if(pu){var i=ns(l);if(i===null)kc(t,e,l,hu,n),Vh(t,l);else if(Fg(i,t,e,n,l))l.stopPropagation();else if(Vh(t,l),e&4&&-1<Pg.indexOf(t)){for(;i!==null;){var c=Ka(i);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var d=Rn(c.pendingLanes);if(d!==0){var h=c;for(h.pendingLanes|=2,h.entangledLanes|=2;d;){var b=1<<31-ge(d);h.entanglements[1]|=b,d&=~b}bn(c),(_t&6)===0&&(Pr=re()+500,Mi(0))}}break;case 13:h=rl(c,2),h!==null&&qe(h,c,2),Ir(),ts(c,2)}if(c=ns(l),c===null&&kc(t,e,l,hu,n),c===i)break;i=c}i!==null&&l.stopPropagation()}else kc(t,e,l,null,n)}}function ns(t){return t=ro(t),as(t)}var hu=null;function as(t){if(hu=null,t=Za(t),t!==null){var e=f(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=p(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return hu=t,null}function Gh(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(un()){case me:return 2;case hn:return 8;case Xe:case ht:return 32;case rr:return 268435456;default:return 32}default:return 32}}var ls=!1,ma=null,ya=null,ga=null,wi=new Map,$i=new Map,va=[],Pg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Vh(t,e){switch(t){case"focusin":case"focusout":ma=null;break;case"dragenter":case"dragleave":ya=null;break;case"mouseover":case"mouseout":ga=null;break;case"pointerover":case"pointerout":wi.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":$i.delete(e.pointerId)}}function Hi(t,e,n,l,i,c){return t===null||t.nativeEvent!==c?(t={blockedOn:e,domEventName:n,eventSystemFlags:l,nativeEvent:c,targetContainers:[i]},e!==null&&(e=Ka(e),e!==null&&kh(e)),t):(t.eventSystemFlags|=l,e=t.targetContainers,i!==null&&e.indexOf(i)===-1&&e.push(i),t)}function Fg(t,e,n,l,i){switch(e){case"focusin":return ma=Hi(ma,t,e,n,l,i),!0;case"dragenter":return ya=Hi(ya,t,e,n,l,i),!0;case"mouseover":return ga=Hi(ga,t,e,n,l,i),!0;case"pointerover":var c=i.pointerId;return wi.set(c,Hi(wi.get(c)||null,t,e,n,l,i)),!0;case"gotpointercapture":return c=i.pointerId,$i.set(c,Hi($i.get(c)||null,t,e,n,l,i)),!0}return!1}function Xh(t){var e=Za(t.target);if(e!==null){var n=f(e);if(n!==null){if(e=n.tag,e===13){if(e=p(n),e!==null){t.blockedOn=e,X0(t.priority,function(){if(n.tag===13){var l=Le();l=Ju(l);var i=rl(n,l);i!==null&&qe(i,n,l),ts(n,l)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function mu(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=ns(t.nativeEvent);if(n===null){n=t.nativeEvent;var l=new n.constructor(n.type,n);io=l,n.target.dispatchEvent(l),io=null}else return e=Ka(n),e!==null&&kh(e),t.blockedOn=n,!1;e.shift()}return!0}function Qh(t,e,n){mu(t)&&n.delete(e)}function Ig(){ls=!1,ma!==null&&mu(ma)&&(ma=null),ya!==null&&mu(ya)&&(ya=null),ga!==null&&mu(ga)&&(ga=null),wi.forEach(Qh),$i.forEach(Qh)}function yu(t,e){t.blockedOn===e&&(t.blockedOn=null,ls||(ls=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Ig)))}var gu=null;function Zh(t){gu!==t&&(gu=t,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){gu===t&&(gu=null);for(var e=0;e<t.length;e+=3){var n=t[e],l=t[e+1],i=t[e+2];if(typeof l!="function"){if(as(l||n)===null)continue;break}var c=Ka(n);c!==null&&(t.splice(e,3),e-=3,ac(c,{pending:!0,data:i,method:n.method,action:l},l,i))}}))}function ji(t){function e(b){return yu(b,t)}ma!==null&&yu(ma,t),ya!==null&&yu(ya,t),ga!==null&&yu(ga,t),wi.forEach(e),$i.forEach(e);for(var n=0;n<va.length;n++){var l=va[n];l.blockedOn===t&&(l.blockedOn=null)}for(;0<va.length&&(n=va[0],n.blockedOn===null);)Xh(n),n.blockedOn===null&&va.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var i=n[l],c=n[l+1],d=i[Oe]||null;if(typeof c=="function")d||Zh(n);else if(d){var h=null;if(c&&c.hasAttribute("formAction")){if(i=c,d=c[Oe]||null)h=d.formAction;else if(as(i)!==null)continue}else h=d.action;typeof h=="function"?n[l+1]=h:(n.splice(l,3),l-=3),Zh(n)}}}function is(t){this._internalRoot=t}vu.prototype.render=is.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(o(409));var n=e.current,l=Le();qh(n,l,t,e,null,null)},vu.prototype.unmount=is.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;qh(t.current,2,null,t,null,null),Ir(),e[Qa]=null}};function vu(t){this._internalRoot=t}vu.prototype.unstable_scheduleHydration=function(t){if(t){var e=sf();t={blockedOn:null,target:t,priority:e};for(var n=0;n<va.length&&e!==0&&e<va[n].priority;n++);va.splice(n,0,t),n===0&&Xh(t)}};var Kh=r.version;if(Kh!=="19.1.0")throw Error(o(527,Kh,"19.1.0"));Z.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(o(188)):(t=Object.keys(t).join(","),Error(o(268,t)));return t=v(e),t=t!==null?y(t):null,t=t===null?null:t.stateNode,t};var tv={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:N,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var bu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!bu.isDisabled&&bu.supportsFiber)try{Wn=bu.inject(tv),ye=bu}catch{}}return Yi.createRoot=function(t,e){if(!s(t))throw Error(o(299));var n=!1,l="",i=sp,c=fp,d=dp,h=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(l=e.identifierPrefix),e.onUncaughtError!==void 0&&(i=e.onUncaughtError),e.onCaughtError!==void 0&&(c=e.onCaughtError),e.onRecoverableError!==void 0&&(d=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(h=e.unstable_transitionCallbacks)),e=jh(t,1,!1,null,null,n,l,i,c,d,h,null),t[Qa]=e.current,Yc(t),new is(e)},Yi.hydrateRoot=function(t,e,n){if(!s(t))throw Error(o(299));var l=!1,i="",c=sp,d=fp,h=dp,b=null,_=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onUncaughtError!==void 0&&(c=n.onUncaughtError),n.onCaughtError!==void 0&&(d=n.onCaughtError),n.onRecoverableError!==void 0&&(h=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(b=n.unstable_transitionCallbacks),n.formState!==void 0&&(_=n.formState)),e=jh(t,1,!0,e,n??null,l,i,c,d,h,b,_),e.context=Lh(null),n=e.current,l=Le(),l=Ju(l),i=ea(l),i.callback=null,na(n,i,l),n=l,e.current.lanes=n,Vl(e,n),bn(e),t[Qa]=e.current,Yc(t),new vu(e)},Yi.version="19.1.0",Yi}var lm;function pv(){if(lm)return us.exports;lm=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(r){console.error(r)}}return a(),us.exports=dv(),us.exports}var hv=pv();const TC=Km(hv);var $=Us();const Hl=Km($),Ss=lv({__proto__:null,default:Hl},[$]),Zi={black:"#000",white:"#fff"},Dl={300:"#e57373",400:"#ef5350",500:"#f44336",700:"#d32f2f",800:"#c62828"},Bl={50:"#f3e5f5",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",700:"#7b1fa2"},Nl={50:"#e3f2fd",200:"#90caf9",400:"#42a5f5",700:"#1976d2",800:"#1565c0"},Ul={300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",700:"#0288d1",900:"#01579b"},wl={300:"#81c784",400:"#66bb6a",500:"#4caf50",700:"#388e3c",800:"#2e7d32",900:"#1b5e20"},ki={300:"#ffb74d",400:"#ffa726",500:"#ff9800",700:"#f57c00",900:"#e65100"},mv={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"};function Ga(a,...r){const u=new URL(`https://mui.com/production-error/?code=${a}`);return r.forEach(o=>u.searchParams.append("args[]",o)),`Minified MUI error #${a}; visit ${u} for the full message.`}const En="$$material";function _u(){return _u=Object.assign?Object.assign.bind():function(a){for(var r=1;r<arguments.length;r++){var u=arguments[r];for(var o in u)({}).hasOwnProperty.call(u,o)&&(a[o]=u[o])}return a},_u.apply(null,arguments)}function yv(a){if(a.sheet)return a.sheet;for(var r=0;r<document.styleSheets.length;r++)if(document.styleSheets[r].ownerNode===a)return document.styleSheets[r]}function gv(a){var r=document.createElement("style");return r.setAttribute("data-emotion",a.key),a.nonce!==void 0&&r.setAttribute("nonce",a.nonce),r.appendChild(document.createTextNode("")),r.setAttribute("data-s",""),r}var vv=function(){function a(u){var o=this;this._insertTag=function(s){var f;o.tags.length===0?o.insertionPoint?f=o.insertionPoint.nextSibling:o.prepend?f=o.container.firstChild:f=o.before:f=o.tags[o.tags.length-1].nextSibling,o.container.insertBefore(s,f),o.tags.push(s)},this.isSpeedy=u.speedy===void 0?!0:u.speedy,this.tags=[],this.ctr=0,this.nonce=u.nonce,this.key=u.key,this.container=u.container,this.prepend=u.prepend,this.insertionPoint=u.insertionPoint,this.before=null}var r=a.prototype;return r.hydrate=function(o){o.forEach(this._insertTag)},r.insert=function(o){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(gv(this));var s=this.tags[this.tags.length-1];if(this.isSpeedy){var f=yv(s);try{f.insertRule(o,f.cssRules.length)}catch{}}else s.appendChild(document.createTextNode(o));this.ctr++},r.flush=function(){this.tags.forEach(function(o){var s;return(s=o.parentNode)==null?void 0:s.removeChild(o)}),this.tags=[],this.ctr=0},a}(),xe="-ms-",Du="-moz-",Ot="-webkit-",Jm="comm",ws="rule",$s="decl",bv="@import",Wm="@keyframes",Sv="@layer",Cv=Math.abs,Hu=String.fromCharCode,Tv=Object.assign;function xv(a,r){return he(a,0)^45?(((r<<2^he(a,0))<<2^he(a,1))<<2^he(a,2))<<2^he(a,3):0}function Pm(a){return a.trim()}function Ev(a,r){return(a=r.exec(a))?a[0]:a}function Mt(a,r,u){return a.replace(r,u)}function Cs(a,r){return a.indexOf(r)}function he(a,r){return a.charCodeAt(r)|0}function Ki(a,r,u){return a.slice(r,u)}function Cn(a){return a.length}function Hs(a){return a.length}function Su(a,r){return r.push(a),a}function Av(a,r){return a.map(r).join("")}var ju=1,ql=1,Fm=0,Be=0,ae=0,Yl="";function Lu(a,r,u,o,s,f,p){return{value:a,root:r,parent:u,type:o,props:s,children:f,line:ju,column:ql,length:p,return:""}}function Gi(a,r){return Tv(Lu("",null,null,"",null,null,0),a,{length:-a.length},r)}function Ov(){return ae}function Mv(){return ae=Be>0?he(Yl,--Be):0,ql--,ae===10&&(ql=1,ju--),ae}function ke(){return ae=Be<Fm?he(Yl,Be++):0,ql++,ae===10&&(ql=1,ju++),ae}function An(){return he(Yl,Be)}function Eu(){return Be}function Ii(a,r){return Ki(Yl,a,r)}function Ji(a){switch(a){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Im(a){return ju=ql=1,Fm=Cn(Yl=a),Be=0,[]}function t0(a){return Yl="",a}function Au(a){return Pm(Ii(Be-1,Ts(a===91?a+2:a===40?a+1:a)))}function Rv(a){for(;(ae=An())&&ae<33;)ke();return Ji(a)>2||Ji(ae)>3?"":" "}function zv(a,r){for(;--r&&ke()&&!(ae<48||ae>102||ae>57&&ae<65||ae>70&&ae<97););return Ii(a,Eu()+(r<6&&An()==32&&ke()==32))}function Ts(a){for(;ke();)switch(ae){case a:return Be;case 34:case 39:a!==34&&a!==39&&Ts(ae);break;case 40:a===41&&Ts(a);break;case 92:ke();break}return Be}function _v(a,r){for(;ke()&&a+ae!==57;)if(a+ae===84&&An()===47)break;return"/*"+Ii(r,Be-1)+"*"+Hu(a===47?a:ke())}function Dv(a){for(;!Ji(An());)ke();return Ii(a,Be)}function Bv(a){return t0(Ou("",null,null,null,[""],a=Im(a),0,[0],a))}function Ou(a,r,u,o,s,f,p,m,v){for(var y=0,S=0,E=p,A=0,U=0,M=0,T=1,G=1,X=1,W=0,V="",j=s,R=f,Q=o,F=V;G;)switch(M=W,W=ke()){case 40:if(M!=108&&he(F,E-1)==58){Cs(F+=Mt(Au(W),"&","&\f"),"&\f")!=-1&&(X=-1);break}case 34:case 39:case 91:F+=Au(W);break;case 9:case 10:case 13:case 32:F+=Rv(M);break;case 92:F+=zv(Eu()-1,7);continue;case 47:switch(An()){case 42:case 47:Su(Nv(_v(ke(),Eu()),r,u),v);break;default:F+="/"}break;case 123*T:m[y++]=Cn(F)*X;case 125*T:case 59:case 0:switch(W){case 0:case 125:G=0;case 59+S:X==-1&&(F=Mt(F,/\f/g,"")),U>0&&Cn(F)-E&&Su(U>32?rm(F+";",o,u,E-1):rm(Mt(F," ","")+";",o,u,E-2),v);break;case 59:F+=";";default:if(Su(Q=im(F,r,u,y,S,s,m,V,j=[],R=[],E),f),W===123)if(S===0)Ou(F,r,Q,Q,j,f,E,m,R);else switch(A===99&&he(F,3)===110?100:A){case 100:case 108:case 109:case 115:Ou(a,Q,Q,o&&Su(im(a,Q,Q,0,0,s,m,V,s,j=[],E),R),s,R,E,m,o?j:R);break;default:Ou(F,Q,Q,Q,[""],R,0,m,R)}}y=S=U=0,T=X=1,V=F="",E=p;break;case 58:E=1+Cn(F),U=M;default:if(T<1){if(W==123)--T;else if(W==125&&T++==0&&Mv()==125)continue}switch(F+=Hu(W),W*T){case 38:X=S>0?1:(F+="\f",-1);break;case 44:m[y++]=(Cn(F)-1)*X,X=1;break;case 64:An()===45&&(F+=Au(ke())),A=An(),S=E=Cn(V=F+=Dv(Eu())),W++;break;case 45:M===45&&Cn(F)==2&&(T=0)}}return f}function im(a,r,u,o,s,f,p,m,v,y,S){for(var E=s-1,A=s===0?f:[""],U=Hs(A),M=0,T=0,G=0;M<o;++M)for(var X=0,W=Ki(a,E+1,E=Cv(T=p[M])),V=a;X<U;++X)(V=Pm(T>0?A[X]+" "+W:Mt(W,/&\f/g,A[X])))&&(v[G++]=V);return Lu(a,r,u,s===0?ws:m,v,y,S)}function Nv(a,r,u){return Lu(a,r,u,Jm,Hu(Ov()),Ki(a,2,-2),0)}function rm(a,r,u,o){return Lu(a,r,u,$s,Ki(a,0,o),Ki(a,o+1,-1),o)}function jl(a,r){for(var u="",o=Hs(a),s=0;s<o;s++)u+=r(a[s],s,a,r)||"";return u}function Uv(a,r,u,o){switch(a.type){case Sv:if(a.children.length)break;case bv:case $s:return a.return=a.return||a.value;case Jm:return"";case Wm:return a.return=a.value+"{"+jl(a.children,o)+"}";case ws:a.value=a.props.join(",")}return Cn(u=jl(a.children,o))?a.return=a.value+"{"+u+"}":""}function wv(a){var r=Hs(a);return function(u,o,s,f){for(var p="",m=0;m<r;m++)p+=a[m](u,o,s,f)||"";return p}}function $v(a){return function(r){r.root||(r=r.return)&&a(r)}}function e0(a){var r=Object.create(null);return function(u){return r[u]===void 0&&(r[u]=a(u)),r[u]}}var Hv=function(r,u,o){for(var s=0,f=0;s=f,f=An(),s===38&&f===12&&(u[o]=1),!Ji(f);)ke();return Ii(r,Be)},jv=function(r,u){var o=-1,s=44;do switch(Ji(s)){case 0:s===38&&An()===12&&(u[o]=1),r[o]+=Hv(Be-1,u,o);break;case 2:r[o]+=Au(s);break;case 4:if(s===44){r[++o]=An()===58?"&\f":"",u[o]=r[o].length;break}default:r[o]+=Hu(s)}while(s=ke());return r},Lv=function(r,u){return t0(jv(Im(r),u))},um=new WeakMap,qv=function(r){if(!(r.type!=="rule"||!r.parent||r.length<1)){for(var u=r.value,o=r.parent,s=r.column===o.column&&r.line===o.line;o.type!=="rule";)if(o=o.parent,!o)return;if(!(r.props.length===1&&u.charCodeAt(0)!==58&&!um.get(o))&&!s){um.set(r,!0);for(var f=[],p=Lv(u,f),m=o.props,v=0,y=0;v<p.length;v++)for(var S=0;S<m.length;S++,y++)r.props[y]=f[v]?p[v].replace(/&\f/g,m[S]):m[S]+" "+p[v]}}},Yv=function(r){if(r.type==="decl"){var u=r.value;u.charCodeAt(0)===108&&u.charCodeAt(2)===98&&(r.return="",r.value="")}};function n0(a,r){switch(xv(a,r)){case 5103:return Ot+"print-"+a+a;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return Ot+a+a;case 5349:case 4246:case 4810:case 6968:case 2756:return Ot+a+Du+a+xe+a+a;case 6828:case 4268:return Ot+a+xe+a+a;case 6165:return Ot+a+xe+"flex-"+a+a;case 5187:return Ot+a+Mt(a,/(\w+).+(:[^]+)/,Ot+"box-$1$2"+xe+"flex-$1$2")+a;case 5443:return Ot+a+xe+"flex-item-"+Mt(a,/flex-|-self/,"")+a;case 4675:return Ot+a+xe+"flex-line-pack"+Mt(a,/align-content|flex-|-self/,"")+a;case 5548:return Ot+a+xe+Mt(a,"shrink","negative")+a;case 5292:return Ot+a+xe+Mt(a,"basis","preferred-size")+a;case 6060:return Ot+"box-"+Mt(a,"-grow","")+Ot+a+xe+Mt(a,"grow","positive")+a;case 4554:return Ot+Mt(a,/([^-])(transform)/g,"$1"+Ot+"$2")+a;case 6187:return Mt(Mt(Mt(a,/(zoom-|grab)/,Ot+"$1"),/(image-set)/,Ot+"$1"),a,"")+a;case 5495:case 3959:return Mt(a,/(image-set\([^]*)/,Ot+"$1$`$1");case 4968:return Mt(Mt(a,/(.+:)(flex-)?(.*)/,Ot+"box-pack:$3"+xe+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+Ot+a+a;case 4095:case 3583:case 4068:case 2532:return Mt(a,/(.+)-inline(.+)/,Ot+"$1$2")+a;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(Cn(a)-1-r>6)switch(he(a,r+1)){case 109:if(he(a,r+4)!==45)break;case 102:return Mt(a,/(.+:)(.+)-([^]+)/,"$1"+Ot+"$2-$3$1"+Du+(he(a,r+3)==108?"$3":"$2-$3"))+a;case 115:return~Cs(a,"stretch")?n0(Mt(a,"stretch","fill-available"),r)+a:a}break;case 4949:if(he(a,r+1)!==115)break;case 6444:switch(he(a,Cn(a)-3-(~Cs(a,"!important")&&10))){case 107:return Mt(a,":",":"+Ot)+a;case 101:return Mt(a,/(.+:)([^;!]+)(;|!.+)?/,"$1"+Ot+(he(a,14)===45?"inline-":"")+"box$3$1"+Ot+"$2$3$1"+xe+"$2box$3")+a}break;case 5936:switch(he(a,r+11)){case 114:return Ot+a+xe+Mt(a,/[svh]\w+-[tblr]{2}/,"tb")+a;case 108:return Ot+a+xe+Mt(a,/[svh]\w+-[tblr]{2}/,"tb-rl")+a;case 45:return Ot+a+xe+Mt(a,/[svh]\w+-[tblr]{2}/,"lr")+a}return Ot+a+xe+a+a}return a}var kv=function(r,u,o,s){if(r.length>-1&&!r.return)switch(r.type){case $s:r.return=n0(r.value,r.length);break;case Wm:return jl([Gi(r,{value:Mt(r.value,"@","@"+Ot)})],s);case ws:if(r.length)return Av(r.props,function(f){switch(Ev(f,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return jl([Gi(r,{props:[Mt(f,/:(read-\w+)/,":"+Du+"$1")]})],s);case"::placeholder":return jl([Gi(r,{props:[Mt(f,/:(plac\w+)/,":"+Ot+"input-$1")]}),Gi(r,{props:[Mt(f,/:(plac\w+)/,":"+Du+"$1")]}),Gi(r,{props:[Mt(f,/:(plac\w+)/,xe+"input-$1")]})],s)}return""})}},Gv=[kv],Vv=function(r){var u=r.key;if(u==="css"){var o=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(o,function(T){var G=T.getAttribute("data-emotion");G.indexOf(" ")!==-1&&(document.head.appendChild(T),T.setAttribute("data-s",""))})}var s=r.stylisPlugins||Gv,f={},p,m=[];p=r.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+u+' "]'),function(T){for(var G=T.getAttribute("data-emotion").split(" "),X=1;X<G.length;X++)f[G[X]]=!0;m.push(T)});var v,y=[qv,Yv];{var S,E=[Uv,$v(function(T){S.insert(T)})],A=wv(y.concat(s,E)),U=function(G){return jl(Bv(G),A)};v=function(G,X,W,V){S=W,U(G?G+"{"+X.styles+"}":X.styles),V&&(M.inserted[X.name]=!0)}}var M={key:u,sheet:new vv({key:u,container:p,nonce:r.nonce,speedy:r.speedy,prepend:r.prepend,insertionPoint:r.insertionPoint}),nonce:r.nonce,inserted:f,registered:{},insert:v};return M.sheet.hydrate(m),M},ds={exports:{}},Rt={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var om;function Xv(){if(om)return Rt;om=1;var a=typeof Symbol=="function"&&Symbol.for,r=a?Symbol.for("react.element"):60103,u=a?Symbol.for("react.portal"):60106,o=a?Symbol.for("react.fragment"):60107,s=a?Symbol.for("react.strict_mode"):60108,f=a?Symbol.for("react.profiler"):60114,p=a?Symbol.for("react.provider"):60109,m=a?Symbol.for("react.context"):60110,v=a?Symbol.for("react.async_mode"):60111,y=a?Symbol.for("react.concurrent_mode"):60111,S=a?Symbol.for("react.forward_ref"):60112,E=a?Symbol.for("react.suspense"):60113,A=a?Symbol.for("react.suspense_list"):60120,U=a?Symbol.for("react.memo"):60115,M=a?Symbol.for("react.lazy"):60116,T=a?Symbol.for("react.block"):60121,G=a?Symbol.for("react.fundamental"):60117,X=a?Symbol.for("react.responder"):60118,W=a?Symbol.for("react.scope"):60119;function V(R){if(typeof R=="object"&&R!==null){var Q=R.$$typeof;switch(Q){case r:switch(R=R.type,R){case v:case y:case o:case f:case s:case E:return R;default:switch(R=R&&R.$$typeof,R){case m:case S:case M:case U:case p:return R;default:return Q}}case u:return Q}}}function j(R){return V(R)===y}return Rt.AsyncMode=v,Rt.ConcurrentMode=y,Rt.ContextConsumer=m,Rt.ContextProvider=p,Rt.Element=r,Rt.ForwardRef=S,Rt.Fragment=o,Rt.Lazy=M,Rt.Memo=U,Rt.Portal=u,Rt.Profiler=f,Rt.StrictMode=s,Rt.Suspense=E,Rt.isAsyncMode=function(R){return j(R)||V(R)===v},Rt.isConcurrentMode=j,Rt.isContextConsumer=function(R){return V(R)===m},Rt.isContextProvider=function(R){return V(R)===p},Rt.isElement=function(R){return typeof R=="object"&&R!==null&&R.$$typeof===r},Rt.isForwardRef=function(R){return V(R)===S},Rt.isFragment=function(R){return V(R)===o},Rt.isLazy=function(R){return V(R)===M},Rt.isMemo=function(R){return V(R)===U},Rt.isPortal=function(R){return V(R)===u},Rt.isProfiler=function(R){return V(R)===f},Rt.isStrictMode=function(R){return V(R)===s},Rt.isSuspense=function(R){return V(R)===E},Rt.isValidElementType=function(R){return typeof R=="string"||typeof R=="function"||R===o||R===y||R===f||R===s||R===E||R===A||typeof R=="object"&&R!==null&&(R.$$typeof===M||R.$$typeof===U||R.$$typeof===p||R.$$typeof===m||R.$$typeof===S||R.$$typeof===G||R.$$typeof===X||R.$$typeof===W||R.$$typeof===T)},Rt.typeOf=V,Rt}var cm;function Qv(){return cm||(cm=1,ds.exports=Xv()),ds.exports}var ps,sm;function Zv(){if(sm)return ps;sm=1;var a=Qv(),r={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},u={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},o={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},s={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},f={};f[a.ForwardRef]=o,f[a.Memo]=s;function p(M){return a.isMemo(M)?s:f[M.$$typeof]||r}var m=Object.defineProperty,v=Object.getOwnPropertyNames,y=Object.getOwnPropertySymbols,S=Object.getOwnPropertyDescriptor,E=Object.getPrototypeOf,A=Object.prototype;function U(M,T,G){if(typeof T!="string"){if(A){var X=E(T);X&&X!==A&&U(M,X,G)}var W=v(T);y&&(W=W.concat(y(T)));for(var V=p(M),j=p(T),R=0;R<W.length;++R){var Q=W[R];if(!u[Q]&&!(G&&G[Q])&&!(j&&j[Q])&&!(V&&V[Q])){var F=S(T,Q);try{m(M,Q,F)}catch{}}}}return M}return ps=U,ps}Zv();var Kv=!0;function a0(a,r,u){var o="";return u.split(" ").forEach(function(s){a[s]!==void 0?r.push(a[s]+";"):s&&(o+=s+" ")}),o}var js=function(r,u,o){var s=r.key+"-"+u.name;(o===!1||Kv===!1)&&r.registered[s]===void 0&&(r.registered[s]=u.styles)},Ls=function(r,u,o){js(r,u,o);var s=r.key+"-"+u.name;if(r.inserted[u.name]===void 0){var f=u;do r.insert(u===f?"."+s:"",f,r.sheet,!0),f=f.next;while(f!==void 0)}};function Jv(a){for(var r=0,u,o=0,s=a.length;s>=4;++o,s-=4)u=a.charCodeAt(o)&255|(a.charCodeAt(++o)&255)<<8|(a.charCodeAt(++o)&255)<<16|(a.charCodeAt(++o)&255)<<24,u=(u&65535)*1540483477+((u>>>16)*59797<<16),u^=u>>>24,r=(u&65535)*1540483477+((u>>>16)*59797<<16)^(r&65535)*1540483477+((r>>>16)*59797<<16);switch(s){case 3:r^=(a.charCodeAt(o+2)&255)<<16;case 2:r^=(a.charCodeAt(o+1)&255)<<8;case 1:r^=a.charCodeAt(o)&255,r=(r&65535)*1540483477+((r>>>16)*59797<<16)}return r^=r>>>13,r=(r&65535)*1540483477+((r>>>16)*59797<<16),((r^r>>>15)>>>0).toString(36)}var Wv={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},Pv=/[A-Z]|^ms/g,Fv=/_EMO_([^_]+?)_([^]*?)_EMO_/g,l0=function(r){return r.charCodeAt(1)===45},fm=function(r){return r!=null&&typeof r!="boolean"},hs=e0(function(a){return l0(a)?a:a.replace(Pv,"-$&").toLowerCase()}),dm=function(r,u){switch(r){case"animation":case"animationName":if(typeof u=="string")return u.replace(Fv,function(o,s,f){return Tn={name:s,styles:f,next:Tn},s})}return Wv[r]!==1&&!l0(r)&&typeof u=="number"&&u!==0?u+"px":u};function Wi(a,r,u){if(u==null)return"";var o=u;if(o.__emotion_styles!==void 0)return o;switch(typeof u){case"boolean":return"";case"object":{var s=u;if(s.anim===1)return Tn={name:s.name,styles:s.styles,next:Tn},s.name;var f=u;if(f.styles!==void 0){var p=f.next;if(p!==void 0)for(;p!==void 0;)Tn={name:p.name,styles:p.styles,next:Tn},p=p.next;var m=f.styles+";";return m}return Iv(a,r,u)}case"function":{if(a!==void 0){var v=Tn,y=u(a);return Tn=v,Wi(a,r,y)}break}}var S=u;if(r==null)return S;var E=r[S];return E!==void 0?E:S}function Iv(a,r,u){var o="";if(Array.isArray(u))for(var s=0;s<u.length;s++)o+=Wi(a,r,u[s])+";";else for(var f in u){var p=u[f];if(typeof p!="object"){var m=p;r!=null&&r[m]!==void 0?o+=f+"{"+r[m]+"}":fm(m)&&(o+=hs(f)+":"+dm(f,m)+";")}else if(Array.isArray(p)&&typeof p[0]=="string"&&(r==null||r[p[0]]===void 0))for(var v=0;v<p.length;v++)fm(p[v])&&(o+=hs(f)+":"+dm(f,p[v])+";");else{var y=Wi(a,r,p);switch(f){case"animation":case"animationName":{o+=hs(f)+":"+y+";";break}default:o+=f+"{"+y+"}"}}}return o}var pm=/label:\s*([^\s;{]+)\s*(;|$)/g,Tn;function tr(a,r,u){if(a.length===1&&typeof a[0]=="object"&&a[0]!==null&&a[0].styles!==void 0)return a[0];var o=!0,s="";Tn=void 0;var f=a[0];if(f==null||f.raw===void 0)o=!1,s+=Wi(u,r,f);else{var p=f;s+=p[0]}for(var m=1;m<a.length;m++)if(s+=Wi(u,r,a[m]),o){var v=f;s+=v[m]}pm.lastIndex=0;for(var y="",S;(S=pm.exec(s))!==null;)y+="-"+S[1];var E=Jv(s)+y;return{name:E,styles:s,next:Tn}}var t1=function(r){return r()},i0=Ss.useInsertionEffect?Ss.useInsertionEffect:!1,r0=i0||t1,hm=i0||$.useLayoutEffect,u0=$.createContext(typeof HTMLElement<"u"?Vv({key:"css"}):null);u0.Provider;var qs=function(r){return $.forwardRef(function(u,o){var s=$.useContext(u0);return r(u,s,o)})},er=$.createContext({}),Ys={}.hasOwnProperty,xs="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",e1=function(r,u){var o={};for(var s in u)Ys.call(u,s)&&(o[s]=u[s]);return o[xs]=r,o},n1=function(r){var u=r.cache,o=r.serialized,s=r.isStringTag;return js(u,o,s),r0(function(){return Ls(u,o,s)}),null},a1=qs(function(a,r,u){var o=a.css;typeof o=="string"&&r.registered[o]!==void 0&&(o=r.registered[o]);var s=a[xs],f=[o],p="";typeof a.className=="string"?p=a0(r.registered,f,a.className):a.className!=null&&(p=a.className+" ");var m=tr(f,void 0,$.useContext(er));p+=r.key+"-"+m.name;var v={};for(var y in a)Ys.call(a,y)&&y!=="css"&&y!==xs&&(v[y]=a[y]);return v.className=p,u&&(v.ref=u),$.createElement($.Fragment,null,$.createElement(n1,{cache:r,serialized:m,isStringTag:typeof s=="string"}),$.createElement(s,v))}),l1=a1,mm=function(r,u){var o=arguments;if(u==null||!Ys.call(u,"css"))return $.createElement.apply(void 0,o);var s=o.length,f=new Array(s);f[0]=l1,f[1]=e1(r,u);for(var p=2;p<s;p++)f[p]=o[p];return $.createElement.apply(null,f)};(function(a){var r;r||(r=a.JSX||(a.JSX={}))})(mm||(mm={}));var i1=qs(function(a,r){var u=a.styles,o=tr([u],void 0,$.useContext(er)),s=$.useRef();return hm(function(){var f=r.key+"-global",p=new r.sheet.constructor({key:f,nonce:r.sheet.nonce,container:r.sheet.container,speedy:r.sheet.isSpeedy}),m=!1,v=document.querySelector('style[data-emotion="'+f+" "+o.name+'"]');return r.sheet.tags.length&&(p.before=r.sheet.tags[0]),v!==null&&(m=!0,v.setAttribute("data-emotion",f),p.hydrate([v])),s.current=[p,m],function(){p.flush()}},[r]),hm(function(){var f=s.current,p=f[0],m=f[1];if(m){f[1]=!1;return}if(o.next!==void 0&&Ls(r,o.next,!0),p.tags.length){var v=p.tags[p.tags.length-1].nextElementSibling;p.before=v,p.flush()}r.insert("",o,p,!1)},[r,o.name]),null});function ks(){for(var a=arguments.length,r=new Array(a),u=0;u<a;u++)r[u]=arguments[u];return tr(r)}function nr(){var a=ks.apply(void 0,arguments),r="animation-"+a.name;return{name:r,styles:"@keyframes "+r+"{"+a.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}var r1=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,u1=e0(function(a){return r1.test(a)||a.charCodeAt(0)===111&&a.charCodeAt(1)===110&&a.charCodeAt(2)<91}),o1=u1,c1=function(r){return r!=="theme"},ym=function(r){return typeof r=="string"&&r.charCodeAt(0)>96?o1:c1},gm=function(r,u,o){var s;if(u){var f=u.shouldForwardProp;s=r.__emotion_forwardProp&&f?function(p){return r.__emotion_forwardProp(p)&&f(p)}:f}return typeof s!="function"&&o&&(s=r.__emotion_forwardProp),s},s1=function(r){var u=r.cache,o=r.serialized,s=r.isStringTag;return js(u,o,s),r0(function(){return Ls(u,o,s)}),null},f1=function a(r,u){var o=r.__emotion_real===r,s=o&&r.__emotion_base||r,f,p;u!==void 0&&(f=u.label,p=u.target);var m=gm(r,u,o),v=m||ym(s),y=!v("as");return function(){var S=arguments,E=o&&r.__emotion_styles!==void 0?r.__emotion_styles.slice(0):[];if(f!==void 0&&E.push("label:"+f+";"),S[0]==null||S[0].raw===void 0)E.push.apply(E,S);else{var A=S[0];E.push(A[0]);for(var U=S.length,M=1;M<U;M++)E.push(S[M],A[M])}var T=qs(function(G,X,W){var V=y&&G.as||s,j="",R=[],Q=G;if(G.theme==null){Q={};for(var F in G)Q[F]=G[F];Q.theme=$.useContext(er)}typeof G.className=="string"?j=a0(X.registered,R,G.className):G.className!=null&&(j=G.className+" ");var I=tr(E.concat(R),X.registered,Q);j+=X.key+"-"+I.name,p!==void 0&&(j+=" "+p);var et=y&&m===void 0?ym(V):v,g={};for(var Y in G)y&&Y==="as"||et(Y)&&(g[Y]=G[Y]);return g.className=j,W&&(g.ref=W),$.createElement($.Fragment,null,$.createElement(s1,{cache:X,serialized:I,isStringTag:typeof V=="string"}),$.createElement(V,g))});return T.displayName=f!==void 0?f:"Styled("+(typeof s=="string"?s:s.displayName||s.name||"Component")+")",T.defaultProps=r.defaultProps,T.__emotion_real=T,T.__emotion_base=s,T.__emotion_styles=E,T.__emotion_forwardProp=m,Object.defineProperty(T,"toString",{value:function(){return"."+p}}),T.withComponent=function(G,X){var W=a(G,_u({},u,X,{shouldForwardProp:gm(T,X,!0)}));return W.apply(void 0,E)},T}},d1=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"],Es=f1.bind(null);d1.forEach(function(a){Es[a]=Es(a)});function p1(a){return a==null||Object.keys(a).length===0}function o0(a){const{styles:r,defaultTheme:u={}}=a,o=typeof r=="function"?s=>r(p1(s)?u:s):r;return tt.jsx(i1,{styles:o})}function c0(a,r){return Es(a,r)}function h1(a,r){Array.isArray(a.__emotion_styles)&&(a.__emotion_styles=r(a.__emotion_styles))}const vm=[];function bm(a){return vm[0]=a,tr(vm)}var ms={exports:{}},wt={};/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sm;function m1(){if(Sm)return wt;Sm=1;var a=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),p=Symbol.for("react.context"),m=Symbol.for("react.forward_ref"),v=Symbol.for("react.suspense"),y=Symbol.for("react.suspense_list"),S=Symbol.for("react.memo"),E=Symbol.for("react.lazy"),A=Symbol.for("react.view_transition"),U=Symbol.for("react.client.reference");function M(T){if(typeof T=="object"&&T!==null){var G=T.$$typeof;switch(G){case a:switch(T=T.type,T){case u:case s:case o:case v:case y:case A:return T;default:switch(T=T&&T.$$typeof,T){case p:case m:case E:case S:return T;case f:return T;default:return G}}case r:return G}}}return wt.ContextConsumer=f,wt.ContextProvider=p,wt.Element=a,wt.ForwardRef=m,wt.Fragment=u,wt.Lazy=E,wt.Memo=S,wt.Portal=r,wt.Profiler=s,wt.StrictMode=o,wt.Suspense=v,wt.SuspenseList=y,wt.isContextConsumer=function(T){return M(T)===f},wt.isContextProvider=function(T){return M(T)===p},wt.isElement=function(T){return typeof T=="object"&&T!==null&&T.$$typeof===a},wt.isForwardRef=function(T){return M(T)===m},wt.isFragment=function(T){return M(T)===u},wt.isLazy=function(T){return M(T)===E},wt.isMemo=function(T){return M(T)===S},wt.isPortal=function(T){return M(T)===r},wt.isProfiler=function(T){return M(T)===s},wt.isStrictMode=function(T){return M(T)===o},wt.isSuspense=function(T){return M(T)===v},wt.isSuspenseList=function(T){return M(T)===y},wt.isValidElementType=function(T){return typeof T=="string"||typeof T=="function"||T===u||T===s||T===o||T===v||T===y||typeof T=="object"&&T!==null&&(T.$$typeof===E||T.$$typeof===S||T.$$typeof===p||T.$$typeof===f||T.$$typeof===m||T.$$typeof===U||T.getModuleId!==void 0)},wt.typeOf=M,wt}var Cm;function y1(){return Cm||(Cm=1,ms.exports=m1()),ms.exports}var s0=y1();function xn(a){if(typeof a!="object"||a===null)return!1;const r=Object.getPrototypeOf(a);return(r===null||r===Object.prototype||Object.getPrototypeOf(r)===null)&&!(Symbol.toStringTag in a)&&!(Symbol.iterator in a)}function f0(a){if($.isValidElement(a)||s0.isValidElementType(a)||!xn(a))return a;const r={};return Object.keys(a).forEach(u=>{r[u]=f0(a[u])}),r}function De(a,r,u={clone:!0}){const o=u.clone?{...a}:a;return xn(a)&&xn(r)&&Object.keys(r).forEach(s=>{$.isValidElement(r[s])||s0.isValidElementType(r[s])?o[s]=r[s]:xn(r[s])&&Object.prototype.hasOwnProperty.call(a,s)&&xn(a[s])?o[s]=De(a[s],r[s],u):u.clone?o[s]=xn(r[s])?f0(r[s]):r[s]:o[s]=r[s]}),o}const g1=a=>{const r=Object.keys(a).map(u=>({key:u,val:a[u]}))||[];return r.sort((u,o)=>u.val-o.val),r.reduce((u,o)=>({...u,[o.key]:o.val}),{})};function v1(a){const{values:r={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:u="px",step:o=5,...s}=a,f=g1(r),p=Object.keys(f);function m(A){return`@media (min-width:${typeof r[A]=="number"?r[A]:A}${u})`}function v(A){return`@media (max-width:${(typeof r[A]=="number"?r[A]:A)-o/100}${u})`}function y(A,U){const M=p.indexOf(U);return`@media (min-width:${typeof r[A]=="number"?r[A]:A}${u}) and (max-width:${(M!==-1&&typeof r[p[M]]=="number"?r[p[M]]:U)-o/100}${u})`}function S(A){return p.indexOf(A)+1<p.length?y(A,p[p.indexOf(A)+1]):m(A)}function E(A){const U=p.indexOf(A);return U===0?m(p[1]):U===p.length-1?v(p[U]):y(A,p[p.indexOf(A)+1]).replace("@media","@media not all and")}return{keys:p,values:f,up:m,down:v,between:y,only:S,not:E,unit:u,...s}}function b1(a,r){if(!a.containerQueries)return r;const u=Object.keys(r).filter(o=>o.startsWith("@container")).sort((o,s)=>{var p,m;const f=/min-width:\s*([0-9.]+)/;return+(((p=o.match(f))==null?void 0:p[1])||0)-+(((m=s.match(f))==null?void 0:m[1])||0)});return u.length?u.reduce((o,s)=>{const f=r[s];return delete o[s],o[s]=f,o},{...r}):r}function S1(a,r){return r==="@"||r.startsWith("@")&&(a.some(u=>r.startsWith(`@${u}`))||!!r.match(/^@\d/))}function C1(a,r){const u=r.match(/^@([^/]+)?\/?(.+)?$/);if(!u)return null;const[,o,s]=u,f=Number.isNaN(+o)?o||0:+o;return a.containerQueries(s).up(f)}function T1(a){const r=(f,p)=>f.replace("@media",p?`@container ${p}`:"@container");function u(f,p){f.up=(...m)=>r(a.breakpoints.up(...m),p),f.down=(...m)=>r(a.breakpoints.down(...m),p),f.between=(...m)=>r(a.breakpoints.between(...m),p),f.only=(...m)=>r(a.breakpoints.only(...m),p),f.not=(...m)=>{const v=r(a.breakpoints.not(...m),p);return v.includes("not all and")?v.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):v}}const o={},s=f=>(u(o,f),o);return u(s),{...a,containerQueries:s}}const x1={borderRadius:4};function Qi(a,r){return r?De(a,r,{clone:!1}):a}const qu={xs:0,sm:600,md:900,lg:1200,xl:1536},Tm={keys:["xs","sm","md","lg","xl"],up:a=>`@media (min-width:${qu[a]}px)`},E1={containerQueries:a=>({up:r=>{let u=typeof r=="number"?r:qu[r]||r;return typeof u=="number"&&(u=`${u}px`),a?`@container ${a} (min-width:${u})`:`@container (min-width:${u})`}})};function Qn(a,r,u){const o=a.theme||{};if(Array.isArray(r)){const f=o.breakpoints||Tm;return r.reduce((p,m,v)=>(p[f.up(f.keys[v])]=u(r[v]),p),{})}if(typeof r=="object"){const f=o.breakpoints||Tm;return Object.keys(r).reduce((p,m)=>{if(S1(f.keys,m)){const v=C1(o.containerQueries?o:E1,m);v&&(p[v]=u(r[m],m))}else if(Object.keys(f.values||qu).includes(m)){const v=f.up(m);p[v]=u(r[m],m)}else{const v=m;p[v]=r[v]}return p},{})}return u(r)}function d0(a={}){var u;return((u=a.keys)==null?void 0:u.reduce((o,s)=>{const f=a.up(s);return o[f]={},o},{}))||{}}function p0(a,r){return a.reduce((u,o)=>{const s=u[o];return(!s||Object.keys(s).length===0)&&delete u[o],u},r)}function xC(a,...r){const u=d0(a),o=[u,...r].reduce((s,f)=>De(s,f),{});return p0(Object.keys(u),o)}function A1(a,r){if(typeof a!="object")return{};const u={},o=Object.keys(r);return Array.isArray(a)?o.forEach((s,f)=>{f<a.length&&(u[s]=!0)}):o.forEach(s=>{a[s]!=null&&(u[s]=!0)}),u}function EC({values:a,breakpoints:r,base:u}){const o=u||A1(a,r),s=Object.keys(o);if(s.length===0)return a;let f;return s.reduce((p,m,v)=>(Array.isArray(a)?(p[m]=a[v]!=null?a[v]:a[f],f=v):typeof a=="object"?(p[m]=a[m]!=null?a[m]:a[f],f=m):p[m]=a,p),{})}function rt(a){if(typeof a!="string")throw new Error(Ga(7));return a.charAt(0).toUpperCase()+a.slice(1)}function Yu(a,r,u=!0){if(!r||typeof r!="string")return null;if(a&&a.vars&&u){const o=`vars.${r}`.split(".").reduce((s,f)=>s&&s[f]?s[f]:null,a);if(o!=null)return o}return r.split(".").reduce((o,s)=>o&&o[s]!=null?o[s]:null,a)}function Bu(a,r,u,o=u){let s;return typeof a=="function"?s=a(u):Array.isArray(a)?s=a[u]||o:s=Yu(a,u)||o,r&&(s=r(s,o,a)),s}function It(a){const{prop:r,cssProperty:u=a.prop,themeKey:o,transform:s}=a,f=p=>{if(p[r]==null)return null;const m=p[r],v=p.theme,y=Yu(v,o)||{};return Qn(p,m,E=>{let A=Bu(y,s,E);return E===A&&typeof E=="string"&&(A=Bu(y,s,`${r}${E==="default"?"":rt(E)}`,E)),u===!1?A:{[u]:A}})};return f.propTypes={},f.filterProps=[r],f}function O1(a){const r={};return u=>(r[u]===void 0&&(r[u]=a(u)),r[u])}const M1={m:"margin",p:"padding"},R1={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},xm={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},z1=O1(a=>{if(a.length>2)if(xm[a])a=xm[a];else return[a];const[r,u]=a.split(""),o=M1[r],s=R1[u]||"";return Array.isArray(s)?s.map(f=>o+f):[o+s]}),Gs=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],Vs=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"];[...Gs,...Vs];function ar(a,r,u,o){const s=Yu(a,r,!0)??u;return typeof s=="number"||typeof s=="string"?f=>typeof f=="string"?f:typeof s=="string"?s.startsWith("var(")&&f===0?0:s.startsWith("var(")&&f===1?s:`calc(${f} * ${s})`:s*f:Array.isArray(s)?f=>{if(typeof f=="string")return f;const p=Math.abs(f),m=s[p];return f>=0?m:typeof m=="number"?-m:typeof m=="string"&&m.startsWith("var(")?`calc(-1 * ${m})`:`-${m}`}:typeof s=="function"?s:()=>{}}function Xs(a){return ar(a,"spacing",8)}function lr(a,r){return typeof r=="string"||r==null?r:a(r)}function _1(a,r){return u=>a.reduce((o,s)=>(o[s]=lr(r,u),o),{})}function D1(a,r,u,o){if(!r.includes(u))return null;const s=z1(u),f=_1(s,o),p=a[u];return Qn(a,p,f)}function h0(a,r){const u=Xs(a.theme);return Object.keys(a).map(o=>D1(a,r,o,u)).reduce(Qi,{})}function Jt(a){return h0(a,Gs)}Jt.propTypes={};Jt.filterProps=Gs;function Wt(a){return h0(a,Vs)}Wt.propTypes={};Wt.filterProps=Vs;function m0(a=8,r=Xs({spacing:a})){if(a.mui)return a;const u=(...o)=>(o.length===0?[1]:o).map(f=>{const p=r(f);return typeof p=="number"?`${p}px`:p}).join(" ");return u.mui=!0,u}function ku(...a){const r=a.reduce((o,s)=>(s.filterProps.forEach(f=>{o[f]=s}),o),{}),u=o=>Object.keys(o).reduce((s,f)=>r[f]?Qi(s,r[f](o)):s,{});return u.propTypes={},u.filterProps=a.reduce((o,s)=>o.concat(s.filterProps),[]),u}function nn(a){return typeof a!="number"?a:`${a}px solid`}function ln(a,r){return It({prop:a,themeKey:"borders",transform:r})}const B1=ln("border",nn),N1=ln("borderTop",nn),U1=ln("borderRight",nn),w1=ln("borderBottom",nn),$1=ln("borderLeft",nn),H1=ln("borderColor"),j1=ln("borderTopColor"),L1=ln("borderRightColor"),q1=ln("borderBottomColor"),Y1=ln("borderLeftColor"),k1=ln("outline",nn),G1=ln("outlineColor"),Gu=a=>{if(a.borderRadius!==void 0&&a.borderRadius!==null){const r=ar(a.theme,"shape.borderRadius",4),u=o=>({borderRadius:lr(r,o)});return Qn(a,a.borderRadius,u)}return null};Gu.propTypes={};Gu.filterProps=["borderRadius"];ku(B1,N1,U1,w1,$1,H1,j1,L1,q1,Y1,Gu,k1,G1);const Vu=a=>{if(a.gap!==void 0&&a.gap!==null){const r=ar(a.theme,"spacing",8),u=o=>({gap:lr(r,o)});return Qn(a,a.gap,u)}return null};Vu.propTypes={};Vu.filterProps=["gap"];const Xu=a=>{if(a.columnGap!==void 0&&a.columnGap!==null){const r=ar(a.theme,"spacing",8),u=o=>({columnGap:lr(r,o)});return Qn(a,a.columnGap,u)}return null};Xu.propTypes={};Xu.filterProps=["columnGap"];const Qu=a=>{if(a.rowGap!==void 0&&a.rowGap!==null){const r=ar(a.theme,"spacing",8),u=o=>({rowGap:lr(r,o)});return Qn(a,a.rowGap,u)}return null};Qu.propTypes={};Qu.filterProps=["rowGap"];const V1=It({prop:"gridColumn"}),X1=It({prop:"gridRow"}),Q1=It({prop:"gridAutoFlow"}),Z1=It({prop:"gridAutoColumns"}),K1=It({prop:"gridAutoRows"}),J1=It({prop:"gridTemplateColumns"}),W1=It({prop:"gridTemplateRows"}),P1=It({prop:"gridTemplateAreas"}),F1=It({prop:"gridArea"});ku(Vu,Xu,Qu,V1,X1,Q1,Z1,K1,J1,W1,P1,F1);function Ll(a,r){return r==="grey"?r:a}const I1=It({prop:"color",themeKey:"palette",transform:Ll}),tb=It({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:Ll}),eb=It({prop:"backgroundColor",themeKey:"palette",transform:Ll});ku(I1,tb,eb);function Ye(a){return a<=1&&a!==0?`${a*100}%`:a}const nb=It({prop:"width",transform:Ye}),Qs=a=>{if(a.maxWidth!==void 0&&a.maxWidth!==null){const r=u=>{var s,f,p,m,v;const o=((p=(f=(s=a.theme)==null?void 0:s.breakpoints)==null?void 0:f.values)==null?void 0:p[u])||qu[u];return o?((v=(m=a.theme)==null?void 0:m.breakpoints)==null?void 0:v.unit)!=="px"?{maxWidth:`${o}${a.theme.breakpoints.unit}`}:{maxWidth:o}:{maxWidth:Ye(u)}};return Qn(a,a.maxWidth,r)}return null};Qs.filterProps=["maxWidth"];const ab=It({prop:"minWidth",transform:Ye}),lb=It({prop:"height",transform:Ye}),ib=It({prop:"maxHeight",transform:Ye}),rb=It({prop:"minHeight",transform:Ye});It({prop:"size",cssProperty:"width",transform:Ye});It({prop:"size",cssProperty:"height",transform:Ye});const ub=It({prop:"boxSizing"});ku(nb,Qs,ab,lb,ib,rb,ub);const ir={border:{themeKey:"borders",transform:nn},borderTop:{themeKey:"borders",transform:nn},borderRight:{themeKey:"borders",transform:nn},borderBottom:{themeKey:"borders",transform:nn},borderLeft:{themeKey:"borders",transform:nn},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:nn},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:Gu},color:{themeKey:"palette",transform:Ll},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:Ll},backgroundColor:{themeKey:"palette",transform:Ll},p:{style:Wt},pt:{style:Wt},pr:{style:Wt},pb:{style:Wt},pl:{style:Wt},px:{style:Wt},py:{style:Wt},padding:{style:Wt},paddingTop:{style:Wt},paddingRight:{style:Wt},paddingBottom:{style:Wt},paddingLeft:{style:Wt},paddingX:{style:Wt},paddingY:{style:Wt},paddingInline:{style:Wt},paddingInlineStart:{style:Wt},paddingInlineEnd:{style:Wt},paddingBlock:{style:Wt},paddingBlockStart:{style:Wt},paddingBlockEnd:{style:Wt},m:{style:Jt},mt:{style:Jt},mr:{style:Jt},mb:{style:Jt},ml:{style:Jt},mx:{style:Jt},my:{style:Jt},margin:{style:Jt},marginTop:{style:Jt},marginRight:{style:Jt},marginBottom:{style:Jt},marginLeft:{style:Jt},marginX:{style:Jt},marginY:{style:Jt},marginInline:{style:Jt},marginInlineStart:{style:Jt},marginInlineEnd:{style:Jt},marginBlock:{style:Jt},marginBlockStart:{style:Jt},marginBlockEnd:{style:Jt},displayPrint:{cssProperty:!1,transform:a=>({"@media print":{display:a}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:Vu},rowGap:{style:Qu},columnGap:{style:Xu},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:Ye},maxWidth:{style:Qs},minWidth:{transform:Ye},height:{transform:Ye},maxHeight:{transform:Ye},minHeight:{transform:Ye},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}};function ob(...a){const r=a.reduce((o,s)=>o.concat(Object.keys(s)),[]),u=new Set(r);return a.every(o=>u.size===Object.keys(o).length)}function cb(a,r){return typeof a=="function"?a(r):a}function sb(){function a(u,o,s,f){const p={[u]:o,theme:s},m=f[u];if(!m)return{[u]:o};const{cssProperty:v=u,themeKey:y,transform:S,style:E}=m;if(o==null)return null;if(y==="typography"&&o==="inherit")return{[u]:o};const A=Yu(s,y)||{};return E?E(p):Qn(p,o,M=>{let T=Bu(A,S,M);return M===T&&typeof M=="string"&&(T=Bu(A,S,`${u}${M==="default"?"":rt(M)}`,M)),v===!1?T:{[v]:T}})}function r(u){const{sx:o,theme:s={}}=u||{};if(!o)return null;const f=s.unstable_sxConfig??ir;function p(m){let v=m;if(typeof m=="function")v=m(s);else if(typeof m!="object")return m;if(!v)return null;const y=d0(s.breakpoints),S=Object.keys(y);let E=y;return Object.keys(v).forEach(A=>{const U=cb(v[A],s);if(U!=null)if(typeof U=="object")if(f[A])E=Qi(E,a(A,U,s,f));else{const M=Qn({theme:s},U,T=>({[A]:T}));ob(M,U)?E[A]=r({sx:U,theme:s}):E=Qi(E,M)}else E=Qi(E,a(A,U,s,f))}),b1(s,p0(S,E))}return Array.isArray(o)?o.map(p):p(o)}return r}const Ca=sb();Ca.filterProps=["sx"];function fb(a,r){var o;const u=this;if(u.vars){if(!((o=u.colorSchemes)!=null&&o[a])||typeof u.getColorSchemeSelector!="function")return{};let s=u.getColorSchemeSelector(a);return s==="&"?r:((s.includes("data-")||s.includes("."))&&(s=`*:where(${s.replace(/\s*&$/,"")}) &`),{[s]:r})}return u.palette.mode===a?r:{}}function Zs(a={},...r){const{breakpoints:u={},palette:o={},spacing:s,shape:f={},...p}=a,m=v1(u),v=m0(s);let y=De({breakpoints:m,direction:"ltr",components:{},palette:{mode:"light",...o},spacing:v,shape:{...x1,...f}},p);return y=T1(y),y.applyStyles=fb,y=r.reduce((S,E)=>De(S,E),y),y.unstable_sxConfig={...ir,...p==null?void 0:p.unstable_sxConfig},y.unstable_sx=function(E){return Ca({sx:E,theme:this})},y}function db(a){return Object.keys(a).length===0}function y0(a=null){const r=$.useContext(er);return!r||db(r)?a:r}const pb=Zs();function Ks(a=pb){return y0(a)}function hb({styles:a,themeId:r,defaultTheme:u={}}){const o=Ks(u),s=typeof a=="function"?a(r&&o[r]||o):a;return tt.jsx(o0,{styles:s})}const mb=a=>{var o;const r={systemProps:{},otherProps:{}},u=((o=a==null?void 0:a.theme)==null?void 0:o.unstable_sxConfig)??ir;return Object.keys(a).forEach(s=>{u[s]?r.systemProps[s]=a[s]:r.otherProps[s]=a[s]}),r};function g0(a){const{sx:r,...u}=a,{systemProps:o,otherProps:s}=mb(u);let f;return Array.isArray(r)?f=[o,...r]:typeof r=="function"?f=(...p)=>{const m=r(...p);return xn(m)?{...o,...m}:o}:f={...o,...r},{...s,sx:f}}const Em=a=>a,yb=()=>{let a=Em;return{configure(r){a=r},generate(r){return a(r)},reset(){a=Em}}},v0=yb();function b0(a){var r,u,o="";if(typeof a=="string"||typeof a=="number")o+=a;else if(typeof a=="object")if(Array.isArray(a)){var s=a.length;for(r=0;r<s;r++)a[r]&&(u=b0(a[r]))&&(o&&(o+=" "),o+=u)}else for(u in a)a[u]&&(o&&(o+=" "),o+=u);return o}function Lt(){for(var a,r,u=0,o="",s=arguments.length;u<s;u++)(a=arguments[u])&&(r=b0(a))&&(o&&(o+=" "),o+=r);return o}function gb(a={}){const{themeId:r,defaultTheme:u,defaultClassName:o="MuiBox-root",generateClassName:s}=a,f=c0("div",{shouldForwardProp:m=>m!=="theme"&&m!=="sx"&&m!=="as"})(Ca);return $.forwardRef(function(v,y){const S=Ks(u),{className:E,component:A="div",...U}=g0(v);return tt.jsx(f,{as:A,ref:y,className:Lt(E,s?s(o):o),theme:r&&S[r]||S,...U})})}const vb={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function rn(a,r,u="Mui"){const o=vb[r];return o?`${u}-${o}`:`${v0.generate(a)}-${r}`}function Ge(a,r,u="Mui"){const o={};return r.forEach(s=>{o[s]=rn(a,s,u)}),o}function S0(a){const{variants:r,...u}=a,o={variants:r,style:bm(u),isProcessed:!0};return o.style===u||r&&r.forEach(s=>{typeof s.style!="function"&&(s.style=bm(s.style))}),o}const bb=Zs();function ys(a){return a!=="ownerState"&&a!=="theme"&&a!=="sx"&&a!=="as"}function Sb(a){return a?(r,u)=>u[a]:null}function Cb(a,r,u){a.theme=Eb(a.theme)?u:a.theme[r]||a.theme}function Mu(a,r){const u=typeof r=="function"?r(a):r;if(Array.isArray(u))return u.flatMap(o=>Mu(a,o));if(Array.isArray(u==null?void 0:u.variants)){let o;if(u.isProcessed)o=u.style;else{const{variants:s,...f}=u;o=f}return C0(a,u.variants,[o])}return u!=null&&u.isProcessed?u.style:u}function C0(a,r,u=[]){var s;let o;t:for(let f=0;f<r.length;f+=1){const p=r[f];if(typeof p.props=="function"){if(o??(o={...a,...a.ownerState,ownerState:a.ownerState}),!p.props(o))continue}else for(const m in p.props)if(a[m]!==p.props[m]&&((s=a.ownerState)==null?void 0:s[m])!==p.props[m])continue t;typeof p.style=="function"?(o??(o={...a,...a.ownerState,ownerState:a.ownerState}),u.push(p.style(o))):u.push(p.style)}return u}function Tb(a={}){const{themeId:r,defaultTheme:u=bb,rootShouldForwardProp:o=ys,slotShouldForwardProp:s=ys}=a;function f(m){Cb(m,r,u)}return(m,v={})=>{h1(m,R=>R.filter(Q=>Q!==Ca));const{name:y,slot:S,skipVariantsResolver:E,skipSx:A,overridesResolver:U=Sb(Ob(S)),...M}=v,T=E!==void 0?E:S&&S!=="Root"&&S!=="root"||!1,G=A||!1;let X=ys;S==="Root"||S==="root"?X=o:S?X=s:Ab(m)&&(X=void 0);const W=c0(m,{shouldForwardProp:X,label:xb(),...M}),V=R=>{if(typeof R=="function"&&R.__emotion_real!==R)return function(F){return Mu(F,R)};if(xn(R)){const Q=S0(R);return Q.variants?function(I){return Mu(I,Q)}:Q.style}return R},j=(...R)=>{const Q=[],F=R.map(V),I=[];if(Q.push(f),y&&U&&I.push(function(J){var N,Z;const pt=(Z=(N=J.theme.components)==null?void 0:N[y])==null?void 0:Z.styleOverrides;if(!pt)return null;const mt={};for(const nt in pt)mt[nt]=Mu(J,pt[nt]);return U(J,mt)}),y&&!T&&I.push(function(J){var mt,N;const it=J.theme,pt=(N=(mt=it==null?void 0:it.components)==null?void 0:mt[y])==null?void 0:N.variants;return pt?C0(J,pt):null}),G||I.push(Ca),Array.isArray(F[0])){const Y=F.shift(),J=new Array(Q.length).fill(""),it=new Array(I.length).fill("");let pt;pt=[...J,...Y,...it],pt.raw=[...J,...Y.raw,...it],Q.unshift(pt)}const et=[...Q,...F,...I],g=W(...et);return m.muiName&&(g.muiName=m.muiName),g};return W.withConfig&&(j.withConfig=W.withConfig),j}}function xb(a,r){return void 0}function Eb(a){for(const r in a)return!1;return!0}function Ab(a){return typeof a=="string"&&a.charCodeAt(0)>96}function Ob(a){return a&&a.charAt(0).toLowerCase()+a.slice(1)}function Nu(a,r){const u={...r};for(const o in a)if(Object.prototype.hasOwnProperty.call(a,o)){const s=o;if(s==="components"||s==="slots")u[s]={...a[s],...u[s]};else if(s==="componentsProps"||s==="slotProps"){const f=a[s],p=r[s];if(!p)u[s]=f||{};else if(!f)u[s]=p;else{u[s]={...p};for(const m in f)if(Object.prototype.hasOwnProperty.call(f,m)){const v=m;u[s][v]=Nu(f[v],p[v])}}}else u[s]===void 0&&(u[s]=a[s])}return u}const T0=typeof window<"u"?$.useLayoutEffect:$.useEffect;function Mb(a,r=Number.MIN_SAFE_INTEGER,u=Number.MAX_SAFE_INTEGER){return Math.max(r,Math.min(a,u))}function Js(a,r=0,u=1){return Mb(a,r,u)}function Rb(a){a=a.slice(1);const r=new RegExp(`.{1,${a.length>=6?2:1}}`,"g");let u=a.match(r);return u&&u[0].length===1&&(u=u.map(o=>o+o)),u?`rgb${u.length===4?"a":""}(${u.map((o,s)=>s<3?parseInt(o,16):Math.round(parseInt(o,16)/255*1e3)/1e3).join(", ")})`:""}function Ta(a){if(a.type)return a;if(a.charAt(0)==="#")return Ta(Rb(a));const r=a.indexOf("("),u=a.substring(0,r);if(!["rgb","rgba","hsl","hsla","color"].includes(u))throw new Error(Ga(9,a));let o=a.substring(r+1,a.length-1),s;if(u==="color"){if(o=o.split(" "),s=o.shift(),o.length===4&&o[3].charAt(0)==="/"&&(o[3]=o[3].slice(1)),!["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].includes(s))throw new Error(Ga(10,s))}else o=o.split(",");return o=o.map(f=>parseFloat(f)),{type:u,values:o,colorSpace:s}}const zb=a=>{const r=Ta(a);return r.values.slice(0,3).map((u,o)=>r.type.includes("hsl")&&o!==0?`${u}%`:u).join(" ")},Vi=(a,r)=>{try{return zb(a)}catch{return a}};function Zu(a){const{type:r,colorSpace:u}=a;let{values:o}=a;return r.includes("rgb")?o=o.map((s,f)=>f<3?parseInt(s,10):s):r.includes("hsl")&&(o[1]=`${o[1]}%`,o[2]=`${o[2]}%`),r.includes("color")?o=`${u} ${o.join(" ")}`:o=`${o.join(", ")}`,`${r}(${o})`}function x0(a){a=Ta(a);const{values:r}=a,u=r[0],o=r[1]/100,s=r[2]/100,f=o*Math.min(s,1-s),p=(y,S=(y+u/30)%12)=>s-f*Math.max(Math.min(S-3,9-S,1),-1);let m="rgb";const v=[Math.round(p(0)*255),Math.round(p(8)*255),Math.round(p(4)*255)];return a.type==="hsla"&&(m+="a",v.push(r[3])),Zu({type:m,values:v})}function As(a){a=Ta(a);let r=a.type==="hsl"||a.type==="hsla"?Ta(x0(a)).values:a.values;return r=r.map(u=>(a.type!=="color"&&(u/=255),u<=.03928?u/12.92:((u+.055)/1.055)**2.4)),Number((.2126*r[0]+.7152*r[1]+.0722*r[2]).toFixed(3))}function _b(a,r){const u=As(a),o=As(r);return(Math.max(u,o)+.05)/(Math.min(u,o)+.05)}function ne(a,r){return a=Ta(a),r=Js(r),(a.type==="rgb"||a.type==="hsl")&&(a.type+="a"),a.type==="color"?a.values[3]=`/${r}`:a.values[3]=r,Zu(a)}function Cu(a,r,u){try{return ne(a,r)}catch{return a}}function Pi(a,r){if(a=Ta(a),r=Js(r),a.type.includes("hsl"))a.values[2]*=1-r;else if(a.type.includes("rgb")||a.type.includes("color"))for(let u=0;u<3;u+=1)a.values[u]*=1-r;return Zu(a)}function Ht(a,r,u){try{return Pi(a,r)}catch{return a}}function Fi(a,r){if(a=Ta(a),r=Js(r),a.type.includes("hsl"))a.values[2]+=(100-a.values[2])*r;else if(a.type.includes("rgb"))for(let u=0;u<3;u+=1)a.values[u]+=(255-a.values[u])*r;else if(a.type.includes("color"))for(let u=0;u<3;u+=1)a.values[u]+=(1-a.values[u])*r;return Zu(a)}function jt(a,r,u){try{return Fi(a,r)}catch{return a}}function Db(a,r=.15){return As(a)>.5?Pi(a,r):Fi(a,r)}function Tu(a,r,u){try{return Db(a,r)}catch{return a}}const E0=$.createContext(null);function Ws(){return $.useContext(E0)}const Bb=typeof Symbol=="function"&&Symbol.for,Nb=Bb?Symbol.for("mui.nested"):"__THEME_NESTED__";function Ub(a,r){return typeof r=="function"?r(a):{...a,...r}}function wb(a){const{children:r,theme:u}=a,o=Ws(),s=$.useMemo(()=>{const f=o===null?{...u}:Ub(o,u);return f!=null&&(f[Nb]=o!==null),f},[u,o]);return tt.jsx(E0.Provider,{value:s,children:r})}const A0=$.createContext();function $b({value:a,...r}){return tt.jsx(A0.Provider,{value:a??!0,...r})}const AC=()=>$.useContext(A0)??!1,O0=$.createContext(void 0);function Hb({value:a,children:r}){return tt.jsx(O0.Provider,{value:a,children:r})}function jb(a){const{theme:r,name:u,props:o}=a;if(!r||!r.components||!r.components[u])return o;const s=r.components[u];return s.defaultProps?Nu(s.defaultProps,o):!s.styleOverrides&&!s.variants?Nu(s,o):o}function Lb({props:a,name:r}){const u=$.useContext(O0);return jb({props:a,name:r,theme:{components:u}})}const Am={};function Om(a,r,u,o=!1){return $.useMemo(()=>{const s=a&&r[a]||r;if(typeof u=="function"){const f=u(s),p=a?{...r,[a]:f}:f;return o?()=>p:p}return a?{...r,[a]:u}:{...r,...u}},[a,r,u,o])}function M0(a){const{children:r,theme:u,themeId:o}=a,s=y0(Am),f=Ws()||Am,p=Om(o,s,u),m=Om(o,f,u,!0),v=(o?p[o]:p).direction==="rtl";return tt.jsx(wb,{theme:m,children:tt.jsx(er.Provider,{value:p,children:tt.jsx($b,{value:v,children:tt.jsx(Hb,{value:o?p[o].components:p.components,children:r})})})})}const Mm={theme:void 0};function qb(a){let r,u;return function(s){let f=r;return(f===void 0||s.theme!==u)&&(Mm.theme=s.theme,f=S0(a(Mm)),r=f,u=s.theme),f}}const Ps="mode",Fs="color-scheme",Yb="data-color-scheme";function kb(a){const{defaultMode:r="system",defaultLightColorScheme:u="light",defaultDarkColorScheme:o="dark",modeStorageKey:s=Ps,colorSchemeStorageKey:f=Fs,attribute:p=Yb,colorSchemeNode:m="document.documentElement",nonce:v}=a||{};let y="",S=p;if(p==="class"&&(S=".%s"),p==="data"&&(S="[data-%s]"),S.startsWith(".")){const A=S.substring(1);y+=`${m}.classList.remove('${A}'.replace('%s', light), '${A}'.replace('%s', dark));
      ${m}.classList.add('${A}'.replace('%s', colorScheme));`}const E=S.match(/\[([^\]]+)\]/);if(E){const[A,U]=E[1].split("=");U||(y+=`${m}.removeAttribute('${A}'.replace('%s', light));
      ${m}.removeAttribute('${A}'.replace('%s', dark));`),y+=`
      ${m}.setAttribute('${A}'.replace('%s', colorScheme), ${U?`${U}.replace('%s', colorScheme)`:'""'});`}else y+=`${m}.setAttribute('${S}', colorScheme);`;return tt.jsx("script",{suppressHydrationWarning:!0,nonce:typeof window>"u"?v:"",dangerouslySetInnerHTML:{__html:`(function() {
try {
  let colorScheme = '';
  const mode = localStorage.getItem('${s}') || '${r}';
  const dark = localStorage.getItem('${f}-dark') || '${o}';
  const light = localStorage.getItem('${f}-light') || '${u}';
  if (mode === 'system') {
    // handle system mode
    const mql = window.matchMedia('(prefers-color-scheme: dark)');
    if (mql.matches) {
      colorScheme = dark
    } else {
      colorScheme = light
    }
  }
  if (mode === 'light') {
    colorScheme = light;
  }
  if (mode === 'dark') {
    colorScheme = dark;
  }
  if (colorScheme) {
    ${y}
  }
} catch(e){}})();`}},"mui-color-scheme-init")}function Gb(){}const Vb=({key:a,storageWindow:r})=>(!r&&typeof window<"u"&&(r=window),{get(u){if(typeof window>"u")return;if(!r)return u;let o;try{o=r.localStorage.getItem(a)}catch{}return o||u},set:u=>{if(r)try{r.localStorage.setItem(a,u)}catch{}},subscribe:u=>{if(!r)return Gb;const o=s=>{const f=s.newValue;s.key===a&&u(f)};return r.addEventListener("storage",o),()=>{r.removeEventListener("storage",o)}}});function gs(){}function Rm(a){if(typeof window<"u"&&typeof window.matchMedia=="function"&&a==="system")return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}function R0(a,r){if(a.mode==="light"||a.mode==="system"&&a.systemMode==="light")return r("light");if(a.mode==="dark"||a.mode==="system"&&a.systemMode==="dark")return r("dark")}function Xb(a){return R0(a,r=>{if(r==="light")return a.lightColorScheme;if(r==="dark")return a.darkColorScheme})}function Qb(a){const{defaultMode:r="light",defaultLightColorScheme:u,defaultDarkColorScheme:o,supportedColorSchemes:s=[],modeStorageKey:f=Ps,colorSchemeStorageKey:p=Fs,storageWindow:m=typeof window>"u"?void 0:window,storageManager:v=Vb,noSsr:y=!1}=a,S=s.join(","),E=s.length>1,A=$.useMemo(()=>v==null?void 0:v({key:f,storageWindow:m}),[v,f,m]),U=$.useMemo(()=>v==null?void 0:v({key:`${p}-light`,storageWindow:m}),[v,p,m]),M=$.useMemo(()=>v==null?void 0:v({key:`${p}-dark`,storageWindow:m}),[v,p,m]),[T,G]=$.useState(()=>{const I=(A==null?void 0:A.get(r))||r,et=(U==null?void 0:U.get(u))||u,g=(M==null?void 0:M.get(o))||o;return{mode:I,systemMode:Rm(I),lightColorScheme:et,darkColorScheme:g}}),[X,W]=$.useState(y||!E);$.useEffect(()=>{W(!0)},[]);const V=Xb(T),j=$.useCallback(I=>{G(et=>{if(I===et.mode)return et;const g=I??r;return A==null||A.set(g),{...et,mode:g,systemMode:Rm(g)}})},[A,r]),R=$.useCallback(I=>{I?typeof I=="string"?I&&!S.includes(I)?console.error(`\`${I}\` does not exist in \`theme.colorSchemes\`.`):G(et=>{const g={...et};return R0(et,Y=>{Y==="light"&&(U==null||U.set(I),g.lightColorScheme=I),Y==="dark"&&(M==null||M.set(I),g.darkColorScheme=I)}),g}):G(et=>{const g={...et},Y=I.light===null?u:I.light,J=I.dark===null?o:I.dark;return Y&&(S.includes(Y)?(g.lightColorScheme=Y,U==null||U.set(Y)):console.error(`\`${Y}\` does not exist in \`theme.colorSchemes\`.`)),J&&(S.includes(J)?(g.darkColorScheme=J,M==null||M.set(J)):console.error(`\`${J}\` does not exist in \`theme.colorSchemes\`.`)),g}):G(et=>(U==null||U.set(u),M==null||M.set(o),{...et,lightColorScheme:u,darkColorScheme:o}))},[S,U,M,u,o]),Q=$.useCallback(I=>{T.mode==="system"&&G(et=>{const g=I!=null&&I.matches?"dark":"light";return et.systemMode===g?et:{...et,systemMode:g}})},[T.mode]),F=$.useRef(Q);return F.current=Q,$.useEffect(()=>{if(typeof window.matchMedia!="function"||!E)return;const I=(...g)=>F.current(...g),et=window.matchMedia("(prefers-color-scheme: dark)");return et.addListener(I),I(et),()=>{et.removeListener(I)}},[E]),$.useEffect(()=>{if(E){const I=(A==null?void 0:A.subscribe(Y=>{(!Y||["light","dark","system"].includes(Y))&&j(Y||r)}))||gs,et=(U==null?void 0:U.subscribe(Y=>{(!Y||S.match(Y))&&R({light:Y})}))||gs,g=(M==null?void 0:M.subscribe(Y=>{(!Y||S.match(Y))&&R({dark:Y})}))||gs;return()=>{I(),et(),g()}}},[R,j,S,r,m,E,A,U,M]),{...T,mode:X?T.mode:void 0,systemMode:X?T.systemMode:void 0,colorScheme:X?V:void 0,setMode:j,setColorScheme:R}}const Zb="*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function Kb(a){const{themeId:r,theme:u={},modeStorageKey:o=Ps,colorSchemeStorageKey:s=Fs,disableTransitionOnChange:f=!1,defaultColorScheme:p,resolveTheme:m}=a,v={allColorSchemes:[],colorScheme:void 0,darkColorScheme:void 0,lightColorScheme:void 0,mode:void 0,setColorScheme:()=>{},setMode:()=>{},systemMode:void 0},y=$.createContext(void 0),S=()=>$.useContext(y)||v,E={},A={};function U(X){var Wn,ye,on,ge;const{children:W,theme:V,modeStorageKey:j=o,colorSchemeStorageKey:R=s,disableTransitionOnChange:Q=f,storageManager:F,storageWindow:I=typeof window>"u"?void 0:window,documentNode:et=typeof document>"u"?void 0:document,colorSchemeNode:g=typeof document>"u"?void 0:document.documentElement,disableNestedContext:Y=!1,disableStyleSheetGeneration:J=!1,defaultMode:it="system",forceThemeRerender:pt=!1,noSsr:mt}=X,N=$.useRef(!1),Z=Ws(),nt=$.useContext(y),at=!!nt&&!Y,C=$.useMemo(()=>V||(typeof u=="function"?u():u),[V]),q=C[r],K=q||C,{colorSchemes:P=E,components:ot=A,cssVarPrefix:vt}=K,ft=Object.keys(P).filter(ve=>!!P[ve]).join(","),Qt=$.useMemo(()=>ft.split(","),[ft]),zt=typeof p=="string"?p:p.light,Ne=typeof p=="string"?p:p.dark,xa=P[zt]&&P[Ne]?it:((ye=(Wn=P[K.defaultColorScheme])==null?void 0:Wn.palette)==null?void 0:ye.mode)||((on=K.palette)==null?void 0:on.mode),{mode:Zn,setMode:Kn,systemMode:Jn,lightColorScheme:pn,darkColorScheme:Xa,colorScheme:kl,setColorScheme:re}=Qb({supportedColorSchemes:Qt,defaultLightColorScheme:zt,defaultDarkColorScheme:Ne,modeStorageKey:j,colorSchemeStorageKey:R,defaultMode:xa,storageManager:F,storageWindow:I,noSsr:mt});let un=Zn,me=kl;at&&(un=nt.mode,me=nt.colorScheme);let hn=me||K.defaultColorScheme;K.vars&&!pt&&(hn=K.defaultColorScheme);const Xe=$.useMemo(()=>{var Mn;const ve=((Mn=K.generateThemeVars)==null?void 0:Mn.call(K))||K.vars,Vt={...K,components:ot,colorSchemes:P,cssVarPrefix:vt,vars:ve};if(typeof Vt.generateSpacing=="function"&&(Vt.spacing=Vt.generateSpacing()),hn){const Ee=P[hn];Ee&&typeof Ee=="object"&&Object.keys(Ee).forEach(be=>{Ee[be]&&typeof Ee[be]=="object"?Vt[be]={...Vt[be],...Ee[be]}:Vt[be]=Ee[be]})}return m?m(Vt):Vt},[K,hn,ot,P,vt]),ht=K.colorSchemeSelector;T0(()=>{if(me&&g&&ht&&ht!=="media"){const ve=ht;let Vt=ht;if(ve==="class"&&(Vt=".%s"),ve==="data"&&(Vt="[data-%s]"),ve!=null&&ve.startsWith("data-")&&!ve.includes("%s")&&(Vt=`[${ve}="%s"]`),Vt.startsWith("."))g.classList.remove(...Qt.map(Mn=>Vt.substring(1).replace("%s",Mn))),g.classList.add(Vt.substring(1).replace("%s",me));else{const Mn=Vt.replace("%s",me).match(/\[([^\]]+)\]/);if(Mn){const[Ee,be]=Mn[1].split("=");be||Qt.forEach(Rn=>{g.removeAttribute(Ee.replace(me,Rn))}),g.setAttribute(Ee,be?be.replace(/"|'/g,""):"")}else g.setAttribute(Vt,me)}}},[me,ht,g,Qt]),$.useEffect(()=>{let ve;if(Q&&N.current&&et){const Vt=et.createElement("style");Vt.appendChild(et.createTextNode(Zb)),et.head.appendChild(Vt),window.getComputedStyle(et.body),ve=setTimeout(()=>{et.head.removeChild(Vt)},1)}return()=>{clearTimeout(ve)}},[me,Q,et]),$.useEffect(()=>(N.current=!0,()=>{N.current=!1}),[]);const rr=$.useMemo(()=>({allColorSchemes:Qt,colorScheme:me,darkColorScheme:Xa,lightColorScheme:pn,mode:un,setColorScheme:re,setMode:Kn,systemMode:Jn}),[Qt,me,Xa,pn,un,re,Kn,Jn,Xe.colorSchemeSelector]);let ur=!0;(J||K.cssVariables===!1||at&&(Z==null?void 0:Z.cssVarPrefix)===vt)&&(ur=!1);const or=tt.jsxs($.Fragment,{children:[tt.jsx(M0,{themeId:q?r:void 0,theme:Xe,children:W}),ur&&tt.jsx(o0,{styles:((ge=Xe.generateStyleSheets)==null?void 0:ge.call(Xe))||[]})]});return at?or:tt.jsx(y.Provider,{value:rr,children:or})}const M=typeof p=="string"?p:p.light,T=typeof p=="string"?p:p.dark;return{CssVarsProvider:U,useColorScheme:S,getInitColorSchemeScript:X=>kb({colorSchemeStorageKey:s,defaultLightColorScheme:M,defaultDarkColorScheme:T,modeStorageKey:o,...X})}}function Jb(a=""){function r(...o){if(!o.length)return"";const s=o[0];return typeof s=="string"&&!s.match(/(#|\(|\)|(-?(\d*\.)?\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\d*\.)?\d+)$|(\d+ \d+ \d+)/)?`, var(--${a?`${a}-`:""}${s}${r(...o.slice(1))})`:`, ${s}`}return(o,...s)=>`var(--${a?`${a}-`:""}${o}${r(...s)})`}const zm=(a,r,u,o=[])=>{let s=a;r.forEach((f,p)=>{p===r.length-1?Array.isArray(s)?s[Number(f)]=u:s&&typeof s=="object"&&(s[f]=u):s&&typeof s=="object"&&(s[f]||(s[f]=o.includes(f)?[]:{}),s=s[f])})},Wb=(a,r,u)=>{function o(s,f=[],p=[]){Object.entries(s).forEach(([m,v])=>{(!u||u&&!u([...f,m]))&&v!=null&&(typeof v=="object"&&Object.keys(v).length>0?o(v,[...f,m],Array.isArray(v)?[...p,m]:p):r([...f,m],v,p))})}o(a)},Pb=(a,r)=>typeof r=="number"?["lineHeight","fontWeight","opacity","zIndex"].some(o=>a.includes(o))||a[a.length-1].toLowerCase().includes("opacity")?r:`${r}px`:r;function vs(a,r){const{prefix:u,shouldSkipGeneratingVar:o}=r||{},s={},f={},p={};return Wb(a,(m,v,y)=>{if((typeof v=="string"||typeof v=="number")&&(!o||!o(m,v))){const S=`--${u?`${u}-`:""}${m.join("-")}`,E=Pb(m,v);Object.assign(s,{[S]:E}),zm(f,m,`var(${S})`,y),zm(p,m,`var(${S}, ${E})`,y)}},m=>m[0]==="vars"),{css:s,vars:f,varsWithDefaults:p}}function Fb(a,r={}){const{getSelector:u=G,disableCssColorScheme:o,colorSchemeSelector:s}=r,{colorSchemes:f={},components:p,defaultColorScheme:m="light",...v}=a,{vars:y,css:S,varsWithDefaults:E}=vs(v,r);let A=E;const U={},{[m]:M,...T}=f;if(Object.entries(T||{}).forEach(([V,j])=>{const{vars:R,css:Q,varsWithDefaults:F}=vs(j,r);A=De(A,F),U[V]={css:Q,vars:R}}),M){const{css:V,vars:j,varsWithDefaults:R}=vs(M,r);A=De(A,R),U[m]={css:V,vars:j}}function G(V,j){var Q,F;let R=s;if(s==="class"&&(R=".%s"),s==="data"&&(R="[data-%s]"),s!=null&&s.startsWith("data-")&&!s.includes("%s")&&(R=`[${s}="%s"]`),V){if(R==="media")return a.defaultColorScheme===V?":root":{[`@media (prefers-color-scheme: ${((F=(Q=f[V])==null?void 0:Q.palette)==null?void 0:F.mode)||V})`]:{":root":j}};if(R)return a.defaultColorScheme===V?`:root, ${R.replace("%s",String(V))}`:R.replace("%s",String(V))}return":root"}return{vars:A,generateThemeVars:()=>{let V={...y};return Object.entries(U).forEach(([,{vars:j}])=>{V=De(V,j)}),V},generateStyleSheets:()=>{var I,et;const V=[],j=a.defaultColorScheme||"light";function R(g,Y){Object.keys(Y).length&&V.push(typeof g=="string"?{[g]:{...Y}}:g)}R(u(void 0,{...S}),S);const{[j]:Q,...F}=U;if(Q){const{css:g}=Q,Y=(et=(I=f[j])==null?void 0:I.palette)==null?void 0:et.mode,J=!o&&Y?{colorScheme:Y,...g}:{...g};R(u(j,{...J}),J)}return Object.entries(F).forEach(([g,{css:Y}])=>{var pt,mt;const J=(mt=(pt=f[g])==null?void 0:pt.palette)==null?void 0:mt.mode,it=!o&&J?{colorScheme:J,...Y}:{...Y};R(u(g,{...it}),it)}),V}}}function Ib(a){return function(u){return a==="media"?`@media (prefers-color-scheme: ${u})`:a?a.startsWith("data-")&&!a.includes("%s")?`[${a}="${u}"] &`:a==="class"?`.${u} &`:a==="data"?`[data-${u}] &`:`${a.replace("%s",u)} &`:"&"}}function dn(a,r,u=void 0){const o={};for(const s in a){const f=a[s];let p="",m=!0;for(let v=0;v<f.length;v+=1){const y=f[v];y&&(p+=(m===!0?"":" ")+r(y),m=!1,u&&u[y]&&(p+=" "+u[y]))}o[s]=p}return o}function OC(a,r){var u,o,s;return $.isValidElement(a)&&r.indexOf(a.type.muiName??((s=(o=(u=a.type)==null?void 0:u._payload)==null?void 0:o.value)==null?void 0:s.muiName))!==-1}function z0(){return{text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:Zi.white,default:Zi.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}}}const tS=z0();function _0(){return{text:{primary:Zi.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:Zi.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}}}const _m=_0();function Dm(a,r,u,o){const s=o.light||o,f=o.dark||o*1.5;a[r]||(a.hasOwnProperty(u)?a[r]=a[u]:r==="light"?a.light=Fi(a.main,s):r==="dark"&&(a.dark=Pi(a.main,f)))}function eS(a="light"){return a==="dark"?{main:Nl[200],light:Nl[50],dark:Nl[400]}:{main:Nl[700],light:Nl[400],dark:Nl[800]}}function nS(a="light"){return a==="dark"?{main:Bl[200],light:Bl[50],dark:Bl[400]}:{main:Bl[500],light:Bl[300],dark:Bl[700]}}function aS(a="light"){return a==="dark"?{main:Dl[500],light:Dl[300],dark:Dl[700]}:{main:Dl[700],light:Dl[400],dark:Dl[800]}}function lS(a="light"){return a==="dark"?{main:Ul[400],light:Ul[300],dark:Ul[700]}:{main:Ul[700],light:Ul[500],dark:Ul[900]}}function iS(a="light"){return a==="dark"?{main:wl[400],light:wl[300],dark:wl[700]}:{main:wl[800],light:wl[500],dark:wl[900]}}function rS(a="light"){return a==="dark"?{main:ki[400],light:ki[300],dark:ki[700]}:{main:"#ed6c02",light:ki[500],dark:ki[900]}}function Is(a){const{mode:r="light",contrastThreshold:u=3,tonalOffset:o=.2,...s}=a,f=a.primary||eS(r),p=a.secondary||nS(r),m=a.error||aS(r),v=a.info||lS(r),y=a.success||iS(r),S=a.warning||rS(r);function E(T){return _b(T,_m.text.primary)>=u?_m.text.primary:tS.text.primary}const A=({color:T,name:G,mainShade:X=500,lightShade:W=300,darkShade:V=700})=>{if(T={...T},!T.main&&T[X]&&(T.main=T[X]),!T.hasOwnProperty("main"))throw new Error(Ga(11,G?` (${G})`:"",X));if(typeof T.main!="string")throw new Error(Ga(12,G?` (${G})`:"",JSON.stringify(T.main)));return Dm(T,"light",W,o),Dm(T,"dark",V,o),T.contrastText||(T.contrastText=E(T.main)),T};let U;return r==="light"?U=z0():r==="dark"&&(U=_0()),De({common:{...Zi},mode:r,primary:A({color:f,name:"primary"}),secondary:A({color:p,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:A({color:m,name:"error"}),warning:A({color:S,name:"warning"}),info:A({color:v,name:"info"}),success:A({color:y,name:"success"}),grey:mv,contrastThreshold:u,getContrastText:E,augmentColor:A,tonalOffset:o,...U},s)}function uS(a){const r={};return Object.entries(a).forEach(o=>{const[s,f]=o;typeof f=="object"&&(r[s]=`${f.fontStyle?`${f.fontStyle} `:""}${f.fontVariant?`${f.fontVariant} `:""}${f.fontWeight?`${f.fontWeight} `:""}${f.fontStretch?`${f.fontStretch} `:""}${f.fontSize||""}${f.lineHeight?`/${f.lineHeight} `:""}${f.fontFamily||""}`)}),r}function oS(a,r){return{toolbar:{minHeight:56,[a.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[a.up("sm")]:{minHeight:64}},...r}}function cS(a){return Math.round(a*1e5)/1e5}const Bm={textTransform:"uppercase"},Nm='"Roboto", "Helvetica", "Arial", sans-serif';function D0(a,r){const{fontFamily:u=Nm,fontSize:o=14,fontWeightLight:s=300,fontWeightRegular:f=400,fontWeightMedium:p=500,fontWeightBold:m=700,htmlFontSize:v=16,allVariants:y,pxToRem:S,...E}=typeof r=="function"?r(a):r,A=o/14,U=S||(G=>`${G/v*A}rem`),M=(G,X,W,V,j)=>({fontFamily:u,fontWeight:G,fontSize:U(X),lineHeight:W,...u===Nm?{letterSpacing:`${cS(V/X)}em`}:{},...j,...y}),T={h1:M(s,96,1.167,-1.5),h2:M(s,60,1.2,-.5),h3:M(f,48,1.167,0),h4:M(f,34,1.235,.25),h5:M(f,24,1.334,0),h6:M(p,20,1.6,.15),subtitle1:M(f,16,1.75,.15),subtitle2:M(p,14,1.57,.1),body1:M(f,16,1.5,.15),body2:M(f,14,1.43,.15),button:M(p,14,1.75,.4,Bm),caption:M(f,12,1.66,.4),overline:M(f,12,2.66,1,Bm),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return De({htmlFontSize:v,pxToRem:U,fontFamily:u,fontSize:o,fontWeightLight:s,fontWeightRegular:f,fontWeightMedium:p,fontWeightBold:m,...T},E,{clone:!1})}const sS=.2,fS=.14,dS=.12;function kt(...a){return[`${a[0]}px ${a[1]}px ${a[2]}px ${a[3]}px rgba(0,0,0,${sS})`,`${a[4]}px ${a[5]}px ${a[6]}px ${a[7]}px rgba(0,0,0,${fS})`,`${a[8]}px ${a[9]}px ${a[10]}px ${a[11]}px rgba(0,0,0,${dS})`].join(",")}const pS=["none",kt(0,2,1,-1,0,1,1,0,0,1,3,0),kt(0,3,1,-2,0,2,2,0,0,1,5,0),kt(0,3,3,-2,0,3,4,0,0,1,8,0),kt(0,2,4,-1,0,4,5,0,0,1,10,0),kt(0,3,5,-1,0,5,8,0,0,1,14,0),kt(0,3,5,-1,0,6,10,0,0,1,18,0),kt(0,4,5,-2,0,7,10,1,0,2,16,1),kt(0,5,5,-3,0,8,10,1,0,3,14,2),kt(0,5,6,-3,0,9,12,1,0,3,16,2),kt(0,6,6,-3,0,10,14,1,0,4,18,3),kt(0,6,7,-4,0,11,15,1,0,4,20,3),kt(0,7,8,-4,0,12,17,2,0,5,22,4),kt(0,7,8,-4,0,13,19,2,0,5,24,4),kt(0,7,9,-4,0,14,21,2,0,5,26,4),kt(0,8,9,-5,0,15,22,2,0,6,28,5),kt(0,8,10,-5,0,16,24,2,0,6,30,5),kt(0,8,11,-5,0,17,26,2,0,6,32,5),kt(0,9,11,-5,0,18,28,2,0,7,34,6),kt(0,9,12,-6,0,19,29,2,0,7,36,6),kt(0,10,13,-6,0,20,31,3,0,8,38,7),kt(0,10,13,-6,0,21,33,3,0,8,40,7),kt(0,10,14,-6,0,22,35,3,0,8,42,7),kt(0,11,14,-7,0,23,36,3,0,9,44,8),kt(0,11,15,-7,0,24,38,3,0,9,46,8)],hS={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},mS={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function Um(a){return`${Math.round(a)}ms`}function yS(a){if(!a)return 0;const r=a/36;return Math.min(Math.round((4+15*r**.25+r/5)*10),3e3)}function gS(a){const r={...hS,...a.easing},u={...mS,...a.duration};return{getAutoHeightDuration:yS,create:(s=["all"],f={})=>{const{duration:p=u.standard,easing:m=r.easeInOut,delay:v=0,...y}=f;return(Array.isArray(s)?s:[s]).map(S=>`${S} ${typeof p=="string"?p:Um(p)} ${m} ${typeof v=="string"?v:Um(v)}`).join(",")},...a,easing:r,duration:u}}const vS={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500};function bS(a){return xn(a)||typeof a>"u"||typeof a=="string"||typeof a=="boolean"||typeof a=="number"||Array.isArray(a)}function B0(a={}){const r={...a};function u(o){const s=Object.entries(o);for(let f=0;f<s.length;f++){const[p,m]=s[f];!bS(m)||p.startsWith("unstable_")?delete o[p]:xn(m)&&(o[p]={...m},u(o[p]))}}return u(r),`import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';

const theme = ${JSON.stringify(r,null,2)};

theme.breakpoints = createBreakpoints(theme.breakpoints || {});
theme.transitions = createTransitions(theme.transitions || {});

export default theme;`}function Os(a={},...r){const{breakpoints:u,mixins:o={},spacing:s,palette:f={},transitions:p={},typography:m={},shape:v,...y}=a;if(a.vars&&a.generateThemeVars===void 0)throw new Error(Ga(20));const S=Is(f),E=Zs(a);let A=De(E,{mixins:oS(E.breakpoints,o),palette:S,shadows:pS.slice(),typography:D0(S,m),transitions:gS(p),zIndex:{...vS}});return A=De(A,y),A=r.reduce((U,M)=>De(U,M),A),A.unstable_sxConfig={...ir,...y==null?void 0:y.unstable_sxConfig},A.unstable_sx=function(M){return Ca({sx:M,theme:this})},A.toRuntimeSource=B0,A}function Ms(a){let r;return a<1?r=5.11916*a**2:r=4.5*Math.log(a+1)+2,Math.round(r*10)/1e3}const SS=[...Array(25)].map((a,r)=>{if(r===0)return"none";const u=Ms(r);return`linear-gradient(rgba(255 255 255 / ${u}), rgba(255 255 255 / ${u}))`});function N0(a){return{inputPlaceholder:a==="dark"?.5:.42,inputUnderline:a==="dark"?.7:.42,switchTrackDisabled:a==="dark"?.2:.12,switchTrack:a==="dark"?.3:.38}}function U0(a){return a==="dark"?SS:[]}function CS(a){const{palette:r={mode:"light"},opacity:u,overlays:o,...s}=a,f=Is(r);return{palette:f,opacity:{...N0(f.mode),...u},overlays:o||U0(f.mode),...s}}function TS(a){var r;return!!a[0].match(/(cssVarPrefix|colorSchemeSelector|rootSelector|typography|mixins|breakpoints|direction|transitions)/)||!!a[0].match(/sxConfig$/)||a[0]==="palette"&&!!((r=a[1])!=null&&r.match(/(mode|contrastThreshold|tonalOffset)/))}const xS=a=>[...[...Array(25)].map((r,u)=>`--${a?`${a}-`:""}overlays-${u}`),`--${a?`${a}-`:""}palette-AppBar-darkBg`,`--${a?`${a}-`:""}palette-AppBar-darkColor`],ES=a=>(r,u)=>{const o=a.rootSelector||":root",s=a.colorSchemeSelector;let f=s;if(s==="class"&&(f=".%s"),s==="data"&&(f="[data-%s]"),s!=null&&s.startsWith("data-")&&!s.includes("%s")&&(f=`[${s}="%s"]`),a.defaultColorScheme===r){if(r==="dark"){const p={};return xS(a.cssVarPrefix).forEach(m=>{p[m]=u[m],delete u[m]}),f==="media"?{[o]:u,"@media (prefers-color-scheme: dark)":{[o]:p}}:f?{[f.replace("%s",r)]:p,[`${o}, ${f.replace("%s",r)}`]:u}:{[o]:{...u,...p}}}if(f&&f!=="media")return`${o}, ${f.replace("%s",String(r))}`}else if(r){if(f==="media")return{[`@media (prefers-color-scheme: ${String(r)})`]:{[o]:u}};if(f)return f.replace("%s",String(r))}return o};function AS(a,r){r.forEach(u=>{a[u]||(a[u]={})})}function w(a,r,u){!a[r]&&u&&(a[r]=u)}function Xi(a){return typeof a!="string"||!a.startsWith("hsl")?a:x0(a)}function Vn(a,r){`${r}Channel`in a||(a[`${r}Channel`]=Vi(Xi(a[r])))}function OS(a){return typeof a=="number"?`${a}px`:typeof a=="string"||typeof a=="function"||Array.isArray(a)?a:"8px"}const Sn=a=>{try{return a()}catch{}},MS=(a="mui")=>Jb(a);function bs(a,r,u,o){if(!r)return;r=r===!0?{}:r;const s=o==="dark"?"dark":"light";if(!u){a[o]=CS({...r,palette:{mode:s,...r==null?void 0:r.palette}});return}const{palette:f,...p}=Os({...u,palette:{mode:s,...r==null?void 0:r.palette}});return a[o]={...r,palette:f,opacity:{...N0(s),...r==null?void 0:r.opacity},overlays:(r==null?void 0:r.overlays)||U0(s)},p}function RS(a={},...r){const{colorSchemes:u={light:!0},defaultColorScheme:o,disableCssColorScheme:s=!1,cssVarPrefix:f="mui",shouldSkipGeneratingVar:p=TS,colorSchemeSelector:m=u.light&&u.dark?"media":void 0,rootSelector:v=":root",...y}=a,S=Object.keys(u)[0],E=o||(u.light&&S!=="light"?"light":S),A=MS(f),{[E]:U,light:M,dark:T,...G}=u,X={...G};let W=U;if((E==="dark"&&!("dark"in u)||E==="light"&&!("light"in u))&&(W=!0),!W)throw new Error(Ga(21,E));const V=bs(X,W,y,E);M&&!X.light&&bs(X,M,void 0,"light"),T&&!X.dark&&bs(X,T,void 0,"dark");let j={defaultColorScheme:E,...V,cssVarPrefix:f,colorSchemeSelector:m,rootSelector:v,getCssVar:A,colorSchemes:X,font:{...uS(V.typography),...V.font},spacing:OS(y.spacing)};Object.keys(j.colorSchemes).forEach(et=>{const g=j.colorSchemes[et].palette,Y=J=>{const it=J.split("-"),pt=it[1],mt=it[2];return A(J,g[pt][mt])};if(g.mode==="light"&&(w(g.common,"background","#fff"),w(g.common,"onBackground","#000")),g.mode==="dark"&&(w(g.common,"background","#000"),w(g.common,"onBackground","#fff")),AS(g,["Alert","AppBar","Avatar","Button","Chip","FilledInput","LinearProgress","Skeleton","Slider","SnackbarContent","SpeedDialAction","StepConnector","StepContent","Switch","TableCell","Tooltip"]),g.mode==="light"){w(g.Alert,"errorColor",Ht(g.error.light,.6)),w(g.Alert,"infoColor",Ht(g.info.light,.6)),w(g.Alert,"successColor",Ht(g.success.light,.6)),w(g.Alert,"warningColor",Ht(g.warning.light,.6)),w(g.Alert,"errorFilledBg",Y("palette-error-main")),w(g.Alert,"infoFilledBg",Y("palette-info-main")),w(g.Alert,"successFilledBg",Y("palette-success-main")),w(g.Alert,"warningFilledBg",Y("palette-warning-main")),w(g.Alert,"errorFilledColor",Sn(()=>g.getContrastText(g.error.main))),w(g.Alert,"infoFilledColor",Sn(()=>g.getContrastText(g.info.main))),w(g.Alert,"successFilledColor",Sn(()=>g.getContrastText(g.success.main))),w(g.Alert,"warningFilledColor",Sn(()=>g.getContrastText(g.warning.main))),w(g.Alert,"errorStandardBg",jt(g.error.light,.9)),w(g.Alert,"infoStandardBg",jt(g.info.light,.9)),w(g.Alert,"successStandardBg",jt(g.success.light,.9)),w(g.Alert,"warningStandardBg",jt(g.warning.light,.9)),w(g.Alert,"errorIconColor",Y("palette-error-main")),w(g.Alert,"infoIconColor",Y("palette-info-main")),w(g.Alert,"successIconColor",Y("palette-success-main")),w(g.Alert,"warningIconColor",Y("palette-warning-main")),w(g.AppBar,"defaultBg",Y("palette-grey-100")),w(g.Avatar,"defaultBg",Y("palette-grey-400")),w(g.Button,"inheritContainedBg",Y("palette-grey-300")),w(g.Button,"inheritContainedHoverBg",Y("palette-grey-A100")),w(g.Chip,"defaultBorder",Y("palette-grey-400")),w(g.Chip,"defaultAvatarColor",Y("palette-grey-700")),w(g.Chip,"defaultIconColor",Y("palette-grey-700")),w(g.FilledInput,"bg","rgba(0, 0, 0, 0.06)"),w(g.FilledInput,"hoverBg","rgba(0, 0, 0, 0.09)"),w(g.FilledInput,"disabledBg","rgba(0, 0, 0, 0.12)"),w(g.LinearProgress,"primaryBg",jt(g.primary.main,.62)),w(g.LinearProgress,"secondaryBg",jt(g.secondary.main,.62)),w(g.LinearProgress,"errorBg",jt(g.error.main,.62)),w(g.LinearProgress,"infoBg",jt(g.info.main,.62)),w(g.LinearProgress,"successBg",jt(g.success.main,.62)),w(g.LinearProgress,"warningBg",jt(g.warning.main,.62)),w(g.Skeleton,"bg",`rgba(${Y("palette-text-primaryChannel")} / 0.11)`),w(g.Slider,"primaryTrack",jt(g.primary.main,.62)),w(g.Slider,"secondaryTrack",jt(g.secondary.main,.62)),w(g.Slider,"errorTrack",jt(g.error.main,.62)),w(g.Slider,"infoTrack",jt(g.info.main,.62)),w(g.Slider,"successTrack",jt(g.success.main,.62)),w(g.Slider,"warningTrack",jt(g.warning.main,.62));const J=Tu(g.background.default,.8);w(g.SnackbarContent,"bg",J),w(g.SnackbarContent,"color",Sn(()=>g.getContrastText(J))),w(g.SpeedDialAction,"fabHoverBg",Tu(g.background.paper,.15)),w(g.StepConnector,"border",Y("palette-grey-400")),w(g.StepContent,"border",Y("palette-grey-400")),w(g.Switch,"defaultColor",Y("palette-common-white")),w(g.Switch,"defaultDisabledColor",Y("palette-grey-100")),w(g.Switch,"primaryDisabledColor",jt(g.primary.main,.62)),w(g.Switch,"secondaryDisabledColor",jt(g.secondary.main,.62)),w(g.Switch,"errorDisabledColor",jt(g.error.main,.62)),w(g.Switch,"infoDisabledColor",jt(g.info.main,.62)),w(g.Switch,"successDisabledColor",jt(g.success.main,.62)),w(g.Switch,"warningDisabledColor",jt(g.warning.main,.62)),w(g.TableCell,"border",jt(Cu(g.divider,1),.88)),w(g.Tooltip,"bg",Cu(g.grey[700],.92))}if(g.mode==="dark"){w(g.Alert,"errorColor",jt(g.error.light,.6)),w(g.Alert,"infoColor",jt(g.info.light,.6)),w(g.Alert,"successColor",jt(g.success.light,.6)),w(g.Alert,"warningColor",jt(g.warning.light,.6)),w(g.Alert,"errorFilledBg",Y("palette-error-dark")),w(g.Alert,"infoFilledBg",Y("palette-info-dark")),w(g.Alert,"successFilledBg",Y("palette-success-dark")),w(g.Alert,"warningFilledBg",Y("palette-warning-dark")),w(g.Alert,"errorFilledColor",Sn(()=>g.getContrastText(g.error.dark))),w(g.Alert,"infoFilledColor",Sn(()=>g.getContrastText(g.info.dark))),w(g.Alert,"successFilledColor",Sn(()=>g.getContrastText(g.success.dark))),w(g.Alert,"warningFilledColor",Sn(()=>g.getContrastText(g.warning.dark))),w(g.Alert,"errorStandardBg",Ht(g.error.light,.9)),w(g.Alert,"infoStandardBg",Ht(g.info.light,.9)),w(g.Alert,"successStandardBg",Ht(g.success.light,.9)),w(g.Alert,"warningStandardBg",Ht(g.warning.light,.9)),w(g.Alert,"errorIconColor",Y("palette-error-main")),w(g.Alert,"infoIconColor",Y("palette-info-main")),w(g.Alert,"successIconColor",Y("palette-success-main")),w(g.Alert,"warningIconColor",Y("palette-warning-main")),w(g.AppBar,"defaultBg",Y("palette-grey-900")),w(g.AppBar,"darkBg",Y("palette-background-paper")),w(g.AppBar,"darkColor",Y("palette-text-primary")),w(g.Avatar,"defaultBg",Y("palette-grey-600")),w(g.Button,"inheritContainedBg",Y("palette-grey-800")),w(g.Button,"inheritContainedHoverBg",Y("palette-grey-700")),w(g.Chip,"defaultBorder",Y("palette-grey-700")),w(g.Chip,"defaultAvatarColor",Y("palette-grey-300")),w(g.Chip,"defaultIconColor",Y("palette-grey-300")),w(g.FilledInput,"bg","rgba(255, 255, 255, 0.09)"),w(g.FilledInput,"hoverBg","rgba(255, 255, 255, 0.13)"),w(g.FilledInput,"disabledBg","rgba(255, 255, 255, 0.12)"),w(g.LinearProgress,"primaryBg",Ht(g.primary.main,.5)),w(g.LinearProgress,"secondaryBg",Ht(g.secondary.main,.5)),w(g.LinearProgress,"errorBg",Ht(g.error.main,.5)),w(g.LinearProgress,"infoBg",Ht(g.info.main,.5)),w(g.LinearProgress,"successBg",Ht(g.success.main,.5)),w(g.LinearProgress,"warningBg",Ht(g.warning.main,.5)),w(g.Skeleton,"bg",`rgba(${Y("palette-text-primaryChannel")} / 0.13)`),w(g.Slider,"primaryTrack",Ht(g.primary.main,.5)),w(g.Slider,"secondaryTrack",Ht(g.secondary.main,.5)),w(g.Slider,"errorTrack",Ht(g.error.main,.5)),w(g.Slider,"infoTrack",Ht(g.info.main,.5)),w(g.Slider,"successTrack",Ht(g.success.main,.5)),w(g.Slider,"warningTrack",Ht(g.warning.main,.5));const J=Tu(g.background.default,.98);w(g.SnackbarContent,"bg",J),w(g.SnackbarContent,"color",Sn(()=>g.getContrastText(J))),w(g.SpeedDialAction,"fabHoverBg",Tu(g.background.paper,.15)),w(g.StepConnector,"border",Y("palette-grey-600")),w(g.StepContent,"border",Y("palette-grey-600")),w(g.Switch,"defaultColor",Y("palette-grey-300")),w(g.Switch,"defaultDisabledColor",Y("palette-grey-600")),w(g.Switch,"primaryDisabledColor",Ht(g.primary.main,.55)),w(g.Switch,"secondaryDisabledColor",Ht(g.secondary.main,.55)),w(g.Switch,"errorDisabledColor",Ht(g.error.main,.55)),w(g.Switch,"infoDisabledColor",Ht(g.info.main,.55)),w(g.Switch,"successDisabledColor",Ht(g.success.main,.55)),w(g.Switch,"warningDisabledColor",Ht(g.warning.main,.55)),w(g.TableCell,"border",Ht(Cu(g.divider,1),.68)),w(g.Tooltip,"bg",Cu(g.grey[700],.92))}Vn(g.background,"default"),Vn(g.background,"paper"),Vn(g.common,"background"),Vn(g.common,"onBackground"),Vn(g,"divider"),Object.keys(g).forEach(J=>{const it=g[J];J!=="tonalOffset"&&it&&typeof it=="object"&&(it.main&&w(g[J],"mainChannel",Vi(Xi(it.main))),it.light&&w(g[J],"lightChannel",Vi(Xi(it.light))),it.dark&&w(g[J],"darkChannel",Vi(Xi(it.dark))),it.contrastText&&w(g[J],"contrastTextChannel",Vi(Xi(it.contrastText))),J==="text"&&(Vn(g[J],"primary"),Vn(g[J],"secondary")),J==="action"&&(it.active&&Vn(g[J],"active"),it.selected&&Vn(g[J],"selected")))})}),j=r.reduce((et,g)=>De(et,g),j);const R={prefix:f,disableCssColorScheme:s,shouldSkipGeneratingVar:p,getSelector:ES(j)},{vars:Q,generateThemeVars:F,generateStyleSheets:I}=Fb(j,R);return j.vars=Q,Object.entries(j.colorSchemes[j.defaultColorScheme]).forEach(([et,g])=>{j[et]=g}),j.generateThemeVars=F,j.generateStyleSheets=I,j.generateSpacing=function(){return m0(y.spacing,Xs(this))},j.getColorSchemeSelector=Ib(m),j.spacing=j.generateSpacing(),j.shouldSkipGeneratingVar=p,j.unstable_sxConfig={...ir,...y==null?void 0:y.unstable_sxConfig},j.unstable_sx=function(g){return Ca({sx:g,theme:this})},j.toRuntimeSource=B0,j}function wm(a,r,u){a.colorSchemes&&u&&(a.colorSchemes[r]={...u!==!0&&u,palette:Is({...u===!0?{}:u.palette,mode:r})})}function tf(a={},...r){const{palette:u,cssVariables:o=!1,colorSchemes:s=u?void 0:{light:!0},defaultColorScheme:f=u==null?void 0:u.mode,...p}=a,m=f||"light",v=s==null?void 0:s[m],y={...s,...u?{[m]:{...typeof v!="boolean"&&v,palette:u}}:void 0};if(o===!1){if(!("colorSchemes"in a))return Os(a,...r);let S=u;"palette"in a||y[m]&&(y[m]!==!0?S=y[m].palette:m==="dark"&&(S={mode:"dark"}));const E=Os({...a,palette:S},...r);return E.defaultColorScheme=m,E.colorSchemes=y,E.palette.mode==="light"&&(E.colorSchemes.light={...y.light!==!0&&y.light,palette:E.palette},wm(E,"dark",y.dark)),E.palette.mode==="dark"&&(E.colorSchemes.dark={...y.dark!==!0&&y.dark,palette:E.palette},wm(E,"light",y.light)),E}return!u&&!("light"in y)&&m==="light"&&(y.light=!0),RS({...p,colorSchemes:y,defaultColorScheme:m,...typeof o!="boolean"&&o},...r)}const ef=tf();function zS(){const a=Ks(ef);return a[En]||a}function _S(a){return a!=="ownerState"&&a!=="theme"&&a!=="sx"&&a!=="as"}const w0=a=>_S(a)&&a!=="classes",Gt=Tb({themeId:En,defaultTheme:ef,rootShouldForwardProp:w0});function DS({theme:a,...r}){const u=En in a?a[En]:void 0;return tt.jsx(M0,{...r,themeId:u?En:void 0,theme:u||a})}const xu={colorSchemeStorageKey:"mui-color-scheme",defaultLightColorScheme:"light",defaultDarkColorScheme:"dark",modeStorageKey:"mui-mode"},{CssVarsProvider:BS}=Kb({themeId:En,theme:()=>tf({cssVariables:!0}),colorSchemeStorageKey:xu.colorSchemeStorageKey,modeStorageKey:xu.modeStorageKey,defaultColorScheme:{light:xu.defaultLightColorScheme,dark:xu.defaultDarkColorScheme},resolveTheme:a=>{const r={...a,typography:D0(a.palette,a.typography)};return r.unstable_sx=function(o){return Ca({sx:o,theme:this})},r}}),NS=BS;function MC({theme:a,...r}){const u=$.useMemo(()=>{if(typeof a=="function")return a;const o=En in a?a[En]:a;return"colorSchemes"in o?null:"vars"in o?a:{...a,vars:null}},[a]);return u?tt.jsx(DS,{theme:u,...r}):tt.jsx(NS,{theme:a,...r})}function US(a){return tt.jsx(hb,{...a,defaultTheme:ef,themeId:En})}function $0(a){return function(u){return tt.jsx(US,{styles:typeof a=="function"?o=>a({theme:o,...u}):a})}}function wS(){return g0}const On=qb;function Ve(a){return Lb(a)}function $S(a){return rn("MuiSvgIcon",a)}Ge("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);const HS=a=>{const{color:r,fontSize:u,classes:o}=a,s={root:["root",r!=="inherit"&&`color${rt(r)}`,`fontSize${rt(u)}`]};return dn(s,$S,o)},jS=Gt("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(a,r)=>{const{ownerState:u}=a;return[r.root,u.color!=="inherit"&&r[`color${rt(u.color)}`],r[`fontSize${rt(u.fontSize)}`]]}})(On(({theme:a})=>{var r,u,o,s,f,p,m,v,y,S,E,A,U,M;return{userSelect:"none",width:"1em",height:"1em",display:"inline-block",flexShrink:0,transition:(s=(r=a.transitions)==null?void 0:r.create)==null?void 0:s.call(r,"fill",{duration:(o=(u=(a.vars??a).transitions)==null?void 0:u.duration)==null?void 0:o.shorter}),variants:[{props:T=>!T.hasSvgAsChild,style:{fill:"currentColor"}},{props:{fontSize:"inherit"},style:{fontSize:"inherit"}},{props:{fontSize:"small"},style:{fontSize:((p=(f=a.typography)==null?void 0:f.pxToRem)==null?void 0:p.call(f,20))||"1.25rem"}},{props:{fontSize:"medium"},style:{fontSize:((v=(m=a.typography)==null?void 0:m.pxToRem)==null?void 0:v.call(m,24))||"1.5rem"}},{props:{fontSize:"large"},style:{fontSize:((S=(y=a.typography)==null?void 0:y.pxToRem)==null?void 0:S.call(y,35))||"2.1875rem"}},...Object.entries((a.vars??a).palette).filter(([,T])=>T&&T.main).map(([T])=>{var G,X;return{props:{color:T},style:{color:(X=(G=(a.vars??a).palette)==null?void 0:G[T])==null?void 0:X.main}}}),{props:{color:"action"},style:{color:(A=(E=(a.vars??a).palette)==null?void 0:E.action)==null?void 0:A.active}},{props:{color:"disabled"},style:{color:(M=(U=(a.vars??a).palette)==null?void 0:U.action)==null?void 0:M.disabled}},{props:{color:"inherit"},style:{color:void 0}}]}})),Rs=$.forwardRef(function(r,u){const o=Ve({props:r,name:"MuiSvgIcon"}),{children:s,className:f,color:p="inherit",component:m="svg",fontSize:v="medium",htmlColor:y,inheritViewBox:S=!1,titleAccess:E,viewBox:A="0 0 24 24",...U}=o,M=$.isValidElement(s)&&s.type==="svg",T={...o,color:p,component:m,fontSize:v,instanceFontSize:r.fontSize,inheritViewBox:S,viewBox:A,hasSvgAsChild:M},G={};S||(G.viewBox=A);const X=HS(T);return tt.jsxs(jS,{as:m,className:Lt(X.root,f),focusable:"false",color:y,"aria-hidden":E?void 0:!0,role:E?"img":void 0,ref:u,...G,...U,...M&&s.props,ownerState:T,children:[M?s.props.children:s,E?tt.jsx("title",{children:E}):null]})});Rs.muiName="SvgIcon";function Va(a,r){function u(o,s){return tt.jsx(Rs,{"data-testid":void 0,ref:s,...o,children:a})}return u.muiName=Rs.muiName,$.memo($.forwardRef(u))}let $m=0;function LS(a){const[r,u]=$.useState(a),o=a||r;return $.useEffect(()=>{r==null&&($m+=1,u(`mui-${$m}`))},[r]),o}const qS={...Ss},Hm=qS.useId;function H0(a){if(Hm!==void 0){const r=Hm();return a??r}return LS(a)}function Ru(a){const r=$.useRef(a);return T0(()=>{r.current=a}),$.useRef((...u)=>(0,r.current)(...u)).current}function Uu(...a){const r=$.useRef(void 0),u=$.useCallback(o=>{const s=a.map(f=>{if(f==null)return null;if(typeof f=="function"){const p=f,m=p(o);return typeof m=="function"?m:()=>{p(null)}}return f.current=o,()=>{f.current=null}});return()=>{s.forEach(f=>f==null?void 0:f())}},a);return $.useMemo(()=>a.every(o=>o==null)?null:o=>{r.current&&(r.current(),r.current=void 0),o!=null&&(r.current=u(o))},a)}function YS(a,r){if(a==null)return{};var u={};for(var o in a)if({}.hasOwnProperty.call(a,o)){if(r.indexOf(o)!==-1)continue;u[o]=a[o]}return u}function zs(a,r){return zs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(u,o){return u.__proto__=o,u},zs(a,r)}function kS(a,r){a.prototype=Object.create(r.prototype),a.prototype.constructor=a,zs(a,r)}const jm=Hl.createContext(null);function GS(a){if(a===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a}function nf(a,r){var u=function(f){return r&&$.isValidElement(f)?r(f):f},o=Object.create(null);return a&&$.Children.map(a,function(s){return s}).forEach(function(s){o[s.key]=u(s)}),o}function VS(a,r){a=a||{},r=r||{};function u(S){return S in r?r[S]:a[S]}var o=Object.create(null),s=[];for(var f in a)f in r?s.length&&(o[f]=s,s=[]):s.push(f);var p,m={};for(var v in r){if(o[v])for(p=0;p<o[v].length;p++){var y=o[v][p];m[o[v][p]]=u(y)}m[v]=u(v)}for(p=0;p<s.length;p++)m[s[p]]=u(s[p]);return m}function ka(a,r,u){return u[r]!=null?u[r]:a.props[r]}function XS(a,r){return nf(a.children,function(u){return $.cloneElement(u,{onExited:r.bind(null,u),in:!0,appear:ka(u,"appear",a),enter:ka(u,"enter",a),exit:ka(u,"exit",a)})})}function QS(a,r,u){var o=nf(a.children),s=VS(r,o);return Object.keys(s).forEach(function(f){var p=s[f];if($.isValidElement(p)){var m=f in r,v=f in o,y=r[f],S=$.isValidElement(y)&&!y.props.in;v&&(!m||S)?s[f]=$.cloneElement(p,{onExited:u.bind(null,p),in:!0,exit:ka(p,"exit",a),enter:ka(p,"enter",a)}):!v&&m&&!S?s[f]=$.cloneElement(p,{in:!1}):v&&m&&$.isValidElement(y)&&(s[f]=$.cloneElement(p,{onExited:u.bind(null,p),in:y.props.in,exit:ka(p,"exit",a),enter:ka(p,"enter",a)}))}}),s}var ZS=Object.values||function(a){return Object.keys(a).map(function(r){return a[r]})},KS={component:"div",childFactory:function(r){return r}},af=function(a){kS(r,a);function r(o,s){var f;f=a.call(this,o,s)||this;var p=f.handleExited.bind(GS(f));return f.state={contextValue:{isMounting:!0},handleExited:p,firstRender:!0},f}var u=r.prototype;return u.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},u.componentWillUnmount=function(){this.mounted=!1},r.getDerivedStateFromProps=function(s,f){var p=f.children,m=f.handleExited,v=f.firstRender;return{children:v?XS(s,m):QS(s,p,m),firstRender:!1}},u.handleExited=function(s,f){var p=nf(this.props.children);s.key in p||(s.props.onExited&&s.props.onExited(f),this.mounted&&this.setState(function(m){var v=_u({},m.children);return delete v[s.key],{children:v}}))},u.render=function(){var s=this.props,f=s.component,p=s.childFactory,m=YS(s,["component","childFactory"]),v=this.state.contextValue,y=ZS(this.state.children).map(p);return delete m.appear,delete m.enter,delete m.exit,f===null?Hl.createElement(jm.Provider,{value:v},y):Hl.createElement(jm.Provider,{value:v},Hl.createElement(f,m,y))},r}(Hl.Component);af.propTypes={};af.defaultProps=KS;const Lm={};function j0(a,r){const u=$.useRef(Lm);return u.current===Lm&&(u.current=a(r)),u}const JS=[];function WS(a){$.useEffect(a,JS)}class lf{constructor(){Li(this,"currentId",null);Li(this,"clear",()=>{this.currentId!==null&&(clearTimeout(this.currentId),this.currentId=null)});Li(this,"disposeEffect",()=>this.clear)}static create(){return new lf}start(r,u){this.clear(),this.currentId=setTimeout(()=>{this.currentId=null,u()},r)}}function PS(){const a=j0(lf.create).current;return WS(a.disposeEffect),a}function FS(a){return rn("MuiPaper",a)}Ge("MuiPaper",["root","rounded","outlined","elevation","elevation0","elevation1","elevation2","elevation3","elevation4","elevation5","elevation6","elevation7","elevation8","elevation9","elevation10","elevation11","elevation12","elevation13","elevation14","elevation15","elevation16","elevation17","elevation18","elevation19","elevation20","elevation21","elevation22","elevation23","elevation24"]);const IS=a=>{const{square:r,elevation:u,variant:o,classes:s}=a,f={root:["root",o,!r&&"rounded",o==="elevation"&&`elevation${u}`]};return dn(f,FS,s)},t2=Gt("div",{name:"MuiPaper",slot:"Root",overridesResolver:(a,r)=>{const{ownerState:u}=a;return[r.root,r[u.variant],!u.square&&r.rounded,u.variant==="elevation"&&r[`elevation${u.elevation}`]]}})(On(({theme:a})=>({backgroundColor:(a.vars||a).palette.background.paper,color:(a.vars||a).palette.text.primary,transition:a.transitions.create("box-shadow"),variants:[{props:({ownerState:r})=>!r.square,style:{borderRadius:a.shape.borderRadius}},{props:{variant:"outlined"},style:{border:`1px solid ${(a.vars||a).palette.divider}`}},{props:{variant:"elevation"},style:{boxShadow:"var(--Paper-shadow)",backgroundImage:"var(--Paper-overlay)"}}]}))),L0=$.forwardRef(function(r,u){var U;const o=Ve({props:r,name:"MuiPaper"}),s=zS(),{className:f,component:p="div",elevation:m=1,square:v=!1,variant:y="elevation",...S}=o,E={...o,component:p,elevation:m,square:v,variant:y},A=IS(E);return tt.jsx(t2,{as:p,ownerState:E,className:Lt(A.root,f),ref:u,...S,style:{...y==="elevation"&&{"--Paper-shadow":(s.vars||s).shadows[m],...s.vars&&{"--Paper-overlay":(U=s.vars.overlays)==null?void 0:U[m]},...!s.vars&&s.palette.mode==="dark"&&{"--Paper-overlay":`linear-gradient(${ne("#fff",Ms(m))}, ${ne("#fff",Ms(m))})`}},...S.style}})});function e2(a){return typeof a=="string"}function n2(a,r,u){return a===void 0||e2(a)?r:{...r,ownerState:{...r.ownerState,...u}}}function a2(a,r,u){return typeof a=="function"?a(r,u):a}function l2(a,r=[]){if(a===void 0)return{};const u={};return Object.keys(a).filter(o=>o.match(/^on[A-Z]/)&&typeof a[o]=="function"&&!r.includes(o)).forEach(o=>{u[o]=a[o]}),u}function qm(a){if(a===void 0)return{};const r={};return Object.keys(a).filter(u=>!(u.match(/^on[A-Z]/)&&typeof a[u]=="function")).forEach(u=>{r[u]=a[u]}),r}function i2(a){const{getSlotProps:r,additionalProps:u,externalSlotProps:o,externalForwardedProps:s,className:f}=a;if(!r){const U=Lt(u==null?void 0:u.className,f,s==null?void 0:s.className,o==null?void 0:o.className),M={...u==null?void 0:u.style,...s==null?void 0:s.style,...o==null?void 0:o.style},T={...u,...s,...o};return U.length>0&&(T.className=U),Object.keys(M).length>0&&(T.style=M),{props:T,internalRef:void 0}}const p=l2({...s,...o}),m=qm(o),v=qm(s),y=r(p),S=Lt(y==null?void 0:y.className,u==null?void 0:u.className,f,s==null?void 0:s.className,o==null?void 0:o.className),E={...y==null?void 0:y.style,...u==null?void 0:u.style,...s==null?void 0:s.style,...o==null?void 0:o.style},A={...y,...u,...v,...m};return S.length>0&&(A.className=S),Object.keys(E).length>0&&(A.style=E),{props:A,internalRef:y.ref}}function $l(a,r){const{className:u,elementType:o,ownerState:s,externalForwardedProps:f,internalForwardedProps:p,shouldForwardComponentProp:m=!1,...v}=r,{component:y,slots:S={[a]:void 0},slotProps:E={[a]:void 0},...A}=f,U=S[a]||o,M=a2(E[a],s),{props:{component:T,...G},internalRef:X}=i2({className:u,...v,externalForwardedProps:a==="root"?A:void 0,externalSlotProps:M}),W=Uu(X,M==null?void 0:M.ref,r.ref),V=a==="root"?T||y:T,j=n2(U,{...a==="root"&&!y&&!S[a]&&p,...a!=="root"&&!S[a]&&p,...G,...V&&!m&&{as:V},...V&&m&&{component:V},ref:W},s);return[U,j]}function Ym(a){try{return a.matches(":focus-visible")}catch{}return!1}class wu{constructor(){Li(this,"mountEffect",()=>{this.shouldMount&&!this.didMount&&this.ref.current!==null&&(this.didMount=!0,this.mounted.resolve())});this.ref={current:null},this.mounted=null,this.didMount=!1,this.shouldMount=!1,this.setShouldMount=null}static create(){return new wu}static use(){const r=j0(wu.create).current,[u,o]=$.useState(!1);return r.shouldMount=u,r.setShouldMount=o,$.useEffect(r.mountEffect,[u]),r}mount(){return this.mounted||(this.mounted=u2(),this.shouldMount=!0,this.setShouldMount(this.shouldMount)),this.mounted}start(...r){this.mount().then(()=>{var u;return(u=this.ref.current)==null?void 0:u.start(...r)})}stop(...r){this.mount().then(()=>{var u;return(u=this.ref.current)==null?void 0:u.stop(...r)})}pulsate(...r){this.mount().then(()=>{var u;return(u=this.ref.current)==null?void 0:u.pulsate(...r)})}}function r2(){return wu.use()}function u2(){let a,r;const u=new Promise((o,s)=>{a=o,r=s});return u.resolve=a,u.reject=r,u}function o2(a){const{className:r,classes:u,pulsate:o=!1,rippleX:s,rippleY:f,rippleSize:p,in:m,onExited:v,timeout:y}=a,[S,E]=$.useState(!1),A=Lt(r,u.ripple,u.rippleVisible,o&&u.ripplePulsate),U={width:p,height:p,top:-(p/2)+f,left:-(p/2)+s},M=Lt(u.child,S&&u.childLeaving,o&&u.childPulsate);return!m&&!S&&E(!0),$.useEffect(()=>{if(!m&&v!=null){const T=setTimeout(v,y);return()=>{clearTimeout(T)}}},[v,m,y]),tt.jsx("span",{className:A,style:U,children:tt.jsx("span",{className:M})})}const en=Ge("MuiTouchRipple",["root","ripple","rippleVisible","ripplePulsate","child","childLeaving","childPulsate"]),_s=550,c2=80,s2=nr`
  0% {
    transform: scale(0);
    opacity: 0.1;
  }

  100% {
    transform: scale(1);
    opacity: 0.3;
  }
`,f2=nr`
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
`,d2=nr`
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.92);
  }

  100% {
    transform: scale(1);
  }
`,p2=Gt("span",{name:"MuiTouchRipple",slot:"Root"})({overflow:"hidden",pointerEvents:"none",position:"absolute",zIndex:0,top:0,right:0,bottom:0,left:0,borderRadius:"inherit"}),h2=Gt(o2,{name:"MuiTouchRipple",slot:"Ripple"})`
  opacity: 0;
  position: absolute;

  &.${en.rippleVisible} {
    opacity: 0.3;
    transform: scale(1);
    animation-name: ${s2};
    animation-duration: ${_s}ms;
    animation-timing-function: ${({theme:a})=>a.transitions.easing.easeInOut};
  }

  &.${en.ripplePulsate} {
    animation-duration: ${({theme:a})=>a.transitions.duration.shorter}ms;
  }

  & .${en.child} {
    opacity: 1;
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: currentColor;
  }

  & .${en.childLeaving} {
    opacity: 0;
    animation-name: ${f2};
    animation-duration: ${_s}ms;
    animation-timing-function: ${({theme:a})=>a.transitions.easing.easeInOut};
  }

  & .${en.childPulsate} {
    position: absolute;
    /* @noflip */
    left: 0px;
    top: 0;
    animation-name: ${d2};
    animation-duration: 2500ms;
    animation-timing-function: ${({theme:a})=>a.transitions.easing.easeInOut};
    animation-iteration-count: infinite;
    animation-delay: 200ms;
  }
`,m2=$.forwardRef(function(r,u){const o=Ve({props:r,name:"MuiTouchRipple"}),{center:s=!1,classes:f={},className:p,...m}=o,[v,y]=$.useState([]),S=$.useRef(0),E=$.useRef(null);$.useEffect(()=>{E.current&&(E.current(),E.current=null)},[v]);const A=$.useRef(!1),U=PS(),M=$.useRef(null),T=$.useRef(null),G=$.useCallback(j=>{const{pulsate:R,rippleX:Q,rippleY:F,rippleSize:I,cb:et}=j;y(g=>[...g,tt.jsx(h2,{classes:{ripple:Lt(f.ripple,en.ripple),rippleVisible:Lt(f.rippleVisible,en.rippleVisible),ripplePulsate:Lt(f.ripplePulsate,en.ripplePulsate),child:Lt(f.child,en.child),childLeaving:Lt(f.childLeaving,en.childLeaving),childPulsate:Lt(f.childPulsate,en.childPulsate)},timeout:_s,pulsate:R,rippleX:Q,rippleY:F,rippleSize:I},S.current)]),S.current+=1,E.current=et},[f]),X=$.useCallback((j={},R={},Q=()=>{})=>{const{pulsate:F=!1,center:I=s||R.pulsate,fakeElement:et=!1}=R;if((j==null?void 0:j.type)==="mousedown"&&A.current){A.current=!1;return}(j==null?void 0:j.type)==="touchstart"&&(A.current=!0);const g=et?null:T.current,Y=g?g.getBoundingClientRect():{width:0,height:0,left:0,top:0};let J,it,pt;if(I||j===void 0||j.clientX===0&&j.clientY===0||!j.clientX&&!j.touches)J=Math.round(Y.width/2),it=Math.round(Y.height/2);else{const{clientX:mt,clientY:N}=j.touches&&j.touches.length>0?j.touches[0]:j;J=Math.round(mt-Y.left),it=Math.round(N-Y.top)}if(I)pt=Math.sqrt((2*Y.width**2+Y.height**2)/3),pt%2===0&&(pt+=1);else{const mt=Math.max(Math.abs((g?g.clientWidth:0)-J),J)*2+2,N=Math.max(Math.abs((g?g.clientHeight:0)-it),it)*2+2;pt=Math.sqrt(mt**2+N**2)}j!=null&&j.touches?M.current===null&&(M.current=()=>{G({pulsate:F,rippleX:J,rippleY:it,rippleSize:pt,cb:Q})},U.start(c2,()=>{M.current&&(M.current(),M.current=null)})):G({pulsate:F,rippleX:J,rippleY:it,rippleSize:pt,cb:Q})},[s,G,U]),W=$.useCallback(()=>{X({},{pulsate:!0})},[X]),V=$.useCallback((j,R)=>{if(U.clear(),(j==null?void 0:j.type)==="touchend"&&M.current){M.current(),M.current=null,U.start(0,()=>{V(j,R)});return}M.current=null,y(Q=>Q.length>0?Q.slice(1):Q),E.current=R},[U]);return $.useImperativeHandle(u,()=>({pulsate:W,start:X,stop:V}),[W,X,V]),tt.jsx(p2,{className:Lt(en.root,f.root,p),ref:T,...m,children:tt.jsx(af,{component:null,exit:!0,children:v})})});function y2(a){return rn("MuiButtonBase",a)}const g2=Ge("MuiButtonBase",["root","disabled","focusVisible"]),v2=a=>{const{disabled:r,focusVisible:u,focusVisibleClassName:o,classes:s}=a,p=dn({root:["root",r&&"disabled",u&&"focusVisible"]},y2,s);return u&&o&&(p.root+=` ${o}`),p},b2=Gt("button",{name:"MuiButtonBase",slot:"Root"})({display:"inline-flex",alignItems:"center",justifyContent:"center",position:"relative",boxSizing:"border-box",WebkitTapHighlightColor:"transparent",backgroundColor:"transparent",outline:0,border:0,margin:0,borderRadius:0,padding:0,cursor:"pointer",userSelect:"none",verticalAlign:"middle",MozAppearance:"none",WebkitAppearance:"none",textDecoration:"none",color:"inherit","&::-moz-focus-inner":{borderStyle:"none"},[`&.${g2.disabled}`]:{pointerEvents:"none",cursor:"default"},"@media print":{colorAdjust:"exact"}}),$u=$.forwardRef(function(r,u){const o=Ve({props:r,name:"MuiButtonBase"}),{action:s,centerRipple:f=!1,children:p,className:m,component:v="button",disabled:y=!1,disableRipple:S=!1,disableTouchRipple:E=!1,focusRipple:A=!1,focusVisibleClassName:U,LinkComponent:M="a",onBlur:T,onClick:G,onContextMenu:X,onDragLeave:W,onFocus:V,onFocusVisible:j,onKeyDown:R,onKeyUp:Q,onMouseDown:F,onMouseLeave:I,onMouseUp:et,onTouchEnd:g,onTouchMove:Y,onTouchStart:J,tabIndex:it=0,TouchRippleProps:pt,touchRippleRef:mt,type:N,...Z}=o,nt=$.useRef(null),at=r2(),C=Uu(at.ref,mt),[q,K]=$.useState(!1);y&&q&&K(!1),$.useImperativeHandle(s,()=>({focusVisible:()=>{K(!0),nt.current.focus()}}),[]);const P=at.shouldMount&&!S&&!y;$.useEffect(()=>{q&&A&&!S&&at.pulsate()},[S,A,q,at]);const ot=Xn(at,"start",F,E),vt=Xn(at,"stop",X,E),ft=Xn(at,"stop",W,E),Qt=Xn(at,"stop",et,E),zt=Xn(at,"stop",ht=>{q&&ht.preventDefault(),I&&I(ht)},E),Ne=Xn(at,"start",J,E),xa=Xn(at,"stop",g,E),Zn=Xn(at,"stop",Y,E),Kn=Xn(at,"stop",ht=>{Ym(ht.target)||K(!1),T&&T(ht)},!1),Jn=Ru(ht=>{nt.current||(nt.current=ht.currentTarget),Ym(ht.target)&&(K(!0),j&&j(ht)),V&&V(ht)}),pn=()=>{const ht=nt.current;return v&&v!=="button"&&!(ht.tagName==="A"&&ht.href)},Xa=Ru(ht=>{A&&!ht.repeat&&q&&ht.key===" "&&at.stop(ht,()=>{at.start(ht)}),ht.target===ht.currentTarget&&pn()&&ht.key===" "&&ht.preventDefault(),R&&R(ht),ht.target===ht.currentTarget&&pn()&&ht.key==="Enter"&&!y&&(ht.preventDefault(),G&&G(ht))}),kl=Ru(ht=>{A&&ht.key===" "&&q&&!ht.defaultPrevented&&at.stop(ht,()=>{at.pulsate(ht)}),Q&&Q(ht),G&&ht.target===ht.currentTarget&&pn()&&ht.key===" "&&!ht.defaultPrevented&&G(ht)});let re=v;re==="button"&&(Z.href||Z.to)&&(re=M);const un={};re==="button"?(un.type=N===void 0?"button":N,un.disabled=y):(!Z.href&&!Z.to&&(un.role="button"),y&&(un["aria-disabled"]=y));const me=Uu(u,nt),hn={...o,centerRipple:f,component:v,disabled:y,disableRipple:S,disableTouchRipple:E,focusRipple:A,tabIndex:it,focusVisible:q},Xe=v2(hn);return tt.jsxs(b2,{as:re,className:Lt(Xe.root,m),ownerState:hn,onBlur:Kn,onClick:G,onContextMenu:vt,onFocus:Jn,onKeyDown:Xa,onKeyUp:kl,onMouseDown:ot,onMouseLeave:zt,onMouseUp:Qt,onDragLeave:ft,onTouchEnd:xa,onTouchMove:Zn,onTouchStart:Ne,ref:me,tabIndex:y?-1:it,type:N,...un,...Z,children:[p,P?tt.jsx(m2,{ref:C,center:f,...pt}):null]})});function Xn(a,r,u,o=!1){return Ru(s=>(u&&u(s),o||a[r](s),!0))}function S2(a){return typeof a.main=="string"}function C2(a,r=[]){if(!S2(a))return!1;for(const u of r)if(!a.hasOwnProperty(u)||typeof a[u]!="string")return!1;return!0}function an(a=[]){return([,r])=>r&&C2(r,a)}function T2(a){return rn("MuiAlert",a)}const km=Ge("MuiAlert",["root","action","icon","message","filled","colorSuccess","colorInfo","colorWarning","colorError","filledSuccess","filledInfo","filledWarning","filledError","outlined","outlinedSuccess","outlinedInfo","outlinedWarning","outlinedError","standard","standardSuccess","standardInfo","standardWarning","standardError"]);function x2(a){return rn("MuiCircularProgress",a)}Ge("MuiCircularProgress",["root","determinate","indeterminate","colorPrimary","colorSecondary","svg","circle","circleDeterminate","circleIndeterminate","circleDisableShrink"]);const Sa=44,Ds=nr`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
`,Bs=nr`
  0% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 100px, 200px;
    stroke-dashoffset: -15px;
  }

  100% {
    stroke-dasharray: 1px, 200px;
    stroke-dashoffset: -126px;
  }
`,E2=typeof Ds!="string"?ks`
        animation: ${Ds} 1.4s linear infinite;
      `:null,A2=typeof Bs!="string"?ks`
        animation: ${Bs} 1.4s ease-in-out infinite;
      `:null,O2=a=>{const{classes:r,variant:u,color:o,disableShrink:s}=a,f={root:["root",u,`color${rt(o)}`],svg:["svg"],circle:["circle",`circle${rt(u)}`,s&&"circleDisableShrink"]};return dn(f,x2,r)},M2=Gt("span",{name:"MuiCircularProgress",slot:"Root",overridesResolver:(a,r)=>{const{ownerState:u}=a;return[r.root,r[u.variant],r[`color${rt(u.color)}`]]}})(On(({theme:a})=>({display:"inline-block",variants:[{props:{variant:"determinate"},style:{transition:a.transitions.create("transform")}},{props:{variant:"indeterminate"},style:E2||{animation:`${Ds} 1.4s linear infinite`}},...Object.entries(a.palette).filter(an()).map(([r])=>({props:{color:r},style:{color:(a.vars||a).palette[r].main}}))]}))),R2=Gt("svg",{name:"MuiCircularProgress",slot:"Svg"})({display:"block"}),z2=Gt("circle",{name:"MuiCircularProgress",slot:"Circle",overridesResolver:(a,r)=>{const{ownerState:u}=a;return[r.circle,r[`circle${rt(u.variant)}`],u.disableShrink&&r.circleDisableShrink]}})(On(({theme:a})=>({stroke:"currentColor",variants:[{props:{variant:"determinate"},style:{transition:a.transitions.create("stroke-dashoffset")}},{props:{variant:"indeterminate"},style:{strokeDasharray:"80px, 200px",strokeDashoffset:0}},{props:({ownerState:r})=>r.variant==="indeterminate"&&!r.disableShrink,style:A2||{animation:`${Bs} 1.4s ease-in-out infinite`}}]}))),q0=$.forwardRef(function(r,u){const o=Ve({props:r,name:"MuiCircularProgress"}),{className:s,color:f="primary",disableShrink:p=!1,size:m=40,style:v,thickness:y=3.6,value:S=0,variant:E="indeterminate",...A}=o,U={...o,color:f,disableShrink:p,size:m,thickness:y,value:S,variant:E},M=O2(U),T={},G={},X={};if(E==="determinate"){const W=2*Math.PI*((Sa-y)/2);T.strokeDasharray=W.toFixed(3),X["aria-valuenow"]=Math.round(S),T.strokeDashoffset=`${((100-S)/100*W).toFixed(3)}px`,G.transform="rotate(-90deg)"}return tt.jsx(M2,{className:Lt(M.root,s),style:{width:m,height:m,...G,...v},ownerState:U,ref:u,role:"progressbar",...X,...A,children:tt.jsx(R2,{className:M.svg,ownerState:U,viewBox:`${Sa/2} ${Sa/2} ${Sa} ${Sa}`,children:tt.jsx(z2,{className:M.circle,style:T,ownerState:U,cx:Sa,cy:Sa,r:(Sa-y)/2,fill:"none",strokeWidth:y})})})});function _2(a){return rn("MuiIconButton",a)}const Gm=Ge("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge","loading","loadingIndicator","loadingWrapper"]),D2=a=>{const{classes:r,disabled:u,color:o,edge:s,size:f,loading:p}=a,m={root:["root",p&&"loading",u&&"disabled",o!=="default"&&`color${rt(o)}`,s&&`edge${rt(s)}`,`size${rt(f)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]};return dn(m,_2,r)},B2=Gt($u,{name:"MuiIconButton",slot:"Root",overridesResolver:(a,r)=>{const{ownerState:u}=a;return[r.root,u.loading&&r.loading,u.color!=="default"&&r[`color${rt(u.color)}`],u.edge&&r[`edge${rt(u.edge)}`],r[`size${rt(u.size)}`]]}})(On(({theme:a})=>({textAlign:"center",flex:"0 0 auto",fontSize:a.typography.pxToRem(24),padding:8,borderRadius:"50%",color:(a.vars||a).palette.action.active,transition:a.transitions.create("background-color",{duration:a.transitions.duration.shortest}),variants:[{props:r=>!r.disableRipple,style:{"--IconButton-hoverBg":a.vars?`rgba(${a.vars.palette.action.activeChannel} / ${a.vars.palette.action.hoverOpacity})`:ne(a.palette.action.active,a.palette.action.hoverOpacity),"&:hover":{backgroundColor:"var(--IconButton-hoverBg)","@media (hover: none)":{backgroundColor:"transparent"}}}},{props:{edge:"start"},style:{marginLeft:-12}},{props:{edge:"start",size:"small"},style:{marginLeft:-3}},{props:{edge:"end"},style:{marginRight:-12}},{props:{edge:"end",size:"small"},style:{marginRight:-3}}]})),On(({theme:a})=>({variants:[{props:{color:"inherit"},style:{color:"inherit"}},...Object.entries(a.palette).filter(an()).map(([r])=>({props:{color:r},style:{color:(a.vars||a).palette[r].main}})),...Object.entries(a.palette).filter(an()).map(([r])=>({props:{color:r},style:{"--IconButton-hoverBg":a.vars?`rgba(${(a.vars||a).palette[r].mainChannel} / ${a.vars.palette.action.hoverOpacity})`:ne((a.vars||a).palette[r].main,a.palette.action.hoverOpacity)}})),{props:{size:"small"},style:{padding:5,fontSize:a.typography.pxToRem(18)}},{props:{size:"large"},style:{padding:12,fontSize:a.typography.pxToRem(28)}}],[`&.${Gm.disabled}`]:{backgroundColor:"transparent",color:(a.vars||a).palette.action.disabled},[`&.${Gm.loading}`]:{color:"transparent"}}))),N2=Gt("span",{name:"MuiIconButton",slot:"LoadingIndicator"})(({theme:a})=>({display:"none",position:"absolute",visibility:"visible",top:"50%",left:"50%",transform:"translate(-50%, -50%)",color:(a.vars||a).palette.action.disabled,variants:[{props:{loading:!0},style:{display:"flex"}}]})),U2=$.forwardRef(function(r,u){const o=Ve({props:r,name:"MuiIconButton"}),{edge:s=!1,children:f,className:p,color:m="default",disabled:v=!1,disableFocusRipple:y=!1,size:S="medium",id:E,loading:A=null,loadingIndicator:U,...M}=o,T=H0(E),G=U??tt.jsx(q0,{"aria-labelledby":T,color:"inherit",size:16}),X={...o,edge:s,color:m,disabled:v,disableFocusRipple:y,loading:A,loadingIndicator:G,size:S},W=D2(X);return tt.jsxs(B2,{id:A?T:E,className:Lt(W.root,p),centerRipple:!0,focusRipple:!y,disabled:v||A,ref:u,...M,ownerState:X,children:[typeof A=="boolean"&&tt.jsx("span",{className:W.loadingWrapper,style:{display:"contents"},children:tt.jsx(N2,{className:W.loadingIndicator,ownerState:X,children:A&&G})}),f]})}),w2=Va(tt.jsx("path",{d:"M20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.76,4 13.5,4.11 14.2, 4.31L15.77,2.74C14.61,2.26 13.34,2 12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0, 0 22,12M7.91,10.08L6.5,11.5L11,16L21,6L19.59,4.58L11,13.17L7.91,10.08Z"})),$2=Va(tt.jsx("path",{d:"M12 5.99L19.53 19H4.47L12 5.99M12 2L1 21h22L12 2zm1 14h-2v2h2v-2zm0-6h-2v4h2v-4z"})),H2=Va(tt.jsx("path",{d:"M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"})),j2=Va(tt.jsx("path",{d:"M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20, 12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10, 10 0 0,0 12,2M11,17H13V11H11V17Z"})),L2=Va(tt.jsx("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})),q2=a=>{const{variant:r,color:u,severity:o,classes:s}=a,f={root:["root",`color${rt(u||o)}`,`${r}${rt(u||o)}`,`${r}`],icon:["icon"],message:["message"],action:["action"]};return dn(f,T2,s)},Y2=Gt(L0,{name:"MuiAlert",slot:"Root",overridesResolver:(a,r)=>{const{ownerState:u}=a;return[r.root,r[u.variant],r[`${u.variant}${rt(u.color||u.severity)}`]]}})(On(({theme:a})=>{const r=a.palette.mode==="light"?Pi:Fi,u=a.palette.mode==="light"?Fi:Pi;return{...a.typography.body2,backgroundColor:"transparent",display:"flex",padding:"6px 16px",variants:[...Object.entries(a.palette).filter(an(["light"])).map(([o])=>({props:{colorSeverity:o,variant:"standard"},style:{color:a.vars?a.vars.palette.Alert[`${o}Color`]:r(a.palette[o].light,.6),backgroundColor:a.vars?a.vars.palette.Alert[`${o}StandardBg`]:u(a.palette[o].light,.9),[`& .${km.icon}`]:a.vars?{color:a.vars.palette.Alert[`${o}IconColor`]}:{color:a.palette[o].main}}})),...Object.entries(a.palette).filter(an(["light"])).map(([o])=>({props:{colorSeverity:o,variant:"outlined"},style:{color:a.vars?a.vars.palette.Alert[`${o}Color`]:r(a.palette[o].light,.6),border:`1px solid ${(a.vars||a).palette[o].light}`,[`& .${km.icon}`]:a.vars?{color:a.vars.palette.Alert[`${o}IconColor`]}:{color:a.palette[o].main}}})),...Object.entries(a.palette).filter(an(["dark"])).map(([o])=>({props:{colorSeverity:o,variant:"filled"},style:{fontWeight:a.typography.fontWeightMedium,...a.vars?{color:a.vars.palette.Alert[`${o}FilledColor`],backgroundColor:a.vars.palette.Alert[`${o}FilledBg`]}:{backgroundColor:a.palette.mode==="dark"?a.palette[o].dark:a.palette[o].main,color:a.palette.getContrastText(a.palette[o].main)}}}))]}})),k2=Gt("div",{name:"MuiAlert",slot:"Icon"})({marginRight:12,padding:"7px 0",display:"flex",fontSize:22,opacity:.9}),G2=Gt("div",{name:"MuiAlert",slot:"Message"})({padding:"8px 0",minWidth:0,overflow:"auto"}),V2=Gt("div",{name:"MuiAlert",slot:"Action"})({display:"flex",alignItems:"flex-start",padding:"4px 0 0 16px",marginLeft:"auto",marginRight:-8}),Vm={success:tt.jsx(w2,{fontSize:"inherit"}),warning:tt.jsx($2,{fontSize:"inherit"}),error:tt.jsx(H2,{fontSize:"inherit"}),info:tt.jsx(j2,{fontSize:"inherit"})},RC=$.forwardRef(function(r,u){const o=Ve({props:r,name:"MuiAlert"}),{action:s,children:f,className:p,closeText:m="Close",color:v,components:y={},componentsProps:S={},icon:E,iconMapping:A=Vm,onClose:U,role:M="alert",severity:T="success",slotProps:G={},slots:X={},variant:W="standard",...V}=o,j={...o,color:v,severity:T,variant:W,colorSeverity:v||T},R=q2(j),Q={slots:{closeButton:y.CloseButton,closeIcon:y.CloseIcon,...X},slotProps:{...S,...G}},[F,I]=$l("root",{ref:u,shouldForwardComponentProp:!0,className:Lt(R.root,p),elementType:Y2,externalForwardedProps:{...Q,...V},ownerState:j,additionalProps:{role:M,elevation:0}}),[et,g]=$l("icon",{className:R.icon,elementType:k2,externalForwardedProps:Q,ownerState:j}),[Y,J]=$l("message",{className:R.message,elementType:G2,externalForwardedProps:Q,ownerState:j}),[it,pt]=$l("action",{className:R.action,elementType:V2,externalForwardedProps:Q,ownerState:j}),[mt,N]=$l("closeButton",{elementType:U2,externalForwardedProps:Q,ownerState:j}),[Z,nt]=$l("closeIcon",{elementType:L2,externalForwardedProps:Q,ownerState:j});return tt.jsxs(F,{...I,children:[E!==!1?tt.jsx(et,{...g,children:E||A[T]||Vm[T]}):null,tt.jsx(Y,{...J,children:f}),s!=null?tt.jsx(it,{...pt,children:s}):null,s==null&&U?tt.jsx(it,{...pt,children:tt.jsx(mt,{size:"small","aria-label":m,title:m,color:"inherit",onClick:U,...N,children:tt.jsx(Z,{fontSize:"small",...nt})})}):null]})});function X2(a){return rn("MuiTypography",a)}Ge("MuiTypography",["root","h1","h2","h3","h4","h5","h6","subtitle1","subtitle2","body1","body2","inherit","button","caption","overline","alignLeft","alignRight","alignCenter","alignJustify","noWrap","gutterBottom","paragraph"]);const Q2={primary:!0,secondary:!0,error:!0,info:!0,success:!0,warning:!0,textPrimary:!0,textSecondary:!0,textDisabled:!0},Z2=wS(),K2=a=>{const{align:r,gutterBottom:u,noWrap:o,paragraph:s,variant:f,classes:p}=a,m={root:["root",f,a.align!=="inherit"&&`align${rt(r)}`,u&&"gutterBottom",o&&"noWrap",s&&"paragraph"]};return dn(m,X2,p)},J2=Gt("span",{name:"MuiTypography",slot:"Root",overridesResolver:(a,r)=>{const{ownerState:u}=a;return[r.root,u.variant&&r[u.variant],u.align!=="inherit"&&r[`align${rt(u.align)}`],u.noWrap&&r.noWrap,u.gutterBottom&&r.gutterBottom,u.paragraph&&r.paragraph]}})(On(({theme:a})=>{var r;return{margin:0,variants:[{props:{variant:"inherit"},style:{font:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}},...Object.entries(a.typography).filter(([u,o])=>u!=="inherit"&&o&&typeof o=="object").map(([u,o])=>({props:{variant:u},style:o})),...Object.entries(a.palette).filter(an()).map(([u])=>({props:{color:u},style:{color:(a.vars||a).palette[u].main}})),...Object.entries(((r=a.palette)==null?void 0:r.text)||{}).filter(([,u])=>typeof u=="string").map(([u])=>({props:{color:`text${rt(u)}`},style:{color:(a.vars||a).palette.text[u]}})),{props:({ownerState:u})=>u.align!=="inherit",style:{textAlign:"var(--Typography-textAlign)"}},{props:({ownerState:u})=>u.noWrap,style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},{props:({ownerState:u})=>u.gutterBottom,style:{marginBottom:"0.35em"}},{props:({ownerState:u})=>u.paragraph,style:{marginBottom:16}}]}})),Xm={h1:"h1",h2:"h2",h3:"h3",h4:"h4",h5:"h5",h6:"h6",subtitle1:"h6",subtitle2:"h6",body1:"p",body2:"p",inherit:"p"},zC=$.forwardRef(function(r,u){const{color:o,...s}=Ve({props:r,name:"MuiTypography"}),f=!Q2[o],p=Z2({...s,...f&&{color:o}}),{align:m="inherit",className:v,component:y,gutterBottom:S=!1,noWrap:E=!1,paragraph:A=!1,variant:U="body1",variantMapping:M=Xm,...T}=p,G={...p,align:m,color:o,className:v,component:y,gutterBottom:S,noWrap:E,paragraph:A,variant:U,variantMapping:M},X=y||(A?"p":M[U]||Xm[U])||"span",W=K2(G);return tt.jsx(J2,{as:X,ref:u,className:Lt(W.root,v),...T,ownerState:G,style:{...m!=="inherit"&&{"--Typography-textAlign":m},...T.style}})}),W2=Va(tt.jsx("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"}));function P2(a){return rn("MuiChip",a)}const Tt=Ge("MuiChip",["root","sizeSmall","sizeMedium","colorDefault","colorError","colorInfo","colorPrimary","colorSecondary","colorSuccess","colorWarning","disabled","clickable","clickableColorPrimary","clickableColorSecondary","deletable","deletableColorPrimary","deletableColorSecondary","outlined","filled","outlinedPrimary","outlinedSecondary","filledPrimary","filledSecondary","avatar","avatarSmall","avatarMedium","avatarColorPrimary","avatarColorSecondary","icon","iconSmall","iconMedium","iconColorPrimary","iconColorSecondary","label","labelSmall","labelMedium","deleteIcon","deleteIconSmall","deleteIconMedium","deleteIconColorPrimary","deleteIconColorSecondary","deleteIconOutlinedColorPrimary","deleteIconOutlinedColorSecondary","deleteIconFilledColorPrimary","deleteIconFilledColorSecondary","focusVisible"]),F2=a=>{const{classes:r,disabled:u,size:o,color:s,iconColor:f,onDelete:p,clickable:m,variant:v}=a,y={root:["root",v,u&&"disabled",`size${rt(o)}`,`color${rt(s)}`,m&&"clickable",m&&`clickableColor${rt(s)}`,p&&"deletable",p&&`deletableColor${rt(s)}`,`${v}${rt(s)}`],label:["label",`label${rt(o)}`],avatar:["avatar",`avatar${rt(o)}`,`avatarColor${rt(s)}`],icon:["icon",`icon${rt(o)}`,`iconColor${rt(f)}`],deleteIcon:["deleteIcon",`deleteIcon${rt(o)}`,`deleteIconColor${rt(s)}`,`deleteIcon${rt(v)}Color${rt(s)}`]};return dn(y,P2,r)},I2=Gt("div",{name:"MuiChip",slot:"Root",overridesResolver:(a,r)=>{const{ownerState:u}=a,{color:o,iconColor:s,clickable:f,onDelete:p,size:m,variant:v}=u;return[{[`& .${Tt.avatar}`]:r.avatar},{[`& .${Tt.avatar}`]:r[`avatar${rt(m)}`]},{[`& .${Tt.avatar}`]:r[`avatarColor${rt(o)}`]},{[`& .${Tt.icon}`]:r.icon},{[`& .${Tt.icon}`]:r[`icon${rt(m)}`]},{[`& .${Tt.icon}`]:r[`iconColor${rt(s)}`]},{[`& .${Tt.deleteIcon}`]:r.deleteIcon},{[`& .${Tt.deleteIcon}`]:r[`deleteIcon${rt(m)}`]},{[`& .${Tt.deleteIcon}`]:r[`deleteIconColor${rt(o)}`]},{[`& .${Tt.deleteIcon}`]:r[`deleteIcon${rt(v)}Color${rt(o)}`]},r.root,r[`size${rt(m)}`],r[`color${rt(o)}`],f&&r.clickable,f&&o!=="default"&&r[`clickableColor${rt(o)})`],p&&r.deletable,p&&o!=="default"&&r[`deletableColor${rt(o)}`],r[v],r[`${v}${rt(o)}`]]}})(On(({theme:a})=>{const r=a.palette.mode==="light"?a.palette.grey[700]:a.palette.grey[300];return{maxWidth:"100%",fontFamily:a.typography.fontFamily,fontSize:a.typography.pxToRem(13),display:"inline-flex",alignItems:"center",justifyContent:"center",height:32,color:(a.vars||a).palette.text.primary,backgroundColor:(a.vars||a).palette.action.selected,borderRadius:32/2,whiteSpace:"nowrap",transition:a.transitions.create(["background-color","box-shadow"]),cursor:"unset",outline:0,textDecoration:"none",border:0,padding:0,verticalAlign:"middle",boxSizing:"border-box",[`&.${Tt.disabled}`]:{opacity:(a.vars||a).palette.action.disabledOpacity,pointerEvents:"none"},[`& .${Tt.avatar}`]:{marginLeft:5,marginRight:-6,width:24,height:24,color:a.vars?a.vars.palette.Chip.defaultAvatarColor:r,fontSize:a.typography.pxToRem(12)},[`& .${Tt.avatarColorPrimary}`]:{color:(a.vars||a).palette.primary.contrastText,backgroundColor:(a.vars||a).palette.primary.dark},[`& .${Tt.avatarColorSecondary}`]:{color:(a.vars||a).palette.secondary.contrastText,backgroundColor:(a.vars||a).palette.secondary.dark},[`& .${Tt.avatarSmall}`]:{marginLeft:4,marginRight:-4,width:18,height:18,fontSize:a.typography.pxToRem(10)},[`& .${Tt.icon}`]:{marginLeft:5,marginRight:-6},[`& .${Tt.deleteIcon}`]:{WebkitTapHighlightColor:"transparent",color:a.vars?`rgba(${a.vars.palette.text.primaryChannel} / 0.26)`:ne(a.palette.text.primary,.26),fontSize:22,cursor:"pointer",margin:"0 5px 0 -6px","&:hover":{color:a.vars?`rgba(${a.vars.palette.text.primaryChannel} / 0.4)`:ne(a.palette.text.primary,.4)}},variants:[{props:{size:"small"},style:{height:24,[`& .${Tt.icon}`]:{fontSize:18,marginLeft:4,marginRight:-4},[`& .${Tt.deleteIcon}`]:{fontSize:16,marginRight:4,marginLeft:-4}}},...Object.entries(a.palette).filter(an(["contrastText"])).map(([u])=>({props:{color:u},style:{backgroundColor:(a.vars||a).palette[u].main,color:(a.vars||a).palette[u].contrastText,[`& .${Tt.deleteIcon}`]:{color:a.vars?`rgba(${a.vars.palette[u].contrastTextChannel} / 0.7)`:ne(a.palette[u].contrastText,.7),"&:hover, &:active":{color:(a.vars||a).palette[u].contrastText}}}})),{props:u=>u.iconColor===u.color,style:{[`& .${Tt.icon}`]:{color:a.vars?a.vars.palette.Chip.defaultIconColor:r}}},{props:u=>u.iconColor===u.color&&u.color!=="default",style:{[`& .${Tt.icon}`]:{color:"inherit"}}},{props:{onDelete:!0},style:{[`&.${Tt.focusVisible}`]:{backgroundColor:a.vars?`rgba(${a.vars.palette.action.selectedChannel} / calc(${a.vars.palette.action.selectedOpacity} + ${a.vars.palette.action.focusOpacity}))`:ne(a.palette.action.selected,a.palette.action.selectedOpacity+a.palette.action.focusOpacity)}}},...Object.entries(a.palette).filter(an(["dark"])).map(([u])=>({props:{color:u,onDelete:!0},style:{[`&.${Tt.focusVisible}`]:{background:(a.vars||a).palette[u].dark}}})),{props:{clickable:!0},style:{userSelect:"none",WebkitTapHighlightColor:"transparent",cursor:"pointer","&:hover":{backgroundColor:a.vars?`rgba(${a.vars.palette.action.selectedChannel} / calc(${a.vars.palette.action.selectedOpacity} + ${a.vars.palette.action.hoverOpacity}))`:ne(a.palette.action.selected,a.palette.action.selectedOpacity+a.palette.action.hoverOpacity)},[`&.${Tt.focusVisible}`]:{backgroundColor:a.vars?`rgba(${a.vars.palette.action.selectedChannel} / calc(${a.vars.palette.action.selectedOpacity} + ${a.vars.palette.action.focusOpacity}))`:ne(a.palette.action.selected,a.palette.action.selectedOpacity+a.palette.action.focusOpacity)},"&:active":{boxShadow:(a.vars||a).shadows[1]}}},...Object.entries(a.palette).filter(an(["dark"])).map(([u])=>({props:{color:u,clickable:!0},style:{[`&:hover, &.${Tt.focusVisible}`]:{backgroundColor:(a.vars||a).palette[u].dark}}})),{props:{variant:"outlined"},style:{backgroundColor:"transparent",border:a.vars?`1px solid ${a.vars.palette.Chip.defaultBorder}`:`1px solid ${a.palette.mode==="light"?a.palette.grey[400]:a.palette.grey[700]}`,[`&.${Tt.clickable}:hover`]:{backgroundColor:(a.vars||a).palette.action.hover},[`&.${Tt.focusVisible}`]:{backgroundColor:(a.vars||a).palette.action.focus},[`& .${Tt.avatar}`]:{marginLeft:4},[`& .${Tt.avatarSmall}`]:{marginLeft:2},[`& .${Tt.icon}`]:{marginLeft:4},[`& .${Tt.iconSmall}`]:{marginLeft:2},[`& .${Tt.deleteIcon}`]:{marginRight:5},[`& .${Tt.deleteIconSmall}`]:{marginRight:3}}},...Object.entries(a.palette).filter(an()).map(([u])=>({props:{variant:"outlined",color:u},style:{color:(a.vars||a).palette[u].main,border:`1px solid ${a.vars?`rgba(${a.vars.palette[u].mainChannel} / 0.7)`:ne(a.palette[u].main,.7)}`,[`&.${Tt.clickable}:hover`]:{backgroundColor:a.vars?`rgba(${a.vars.palette[u].mainChannel} / ${a.vars.palette.action.hoverOpacity})`:ne(a.palette[u].main,a.palette.action.hoverOpacity)},[`&.${Tt.focusVisible}`]:{backgroundColor:a.vars?`rgba(${a.vars.palette[u].mainChannel} / ${a.vars.palette.action.focusOpacity})`:ne(a.palette[u].main,a.palette.action.focusOpacity)},[`& .${Tt.deleteIcon}`]:{color:a.vars?`rgba(${a.vars.palette[u].mainChannel} / 0.7)`:ne(a.palette[u].main,.7),"&:hover, &:active":{color:(a.vars||a).palette[u].main}}}}))]}})),tC=Gt("span",{name:"MuiChip",slot:"Label",overridesResolver:(a,r)=>{const{ownerState:u}=a,{size:o}=u;return[r.label,r[`label${rt(o)}`]]}})({overflow:"hidden",textOverflow:"ellipsis",paddingLeft:12,paddingRight:12,whiteSpace:"nowrap",variants:[{props:{variant:"outlined"},style:{paddingLeft:11,paddingRight:11}},{props:{size:"small"},style:{paddingLeft:8,paddingRight:8}},{props:{size:"small",variant:"outlined"},style:{paddingLeft:7,paddingRight:7}}]});function Qm(a){return a.key==="Backspace"||a.key==="Delete"}const _C=$.forwardRef(function(r,u){const o=Ve({props:r,name:"MuiChip"}),{avatar:s,className:f,clickable:p,color:m="default",component:v,deleteIcon:y,disabled:S=!1,icon:E,label:A,onClick:U,onDelete:M,onKeyDown:T,onKeyUp:G,size:X="medium",variant:W="filled",tabIndex:V,skipFocusWhenDisabled:j=!1,...R}=o,Q=$.useRef(null),F=Uu(Q,u),I=at=>{at.stopPropagation(),M&&M(at)},et=at=>{at.currentTarget===at.target&&Qm(at)&&at.preventDefault(),T&&T(at)},g=at=>{at.currentTarget===at.target&&M&&Qm(at)&&M(at),G&&G(at)},Y=p!==!1&&U?!0:p,J=Y||M?$u:v||"div",it={...o,component:J,disabled:S,size:X,color:m,iconColor:$.isValidElement(E)&&E.props.color||m,onDelete:!!M,clickable:Y,variant:W},pt=F2(it),mt=J===$u?{component:v||"div",focusVisibleClassName:pt.focusVisible,...M&&{disableRipple:!0}}:{};let N=null;M&&(N=y&&$.isValidElement(y)?$.cloneElement(y,{className:Lt(y.props.className,pt.deleteIcon),onClick:I}):tt.jsx(W2,{className:pt.deleteIcon,onClick:I}));let Z=null;s&&$.isValidElement(s)&&(Z=$.cloneElement(s,{className:Lt(pt.avatar,s.props.className)}));let nt=null;return E&&$.isValidElement(E)&&(nt=$.cloneElement(E,{className:Lt(pt.icon,E.props.className)})),tt.jsxs(I2,{as:J,className:Lt(pt.root,f),disabled:Y&&S?!0:void 0,onClick:U,onKeyDown:et,onKeyUp:g,ref:F,tabIndex:j&&S?-1:V,ownerState:it,...mt,...R,children:[Z||nt,tt.jsx(tC,{className:pt.label,ownerState:it,children:A}),N]})}),eC=Ge("MuiBox",["root"]),nC=tf(),DC=gb({themeId:En,defaultTheme:nC,defaultClassName:eC.root,generateClassName:v0.generate});function aC(a){return rn("MuiButton",a)}const Ya=Ge("MuiButton",["root","text","textInherit","textPrimary","textSecondary","textSuccess","textError","textInfo","textWarning","outlined","outlinedInherit","outlinedPrimary","outlinedSecondary","outlinedSuccess","outlinedError","outlinedInfo","outlinedWarning","contained","containedInherit","containedPrimary","containedSecondary","containedSuccess","containedError","containedInfo","containedWarning","disableElevation","focusVisible","disabled","colorInherit","colorPrimary","colorSecondary","colorSuccess","colorError","colorInfo","colorWarning","textSizeSmall","textSizeMedium","textSizeLarge","outlinedSizeSmall","outlinedSizeMedium","outlinedSizeLarge","containedSizeSmall","containedSizeMedium","containedSizeLarge","sizeMedium","sizeSmall","sizeLarge","fullWidth","startIcon","endIcon","icon","iconSizeSmall","iconSizeMedium","iconSizeLarge","loading","loadingWrapper","loadingIconPlaceholder","loadingIndicator","loadingPositionCenter","loadingPositionStart","loadingPositionEnd"]),lC=$.createContext({}),iC=$.createContext(void 0),rC=a=>{const{color:r,disableElevation:u,fullWidth:o,size:s,variant:f,loading:p,loadingPosition:m,classes:v}=a,y={root:["root",p&&"loading",f,`${f}${rt(r)}`,`size${rt(s)}`,`${f}Size${rt(s)}`,`color${rt(r)}`,u&&"disableElevation",o&&"fullWidth",p&&`loadingPosition${rt(m)}`],startIcon:["icon","startIcon",`iconSize${rt(s)}`],endIcon:["icon","endIcon",`iconSize${rt(s)}`],loadingIndicator:["loadingIndicator"],loadingWrapper:["loadingWrapper"]},S=dn(y,aC,v);return{...v,...S}},Y0=[{props:{size:"small"},style:{"& > *:nth-of-type(1)":{fontSize:18}}},{props:{size:"medium"},style:{"& > *:nth-of-type(1)":{fontSize:20}}},{props:{size:"large"},style:{"& > *:nth-of-type(1)":{fontSize:22}}}],uC=Gt($u,{shouldForwardProp:a=>w0(a)||a==="classes",name:"MuiButton",slot:"Root",overridesResolver:(a,r)=>{const{ownerState:u}=a;return[r.root,r[u.variant],r[`${u.variant}${rt(u.color)}`],r[`size${rt(u.size)}`],r[`${u.variant}Size${rt(u.size)}`],u.color==="inherit"&&r.colorInherit,u.disableElevation&&r.disableElevation,u.fullWidth&&r.fullWidth,u.loading&&r.loading]}})(On(({theme:a})=>{const r=a.palette.mode==="light"?a.palette.grey[300]:a.palette.grey[800],u=a.palette.mode==="light"?a.palette.grey.A100:a.palette.grey[700];return{...a.typography.button,minWidth:64,padding:"6px 16px",border:0,borderRadius:(a.vars||a).shape.borderRadius,transition:a.transitions.create(["background-color","box-shadow","border-color","color"],{duration:a.transitions.duration.short}),"&:hover":{textDecoration:"none"},[`&.${Ya.disabled}`]:{color:(a.vars||a).palette.action.disabled},variants:[{props:{variant:"contained"},style:{color:"var(--variant-containedColor)",backgroundColor:"var(--variant-containedBg)",boxShadow:(a.vars||a).shadows[2],"&:hover":{boxShadow:(a.vars||a).shadows[4],"@media (hover: none)":{boxShadow:(a.vars||a).shadows[2]}},"&:active":{boxShadow:(a.vars||a).shadows[8]},[`&.${Ya.focusVisible}`]:{boxShadow:(a.vars||a).shadows[6]},[`&.${Ya.disabled}`]:{color:(a.vars||a).palette.action.disabled,boxShadow:(a.vars||a).shadows[0],backgroundColor:(a.vars||a).palette.action.disabledBackground}}},{props:{variant:"outlined"},style:{padding:"5px 15px",border:"1px solid currentColor",borderColor:"var(--variant-outlinedBorder, currentColor)",backgroundColor:"var(--variant-outlinedBg)",color:"var(--variant-outlinedColor)",[`&.${Ya.disabled}`]:{border:`1px solid ${(a.vars||a).palette.action.disabledBackground}`}}},{props:{variant:"text"},style:{padding:"6px 8px",color:"var(--variant-textColor)",backgroundColor:"var(--variant-textBg)"}},...Object.entries(a.palette).filter(an()).map(([o])=>({props:{color:o},style:{"--variant-textColor":(a.vars||a).palette[o].main,"--variant-outlinedColor":(a.vars||a).palette[o].main,"--variant-outlinedBorder":a.vars?`rgba(${a.vars.palette[o].mainChannel} / 0.5)`:ne(a.palette[o].main,.5),"--variant-containedColor":(a.vars||a).palette[o].contrastText,"--variant-containedBg":(a.vars||a).palette[o].main,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":(a.vars||a).palette[o].dark,"--variant-textBg":a.vars?`rgba(${a.vars.palette[o].mainChannel} / ${a.vars.palette.action.hoverOpacity})`:ne(a.palette[o].main,a.palette.action.hoverOpacity),"--variant-outlinedBorder":(a.vars||a).palette[o].main,"--variant-outlinedBg":a.vars?`rgba(${a.vars.palette[o].mainChannel} / ${a.vars.palette.action.hoverOpacity})`:ne(a.palette[o].main,a.palette.action.hoverOpacity)}}}})),{props:{color:"inherit"},style:{color:"inherit",borderColor:"currentColor","--variant-containedBg":a.vars?a.vars.palette.Button.inheritContainedBg:r,"@media (hover: hover)":{"&:hover":{"--variant-containedBg":a.vars?a.vars.palette.Button.inheritContainedHoverBg:u,"--variant-textBg":a.vars?`rgba(${a.vars.palette.text.primaryChannel} / ${a.vars.palette.action.hoverOpacity})`:ne(a.palette.text.primary,a.palette.action.hoverOpacity),"--variant-outlinedBg":a.vars?`rgba(${a.vars.palette.text.primaryChannel} / ${a.vars.palette.action.hoverOpacity})`:ne(a.palette.text.primary,a.palette.action.hoverOpacity)}}}},{props:{size:"small",variant:"text"},style:{padding:"4px 5px",fontSize:a.typography.pxToRem(13)}},{props:{size:"large",variant:"text"},style:{padding:"8px 11px",fontSize:a.typography.pxToRem(15)}},{props:{size:"small",variant:"outlined"},style:{padding:"3px 9px",fontSize:a.typography.pxToRem(13)}},{props:{size:"large",variant:"outlined"},style:{padding:"7px 21px",fontSize:a.typography.pxToRem(15)}},{props:{size:"small",variant:"contained"},style:{padding:"4px 10px",fontSize:a.typography.pxToRem(13)}},{props:{size:"large",variant:"contained"},style:{padding:"8px 22px",fontSize:a.typography.pxToRem(15)}},{props:{disableElevation:!0},style:{boxShadow:"none","&:hover":{boxShadow:"none"},[`&.${Ya.focusVisible}`]:{boxShadow:"none"},"&:active":{boxShadow:"none"},[`&.${Ya.disabled}`]:{boxShadow:"none"}}},{props:{fullWidth:!0},style:{width:"100%"}},{props:{loadingPosition:"center"},style:{transition:a.transitions.create(["background-color","box-shadow","border-color"],{duration:a.transitions.duration.short}),[`&.${Ya.loading}`]:{color:"transparent"}}}]}})),oC=Gt("span",{name:"MuiButton",slot:"StartIcon",overridesResolver:(a,r)=>{const{ownerState:u}=a;return[r.startIcon,u.loading&&r.startIconLoadingStart,r[`iconSize${rt(u.size)}`]]}})(({theme:a})=>({display:"inherit",marginRight:8,marginLeft:-4,variants:[{props:{size:"small"},style:{marginLeft:-2}},{props:{loadingPosition:"start",loading:!0},style:{transition:a.transitions.create(["opacity"],{duration:a.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"start",loading:!0,fullWidth:!0},style:{marginRight:-8}},...Y0]})),cC=Gt("span",{name:"MuiButton",slot:"EndIcon",overridesResolver:(a,r)=>{const{ownerState:u}=a;return[r.endIcon,u.loading&&r.endIconLoadingEnd,r[`iconSize${rt(u.size)}`]]}})(({theme:a})=>({display:"inherit",marginRight:-4,marginLeft:8,variants:[{props:{size:"small"},style:{marginRight:-2}},{props:{loadingPosition:"end",loading:!0},style:{transition:a.transitions.create(["opacity"],{duration:a.transitions.duration.short}),opacity:0}},{props:{loadingPosition:"end",loading:!0,fullWidth:!0},style:{marginLeft:-8}},...Y0]})),sC=Gt("span",{name:"MuiButton",slot:"LoadingIndicator"})(({theme:a})=>({display:"none",position:"absolute",visibility:"visible",variants:[{props:{loading:!0},style:{display:"flex"}},{props:{loadingPosition:"start"},style:{left:14}},{props:{loadingPosition:"start",size:"small"},style:{left:10}},{props:{variant:"text",loadingPosition:"start"},style:{left:6}},{props:{loadingPosition:"center"},style:{left:"50%",transform:"translate(-50%)",color:(a.vars||a).palette.action.disabled}},{props:{loadingPosition:"end"},style:{right:14}},{props:{loadingPosition:"end",size:"small"},style:{right:10}},{props:{variant:"text",loadingPosition:"end"},style:{right:6}},{props:{loadingPosition:"start",fullWidth:!0},style:{position:"relative",left:-10}},{props:{loadingPosition:"end",fullWidth:!0},style:{position:"relative",right:-10}}]})),Zm=Gt("span",{name:"MuiButton",slot:"LoadingIconPlaceholder"})({display:"inline-block",width:"1em",height:"1em"}),BC=$.forwardRef(function(r,u){const o=$.useContext(lC),s=$.useContext(iC),f=Nu(o,r),p=Ve({props:f,name:"MuiButton"}),{children:m,color:v="primary",component:y="button",className:S,disabled:E=!1,disableElevation:A=!1,disableFocusRipple:U=!1,endIcon:M,focusVisibleClassName:T,fullWidth:G=!1,id:X,loading:W=null,loadingIndicator:V,loadingPosition:j="center",size:R="medium",startIcon:Q,type:F,variant:I="text",...et}=p,g=H0(X),Y=V??tt.jsx(q0,{"aria-labelledby":g,color:"inherit",size:16}),J={...p,color:v,component:y,disabled:E,disableElevation:A,disableFocusRipple:U,fullWidth:G,loading:W,loadingIndicator:Y,loadingPosition:j,size:R,type:F,variant:I},it=rC(J),pt=(Q||W&&j==="start")&&tt.jsx(oC,{className:it.startIcon,ownerState:J,children:Q||tt.jsx(Zm,{className:it.loadingIconPlaceholder,ownerState:J})}),mt=(M||W&&j==="end")&&tt.jsx(cC,{className:it.endIcon,ownerState:J,children:M||tt.jsx(Zm,{className:it.loadingIconPlaceholder,ownerState:J})}),N=s||"",Z=typeof W=="boolean"?tt.jsx("span",{className:it.loadingWrapper,style:{display:"contents"},children:W&&tt.jsx(sC,{className:it.loadingIndicator,ownerState:J,children:Y})}):null;return tt.jsxs(uC,{ownerState:J,className:Lt(o.className,it.root,S,N),component:y,disabled:E||W,focusRipple:!U,focusVisibleClassName:Lt(it.focusVisible,T),ref:u,type:F,id:W?g:X,...et,classes:it,children:[pt,j!=="end"&&Z,m,j==="end"&&Z,mt]})});function fC(a){return rn("MuiCard",a)}Ge("MuiCard",["root"]);const dC=a=>{const{classes:r}=a;return dn({root:["root"]},fC,r)},pC=Gt(L0,{name:"MuiCard",slot:"Root"})({overflow:"hidden"}),NC=$.forwardRef(function(r,u){const o=Ve({props:r,name:"MuiCard"}),{className:s,raised:f=!1,...p}=o,m={...o,raised:f},v=dC(m);return tt.jsx(pC,{className:Lt(v.root,s),elevation:f?8:void 0,ref:u,ownerState:m,...p})});function hC(a){return rn("MuiCardContent",a)}Ge("MuiCardContent",["root"]);const mC=a=>{const{classes:r}=a;return dn({root:["root"]},hC,r)},yC=Gt("div",{name:"MuiCardContent",slot:"Root"})({padding:16,"&:last-child":{paddingBottom:24}}),UC=$.forwardRef(function(r,u){const o=Ve({props:r,name:"MuiCardContent"}),{className:s,component:f="div",...p}=o,m={...o,component:f},v=mC(m);return tt.jsx(yC,{as:f,className:Lt(v.root,s),ownerState:m,ref:u,...p})}),Ns=typeof $0({})=="function",gC=(a,r)=>({WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",boxSizing:"border-box",WebkitTextSizeAdjust:"100%",...r&&!a.vars&&{colorScheme:a.palette.mode}}),vC=a=>({color:(a.vars||a).palette.text.primary,...a.typography.body1,backgroundColor:(a.vars||a).palette.background.default,"@media print":{backgroundColor:(a.vars||a).palette.common.white}}),k0=(a,r=!1)=>{var f,p;const u={};r&&a.colorSchemes&&typeof a.getColorSchemeSelector=="function"&&Object.entries(a.colorSchemes).forEach(([m,v])=>{var S,E;const y=a.getColorSchemeSelector(m);y.startsWith("@")?u[y]={":root":{colorScheme:(S=v.palette)==null?void 0:S.mode}}:u[y.replace(/\s*&/,"")]={colorScheme:(E=v.palette)==null?void 0:E.mode}});let o={html:gC(a,r),"*, *::before, *::after":{boxSizing:"inherit"},"strong, b":{fontWeight:a.typography.fontWeightBold},body:{margin:0,...vC(a),"&::backdrop":{backgroundColor:(a.vars||a).palette.background.default}},...u};const s=(p=(f=a.components)==null?void 0:f.MuiCssBaseline)==null?void 0:p.styleOverrides;return s&&(o=[o,s]),o},zu="mui-ecs",bC=a=>{const r=k0(a,!1),u=Array.isArray(r)?r[0]:r;return!a.vars&&u&&(u.html[`:root:has(${zu})`]={colorScheme:a.palette.mode}),a.colorSchemes&&Object.entries(a.colorSchemes).forEach(([o,s])=>{var p,m;const f=a.getColorSchemeSelector(o);f.startsWith("@")?u[f]={[`:root:not(:has(.${zu}))`]:{colorScheme:(p=s.palette)==null?void 0:p.mode}}:u[f.replace(/\s*&/,"")]={[`&:not(:has(.${zu}))`]:{colorScheme:(m=s.palette)==null?void 0:m.mode}}}),r},SC=$0(Ns?({theme:a,enableColorScheme:r})=>k0(a,r):({theme:a})=>bC(a));function wC(a){const r=Ve({props:a,name:"MuiCssBaseline"}),{children:u,enableColorScheme:o=!1}=r;return tt.jsxs($.Fragment,{children:[Ns&&tt.jsx(SC,{enableColorScheme:o}),!Ns&&!o&&tt.jsx("span",{className:zu,style:{display:"none"}}),u]})}const $C=Va(tt.jsx("path",{d:"m16 6 2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"}));export{EC as $,RC as A,DC as B,wC as C,ne as D,Mb as E,Ru as F,l2 as G,Ym as H,U2 as I,an as J,Fi as K,Pi as L,_S as M,PS as N,lf as O,L0 as P,q0 as Q,TC as R,hv as S,MC as T,Tb as U,Nu as V,Ks as W,g0 as X,OC as Y,Zs as Z,Qn as _,Ge as a,Xs as a0,De as a1,xC as a2,lr as a3,lC as a4,iC as a5,mS as a6,ks as a7,nr as a8,Rs as a9,Hl as aa,Km as ab,fv as ac,kS as ad,YS as ae,jm as af,a2 as ag,i2 as ah,n2 as ai,$0 as aj,Ga as ak,dn as b,Lt as c,Va as d,tf as e,zC as f,rn as g,NC as h,UC as i,tt as j,$C as k,BC as l,_C as m,Uu as n,T0 as o,AC as p,$l as q,$ as r,Gt as s,rt as t,Ve as u,$u as v,w0 as w,zS as x,H0 as y,On as z};
