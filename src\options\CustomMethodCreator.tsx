import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  Card,
  CardContent,
  IconButton,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Alert,
  Grid,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Close as CloseIcon
} from '@mui/icons-material';

interface Question {
  text: string;
  type: 'radio' | 'select' | 'textarea';
  options?: Array<{
    value: string;
    label: string;
    weight: number;
  }>;
  placeholder?: string;
}

interface CustomMethod {
  id?: string;
  name: string;
  icon: string;
  description: string;
  questions: Question[];
  totalMaxScore: number;
  createdAt?: string;
  isCustom: boolean;
}

interface Props {
  open: boolean;
  onClose: () => void;
  onSave: (method: CustomMethod) => void;
  editingMethod?: CustomMethod | null;
}

const CustomMethodCreator: React.FC<Props> = ({ open, onClose, onSave, editingMethod }) => {
  const [method, setMethod] = useState<CustomMethod>({
    name: '',
    icon: '📈',
    description: '',
    questions: [],
    totalMaxScore: 0,
    isCustom: true
  });
  
  const [errors, setErrors] = useState<string[]>([]);

  useEffect(() => {
    if (editingMethod) {
      setMethod(editingMethod);
    } else {
      // Reset to default with 3 empty questions
      setMethod({
        name: '',
        icon: '📈',
        description: '',
        questions: [
          { text: '', type: 'radio', options: [{ value: 'option_0', label: '', weight: 10 }, { value: 'option_1', label: '', weight: 5 }] },
          { text: '', type: 'radio', options: [{ value: 'option_0', label: '', weight: 10 }, { value: 'option_1', label: '', weight: 5 }] },
          { text: '', type: 'radio', options: [{ value: 'option_0', label: '', weight: 10 }, { value: 'option_1', label: '', weight: 5 }] }
        ],
        totalMaxScore: 0,
        isCustom: true
      });
    }
  }, [editingMethod, open]);

  const addQuestion = () => {
    if (method.questions.length < 7) {
      setMethod(prev => ({
        ...prev,
        questions: [...prev.questions, {
          text: '',
          type: 'radio',
          options: [
            { value: 'option_0', label: '', weight: 10 },
            { value: 'option_1', label: '', weight: 5 }
          ]
        }]
      }));
    }
  };

  const removeQuestion = (index: number) => {
    if (method.questions.length > 3) {
      setMethod(prev => ({
        ...prev,
        questions: prev.questions.filter((_, i) => i !== index)
      }));
    }
  };

  const updateQuestion = (index: number, field: string, value: any) => {
    setMethod(prev => ({
      ...prev,
      questions: prev.questions.map((q, i) => 
        i === index ? { ...q, [field]: value } : q
      )
    }));
  };

  const addOption = (questionIndex: number) => {
    const question = method.questions[questionIndex];
    if (question.options && question.options.length < 6) {
      const newOption = {
        value: `option_${question.options.length}`,
        label: '',
        weight: 5
      };
      updateQuestion(questionIndex, 'options', [...question.options, newOption]);
    }
  };

  const removeOption = (questionIndex: number, optionIndex: number) => {
    const question = method.questions[questionIndex];
    if (question.options && question.options.length > 2) {
      const newOptions = question.options.filter((_, i) => i !== optionIndex);
      updateQuestion(questionIndex, 'options', newOptions);
    }
  };

  const updateOption = (questionIndex: number, optionIndex: number, field: string, value: any) => {
    const question = method.questions[questionIndex];
    if (question.options) {
      const newOptions = question.options.map((opt, i) =>
        i === optionIndex ? { ...opt, [field]: value } : opt
      );
      updateQuestion(questionIndex, 'options', newOptions);
    }
  };

  const calculateTotalScore = () => {
    let total = 0;
    method.questions.forEach(question => {
      if (question.type === 'textarea') {
        total += 10; // Default score for textarea
      } else if (question.options && question.options.length > 0) {
        const maxWeight = Math.max(...question.options.map(opt => opt.weight));
        total += maxWeight;
      }
    });
    return total;
  };

  const validateMethod = (): string[] => {
    const errors: string[] = [];
    
    if (!method.name || method.name.length < 3) {
      errors.push('Tên phương pháp phải có ít nhất 3 ký tự');
    }
    
    if (!method.description || method.description.length < 10) {
      errors.push('Mô tả phải có ít nhất 10 ký tự');
    }
    
    if (!method.icon) {
      errors.push('Vui lòng chọn icon');
    }
    
    if (method.questions.length < 3) {
      errors.push('Cần ít nhất 3 câu hỏi');
    }
    
    method.questions.forEach((question, i) => {
      if (!question.text || question.text.length < 5) {
        errors.push(`Câu hỏi ${i + 1}: Nội dung phải có ít nhất 5 ký tự`);
      }
      
      if (question.type !== 'textarea') {
        if (!question.options || question.options.length < 2) {
          errors.push(`Câu hỏi ${i + 1}: Cần ít nhất 2 đáp án`);
        } else {
          question.options.forEach((option, j) => {
            if (!option.label || option.label.length < 2) {
              errors.push(`Câu hỏi ${i + 1}, Đáp án ${j + 1}: Nội dung quá ngắn`);
            }
            if (option.weight < 0 || option.weight > 50) {
              errors.push(`Câu hỏi ${i + 1}, Đáp án ${j + 1}: Điểm phải từ 0-50`);
            }
          });
        }
      }
    });
    
    const totalScore = calculateTotalScore();
    if (totalScore < 50) {
      errors.push('Tổng điểm tối đa quá thấp (< 50)');
    }
    if (totalScore > 200) {
      errors.push('Tổng điểm tối đa quá cao (> 200)');
    }
    
    return errors;
  };

  const handleSave = () => {
    const validationErrors = validateMethod();
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }
    
    const finalMethod = {
      ...method,
      id: editingMethod?.id || `custom_${Date.now()}`,
      totalMaxScore: calculateTotalScore(),
      createdAt: editingMethod?.createdAt || new Date().toISOString()
    };
    
    onSave(finalMethod);
    onClose();
  };

  const handleClose = () => {
    setErrors([]);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            {editingMethod ? '✏️ Chỉnh sửa phương pháp' : '⚙️ Tạo phương pháp tùy chỉnh'}
          </Typography>
          <IconButton onClick={handleClose}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      
      <DialogContent>
        {errors.length > 0 && (
          <Alert severity="error" sx={{ mb: 2 }}>
            <ul style={{ margin: 0, paddingLeft: 20 }}>
              {errors.map((error, i) => (
                <li key={i}>{error}</li>
              ))}
            </ul>
          </Alert>
        )}
        
        <Grid container spacing={2}>
          {/* Basic Info */}
          <Grid size={12}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>📊 Thông tin cơ bản</Typography>
                <Grid container spacing={2}>
                  <Grid size={{ xs: 12, md: 8 }}>
                    <TextField
                      fullWidth
                      label="Tên phương pháp"
                      value={method.name}
                      onChange={(e) => setMethod(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Ví dụ: Fibonacci Retracement Strategy"
                    />
                  </Grid>
                  <Grid size={{ xs: 12 ,md: 4 }}>
                    <TextField
                      fullWidth
                      label="Icon (emoji)"
                      value={method.icon}
                      onChange={(e) => setMethod(prev => ({ ...prev, icon: e.target.value }))}
                      inputProps={{ maxLength: 2, style: { textAlign: 'center', fontSize: '20px' } }}
                    />
                  </Grid>
                  <Grid size={12}>
                    <TextField
                      fullWidth
                      multiline
                      rows={3}
                      label="Mô tả phương pháp"
                      value={method.description}
                      onChange={(e) => setMethod(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Mô tả ngắn gọn về phương pháp giao dịch này..."
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
          
          {/* Questions */}
          <Grid size={12}>
            <Card variant="outlined">
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">❓ Câu hỏi phân tích</Typography>
                  <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                    <Chip label={`${method.questions.length} câu hỏi`} size="small" />
                    <Chip label={`${calculateTotalScore()} điểm`} size="small" color="primary" />
                    <Button
                      size="small"
                      startIcon={<AddIcon />}
                      onClick={addQuestion}
                      disabled={method.questions.length >= 7}
                    >
                      Thêm câu hỏi
                    </Button>
                  </Box>
                </Box>
                
                {method.questions.map((question, qIndex) => (
                  <Card key={qIndex} variant="outlined" sx={{ mb: 2 }}>
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                          Câu hỏi {qIndex + 1}
                        </Typography>
                        {method.questions.length > 3 && (
                          <IconButton size="small" color="error" onClick={() => removeQuestion(qIndex)}>
                            <DeleteIcon />
                          </IconButton>
                        )}
                      </Box>
                      
                      <Grid container spacing={2}>
                        <Grid size={{ xs: 12, md: 8 }}>
                          <TextField
                            fullWidth
                            label="Nội dung câu hỏi"
                            value={question.text}
                            onChange={(e) => updateQuestion(qIndex, 'text', e.target.value)}
                            placeholder="Ví dụ: Có tín hiệu divergence rõ ràng không?"
                          />
                        </Grid>
                        <Grid size={{ xs: 12, md: 4 }}>
                          <FormControl fullWidth>
                            <InputLabel>Loại câu hỏi</InputLabel>
                            <Select
                              value={question.type}
                              label="Loại câu hỏi"
                              onChange={(e) => {
                                const newType = e.target.value as 'radio' | 'select' | 'textarea';
                                if (newType === 'textarea') {
                                  updateQuestion(qIndex, 'type', newType);
                                  updateQuestion(qIndex, 'options', undefined);
                                  updateQuestion(qIndex, 'placeholder', 'Mô tả chi tiết setup...');
                                } else {
                                  updateQuestion(qIndex, 'type', newType);
                                  if (!question.options) {
                                    updateQuestion(qIndex, 'options', [
                                      { value: 'option_0', label: '', weight: 10 },
                                      { value: 'option_1', label: '', weight: 5 }
                                    ]);
                                  }
                                }
                              }}
                            >
                              <MenuItem value="radio">Multiple Choice (Radio)</MenuItem>
                              <MenuItem value="select">Dropdown (Select)</MenuItem>
                              <MenuItem value="textarea">Mô tả chi tiết (Textarea)</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>
                        
                        {question.type !== 'textarea' && question.options && (
                          <Grid size={12}>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                              <Typography variant="body2" color="text.secondary">Đáp án:</Typography>
                              <Button
                                size="small"
                                startIcon={<AddIcon />}
                                onClick={() => addOption(qIndex)}
                                disabled={!question.options || question.options.length >= 6}
                              >
                                Thêm đáp án
                              </Button>
                            </Box>
                            {question.options.map((option, oIndex) => (
                              <Box key={oIndex} sx={{ display: 'flex', gap: 1, mb: 1, alignItems: 'center' }}>
                                <TextField
                                  size="small"
                                  placeholder="Nội dung đáp án..."
                                  value={option.label}
                                  onChange={(e) => updateOption(qIndex, oIndex, 'label', e.target.value)}
                                  sx={{ flex: 1 }}
                                />
                                <TextField
                                  size="small"
                                  type="number"
                                  placeholder="Điểm"
                                  value={option.weight}
                                  onChange={(e) => updateOption(qIndex, oIndex, 'weight', parseInt(e.target.value) || 0)}
                                  inputProps={{ min: 0, max: 50 }}
                                  sx={{ width: 80 }}
                                />
                                {question.options && question.options.length > 2 && (
                                  <IconButton size="small" color="error" onClick={() => removeOption(qIndex, oIndex)}>
                                    <DeleteIcon />
                                  </IconButton>
                                )}
                              </Box>
                            ))}
                          </Grid>
                        )}
                      </Grid>
                    </CardContent>
                  </Card>
                ))}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={handleClose}>Hủy</Button>
        <Button variant="contained" startIcon={<SaveIcon />} onClick={handleSave}>
          {editingMethod ? 'Cập nhật' : 'Tạo phương pháp'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CustomMethodCreator;
