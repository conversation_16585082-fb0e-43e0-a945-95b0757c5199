// Binomo Trading Assistant - Daily Goals Modal (Material UI)

import React, { useState, useEffect } from 'react';
import { createRoot } from 'react-dom/client';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Typography,
  Alert,
  Box,
  IconButton,
  ThemeProvider,
  createTheme,
  CssBaseline
} from '@mui/material';
import { Close as CloseIcon, TrackChanges as TargetIcon } from '@mui/icons-material';
import { storage, DailyGoals } from '../utils/storage';
import { formatDate } from '../utils/helpers';

// Material UI Theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#007bff',
    },
    secondary: {
      main: '#6c757d',
    },
  },
});

// Daily Goals Modal Component
interface DailyGoalsModalProps {
  open: boolean;
  onClose: () => void;
  onComplete?: (goals: DailyGoals) => void;
  onSave?: (goals: any) => void;
  existingGoals?: DailyGoals | null;
  compact?: boolean;
}

const DailyGoalsModalComponent: React.FC<DailyGoalsModalProps> = ({
  open,
  onClose,
  onComplete,
  existingGoals
}) => {
  const [formData, setFormData] = useState({
    tradingGoal: '',
    profitTarget: '',
    lossLimit: '',
    lesson1: '',
    lesson2: '',
    lesson3: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    if (existingGoals) {
      setFormData({
        tradingGoal: existingGoals.tradingGoal,
        profitTarget: existingGoals.profitTarget.toString(),
        lossLimit: existingGoals.lossLimit.toString(),
        lesson1: existingGoals.lessons[0] || '',
        lesson2: existingGoals.lessons[1] || '',
        lesson3: existingGoals.lessons[2] || ''
      });
    }
  }, [existingGoals]);

  const handleInputChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value
    }));
    setError(null);
  };

  const validateForm = (): string | null => {
    if (!formData.tradingGoal.trim()) {
      return 'Vui lòng nhập mục tiêu giao dịch';
    }
    if (!formData.profitTarget || isNaN(Number(formData.profitTarget)) || Number(formData.profitTarget) <= 0) {
      return 'Vui lòng nhập mục tiêu lợi nhuận hợp lệ';
    }
    if (!formData.lossLimit || isNaN(Number(formData.lossLimit)) || Number(formData.lossLimit) <= 0) {
      return 'Vui lòng nhập giới hạn thua lỗ hợp lệ';
    }
    if (!formData.lesson1.trim() || !formData.lesson2.trim() || !formData.lesson3.trim()) {
      return 'Vui lòng nhập đầy đủ 3 bài học';
    }
    return null;
  };

  const handleSubmit = async () => {
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const goals: Omit<DailyGoals, 'id' | 'date' | 'createdAt' | 'updatedAt'> = {
        tradingGoal: formData.tradingGoal.trim(),
        profitTarget: Number(formData.profitTarget),
        lossLimit: Number(formData.lossLimit),
        lessons: [
          formData.lesson1.trim(),
          formData.lesson2.trim(),
          formData.lesson3.trim()
        ],
        completed: true
      };

      await storage.setTodayGoals(goals);
      setSuccess('Mục tiêu đã được lưu thành công!');
      
      setTimeout(() => {
        const savedGoals = {
          ...goals,
          id: formatDate(new Date()),
          date: formatDate(new Date()),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        onComplete(savedGoals);
        onClose();
      }, 1500);
    } catch (error) {
      setError('Có lỗi xảy ra khi lưu mục tiêu. Vui lòng thử lại.');
      console.error('Error saving daily goals:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSkip = async () => {
    try {
      const goals: Omit<DailyGoals, 'id' | 'date' | 'createdAt' | 'updatedAt'> = {
        tradingGoal: 'Bỏ qua thiết lập mục tiêu hôm nay',
        profitTarget: 0,
        lossLimit: 0,
        lessons: ['', '', ''],
        completed: false
      };

      await storage.setTodayGoals(goals);
      onClose();
    } catch (error) {
      console.error('Error skipping daily goals:', error);
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 }
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1, pb: 1 }}>
        <TargetIcon color="primary" />
        <Typography variant="h5" component="span" sx={{ flexGrow: 1 }}>
          🎯 Mục tiêu giao dịch hôm nay
        </Typography>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers>
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>Chào mừng bạn đến với phiên giao dịch mới!</strong><br />
            Hãy thiết lập mục tiêu rõ ràng để có một ngày giao dịch thành công.
          </Typography>
        </Alert>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        <Box component="form" sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          <TextField
            label="Mục tiêu giao dịch hôm nay là gì?"
            multiline
            rows={3}
            value={formData.tradingGoal}
            onChange={handleInputChange('tradingGoal')}
            placeholder="Ví dụ: Thực hành phương pháp quá mua/quá bán, kiểm soát tâm lý, tuân thủ kế hoạch..."
            required
            fullWidth
          />

          <Box sx={{ display: 'flex', gap: 2 }}>
            <TextField
              label="Mục tiêu lợi nhuận ($)"
              type="number"
              value={formData.profitTarget}
              onChange={handleInputChange('profitTarget')}
              placeholder="Ví dụ: 50"
              required
              fullWidth
              inputProps={{ min: 1, step: 0.01 }}
            />
            <TextField
              label="Giới hạn thua lỗ ($)"
              type="number"
              value={formData.lossLimit}
              onChange={handleInputChange('lossLimit')}
              placeholder="Ví dụ: 30"
              required
              fullWidth
              inputProps={{ min: 1, step: 0.01 }}
            />
          </Box>

          <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
            3 bài học từ những lần thua sấp mặt trước đây:
          </Typography>

          <TextField
            label="Bài học 1"
            multiline
            rows={2}
            value={formData.lesson1}
            onChange={handleInputChange('lesson1')}
            placeholder="Ví dụ: Không được giao dịch khi tâm lý tham lam"
            required
            fullWidth
          />

          <TextField
            label="Bài học 2"
            multiline
            rows={2}
            value={formData.lesson2}
            onChange={handleInputChange('lesson2')}
            placeholder="Ví dụ: Phải tuân thủ nghiêm ngặt kế hoạch quản lý vốn"
            required
            fullWidth
          />

          <TextField
            label="Bài học 3"
            multiline
            rows={2}
            value={formData.lesson3}
            onChange={handleInputChange('lesson3')}
            placeholder="Ví dụ: Không được revenge trading khi thua liên tiếp"
            required
            fullWidth
          />
        </Box>

        <Alert severity="warning" sx={{ mt: 3 }}>
          <Typography variant="body2">
            <strong>Lưu ý:</strong> Những mục tiêu này sẽ giúp bạn duy trì kỷ luật và tập trung trong suốt phiên giao dịch.
          </Typography>
        </Alert>
      </DialogContent>

      <DialogActions sx={{ p: 2, gap: 1 }}>
        <Button 
          onClick={handleSkip}
          color="secondary"
          disabled={loading}
        >
          Bỏ qua hôm nay
        </Button>
        <Button 
          onClick={handleSubmit}
          variant="contained"
          disabled={loading}
          sx={{ minWidth: 120 }}
        >
          {loading ? 'Đang lưu...' : 'Lưu mục tiêu'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export class DailyGoalsModal {
  private container: HTMLElement | null = null;
  private root: any = null;
  private onComplete: ((goals: DailyGoals) => void) | null = null;

  constructor(onComplete?: (goals: DailyGoals) => void) {
    this.onComplete = onComplete || null;
  }

  async show(): Promise<void> {
    // Check if goals already set for today
    const todayGoals = await storage.getTodayGoals();
    if (todayGoals && todayGoals.completed) {
      return; // Goals already set for today
    }

    this.createModal(todayGoals);
  }

  private createModal(existingGoals?: DailyGoals | null): void {
    // Create container for React component
    this.container = document.createElement('div');
    this.container.id = 'daily-goals-modal-container';
    document.body.appendChild(this.container);

    // Create React root and render component
    this.root = createRoot(this.container);
    this.root.render(
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <DailyGoalsModalComponent
          open={true}
          onClose={() => this.hide()}
          onComplete={(goals) => {
            if (this.onComplete) {
              this.onComplete(goals);
            }
            this.hide();
          }}
          existingGoals={existingGoals}
        />
      </ThemeProvider>
    );
  }

  hide(): void {
    if (this.root) {
      this.root.unmount();
      this.root = null;
    }
    if (this.container) {
      document.body.removeChild(this.container);
      this.container = null;
    }
  }

  static async shouldShow(): Promise<boolean> {
    const todayGoals = await storage.getTodayGoals();
    return !todayGoals; // Show if no goals set for today
  }

  static async showIfNeeded(onComplete?: (goals: DailyGoals) => void): Promise<void> {
    const shouldShow = await DailyGoalsModal.shouldShow();
    if (shouldShow) {
      const modal = new DailyGoalsModal(onComplete);
      await modal.show();
    }
  }
}
