import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { copyFileSync, existsSync, mkdirSync, writeFileSync } from 'fs';

// Custom plugin to generate and copy extension assets
function generateExtensionAssets() {
  return {
    name: 'generate-extension-assets',
    writeBundle() {
      const distDir = resolve(__dirname, 'dist');

      // Ensure dist directory exists
      if (!existsSync(distDir)) {
        mkdirSync(distDir, { recursive: true });
      }

      try {
        // Copy manifest.json
        copyFileSync(
          resolve(__dirname, 'public/manifest.json'),
          resolve(distDir, 'manifest.json')
        );

        // Copy icons
        const icons = ['icon16.png', 'icon48.png', 'icon128.png'];
        icons.forEach(icon => {
          copyFileSync(
            resolve(__dirname, `public/${icon}`),
            resolve(distDir, icon)
          );
        });

        // Copy content.css
        copyFileSync(
          resolve(__dirname, 'src/content/content.css'),
          resolve(distDir, 'content.css')
        );

        // Generate popup.html
        const popupHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Binomo Trading Assistant</title>
  <style>
    body {
      width: 400px;
      height: 600px;
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    #root {
      width: 100%;
      height: 100%;
    }
  </style>
</head>
<body>
  <div id="root"></div>
  <script type="module" src="popup/popup.js"></script>
</body>
</html>`;

        writeFileSync(resolve(distDir, 'popup.html'), popupHtml);
        console.log('✅ popup.html generated');

        // Generate options.html
        const optionsHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Binomo Trading Assistant - Options</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background-color: #f5f5f5;
    }
    #root {
      width: 100%;
      min-height: 100vh;
    }
  </style>
</head>
<body>
  <div id="root"></div>
  <script type="module" src="options/options.js"></script>
</body>
</html>`;

        writeFileSync(resolve(distDir, 'options.html'), optionsHtml);
        console.log('✅ options.html generated');

        // Generate sidebar.html
        const sidebarHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Binomo Trading Assistant - Sidebar</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background-color: #f5f5f5;
      width: 400px;
      min-height: 100vh;
    }
    #root {
      width: 100%;
      min-height: 100vh;
    }
  </style>
</head>
<body>
  <div id="root"></div>
  <script type="module" src="sidebar/sidebar.js"></script>
</body>
</html>`;

        writeFileSync(resolve(distDir, 'sidebar.html'), sidebarHtml);
        console.log('✅ sidebar.html generated');

        console.log('✅ Extension assets generated successfully!');
      } catch (error) {
        console.error('❌ Error generating extension assets:', error);
      }
    }
  };
}

export default defineConfig({
  plugins: [react(), generateExtensionAssets()],
  build: {
    rollupOptions: {
      input: {
        popup: resolve(__dirname, "src/popup/index.tsx"),
        options: resolve(__dirname, "src/options/index.tsx"),
        sidebar: resolve(__dirname, "src/sidebar/index.tsx"),
        background: resolve(__dirname, "src/background/background.ts"),
        content: resolve(__dirname, "src/content/content-minimal.ts"), // Minimal essential features
      },
      output: {
        entryFileNames: chunk => {
          if (chunk.name === "background") return "background/background.js";
          if (chunk.name === "content") return "content/content.js";
          if (chunk.name === "popup") return "popup/popup.js";
          if (chunk.name === "options") return "options/options.js";
          if (chunk.name === "sidebar") return "sidebar/sidebar.js";
          return "[name]/[name].js";
        },
        assetFileNames: () => {
          return "[name].[ext]";
        }
      }
    },
    outDir: "dist",
    emptyOutDir: true,
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
  }
});
