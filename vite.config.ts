import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { copyFileSync, existsSync, mkdirSync, writeFileSync } from 'fs';

// HTML Template Generator
interface HTMLTemplateOptions {
  title: string;
  scriptPath: string;
  width?: string;
  height?: string;
  backgroundColor?: string;
  additionalStyles?: string;
}

function generateHTMLTemplate(options: HTMLTemplateOptions): string {
  const {
    title,
    scriptPath,
    width = '100%',
    height = '100vh',
    backgroundColor = '#f5f5f5',
    additionalStyles = ''
  } = options;

  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${title}</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background-color: ${backgroundColor};
      ${width !== '100%' ? `width: ${width};` : ''}
      ${height !== '100vh' ? `height: ${height};` : `min-height: ${height};`}
    }
    #root {
      width: 100%;
      ${height !== '100vh' ? `height: ${height};` : `min-height: ${height};`}
    }
    ${additionalStyles}
  </style>
</head>
<body>
  <div id="root"></div>
  <script type="module" src="${scriptPath}"></script>
</body>
</html>`;
}

// HTML Files Configuration
const htmlConfigs = {
  popup: {
    title: 'Binomo Trading Assistant',
    scriptPath: 'popup/popup.js',
    width: '400px',
    height: '600px',
    backgroundColor: '#ffffff',
    additionalStyles: `
      #root {
        height: 100%;
      }
    `
  },
  options: {
    title: 'Binomo Trading Assistant - Options',
    scriptPath: 'options/options.js',
    backgroundColor: '#f5f5f5'
  },
  sidebar: {
    title: 'Binomo Trading Assistant - Sidebar',
    scriptPath: 'sidebar/sidebar.js',
    width: '400px',
    backgroundColor: '#f5f5f5'
  }
};

// Generate all HTML files
function generateAllHTMLFiles(distDir: string): void {
  Object.entries(htmlConfigs).forEach(([name, config]) => {
    const htmlContent = generateHTMLTemplate(config);
    const filePath = resolve(distDir, `${name}.html`);
    writeFileSync(filePath, htmlContent);
    console.log(`✅ ${name}.html generated`);
  });
}

// Copy static assets
function copyStaticAssets(distDir: string): void {
  const staticAssets = [
    { from: 'public/manifest.json', to: 'manifest.json' },
    { from: 'public/icon16.png', to: 'icon16.png' },
    { from: 'public/icon48.png', to: 'icon48.png' },
    { from: 'public/icon128.png', to: 'icon128.png' },
    { from: 'src/content/content.css', to: 'content.css' }
  ];

  staticAssets.forEach(({ from, to }) => {
    try {
      copyFileSync(resolve(__dirname, from), resolve(distDir, to));
      console.log(`✅ ${to} copied`);
    } catch (error) {
      console.error(`❌ Error copying ${to}:`, error);
    }
  });
}

// Show generation summary
function showGenerationSummary(): void {
  console.log('\n🎉 Extension Assets Generation Complete!');
  console.log('📁 Generated Files:');
  console.log('   📄 HTML Files: popup.html, options.html, sidebar.html');
  console.log('   📋 Manifest: manifest.json');
  console.log('   🎨 Icons: icon16.png, icon48.png, icon128.png');
  console.log('   💄 Styles: content.css');
  console.log('✨ Ready for Chrome Extension loading!\n');
}

// Custom plugin to generate and copy extension assets
function generateExtensionAssets() {
  return {
    name: 'generate-extension-assets',
    writeBundle() {
      const distDir = resolve(__dirname, 'dist');

      // Ensure dist directory exists
      if (!existsSync(distDir)) {
        mkdirSync(distDir, { recursive: true });
      }

      try {
        // Copy all static assets
        copyStaticAssets(distDir);

        // Generate all HTML files
        generateAllHTMLFiles(distDir);

        // Show generation summary
        showGenerationSummary();
      } catch (error) {
        console.error('❌ Error generating extension assets:', error);
      }
    }
  };
}

export default defineConfig({
  plugins: [react(), generateExtensionAssets()],
  build: {
    rollupOptions: {
      input: {
        popup: resolve(__dirname, "src/popup/index.tsx"),
        options: resolve(__dirname, "src/options/index.tsx"),
        sidebar: resolve(__dirname, "src/sidebar/index.tsx"),
        background: resolve(__dirname, "src/background/background.ts"),
        content: resolve(__dirname, "src/content/content-minimal.ts"), // Minimal essential features
      },
      output: {
        entryFileNames: chunk => {
          if (chunk.name === "background") return "background/background.js";
          if (chunk.name === "content") return "content/content.js";
          if (chunk.name === "popup") return "popup/popup.js";
          if (chunk.name === "options") return "options/options.js";
          if (chunk.name === "sidebar") return "sidebar/sidebar.js";
          return "[name]/[name].js";
        },
        assetFileNames: () => {
          return "[name].[ext]";
        }
      }
    },
    outDir: "dist",
    emptyOutDir: true,
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
  }
});
