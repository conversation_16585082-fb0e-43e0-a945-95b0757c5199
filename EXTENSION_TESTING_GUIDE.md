# 🧪 Extension Testing Guide

## ✅ **Extension đã được sửa và build thành công!**

### **Vấn đề đã khắc phục:**
- ❌ **Before:** Placeholder components với text "Loading Material UI components..."
- ✅ **After:** Functional React-like components với Material UI styling

## 🚀 **Testing Steps:**

### **1. Reload Extension:**
```
Chrome → chrome://extensions/ → Binomo Trading Assistant → Reload (🔄)
```

### **2. Kiểm tra JSON Server:**
```bash
# Đảm bảo JSON Server đang chạy
npm run server

# Output should show:
# \{^_^}/ hi!
# Loading db.json
# Resources: http://localhost:3001/dailyGoals
# Watching...
```

### **3. Test trên Binomo:**
```
1. Visit: https://binomo1.com/trading
2. Wait 2-3 seconds for extension to load
3. Daily Goals modal should appear automatically
```

## 📊 **Expected Behavior:**

### **🎯 Daily Goals Modal:**
- **Appears automatically** when visiting binomo1.com/trading
- **Material UI-like styling** với blue theme
- **Form fields:**
  - 💰 Mục tiêu lợi nhuận ($)
  - 🛑 Giới hạn thua lỗ ($)
  - 📊 Số lệnh tối đa
  - 📝 Mục tiêu học tập hôm nay
- **Action buttons:** "Hủy" và "Lưu mục tiêu"

### **✅ Success Flow:**
1. **Fill form** với valid data
2. **Click "Lưu mục tiêu"**
3. **Success message** appears (green toast)
4. **Psychology Assessment** modal appears after 1.5s

### **🧠 Psychology Assessment:**
- **Dropdown** với các trạng thái tâm lý:
  - 😌 Cân bằng - Tâm trạng ổn định
  - 🤑 Tham lam - Muốn kiếm nhanh
  - 😰 Sợ hãi - Lo lắng mất tiền
  - ⚡ Vội vàng - Không kiên nhẫn
  - 😎 Tự hào - Quá tự tin
  - 😡 Tức giận - Muốn revenge
- **Textarea** để mô tả cảm xúc (min 10 chars)
- **Recommendation** based on selected state

## 🔍 **Console Debugging:**

### **Expected Console Messages:**
```javascript
🎯 Binomo Trading Assistant - Content Script Loaded
🎯 Loading Binomo React Components...
✅ Binomo React Components initialized
🚀 Starting trading flow...
Showing daily goals modal...
```

### **API Success Messages:**
```javascript
✅ Daily goals saved: {profitTarget: 50, lossLimit: 20, ...}
✅ Psychology assessment saved: {state: "balanced", ...}
```

### **Error Messages to Watch:**
```javascript
❌ Error saving goals: [error details]
❌ Failed to load React components script
🚧 Đang tải Material UI components... (fallback mode)
```

## 🎨 **Visual Verification:**

### **Modal Styling:**
- **Overlay:** Semi-transparent black background
- **Modal:** White background, rounded corners, shadow
- **Header:** Blue title với close button (×)
- **Content:** Clean form layout với proper spacing
- **Buttons:** Material UI-style với hover effects

### **Responsive Design:**
- **Max-width:** 600px
- **Width:** 90% on smaller screens
- **Max-height:** 80vh với scroll
- **Animation:** Slide-in effect

## 🔧 **Troubleshooting:**

### **Issue 1: Modal không xuất hiện**
```javascript
// Check console for errors
F12 → Console tab

// Common causes:
- JSON Server not running
- Extension not reloaded
- Content script blocked
- Page not fully loaded
```

### **Issue 2: Fallback placeholder appears**
```javascript
// Indicates React components script failed to load
// Check:
- dist/content/content-react.js exists
- Web accessible resources in manifest.json
- No console errors loading script
```

### **Issue 3: API calls fail**
```javascript
// Check JSON Server status
curl http://localhost:3001/dailyGoals

// Should return 200 OK with JSON data
// If fails:
npm run server  // Restart JSON Server
```

### **Issue 4: Form validation errors**
```javascript
// Daily Goals form requires:
- Profit target: number > 0
- Loss limit: number > 0  
- Max trades: number > 0
- Learning goal: any text (optional)

// Psychology Assessment requires:
- State: must select from dropdown
- Description: min 10 characters
```

## 📱 **Testing Scenarios:**

### **Scenario 1: First-time user**
```
1. Visit binomo1.com/trading
2. Daily Goals modal appears
3. Fill form and save
4. Psychology Assessment appears
5. Complete assessment
6. Get recommendation
```

### **Scenario 2: Returning user (same day)**
```
1. Visit binomo1.com/trading
2. Should skip Daily Goals (already set)
3. Psychology Assessment appears
4. Continue flow from there
```

### **Scenario 3: Error handling**
```
1. Try saving without JSON Server running
2. Should show error alert
3. Try invalid form data
4. Should show validation messages
```

### **Scenario 4: Psychology states**
```
Test each psychology state:
- balanced → ✅ Safe to trade
- greedy → ⚠️ Warning message
- fearful → ⚠️ Consider rest
- impatient → 🛑 Don't trade
- overconfident → ⚠️ Be careful
- angry → 🛑 Absolutely don't trade
```

## 🎯 **Success Criteria:**

### ✅ **Extension Working:**
- Modal appears automatically
- Form validation works
- API calls successful
- Data saves to JSON Server
- Flow continues to next step
- No console errors

### ✅ **UI/UX Quality:**
- Professional Material UI styling
- Smooth animations
- Responsive design
- Clear error messages
- Intuitive navigation

### ✅ **Data Persistence:**
- Goals saved to db.json
- Psychology states recorded
- Data survives page refresh
- API endpoints accessible

## 🚀 **Next Steps After Testing:**

### **If Working:**
1. **Continue development** of remaining components:
   - Trading Method Selector
   - Trading Analysis
   - Trading Interface
   - Stats Dashboard

### **If Issues:**
1. **Check console errors**
2. **Verify JSON Server running**
3. **Reload extension**
4. **Check file permissions**

## 📝 **Test Report Template:**

```
✅ Extension loads on binomo1.com/trading
✅ Daily Goals modal appears
✅ Form validation works
✅ Data saves successfully
✅ Psychology Assessment appears
✅ Recommendations work
✅ No console errors
✅ UI looks professional

Issues found:
- [List any issues here]

Overall Status: ✅ WORKING / ❌ NEEDS FIX
```

---

**🎉 Extension bây giờ có functional components thay vì placeholder! Test và báo cáo kết quả nhé!**
