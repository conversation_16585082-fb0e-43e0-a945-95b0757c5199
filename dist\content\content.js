var y=Object.defineProperty;var b=(u,t,e)=>t in u?y(u,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):u[t]=e;var c=(u,t,e)=>b(u,typeof t!="symbol"?t+"":t,e);console.log("🎯 Binomo Trading Assistant - Enhanced Content Script Loaded");class x{constructor(t){c(this,"apiKey");c(this,"baseURL","https://api.openai.com/v1/chat/completions");this.apiKey=t}async assessPsychology(t){try{const e=this.createAssessmentPrompt(t),o=await this.callOpenAI(e);return this.parseAIResponse(o,t)}catch(e){return console.error("Error in AI psychology assessment:",e),this.fallbackAssessment(t)}}createAssessmentPrompt(t){return`
Bạn là một chuyên gia tâm lý giao dịch với 20 năm kinh nghiệm. H<PERSON>y đánh giá tâm lý giao dịch của người này:

THÔNG TIN ĐÁNH GIÁ:
- Trạng thái cảm xúc: ${t.emotionalState}
- Tình hình tài chính: ${t.financialSituation}
- Kết quả giao dịch gần đây: ${t.recentPerformance}
- Chất lượng giấc ngủ: ${t.sleepQuality}
- Mức độ căng thẳng: ${t.stressLevel}
- Động lực giao dịch: ${t.motivation}
${t.additionalNotes?`- Ghi chú thêm: ${t.additionalNotes}`:""}

Hãy trả về đánh giá theo format JSON chính xác sau:
{
  "score": [số điểm từ 0-100],
  "emotional_factor": [điểm cảm xúc 0-100],
  "financial_factor": [điểm tài chính 0-100],
  "physical_factor": [điểm thể chất 0-100],
  "mental_factor": [điểm tinh thần 0-100],
  "should_trade": [true/false],
  "recommendation": "[khuyến nghị ngắn gọn]",
  "analysis": "[phân tích chi tiết 2-3 câu]",
  "risk_factors": ["[yếu tố rủi ro 1]", "[yếu tố rủi ro 2]"],
  "positive_factors": ["[yếu tố tích cực 1]", "[yếu tố tích cực 2]"]
}

TIÊU CHÍ ĐÁNH GIÁ:
- 90-100: Tâm lý xuất sắc, sẵn sàng giao dịch
- 80-89: Tâm lý tốt, có thể giao dịch cẩn thận
- 60-79: Tâm lý trung bình, nên nghỉ 15-30 phút
- 30-59: Tâm lý kém, nên nghỉ 4-8 tiếng
- 0-29: Tâm lý rất kém, nên nghỉ 12-24 tiếng

Chỉ trả về JSON, không có text khác.
`}async callOpenAI(t){const e=await fetch(this.baseURL,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.apiKey}`},body:JSON.stringify({model:"gpt-4o-mini",messages:[{role:"system",content:"Bạn là chuyên gia tâm lý giao dịch. Luôn trả về JSON hợp lệ."},{role:"user",content:t}],temperature:.3,max_tokens:1e3})});if(!e.ok)throw new Error(`OpenAI API error: ${e.status}`);return(await e.json()).choices[0].message.content}parseAIResponse(t,e){try{const o=t.replace(/```json\n?|\n?```/g,"").trim(),n=JSON.parse(o),a=this.getScoreLevel(n.score),i=this.calculateBlockDuration(n.score,n);return{score:n.score,level:a,shouldTrade:n.should_trade&&n.score>=60,blockDuration:i,recommendation:n.recommendation,aiAnalysis:n.analysis,factors:{emotional:n.emotional_factor||50,financial:n.financial_factor||50,physical:n.physical_factor||50,mental:n.mental_factor||50}}}catch(o){return console.error("Error parsing AI response:",o),this.fallbackAssessment(e)}}getScoreLevel(t){return t>=90?"excellent":t>=80?"good":t>=60?"fair":t>=30?"poor":"critical"}calculateBlockDuration(t,e){let o=0;return t>=80?o=0:t>=60?o=15:t>=45?o=60:t>=30?o=240:t>=15?o=720:o=1440,e.risk_factors&&e.risk_factors.length>2&&(o=Math.min(o*1.5,1440)),e.positive_factors&&e.positive_factors.length>2&&(o=Math.max(o*.7,0)),Math.round(o)}fallbackAssessment(t){let e=50;t.emotionalState.includes("cân bằng")||t.emotionalState.includes("tích cực")?e+=20:(t.emotionalState.includes("căng thẳng")||t.emotionalState.includes("lo âu"))&&(e-=20),t.financialSituation.includes("ổn định")?e+=15:t.financialSituation.includes("khó khăn")&&(e-=15),t.recentPerformance.includes("tốt")||t.recentPerformance.includes("lãi")?e+=10:(t.recentPerformance.includes("thua")||t.recentPerformance.includes("lỗ"))&&(e-=15),e=Math.max(0,Math.min(100,e));const o=this.getScoreLevel(e),n={excellent:0,good:0,fair:15,poor:240,critical:1440};return{score:e,level:o,shouldTrade:e>=60,blockDuration:n[o],recommendation:e>=60?"Có thể giao dịch cẩn thận":"Nên nghỉ ngơi và thiền định",aiAnalysis:"Đánh giá dựa trên quy tắc cơ bản (AI không khả dụng)",factors:{emotional:e,financial:e,physical:e,mental:e}}}}class g{constructor(){c(this,"isBlocked",!1);c(this,"psychologyAI",null);c(this,"lastTradeAmount",0);c(this,"tradeObserver",null);c(this,"lastTradeReasonModalTime",0);c(this,"lastMindfulnessModalTime",0);c(this,"hasShownAIPsychologyModal",!1);this.init()}async init(){await this.initializeAI(),await this.checkTradingBlock(),this.isBlocked?this.showBlockedMessage():(this.checkPsychologyConfirmation(),this.startBehaviorTracking(),this.showAIPsychologyModal())}async initializeAI(){try{const t=await chrome.storage.local.get(["openaiApiKey"]);t.openaiApiKey&&(this.psychologyAI=new x(t.openaiApiKey))}catch(t){console.error("Error initializing AI:",t)}}async checkTradingBlock(){try{const t=await chrome.storage.local.get(["tradingBlocked","blockDate","blockUntil","blockDurationMinutes"]);if(t.tradingBlocked){const e=new Date;if(t.blockUntil){const o=new Date(t.blockUntil);if(e>=o){await chrome.storage.local.remove(["tradingBlocked","blockDate","blockUntil","blockDurationMinutes"]),this.isBlocked=!1;return}}this.isBlocked=!0}}catch(t){console.error("Error checking trading block:",t),this.isBlocked=!1}}showAIPsychologyModal(){this.hasShownAIPsychologyModal||this.isBlocked||!this.psychologyAI||chrome.storage.local.get(["lastAIAssessment"]).then(t=>{if(t.lastAIAssessment){const e=t.lastAIAssessment.timestamp,o=Date.now()-60*60*1e3;if(e>o){console.log("🤖 AI assessment done recently, skipping modal");return}}setTimeout(()=>{!this.isBlocked&&this.psychologyAI&&!this.hasShownAIPsychologyModal&&(this.hasShownAIPsychologyModal=!0,this.createAIPsychologyModal())},3e3)})}createAIPsychologyModal(){var e,o;const t=document.createElement("div");t.id="ai-psychology-modal",t.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,t.innerHTML=`
      <div style="
        background: white;
        padding: 32px;
        border-radius: 16px;
        max-width: 500px;
        width: 90%;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="text-align: center; margin-bottom: 24px;">
          <div style="font-size: 48px; margin-bottom: 16px;">🧠</div>
          <h3 style="color: #1976d2; margin: 0 0 8px 0;">Đánh giá tâm lý AI</h3>
          <p style="color: #666; margin: 0; font-size: 14px;">
            Hãy chia sẻ cảm xúc hiện tại để AI đánh giá tâm lý giao dịch
          </p>
        </div>

        <div style="margin-bottom: 24px;">
          <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">
            Hiện tại bạn đang cảm thấy như thế nào?
          </label>
          <textarea 
            id="psychology-diary" 
            placeholder="Ví dụ: Tôi cảm thấy hơi lo lắng vì thua lỗ hôm qua, nhưng cũng muốn gỡ lại. Tôi đã ngủ đủ giấc và cảm thấy tỉnh táo..."
            style="
              width: 100%;
              height: 120px;
              padding: 12px;
              border: 2px solid #e0e0e0;
              border-radius: 8px;
              font-size: 14px;
              resize: vertical;
              font-family: inherit;
            "
          ></textarea>
          <div style="margin-top: 8px; font-size: 12px; color: #666;">
            💡 Hãy thành thật về cảm xúc: tham lam, giận dữ, lo lắng, sợ hãi, phấn khích...
          </div>
        </div>

        <div style="display: flex; gap: 12px;">
          <button id="skip-assessment" style="
            flex: 1;
            background: #666;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">Bỏ qua</button>
          <button id="ai-analyze" style="
            flex: 2;
            background: #1976d2;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
          ">🤖 Phân tích AI</button>
        </div>

        <div id="ai-result" style="
          margin-top: 16px;
          padding: 16px;
          background: #f5f5f5;
          border-radius: 8px;
          display: none;
        "></div>
      </div>
    `,document.body.appendChild(t),(e=document.getElementById("skip-assessment"))==null||e.addEventListener("click",()=>{t.remove()}),(o=document.getElementById("ai-analyze"))==null||o.addEventListener("click",()=>{this.performAIAnalysis()})}async performAIAnalysis(){var n;const t=(n=document.getElementById("psychology-diary"))==null?void 0:n.value,e=document.getElementById("ai-result"),o=document.getElementById("ai-analyze");if(!t.trim()){alert("Vui lòng chia sẻ cảm xúc hiện tại của bạn");return}if(!this.psychologyAI){alert("AI chưa được cấu hình. Vui lòng thêm OpenAI API key trong Settings.");return}o.textContent="🤖 Đang phân tích...",o.disabled=!0;try{const a={emotionalState:t,financialSituation:"Không rõ",recentPerformance:"Không rõ",sleepQuality:"Không rõ",stressLevel:"Không rõ",motivation:t,additionalNotes:"Đánh giá từ nhật ký tâm trạng"},i=await this.psychologyAI.assessPsychology(a);e&&(e.style.display="block",e.innerHTML=`
          <div style="text-align: center; margin-bottom: 16px;">
            <div style="font-size: 32px; margin-bottom: 8px;">
              ${i.score>=80?"😊":i.score>=60?"😐":"😰"}
            </div>
            <div style="font-size: 24px; font-weight: bold; color: ${i.score>=80?"#4caf50":i.score>=60?"#ff9800":"#f44336"};">
              ${i.score}/100 điểm
            </div>
          </div>
          
          <div style="margin-bottom: 16px;">
            <strong>Phân tích AI:</strong>
            <p style="margin: 8px 0; font-size: 14px; line-height: 1.4;">${i.aiAnalysis}</p>
          </div>
          
          <div style="margin-bottom: 16px;">
            <strong>Khuyến nghị:</strong>
            <p style="margin: 8px 0; font-size: 14px; line-height: 1.4;">${i.recommendation}</p>
          </div>
          
          ${i.blockDuration>0?`
            <div style="background: #fff3cd; padding: 12px; border-radius: 8px; border-left: 4px solid #ffc107;">
              <strong>⚠️ Khuyến nghị nghỉ ngơi:</strong> ${this.formatDuration(i.blockDuration)}
            </div>
          `:`
            <div style="background: #d4edda; padding: 12px; border-radius: 8px; border-left: 4px solid #28a745;">
              <strong>✅ Có thể giao dịch:</strong> Tâm lý ổn định
            </div>
          `}
          
          <div style="margin-top: 16px; display: flex; gap: 8px;">
            ${i.shouldTrade?`
              <button onclick="document.getElementById('ai-psychology-modal').remove()" style="
                flex: 1; background: #4caf50; color: white; border: none; padding: 10px; border-radius: 6px; cursor: pointer;
              ">✅ Tiếp tục giao dịch</button>
            `:`
              <button onclick="window.open('${chrome.runtime.getURL("options.html#meditation")}', '_blank')" style="
                flex: 1; background: #9c27b0; color: white; border: none; padding: 10px; border-radius: 6px; cursor: pointer;
              ">🧘‍♂️ Đi thiền</button>
            `}
            <button onclick="document.getElementById('ai-psychology-modal').remove()" style="
              background: #666; color: white; border: none; padding: 10px 16px; border-radius: 6px; cursor: pointer;
            ">Đóng</button>
          </div>
        `),await chrome.storage.local.set({lastAIAssessment:{timestamp:Date.now(),score:i.score,shouldTrade:i.shouldTrade,blockDuration:i.blockDuration}}),!i.shouldTrade&&i.blockDuration>0&&await this.blockTradingWithDuration(i.blockDuration)}catch(a){console.error("AI analysis error:",a),e&&(e.style.display="block",e.innerHTML=`
          <div style="background: #f8d7da; padding: 12px; border-radius: 8px; color: #721c24;">
            <strong>❌ Lỗi phân tích AI:</strong> ${a.message||"Không thể kết nối với AI"}
          </div>
        `)}o.textContent="🤖 Phân tích AI",o.disabled=!1}formatDuration(t){return t<60?`${t} phút`:t<1440?`${Math.round(t/60)} tiếng`:`${Math.round(t/1440)} ngày`}async blockTradingWithDuration(t){try{const e=new Date,o=new Date(e.getTime()+t*60*1e3);await chrome.storage.local.set({tradingBlocked:!0,blockDate:e.toISOString(),blockUntil:o.toISOString(),blockDurationMinutes:t,needsPsychologyConfirmation:!0}),setTimeout(()=>window.location.reload(),2e3)}catch(e){console.error("Error blocking trading:",e)}}startBehaviorTracking(){setTimeout(()=>{this.observeTradeActions(),this.observeTradeResults()},3e3)}observeTradeActions(){console.log("🎯 Starting trade action observation...");const t=['button[data-test-id*="deal"]','button[class*="deal"]','button[class*="trade"]','button[class*="up"]','button[class*="down"]','button[class*="higher"]','button[class*="lower"]',".deal-button",".trade-button",'[data-direction="up"]','[data-direction="down"]'],e=['input[data-test-id*="amount"]','input[class*="amount"]','input[type="number"]',".amount-input",'[data-field="amount"]'],o=()=>{e.forEach(a=>{document.querySelectorAll(a).forEach(r=>{r.hasAttribute("data-monitored")||(r.setAttribute("data-monitored","true"),r.addEventListener("change",l=>{const s=l.target;this.lastTradeAmount=parseFloat(s.value)||0,console.log("💰 Amount changed:",this.lastTradeAmount)}))})})},n=()=>{t.forEach(a=>{document.querySelectorAll(a).forEach(r=>{r.hasAttribute("data-monitored")||(r.setAttribute("data-monitored","true"),r.addEventListener("click",l=>{console.log("🎯 Deal button clicked:",r),this.isValidTradeButton(r)&&setTimeout(()=>this.showTradeReasonModal(),1e3)}))})})};o(),n(),this.tradeObserver=new MutationObserver(a=>{let i=!1;a.forEach(r=>{r.addedNodes.forEach(l=>{if(l.nodeType===Node.ELEMENT_NODE){const s=l;t.forEach(h=>{s.matches&&s.matches(h)&&(i=!0),s.querySelectorAll&&s.querySelectorAll(h).length>0&&(i=!0)})}})}),i&&setTimeout(()=>{o(),n()},500)}),this.tradeObserver.observe(document.body,{childList:!0,subtree:!0})}isValidTradeButton(t){var l;const e=((l=t.textContent)==null?void 0:l.toLowerCase())||"",o=t.className.toLowerCase(),n=t.id.toLowerCase(),a=["up","down","higher","lower","call","put","deal","trade","buy","sell"],i=["close","cancel","menu","settings","help","login","register","deposit","withdraw"];for(const s of i)if(e.includes(s)||o.includes(s)||n.includes(s))return!1;for(const s of a)if(e.includes(s)||o.includes(s)||n.includes(s))return!0;const r=t.parentElement;if(r){const s=r.className.toLowerCase();if(s.includes("deal")||s.includes("trade")||s.includes("trading"))return!0}return!1}showTradeReasonModal(){const t=Date.now();if(t-this.lastTradeReasonModalTime<1e4){console.log("⏰ Trade reason modal on cooldown");return}if(document.getElementById("trade-reason-modal")){console.log("📝 Trade reason modal already exists");return}this.lastTradeReasonModalTime=t,console.log("🤔 Showing trade reason modal");const o=document.createElement("div");o.id="trade-reason-modal",o.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,o.innerHTML=`
      <div style="
        background: white;
        padding: 24px;
        border-radius: 16px;
        max-width: 400px;
        width: 90%;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        text-align: center;
      ">
        <div style="font-size: 32px; margin-bottom: 16px;">🤔</div>
        <h3 style="color: #1976d2; margin: 0 0 16px 0;">Lý do vào lệnh</h3>
        <p style="color: #666; margin: 0 0 20px 0; font-size: 14px;">
          Lệnh này bạn vào vì điều gì?
        </p>

        <div style="display: flex; flex-direction: column; gap: 8px; margin-bottom: 20px;">
          <button class="reason-btn" data-reason="method" style="
            background: #4caf50;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">✅ Theo nguyên tắc phương pháp</button>
          
          <button class="reason-btn" data-reason="greed" style="
            background: #ff9800;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🤑 Vì tâm tham lam</button>
          
          <button class="reason-btn" data-reason="anger" style="
            background: #f44336;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">😡 Vì tâm giận dữ (gỡ lại)</button>
          
          <button class="reason-btn" data-reason="guess" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🎲 Vì suy đoán tầm bậy</button>
        </div>

        <button onclick="document.getElementById('trade-reason-modal').remove()" style="
          background: #666;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 12px;
        ">Bỏ qua</button>
      </div>
    `,document.body.appendChild(o),o.querySelectorAll(".reason-btn").forEach(n=>{n.addEventListener("click",a=>{const i=a.target.getAttribute("data-reason");this.handleTradeReason(i),o.remove()})})}async handleTradeReason(t){if(t==="method")console.log("✅ Trade based on method principles");else{const e=document.createElement("div");e.style.cssText=`
        position: fixed;
        top: 20px;
        right: 20px;
        background: #fff3cd;
        border: 1px solid #ffc107;
        padding: 16px;
        border-radius: 8px;
        z-index: 999999;
        max-width: 300px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      `,e.innerHTML=`
        <div style="color: #856404;">
          <strong>⚠️ Cảnh báo tâm lý</strong>
          <p style="margin: 8px 0; font-size: 14px;">
            Giao dịch không theo nguyên tắc có thể gây thua lỗ. Hãy nghỉ ngơi và rèn tâm.
          </p>
          <button onclick="window.open('${chrome.runtime.getURL("options.html#meditation")}', '_blank')" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 8px;
          ">🧘‍♂️ Thiền định</button>
          <button onclick="this.parentElement.parentElement.remove()" style="
            background: #666;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
          ">Đóng</button>
        </div>
      `,document.body.appendChild(e),setTimeout(()=>{e.parentElement&&e.remove()},1e4)}await chrome.storage.local.set({lastTradeReason:{timestamp:Date.now(),reason:t,amount:this.lastTradeAmount}})}observeTradeResults(){console.log("📊 Starting trade result observation...");const t=['[class*="win"]','[class*="profit"]','[class*="success"]','[class*="positive"]','[data-result="win"]','[data-result="profit"]',".result-win",".trade-win",".deal-win"],e=['[class*="loss"]','[class*="lose"]','[class*="fail"]','[class*="negative"]','[data-result="loss"]','[data-result="lose"]',".result-loss",".trade-loss",".deal-loss"];let o=0;const n=5e3,a=r=>{const l=Date.now();l-o<n||(t.forEach(s=>{r.querySelectorAll(s).forEach(d=>{this.isValidResultElement(d,"win")&&(console.log("🎉 Win result detected:",d),o=l,setTimeout(()=>this.showMindfulnessModal("win"),2e3))})}),e.forEach(s=>{r.querySelectorAll(s).forEach(d=>{this.isValidResultElement(d,"loss")&&(console.log("😔 Loss result detected:",d),o=l,setTimeout(()=>this.showMindfulnessModal("loss"),2e3))})}))};new MutationObserver(r=>{r.forEach(l=>{if(l.addedNodes.forEach(s=>{s.nodeType===Node.ELEMENT_NODE&&a(s)}),l.type==="attributes"){const s=l.target;s&&this.isValidResultElement(s,"any")&&a(s)}})}).observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["class","data-result","data-status"]})}isValidResultElement(t,e){var h;const o=((h=t.textContent)==null?void 0:h.toLowerCase())||"",n=t.className.toLowerCase(),a=t.id.toLowerCase();if(!(o.includes("win")||o.includes("loss")||o.includes("profit")||o.includes("lose")||o.includes("+")||o.includes("-")))return!1;const r=["menu","nav","header","footer","sidebar","button","link","tab","tooltip"];for(const d of r)if(n.includes(d)||a.includes(d))return!1;const l=["result","trade","deal","position","order","outcome","status","history"];let s=!1;for(const d of l)if(n.includes(d)||a.includes(d)){s=!0;break}if(!s){let d=t.parentElement,p=0;for(;d&&p<3;){const m=d.className.toLowerCase();for(const f of l)if(m.includes(f)){s=!0;break}if(s)break;d=d.parentElement,p++}}return s}showMindfulnessModal(t){const e=Date.now();if(e-this.lastMindfulnessModalTime<15e3){console.log("⏰ Mindfulness modal on cooldown");return}if(document.querySelector('[id*="mindfulness-modal"]')){console.log("🧘‍♂️ Mindfulness modal already exists");return}this.lastMindfulnessModalTime=e,console.log(`🙏 Showing mindfulness modal for ${t}`);const n=t==="win",a=n?["Tôi biết ơn vì kết quả tốt này và sẽ không để nó làm tôi kiêu ngạo","Thắng lợi này là nhờ sự chuẩn bị kỹ lưỡng và tâm tỉnh thức","Tôi sẽ giữ tâm bình thản và tiếp tục theo nguyên tắc","Mọi thắng lợi đều vô thường, tôi không bám víu vào nó"]:["Thua lỗ là bài học quý báu để tôi trưởng thành hơn","Tôi chấp nhận kết quả này với tâm bình thản và không giận dữ","Mọi thua lỗ đều vô thường, tôi sẽ học hỏi và tiến bước","Tôi buông bỏ sự thất vọng và tập trung vào cải thiện bản thân"],i=document.createElement("div");i.id=`mindfulness-modal-${t}-${Date.now()}`,i.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,i.innerHTML=`
      <div style="
        background: white;
        padding: 24px;
        border-radius: 16px;
        max-width: 400px;
        width: 90%;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        text-align: center;
      ">
        <div style="font-size: 32px; margin-bottom: 16px;">${n?"🙏":"🧘‍♂️"}</div>
        <h3 style="color: ${n?"#4caf50":"#ff9800"}; margin: 0 0 16px 0;">
          ${n?"Lời buông xả cho thắng lợi":"Lời buông xả cho thua lỗ"}
        </h3>
        
        <div style="margin-bottom: 20px;">
          <p style="color: #666; margin: 0 0 16px 0; font-size: 14px;">
            Hãy chọn một câu để thực hành buông xả:
          </p>
          
          <div style="display: flex; flex-direction: column; gap: 8px;">
            ${a.map((r,l)=>`
              <button class="mindfulness-btn" data-text="${r}" style="
                background: ${n?"#e8f5e8":"#fff3e0"};
                color: ${n?"#2e7d32":"#f57c00"};
                border: 1px solid ${n?"#c8e6c9":"#ffcc02"};
                padding: 12px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 13px;
                text-align: left;
                line-height: 1.4;
              ">${r}</button>
            `).join("")}
          </div>
        </div>

        <button onclick="this.parentElement.parentElement.remove()" style="
          background: #666;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 12px;
        ">Đóng</button>
      </div>
    `,document.body.appendChild(i),i.querySelectorAll(".mindfulness-btn").forEach(r=>{r.addEventListener("click",l=>{const s=l.target.getAttribute("data-text");this.showMindfulnessConfirmation(s||""),i.remove()})}),setTimeout(()=>{i.parentElement&&i.remove()},3e4)}showMindfulnessConfirmation(t){const e=document.createElement("div");e.style.cssText=`
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4caf50;
      color: white;
      padding: 16px;
      border-radius: 8px;
      z-index: 999999;
      max-width: 300px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      line-height: 1.4;
    `,e.innerHTML=`
      <div>
        <strong>🙏 Thực hành buông xả</strong>
        <p style="margin: 8px 0 0 0;">"${t}"</p>
      </div>
    `,document.body.appendChild(e),setTimeout(()=>{e.parentElement&&e.remove()},5e3)}async showBlockedMessage(){var a,i;const t=await chrome.storage.local.get(["blockUntil","blockDurationMinutes","blockDate"]),e=document.createElement("div");e.id="trading-block-overlay",e.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.9);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;const o=this.calculateTimeRemaining(t),n=this.formatDuration(t.blockDurationMinutes||1440);e.innerHTML=`
      <div style="
        background: white;
        padding: 40px;
        border-radius: 16px;
        text-align: center;
        max-width: 500px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="font-size: 64px; margin-bottom: 20px;">🧘‍♂️</div>
        <h2 style="color: #d32f2f; margin: 0 0 16px 0;">Giao dịch bị khóa</h2>
        <p style="color: #666; margin: 0 0 16px 0; line-height: 1.5;">
          Hệ thống đã khóa giao dịch trong <strong>${n}</strong> để bảo vệ tâm lý của bạn.<br>
          Hãy sử dụng thời gian này để nghỉ ngơi và rèn luyện tâm.
        </p>
        <div id="countdown" style="
          background: #f5f5f5;
          padding: 16px;
          border-radius: 8px;
          margin: 16px 0;
          font-size: 18px;
          font-weight: bold;
          color: #d32f2f;
        ">
          Còn lại: ${o}
        </div>
        <div style="display: flex; gap: 12px; justify-content: center;">
          <button id="meditation-btn" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🧘‍♂️ Thiền định</button>
          <button id="close-tab-btn" style="
            background: #666;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">❌ Đóng tab</button>
        </div>
        <p style="color: #999; font-size: 12px; margin: 16px 0 0 0;">
          Thời gian khóa dựa trên đánh giá tâm lý của bạn
        </p>
      </div>
    `,document.body.appendChild(e),this.startCountdown(t),(a=document.getElementById("meditation-btn"))==null||a.addEventListener("click",()=>{this.openMeditationOptions()}),(i=document.getElementById("close-tab-btn"))==null||i.addEventListener("click",()=>{window.close()})}calculateTimeRemaining(t){const e=new Date;if(t.blockUntil){const n=new Date(t.blockUntil).getTime()-e.getTime();if(n<=0)return"Đã hết hạn";const a=Math.floor(n/(1e3*60*60)),i=Math.floor(n%(1e3*60*60)/(1e3*60));return a>0?`${a} tiếng ${i} phút`:`${i} phút`}return"Không xác định"}startCountdown(t){const e=document.getElementById("countdown");if(!e)return;const n=setInterval(()=>{const a=this.calculateTimeRemaining(t);e.textContent=`Còn lại: ${a}`,a==="Đã hết hạn"&&window.location.reload()},6e4);window.addEventListener("beforeunload",()=>{clearInterval(n)})}async checkPsychologyConfirmation(){try{(await chrome.storage.local.get(["needsPsychologyConfirmation"])).needsPsychologyConfirmation&&this.showPsychologyConfirmation()}catch(t){console.error("Error checking psychology confirmation:",t)}}showPsychologyConfirmation(){var o,n;const t=document.createElement("div");t.id="psychology-confirmation-modal",t.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,t.innerHTML=`
      <div style="
        background: white;
        padding: 32px;
        border-radius: 16px;
        text-align: center;
        max-width: 450px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="font-size: 48px; margin-bottom: 16px;">🧠</div>
        <h3 style="color: #1976d2; margin: 0 0 16px 0;">Xác nhận tâm lý</h3>
        <p style="color: #666; margin: 0 0 24px 0; line-height: 1.5;">
          Bạn đã hoàn thành thiền định hoặc nghỉ ngơi?<br>
          Tâm lý hiện tại có ổn định và sẵn sàng giao dịch không?
        </p>

        <div style="margin-bottom: 24px;">
          <p style="color: #333; font-weight: bold; margin: 0 0 12px 0;">
            Trạng thái tâm lý hiện tại:
          </p>
          <div style="display: flex; flex-direction: column; gap: 8px;">
            <button class="psychology-option" data-state="balanced" style="
              background: #e8f5e8;
              color: #2e7d32;
              border: 2px solid #e8f5e8;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😌 Cân bằng và tỉnh táo</button>
            <button class="psychology-option" data-state="confident" style="
              background: #e3f2fd;
              color: #1976d2;
              border: 2px solid #e3f2fd;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😊 Tự tin và kiên nhẫn</button>
            <button class="psychology-option" data-state="not_ready" style="
              background: #fff3e0;
              color: #f57c00;
              border: 2px solid #fff3e0;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😐 Chưa sẵn sàng, cần thêm thời gian</button>
          </div>
        </div>

        <div style="display: flex; gap: 12px; justify-content: center;">
          <button id="continue-trading-btn" style="
            background: #4caf50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            opacity: 0.5;
          " disabled>✅ Tiếp tục giao dịch</button>
          <button id="more-meditation-btn" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🧘‍♂️ Thiền thêm</button>
        </div>
      </div>
    `,document.body.appendChild(t);let e="";t.querySelectorAll(".psychology-option").forEach(a=>{a.addEventListener("click",i=>{const r=i.target;e=r.getAttribute("data-state")||"",t.querySelectorAll(".psychology-option").forEach(s=>{s.style.borderColor=s.style.backgroundColor}),r.style.borderColor="#1976d2";const l=document.getElementById("continue-trading-btn");e==="balanced"||e==="confident"?(l.disabled=!1,l.style.opacity="1"):(l.disabled=!0,l.style.opacity="0.5")})}),(o=document.getElementById("continue-trading-btn"))==null||o.addEventListener("click",()=>{(e==="balanced"||e==="confident")&&this.confirmPsychologyAndContinue(e)}),(n=document.getElementById("more-meditation-btn"))==null||n.addEventListener("click",()=>{this.openMeditationOptions()})}async confirmPsychologyAndContinue(t){try{await chrome.storage.local.set({needsPsychologyConfirmation:!1,lastConfirmationTime:Date.now(),confirmedPsychologyState:t});const e=document.getElementById("psychology-confirmation-modal");e&&e.remove(),this.showSuccessMessage("✅ Tâm lý đã được xác nhận! Bạn có thể giao dịch an toàn.")}catch(e){console.error("Error confirming psychology:",e)}}openMeditationOptions(){try{chrome.runtime.sendMessage({action:"openOptions",tab:"meditation"})}catch(t){console.error("Error opening meditation options:",t),window.open(chrome.runtime.getURL("options.html#meditation"),"_blank")}}showSuccessMessage(t){const e=document.createElement("div");e.style.cssText=`
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4caf50;
      color: white;
      padding: 16px 24px;
      border-radius: 8px;
      z-index: 999999;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      animation: slideIn 0.3s ease-out;
    `,e.textContent=t;const o=document.createElement("style");o.textContent=`
      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    `,document.head.appendChild(o),document.body.appendChild(e),setTimeout(()=>{e.remove(),o.remove()},3e3)}}document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>new g):new g;
