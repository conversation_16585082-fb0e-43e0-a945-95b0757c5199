var g=Object.defineProperty;var h=(a,t,o)=>t in a?g(a,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):a[t]=o;var l=(a,t,o)=>h(a,typeof t!="symbol"?t+"":t,o);console.log("🎯 Binomo Trading Assistant - Minimal Content Script Loaded");class p{constructor(){l(this,"isBlocked",!1);this.init()}async init(){await this.checkTradingBlock(),this.isBlocked?this.showBlockedMessage():this.checkPsychologyConfirmation()}async checkTradingBlock(){try{const t=await chrome.storage.local.get(["tradingBlocked","blockDate","blockUntil","blockDurationMinutes"]);if(t.tradingBlocked){const o=new Date;if(t.blockUntil){const e=new Date(t.blockUntil);if(o>=e){await chrome.storage.local.remove(["tradingBlocked","blockDate","blockUntil","blockDurationMinutes"]),this.isBlocked=!1;return}}else if(t.blockDate){const e=new Date(t.blockDate);if((o.getTime()-e.getTime())/(1e3*60*60)>=24){await chrome.storage.local.remove(["tradingBlocked","blockDate"]),this.isBlocked=!1;return}}this.isBlocked=!0}}catch(t){console.error("Error checking trading block:",t),this.isBlocked=!1}}async showBlockedMessage(){var i,r;const t=await chrome.storage.local.get(["blockUntil","blockDurationMinutes","blockDate"]),o=document.createElement("div");o.id="trading-block-overlay",o.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.9);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;const e=this.calculateTimeRemaining(t),n=this.formatDuration(t.blockDurationMinutes||1440);o.innerHTML=`
      <div style="
        background: white;
        padding: 40px;
        border-radius: 16px;
        text-align: center;
        max-width: 500px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="font-size: 64px; margin-bottom: 20px;">🧘‍♂️</div>
        <h2 style="color: #d32f2f; margin: 0 0 16px 0;">Giao dịch bị khóa</h2>
        <p style="color: #666; margin: 0 0 16px 0; line-height: 1.5;">
          Hệ thống đã khóa giao dịch trong <strong>${n}</strong> để bảo vệ tâm lý của bạn.<br>
          Hãy sử dụng thời gian này để nghỉ ngơi và rèn luyện tâm.
        </p>
        <div id="countdown" style="
          background: #f5f5f5;
          padding: 16px;
          border-radius: 8px;
          margin: 16px 0;
          font-size: 18px;
          font-weight: bold;
          color: #d32f2f;
        ">
          Còn lại: ${e}
        </div>
        <div style="display: flex; gap: 12px; justify-content: center;">
          <button id="meditation-btn" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🧘‍♂️ Thiền định</button>
          <button id="close-tab-btn" style="
            background: #666;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">❌ Đóng tab</button>
        </div>
        <p style="color: #999; font-size: 12px; margin: 16px 0 0 0;">
          Thời gian khóa dựa trên đánh giá tâm lý của bạn
        </p>
      </div>
    `,document.body.appendChild(o),this.startCountdown(t),(i=document.getElementById("meditation-btn"))==null||i.addEventListener("click",()=>{this.openMeditationOptions()}),(r=document.getElementById("close-tab-btn"))==null||r.addEventListener("click",()=>{window.close()})}calculateTimeRemaining(t){const o=new Date;if(t.blockUntil){const n=new Date(t.blockUntil).getTime()-o.getTime();if(n<=0)return"Đã hết hạn";const i=Math.floor(n/(1e3*60*60)),r=Math.floor(n%(1e3*60*60)/(1e3*60));return i>0?`${i} tiếng ${r} phút`:`${r} phút`}return"Không xác định"}formatDuration(t){return t<60?`${t} phút`:t<1440?`${Math.round(t/60)} tiếng`:`${Math.round(t/1440)} ngày`}startCountdown(t){const o=document.getElementById("countdown");if(!o)return;const n=setInterval(()=>{const i=this.calculateTimeRemaining(t);o.textContent=`Còn lại: ${i}`,i==="Đã hết hạn"&&window.location.reload()},6e4);window.addEventListener("beforeunload",()=>{clearInterval(n)})}async checkPsychologyConfirmation(){try{(await chrome.storage.local.get(["needsPsychologyConfirmation","lastConfirmationTime"])).needsPsychologyConfirmation&&this.showPsychologyConfirmation()}catch(t){console.error("Error checking psychology confirmation:",t)}}showPsychologyConfirmation(){var e,n;const t=document.createElement("div");t.id="psychology-confirmation-modal",t.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,t.innerHTML=`
      <div style="
        background: white;
        padding: 32px;
        border-radius: 16px;
        text-align: center;
        max-width: 450px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="font-size: 48px; margin-bottom: 16px;">🧠</div>
        <h3 style="color: #1976d2; margin: 0 0 16px 0;">Xác nhận tâm lý</h3>
        <p style="color: #666; margin: 0 0 24px 0; line-height: 1.5;">
          Bạn đã hoàn thành thiền định hoặc nghỉ ngơi?<br>
          Tâm lý hiện tại có ổn định và sẵn sàng giao dịch không?
        </p>
        
        <div style="margin-bottom: 24px;">
          <p style="color: #333; font-weight: bold; margin: 0 0 12px 0;">
            Trạng thái tâm lý hiện tại:
          </p>
          <div style="display: flex; flex-direction: column; gap: 8px;">
            <button class="psychology-option" data-state="balanced" style="
              background: #e8f5e8;
              color: #2e7d32;
              border: 2px solid #e8f5e8;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😌 Cân bằng và tỉnh táo</button>
            <button class="psychology-option" data-state="confident" style="
              background: #e3f2fd;
              color: #1976d2;
              border: 2px solid #e3f2fd;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😊 Tự tin và kiên nhẫn</button>
            <button class="psychology-option" data-state="not_ready" style="
              background: #fff3e0;
              color: #f57c00;
              border: 2px solid #fff3e0;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😐 Chưa sẵn sàng, cần thêm thời gian</button>
          </div>
        </div>

        <div style="display: flex; gap: 12px; justify-content: center;">
          <button id="continue-trading-btn" style="
            background: #4caf50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            opacity: 0.5;
          " disabled>✅ Tiếp tục giao dịch</button>
          <button id="more-meditation-btn" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🧘‍♂️ Thiền thêm</button>
        </div>
      </div>
    `,document.body.appendChild(t);let o="";t.querySelectorAll(".psychology-option").forEach(i=>{i.addEventListener("click",r=>{const c=r.target;o=c.getAttribute("data-state")||"",t.querySelectorAll(".psychology-option").forEach(d=>{d.style.borderColor=d.style.backgroundColor}),c.style.borderColor="#1976d2";const s=document.getElementById("continue-trading-btn");o==="balanced"||o==="confident"?(s.disabled=!1,s.style.opacity="1"):(s.disabled=!0,s.style.opacity="0.5")})}),(e=document.getElementById("continue-trading-btn"))==null||e.addEventListener("click",()=>{(o==="balanced"||o==="confident")&&this.confirmPsychologyAndContinue(o)}),(n=document.getElementById("more-meditation-btn"))==null||n.addEventListener("click",()=>{this.openMeditationOptions()})}async confirmPsychologyAndContinue(t){try{await chrome.storage.local.set({needsPsychologyConfirmation:!1,lastConfirmationTime:Date.now(),confirmedPsychologyState:t});const o=document.getElementById("psychology-confirmation-modal");o&&o.remove(),this.showSuccessMessage("✅ Tâm lý đã được xác nhận! Bạn có thể giao dịch an toàn.")}catch(o){console.error("Error confirming psychology:",o)}}openMeditationOptions(){try{chrome.runtime.sendMessage({action:"openOptions",tab:"meditation"})}catch(t){console.error("Error opening meditation options:",t),window.open(chrome.runtime.getURL("options.html#meditation"),"_blank")}}showSuccessMessage(t){const o=document.createElement("div");o.style.cssText=`
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4caf50;
      color: white;
      padding: 16px 24px;
      border-radius: 8px;
      z-index: 999999;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      animation: slideIn 0.3s ease-out;
    `,o.textContent=t;const e=document.createElement("style");e.textContent=`
      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    `,document.head.appendChild(e),document.body.appendChild(o),setTimeout(()=>{o.remove(),e.remove()},3e3)}}document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>new p):new p;
