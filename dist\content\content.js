var p=Object.defineProperty;var g=(s,t,e)=>t in s?p(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e;var c=(s,t,e)=>g(s,typeof t!="symbol"?t+"":t,e);console.log("🎯 Binomo Trading Assistant - Enhanced Content Script Loaded");class m{constructor(t){c(this,"apiKey");c(this,"baseURL","https://api.openai.com/v1/chat/completions");this.apiKey=t}async assessPsychology(t){try{const e=this.createAssessmentPrompt(t),n=await this.callOpenAI(e);return this.parseAIResponse(n,t)}catch(e){return console.error("Error in AI psychology assessment:",e),this.fallbackAssessment(t)}}createAssessmentPrompt(t){return`
Bạn là một chuyên gia tâm lý giao dịch với 20 năm kinh nghiệm. H<PERSON>y đánh giá tâm lý giao dịch của người này:

THÔNG TIN ĐÁNH GIÁ:
- Trạng thái cảm xúc: ${t.emotionalState}
- Tình hình tài chính: ${t.financialSituation}
- Kết quả giao dịch gần đây: ${t.recentPerformance}
- Chất lượng giấc ngủ: ${t.sleepQuality}
- Mức độ căng thẳng: ${t.stressLevel}
- Động lực giao dịch: ${t.motivation}
${t.additionalNotes?`- Ghi chú thêm: ${t.additionalNotes}`:""}

Hãy trả về đánh giá theo format JSON chính xác sau:
{
  "score": [số điểm từ 0-100],
  "emotional_factor": [điểm cảm xúc 0-100],
  "financial_factor": [điểm tài chính 0-100],
  "physical_factor": [điểm thể chất 0-100],
  "mental_factor": [điểm tinh thần 0-100],
  "should_trade": [true/false],
  "recommendation": "[khuyến nghị ngắn gọn]",
  "analysis": "[phân tích chi tiết 2-3 câu]",
  "risk_factors": ["[yếu tố rủi ro 1]", "[yếu tố rủi ro 2]"],
  "positive_factors": ["[yếu tố tích cực 1]", "[yếu tố tích cực 2]"]
}

TIÊU CHÍ ĐÁNH GIÁ:
- 90-100: Tâm lý xuất sắc, sẵn sàng giao dịch
- 80-89: Tâm lý tốt, có thể giao dịch cẩn thận
- 60-79: Tâm lý trung bình, nên nghỉ 15-30 phút
- 30-59: Tâm lý kém, nên nghỉ 4-8 tiếng
- 0-29: Tâm lý rất kém, nên nghỉ 12-24 tiếng

Chỉ trả về JSON, không có text khác.
`}async callOpenAI(t){const e=await fetch(this.baseURL,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.apiKey}`},body:JSON.stringify({model:"gpt-4o-mini",messages:[{role:"system",content:"Bạn là chuyên gia tâm lý giao dịch. Luôn trả về JSON hợp lệ."},{role:"user",content:t}],temperature:.3,max_tokens:1e3})});if(!e.ok)throw new Error(`OpenAI API error: ${e.status}`);return(await e.json()).choices[0].message.content}parseAIResponse(t,e){try{const n=t.replace(/```json\n?|\n?```/g,"").trim(),o=JSON.parse(n),r=this.getScoreLevel(o.score),i=this.calculateBlockDuration(o.score,o);return{score:o.score,level:r,shouldTrade:o.should_trade&&o.score>=60,blockDuration:i,recommendation:o.recommendation,aiAnalysis:o.analysis,factors:{emotional:o.emotional_factor||50,financial:o.financial_factor||50,physical:o.physical_factor||50,mental:o.mental_factor||50}}}catch(n){return console.error("Error parsing AI response:",n),this.fallbackAssessment(e)}}getScoreLevel(t){return t>=90?"excellent":t>=80?"good":t>=60?"fair":t>=30?"poor":"critical"}calculateBlockDuration(t,e){let n=0;return t>=80?n=0:t>=60?n=15:t>=45?n=60:t>=30?n=240:t>=15?n=720:n=1440,e.risk_factors&&e.risk_factors.length>2&&(n=Math.min(n*1.5,1440)),e.positive_factors&&e.positive_factors.length>2&&(n=Math.max(n*.7,0)),Math.round(n)}fallbackAssessment(t){let e=50;t.emotionalState.includes("cân bằng")||t.emotionalState.includes("tích cực")?e+=20:(t.emotionalState.includes("căng thẳng")||t.emotionalState.includes("lo âu"))&&(e-=20),t.financialSituation.includes("ổn định")?e+=15:t.financialSituation.includes("khó khăn")&&(e-=15),t.recentPerformance.includes("tốt")||t.recentPerformance.includes("lãi")?e+=10:(t.recentPerformance.includes("thua")||t.recentPerformance.includes("lỗ"))&&(e-=15),e=Math.max(0,Math.min(100,e));const n=this.getScoreLevel(e),o={excellent:0,good:0,fair:15,poor:240,critical:1440};return{score:e,level:n,shouldTrade:e>=60,blockDuration:o[n],recommendation:e>=60?"Có thể giao dịch cẩn thận":"Nên nghỉ ngơi và thiền định",aiAnalysis:"Đánh giá dựa trên quy tắc cơ bản (AI không khả dụng)",factors:{emotional:e,financial:e,physical:e,mental:e}}}}class h{constructor(){c(this,"isBlocked",!1);c(this,"psychologyAI",null);c(this,"lastTradeAmount",0);c(this,"tradeObserver",null);this.init()}async init(){await this.initializeAI(),await this.checkTradingBlock(),this.isBlocked?this.showBlockedMessage():(this.checkPsychologyConfirmation(),this.startBehaviorTracking(),this.showAIPsychologyModal())}async initializeAI(){try{const t=await chrome.storage.local.get(["openaiApiKey"]);t.openaiApiKey&&(this.psychologyAI=new m(t.openaiApiKey))}catch(t){console.error("Error initializing AI:",t)}}async checkTradingBlock(){try{const t=await chrome.storage.local.get(["tradingBlocked","blockDate","blockUntil","blockDurationMinutes"]);if(t.tradingBlocked){const e=new Date;if(t.blockUntil){const n=new Date(t.blockUntil);if(e>=n){await chrome.storage.local.remove(["tradingBlocked","blockDate","blockUntil","blockDurationMinutes"]),this.isBlocked=!1;return}}this.isBlocked=!0}}catch(t){console.error("Error checking trading block:",t),this.isBlocked=!1}}showAIPsychologyModal(){setTimeout(()=>{!this.isBlocked&&this.psychologyAI&&this.createAIPsychologyModal()},2e3)}createAIPsychologyModal(){var e,n;const t=document.createElement("div");t.id="ai-psychology-modal",t.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,t.innerHTML=`
      <div style="
        background: white;
        padding: 32px;
        border-radius: 16px;
        max-width: 500px;
        width: 90%;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="text-align: center; margin-bottom: 24px;">
          <div style="font-size: 48px; margin-bottom: 16px;">🧠</div>
          <h3 style="color: #1976d2; margin: 0 0 8px 0;">Đánh giá tâm lý AI</h3>
          <p style="color: #666; margin: 0; font-size: 14px;">
            Hãy chia sẻ cảm xúc hiện tại để AI đánh giá tâm lý giao dịch
          </p>
        </div>

        <div style="margin-bottom: 24px;">
          <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">
            Hiện tại bạn đang cảm thấy như thế nào?
          </label>
          <textarea 
            id="psychology-diary" 
            placeholder="Ví dụ: Tôi cảm thấy hơi lo lắng vì thua lỗ hôm qua, nhưng cũng muốn gỡ lại. Tôi đã ngủ đủ giấc và cảm thấy tỉnh táo..."
            style="
              width: 100%;
              height: 120px;
              padding: 12px;
              border: 2px solid #e0e0e0;
              border-radius: 8px;
              font-size: 14px;
              resize: vertical;
              font-family: inherit;
            "
          ></textarea>
          <div style="margin-top: 8px; font-size: 12px; color: #666;">
            💡 Hãy thành thật về cảm xúc: tham lam, giận dữ, lo lắng, sợ hãi, phấn khích...
          </div>
        </div>

        <div style="display: flex; gap: 12px;">
          <button id="skip-assessment" style="
            flex: 1;
            background: #666;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">Bỏ qua</button>
          <button id="ai-analyze" style="
            flex: 2;
            background: #1976d2;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
          ">🤖 Phân tích AI</button>
        </div>

        <div id="ai-result" style="
          margin-top: 16px;
          padding: 16px;
          background: #f5f5f5;
          border-radius: 8px;
          display: none;
        "></div>
      </div>
    `,document.body.appendChild(t),(e=document.getElementById("skip-assessment"))==null||e.addEventListener("click",()=>{t.remove()}),(n=document.getElementById("ai-analyze"))==null||n.addEventListener("click",()=>{this.performAIAnalysis()})}async performAIAnalysis(){var o;const t=(o=document.getElementById("psychology-diary"))==null?void 0:o.value,e=document.getElementById("ai-result"),n=document.getElementById("ai-analyze");if(!t.trim()){alert("Vui lòng chia sẻ cảm xúc hiện tại của bạn");return}if(!this.psychologyAI){alert("AI chưa được cấu hình. Vui lòng thêm OpenAI API key trong Settings.");return}n.textContent="🤖 Đang phân tích...",n.disabled=!0;try{const r={emotionalState:t,financialSituation:"Không rõ",recentPerformance:"Không rõ",sleepQuality:"Không rõ",stressLevel:"Không rõ",motivation:t,additionalNotes:"Đánh giá từ nhật ký tâm trạng"},i=await this.psychologyAI.assessPsychology(r);e&&(e.style.display="block",e.innerHTML=`
          <div style="text-align: center; margin-bottom: 16px;">
            <div style="font-size: 32px; margin-bottom: 8px;">
              ${i.score>=80?"😊":i.score>=60?"😐":"😰"}
            </div>
            <div style="font-size: 24px; font-weight: bold; color: ${i.score>=80?"#4caf50":i.score>=60?"#ff9800":"#f44336"};">
              ${i.score}/100 điểm
            </div>
          </div>
          
          <div style="margin-bottom: 16px;">
            <strong>Phân tích AI:</strong>
            <p style="margin: 8px 0; font-size: 14px; line-height: 1.4;">${i.aiAnalysis}</p>
          </div>
          
          <div style="margin-bottom: 16px;">
            <strong>Khuyến nghị:</strong>
            <p style="margin: 8px 0; font-size: 14px; line-height: 1.4;">${i.recommendation}</p>
          </div>
          
          ${i.blockDuration>0?`
            <div style="background: #fff3cd; padding: 12px; border-radius: 8px; border-left: 4px solid #ffc107;">
              <strong>⚠️ Khuyến nghị nghỉ ngơi:</strong> ${this.formatDuration(i.blockDuration)}
            </div>
          `:`
            <div style="background: #d4edda; padding: 12px; border-radius: 8px; border-left: 4px solid #28a745;">
              <strong>✅ Có thể giao dịch:</strong> Tâm lý ổn định
            </div>
          `}
          
          <div style="margin-top: 16px; display: flex; gap: 8px;">
            ${i.shouldTrade?`
              <button onclick="document.getElementById('ai-psychology-modal').remove()" style="
                flex: 1; background: #4caf50; color: white; border: none; padding: 10px; border-radius: 6px; cursor: pointer;
              ">✅ Tiếp tục giao dịch</button>
            `:`
              <button onclick="window.open('${chrome.runtime.getURL("options.html#meditation")}', '_blank')" style="
                flex: 1; background: #9c27b0; color: white; border: none; padding: 10px; border-radius: 6px; cursor: pointer;
              ">🧘‍♂️ Đi thiền</button>
            `}
            <button onclick="document.getElementById('ai-psychology-modal').remove()" style="
              background: #666; color: white; border: none; padding: 10px 16px; border-radius: 6px; cursor: pointer;
            ">Đóng</button>
          </div>
        `),await chrome.storage.local.set({lastAIAssessment:{timestamp:Date.now(),score:i.score,shouldTrade:i.shouldTrade,blockDuration:i.blockDuration}}),!i.shouldTrade&&i.blockDuration>0&&await this.blockTradingWithDuration(i.blockDuration)}catch(r){console.error("AI analysis error:",r),e&&(e.style.display="block",e.innerHTML=`
          <div style="background: #f8d7da; padding: 12px; border-radius: 8px; color: #721c24;">
            <strong>❌ Lỗi phân tích AI:</strong> ${r.message||"Không thể kết nối với AI"}
          </div>
        `)}n.textContent="🤖 Phân tích AI",n.disabled=!1}formatDuration(t){return t<60?`${t} phút`:t<1440?`${Math.round(t/60)} tiếng`:`${Math.round(t/1440)} ngày`}async blockTradingWithDuration(t){try{const e=new Date,n=new Date(e.getTime()+t*60*1e3);await chrome.storage.local.set({tradingBlocked:!0,blockDate:e.toISOString(),blockUntil:n.toISOString(),blockDurationMinutes:t,needsPsychologyConfirmation:!0}),setTimeout(()=>window.location.reload(),2e3)}catch(e){console.error("Error blocking trading:",e)}}startBehaviorTracking(){this.observeTradeActions(),this.observeTradeResults()}observeTradeActions(){const t=document.querySelectorAll('[class*="deal"], [class*="trade"], button[class*="up"], button[class*="down"]');document.querySelectorAll('input[type="number"], [class*="amount"]').forEach(n=>{n.addEventListener("change",o=>{const r=o.target;this.lastTradeAmount=parseFloat(r.value)||0})}),t.forEach(n=>{n.addEventListener("click",()=>{setTimeout(()=>this.showTradeReasonModal(),500)})}),this.tradeObserver=new MutationObserver(n=>{n.forEach(o=>{o.addedNodes.forEach(r=>{r.nodeType===Node.ELEMENT_NODE&&r.querySelectorAll('[class*="deal"], [class*="trade"], button[class*="up"], button[class*="down"]').forEach(l=>{l.addEventListener("click",()=>{setTimeout(()=>this.showTradeReasonModal(),500)})})})})}),this.tradeObserver.observe(document.body,{childList:!0,subtree:!0})}showTradeReasonModal(){const t=document.createElement("div");t.id="trade-reason-modal",t.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,t.innerHTML=`
      <div style="
        background: white;
        padding: 24px;
        border-radius: 16px;
        max-width: 400px;
        width: 90%;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        text-align: center;
      ">
        <div style="font-size: 32px; margin-bottom: 16px;">🤔</div>
        <h3 style="color: #1976d2; margin: 0 0 16px 0;">Lý do vào lệnh</h3>
        <p style="color: #666; margin: 0 0 20px 0; font-size: 14px;">
          Lệnh này bạn vào vì điều gì?
        </p>

        <div style="display: flex; flex-direction: column; gap: 8px; margin-bottom: 20px;">
          <button class="reason-btn" data-reason="method" style="
            background: #4caf50;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">✅ Theo nguyên tắc phương pháp</button>
          
          <button class="reason-btn" data-reason="greed" style="
            background: #ff9800;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🤑 Vì tâm tham lam</button>
          
          <button class="reason-btn" data-reason="anger" style="
            background: #f44336;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">😡 Vì tâm giận dữ (gỡ lại)</button>
          
          <button class="reason-btn" data-reason="guess" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🎲 Vì suy đoán tầm bậy</button>
        </div>

        <button onclick="document.getElementById('trade-reason-modal').remove()" style="
          background: #666;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 12px;
        ">Bỏ qua</button>
      </div>
    `,document.body.appendChild(t),t.querySelectorAll(".reason-btn").forEach(e=>{e.addEventListener("click",n=>{const o=n.target.getAttribute("data-reason");this.handleTradeReason(o),t.remove()})})}async handleTradeReason(t){if(t==="method")console.log("✅ Trade based on method principles");else{const e=document.createElement("div");e.style.cssText=`
        position: fixed;
        top: 20px;
        right: 20px;
        background: #fff3cd;
        border: 1px solid #ffc107;
        padding: 16px;
        border-radius: 8px;
        z-index: 999999;
        max-width: 300px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      `,e.innerHTML=`
        <div style="color: #856404;">
          <strong>⚠️ Cảnh báo tâm lý</strong>
          <p style="margin: 8px 0; font-size: 14px;">
            Giao dịch không theo nguyên tắc có thể gây thua lỗ. Hãy nghỉ ngơi và rèn tâm.
          </p>
          <button onclick="window.open('${chrome.runtime.getURL("options.html#meditation")}', '_blank')" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 8px;
          ">🧘‍♂️ Thiền định</button>
          <button onclick="this.parentElement.parentElement.remove()" style="
            background: #666;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
          ">Đóng</button>
        </div>
      `,document.body.appendChild(e),setTimeout(()=>{e.parentElement&&e.remove()},1e4)}await chrome.storage.local.set({lastTradeReason:{timestamp:Date.now(),reason:t,amount:this.lastTradeAmount}})}observeTradeResults(){new MutationObserver(e=>{e.forEach(n=>{n.addedNodes.forEach(o=>{if(o.nodeType===Node.ELEMENT_NODE){const r=o,i=r.querySelectorAll('[class*="win"], [class*="profit"], [class*="success"]'),a=r.querySelectorAll('[class*="loss"], [class*="lose"], [class*="fail"]');i.length>0?setTimeout(()=>this.showMindfulnessModal("win"),1e3):a.length>0&&setTimeout(()=>this.showMindfulnessModal("loss"),1e3)}})})}).observe(document.body,{childList:!0,subtree:!0})}showMindfulnessModal(t){const e=t==="win",n=e?["Tôi biết ơn vì kết quả tốt này và sẽ không để nó làm tôi kiêu ngạo","Thắng lợi này là nhờ sự chuẩn bị kỹ lưỡng và tâm tỉnh thức","Tôi sẽ giữ tâm bình thản và tiếp tục theo nguyên tắc","Mọi thắng lợi đều vô thường, tôi không bám víu vào nó"]:["Thua lỗ là bài học quý báu để tôi trưởng thành hơn","Tôi chấp nhận kết quả này với tâm bình thản và không giận dữ","Mọi thua lỗ đều vô thường, tôi sẽ học hỏi và tiến bước","Tôi buông bỏ sự thất vọng và tập trung vào cải thiện bản thân"],o=document.createElement("div");o.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,o.innerHTML=`
      <div style="
        background: white;
        padding: 24px;
        border-radius: 16px;
        max-width: 400px;
        width: 90%;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        text-align: center;
      ">
        <div style="font-size: 32px; margin-bottom: 16px;">${e?"🙏":"🧘‍♂️"}</div>
        <h3 style="color: ${e?"#4caf50":"#ff9800"}; margin: 0 0 16px 0;">
          ${e?"Lời buông xả cho thắng lợi":"Lời buông xả cho thua lỗ"}
        </h3>
        
        <div style="margin-bottom: 20px;">
          <p style="color: #666; margin: 0 0 16px 0; font-size: 14px;">
            Hãy chọn một câu để thực hành buông xả:
          </p>
          
          <div style="display: flex; flex-direction: column; gap: 8px;">
            ${n.map((r,i)=>`
              <button class="mindfulness-btn" data-text="${r}" style="
                background: ${e?"#e8f5e8":"#fff3e0"};
                color: ${e?"#2e7d32":"#f57c00"};
                border: 1px solid ${e?"#c8e6c9":"#ffcc02"};
                padding: 12px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 13px;
                text-align: left;
                line-height: 1.4;
              ">${r}</button>
            `).join("")}
          </div>
        </div>

        <button onclick="this.parentElement.parentElement.remove()" style="
          background: #666;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 12px;
        ">Đóng</button>
      </div>
    `,document.body.appendChild(o),o.querySelectorAll(".mindfulness-btn").forEach(r=>{r.addEventListener("click",i=>{const a=i.target.getAttribute("data-text");this.showMindfulnessConfirmation(a||""),o.remove()})}),setTimeout(()=>{o.parentElement&&o.remove()},3e4)}showMindfulnessConfirmation(t){const e=document.createElement("div");e.style.cssText=`
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4caf50;
      color: white;
      padding: 16px;
      border-radius: 8px;
      z-index: 999999;
      max-width: 300px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      line-height: 1.4;
    `,e.innerHTML=`
      <div>
        <strong>🙏 Thực hành buông xả</strong>
        <p style="margin: 8px 0 0 0;">"${t}"</p>
      </div>
    `,document.body.appendChild(e),setTimeout(()=>{e.parentElement&&e.remove()},5e3)}async showBlockedMessage(){var r,i;const t=await chrome.storage.local.get(["blockUntil","blockDurationMinutes","blockDate"]),e=document.createElement("div");e.id="trading-block-overlay",e.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.9);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;const n=this.calculateTimeRemaining(t),o=this.formatDuration(t.blockDurationMinutes||1440);e.innerHTML=`
      <div style="
        background: white;
        padding: 40px;
        border-radius: 16px;
        text-align: center;
        max-width: 500px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="font-size: 64px; margin-bottom: 20px;">🧘‍♂️</div>
        <h2 style="color: #d32f2f; margin: 0 0 16px 0;">Giao dịch bị khóa</h2>
        <p style="color: #666; margin: 0 0 16px 0; line-height: 1.5;">
          Hệ thống đã khóa giao dịch trong <strong>${o}</strong> để bảo vệ tâm lý của bạn.<br>
          Hãy sử dụng thời gian này để nghỉ ngơi và rèn luyện tâm.
        </p>
        <div id="countdown" style="
          background: #f5f5f5;
          padding: 16px;
          border-radius: 8px;
          margin: 16px 0;
          font-size: 18px;
          font-weight: bold;
          color: #d32f2f;
        ">
          Còn lại: ${n}
        </div>
        <div style="display: flex; gap: 12px; justify-content: center;">
          <button id="meditation-btn" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🧘‍♂️ Thiền định</button>
          <button id="close-tab-btn" style="
            background: #666;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">❌ Đóng tab</button>
        </div>
        <p style="color: #999; font-size: 12px; margin: 16px 0 0 0;">
          Thời gian khóa dựa trên đánh giá tâm lý của bạn
        </p>
      </div>
    `,document.body.appendChild(e),this.startCountdown(t),(r=document.getElementById("meditation-btn"))==null||r.addEventListener("click",()=>{this.openMeditationOptions()}),(i=document.getElementById("close-tab-btn"))==null||i.addEventListener("click",()=>{window.close()})}calculateTimeRemaining(t){const e=new Date;if(t.blockUntil){const o=new Date(t.blockUntil).getTime()-e.getTime();if(o<=0)return"Đã hết hạn";const r=Math.floor(o/(1e3*60*60)),i=Math.floor(o%(1e3*60*60)/(1e3*60));return r>0?`${r} tiếng ${i} phút`:`${i} phút`}return"Không xác định"}startCountdown(t){const e=document.getElementById("countdown");if(!e)return;const o=setInterval(()=>{const r=this.calculateTimeRemaining(t);e.textContent=`Còn lại: ${r}`,r==="Đã hết hạn"&&window.location.reload()},6e4);window.addEventListener("beforeunload",()=>{clearInterval(o)})}async checkPsychologyConfirmation(){try{(await chrome.storage.local.get(["needsPsychologyConfirmation"])).needsPsychologyConfirmation&&this.showPsychologyConfirmation()}catch(t){console.error("Error checking psychology confirmation:",t)}}showPsychologyConfirmation(){var n,o;const t=document.createElement("div");t.id="psychology-confirmation-modal",t.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,t.innerHTML=`
      <div style="
        background: white;
        padding: 32px;
        border-radius: 16px;
        text-align: center;
        max-width: 450px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="font-size: 48px; margin-bottom: 16px;">🧠</div>
        <h3 style="color: #1976d2; margin: 0 0 16px 0;">Xác nhận tâm lý</h3>
        <p style="color: #666; margin: 0 0 24px 0; line-height: 1.5;">
          Bạn đã hoàn thành thiền định hoặc nghỉ ngơi?<br>
          Tâm lý hiện tại có ổn định và sẵn sàng giao dịch không?
        </p>

        <div style="margin-bottom: 24px;">
          <p style="color: #333; font-weight: bold; margin: 0 0 12px 0;">
            Trạng thái tâm lý hiện tại:
          </p>
          <div style="display: flex; flex-direction: column; gap: 8px;">
            <button class="psychology-option" data-state="balanced" style="
              background: #e8f5e8;
              color: #2e7d32;
              border: 2px solid #e8f5e8;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😌 Cân bằng và tỉnh táo</button>
            <button class="psychology-option" data-state="confident" style="
              background: #e3f2fd;
              color: #1976d2;
              border: 2px solid #e3f2fd;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😊 Tự tin và kiên nhẫn</button>
            <button class="psychology-option" data-state="not_ready" style="
              background: #fff3e0;
              color: #f57c00;
              border: 2px solid #fff3e0;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😐 Chưa sẵn sàng, cần thêm thời gian</button>
          </div>
        </div>

        <div style="display: flex; gap: 12px; justify-content: center;">
          <button id="continue-trading-btn" style="
            background: #4caf50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            opacity: 0.5;
          " disabled>✅ Tiếp tục giao dịch</button>
          <button id="more-meditation-btn" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🧘‍♂️ Thiền thêm</button>
        </div>
      </div>
    `,document.body.appendChild(t);let e="";t.querySelectorAll(".psychology-option").forEach(r=>{r.addEventListener("click",i=>{const a=i.target;e=a.getAttribute("data-state")||"",t.querySelectorAll(".psychology-option").forEach(d=>{d.style.borderColor=d.style.backgroundColor}),a.style.borderColor="#1976d2";const l=document.getElementById("continue-trading-btn");e==="balanced"||e==="confident"?(l.disabled=!1,l.style.opacity="1"):(l.disabled=!0,l.style.opacity="0.5")})}),(n=document.getElementById("continue-trading-btn"))==null||n.addEventListener("click",()=>{(e==="balanced"||e==="confident")&&this.confirmPsychologyAndContinue(e)}),(o=document.getElementById("more-meditation-btn"))==null||o.addEventListener("click",()=>{this.openMeditationOptions()})}async confirmPsychologyAndContinue(t){try{await chrome.storage.local.set({needsPsychologyConfirmation:!1,lastConfirmationTime:Date.now(),confirmedPsychologyState:t});const e=document.getElementById("psychology-confirmation-modal");e&&e.remove(),this.showSuccessMessage("✅ Tâm lý đã được xác nhận! Bạn có thể giao dịch an toàn.")}catch(e){console.error("Error confirming psychology:",e)}}openMeditationOptions(){try{chrome.runtime.sendMessage({action:"openOptions",tab:"meditation"})}catch(t){console.error("Error opening meditation options:",t),window.open(chrome.runtime.getURL("options.html#meditation"),"_blank")}}showSuccessMessage(t){const e=document.createElement("div");e.style.cssText=`
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4caf50;
      color: white;
      padding: 16px 24px;
      border-radius: 8px;
      z-index: 999999;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      animation: slideIn 0.3s ease-out;
    `,e.textContent=t;const n=document.createElement("style");n.textContent=`
      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    `,document.head.appendChild(n),document.body.appendChild(e),setTimeout(()=>{e.remove(),n.remove()},3e3)}}document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>new h):new h;
