var x=Object.defineProperty;var v=(g,e,t)=>e in g?x(g,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):g[e]=t;var d=(g,e,t)=>v(g,typeof e!="symbol"?e+"":e,t);console.log("🎯 Binomo Trading Assistant - Enhanced Content Script Loaded");class w{constructor(e){d(this,"apiKey");d(this,"baseURL","https://api.openai.com/v1/chat/completions");this.apiKey=e}async assessPsychology(e){try{const t=this.createAssessmentPrompt(e),o=await this.callOpenAI(t);return this.parseAIResponse(o,e)}catch(t){return console.error("Error in AI psychology assessment:",t),this.fallbackAssessment(e)}}createAssessmentPrompt(e){return`
Bạn là một chuyên gia tâm lý giao dịch với 20 năm kinh nghiệm. H<PERSON>y đánh giá tâm lý giao dịch của người này:

THÔNG TIN ĐÁNH GIÁ:
- Trạng thái cảm xúc: ${e.emotionalState}
- Tình hình tài chính: ${e.financialSituation}
- Kết quả giao dịch gần đây: ${e.recentPerformance}
- Chất lượng giấc ngủ: ${e.sleepQuality}
- Mức độ căng thẳng: ${e.stressLevel}
- Động lực giao dịch: ${e.motivation}
${e.additionalNotes?`- Ghi chú thêm: ${e.additionalNotes}`:""}

Hãy trả về đánh giá theo format JSON chính xác sau:
{
  "score": [số điểm từ 0-100],
  "emotional_factor": [điểm cảm xúc 0-100],
  "financial_factor": [điểm tài chính 0-100],
  "physical_factor": [điểm thể chất 0-100],
  "mental_factor": [điểm tinh thần 0-100],
  "should_trade": [true/false],
  "recommendation": "[khuyến nghị ngắn gọn]",
  "analysis": "[phân tích chi tiết 2-3 câu]",
  "risk_factors": ["[yếu tố rủi ro 1]", "[yếu tố rủi ro 2]"],
  "positive_factors": ["[yếu tố tích cực 1]", "[yếu tố tích cực 2]"]
}

TIÊU CHÍ ĐÁNH GIÁ:
- 90-100: Tâm lý xuất sắc, sẵn sàng giao dịch
- 80-89: Tâm lý tốt, có thể giao dịch cẩn thận
- 60-79: Tâm lý trung bình, nên nghỉ 15-30 phút
- 30-59: Tâm lý kém, nên nghỉ 4-8 tiếng
- 0-29: Tâm lý rất kém, nên nghỉ 12-24 tiếng

Chỉ trả về JSON, không có text khác.
`}async callOpenAI(e){const t=await fetch(this.baseURL,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.apiKey}`},body:JSON.stringify({model:"gpt-4o-mini",messages:[{role:"system",content:"Bạn là chuyên gia tâm lý giao dịch. Luôn trả về JSON hợp lệ."},{role:"user",content:e}],temperature:.3,max_tokens:1e3})});if(!t.ok)throw new Error(`OpenAI API error: ${t.status}`);return(await t.json()).choices[0].message.content}parseAIResponse(e,t){try{const o=e.replace(/```json\n?|\n?```/g,"").trim(),n=JSON.parse(o),s=this.getScoreLevel(n.score),i=this.calculateBlockDuration(n.score,n);return{score:n.score,level:s,shouldTrade:n.should_trade&&n.score>=60,blockDuration:i,recommendation:n.recommendation,aiAnalysis:n.analysis,factors:{emotional:n.emotional_factor||50,financial:n.financial_factor||50,physical:n.physical_factor||50,mental:n.mental_factor||50}}}catch(o){return console.error("Error parsing AI response:",o),this.fallbackAssessment(t)}}getScoreLevel(e){return e>=90?"excellent":e>=80?"good":e>=60?"fair":e>=30?"poor":"critical"}calculateBlockDuration(e,t){let o=0;return e>=80?o=0:e>=60?o=15:e>=45?o=60:e>=30?o=240:e>=15?o=720:o=1440,t.risk_factors&&t.risk_factors.length>2&&(o=Math.min(o*1.5,1440)),t.positive_factors&&t.positive_factors.length>2&&(o=Math.max(o*.7,0)),Math.round(o)}fallbackAssessment(e){let t=50;e.emotionalState.includes("cân bằng")||e.emotionalState.includes("tích cực")?t+=20:(e.emotionalState.includes("căng thẳng")||e.emotionalState.includes("lo âu"))&&(t-=20),e.financialSituation.includes("ổn định")?t+=15:e.financialSituation.includes("khó khăn")&&(t-=15),e.recentPerformance.includes("tốt")||e.recentPerformance.includes("lãi")?t+=10:(e.recentPerformance.includes("thua")||e.recentPerformance.includes("lỗ"))&&(t-=15),t=Math.max(0,Math.min(100,t));const o=this.getScoreLevel(t),n={excellent:0,good:0,fair:15,poor:240,critical:1440};return{score:t,level:o,shouldTrade:t>=60,blockDuration:n[o],recommendation:t>=60?"Có thể giao dịch cẩn thận":"Nên nghỉ ngơi và thiền định",aiAnalysis:"Đánh giá dựa trên quy tắc cơ bản (AI không khả dụng)",factors:{emotional:t,financial:t,physical:t,mental:t}}}}class b{constructor(){d(this,"isBlocked",!1);d(this,"psychologyAI",null);d(this,"lastTradeAmount",0);d(this,"tradeObserver",null);d(this,"lastTradeReasonModalTime",0);d(this,"lastMindfulnessModalTime",0);d(this,"hasShownAIPsychologyModal",!1);d(this,"currentTradeId",null);d(this,"lastTradeElementState",new Map);this.init(),window.testMindfulnessModal=e=>{console.log(`🧪 Manual test trigger for ${e} modal`),this.lastMindfulnessModalTime=0,this.showMindfulnessModal(e)},window.testTradeReasonModal=()=>{console.log("🧪 Manual test trigger for trade reason modal"),this.lastTradeReasonModalTime=0,this.showTradeReasonModal()},window.scanForTradeElements=()=>{console.log("🔍 Manual scan for trade elements");const e=document.querySelectorAll("main.container"),t=document.querySelectorAll("progress-bar-item");console.log(`Found ${e.length} containers and ${t.length} progress items`),e.forEach((o,n)=>{const s=this.isElementVisible(o);console.log(`Container ${n}: visible=${s}, class=${o.className}`),s&&this.checkForTradeExecution(o)}),t.forEach((o,n)=>{const s=this.isElementVisible(o);console.log(`Progress item ${n}: visible=${s}, class=${o.className}`),s&&this.checkForTradeExecution(o)})},window.resetTradeState=()=>{console.log("🔄 Resetting trade state and cooldowns"),this.lastTradeReasonModalTime=0,this.lastMindfulnessModalTime=0,this.currentTradeId=null,this.lastTradeElementState.clear(),console.log("✅ State reset complete")},window.forceTradeModal=()=>{console.log("🚀 Force showing trade reason modal"),this.lastTradeReasonModalTime=0,this.showTradeReasonModal()},console.log("🧪 Test functions available:"),console.log('  - testMindfulnessModal("win") or testMindfulnessModal("loss")'),console.log("  - testTradeReasonModal()"),console.log("  - scanForTradeElements()"),console.log("  - resetTradeState() - Reset all cooldowns and state"),console.log("  - forceTradeModal() - Force show trade reason modal")}async init(){await this.initializeAI(),await this.checkTradingBlock(),this.isBlocked?this.showBlockedMessage():(this.checkPsychologyConfirmation(),this.showPsychologyStateModal(),this.startBehaviorTracking())}async initializeAI(){try{const e=await chrome.storage.local.get(["openaiApiKey"]);e.openaiApiKey&&(this.psychologyAI=new w(e.openaiApiKey))}catch(e){console.error("Error initializing AI:",e)}}async checkTradingBlock(){try{const e=await chrome.storage.local.get(["tradingBlocked","blockDate","blockUntil","blockDurationMinutes"]);if(e.tradingBlocked){const t=new Date;if(e.blockUntil){const o=new Date(e.blockUntil);if(t>=o){await chrome.storage.local.remove(["tradingBlocked","blockDate","blockUntil","blockDurationMinutes"]),this.isBlocked=!1;return}}this.isBlocked=!0}}catch(e){console.error("Error checking trading block:",e),this.isBlocked=!1}}showPsychologyStateModal(){setTimeout(()=>{!this.isBlocked&&!document.getElementById("psychology-state-modal")&&this.createPsychologyStateModal()},2e3)}createPsychologyStateModal(){var o,n;const e=document.createElement("div");e.id="psychology-state-modal",e.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,e.innerHTML=`
      <div style="
        background: white;
        padding: 32px;
        border-radius: 16px;
        max-width: 500px;
        width: 90%;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="text-align: center; margin-bottom: 24px;">
          <div style="font-size: 48px; margin-bottom: 16px;">🧠</div>
          <h3 style="color: #1976d2; margin: 0 0 8px 0;">Trạng thái tâm lý hiện tại</h3>
          <p style="color: #666; margin: 0; font-size: 14px;">
            Hãy chia sẻ cảm xúc và trạng thái tâm lý của bạn lúc này
          </p>
        </div>

        <div style="margin-bottom: 24px;">
          <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">
            Hiện tại bạn đang cảm thấy như thế nào?
          </label>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 16px;">
            <button class="emotion-btn" data-emotion="tham" style="
              background: #ffebee;
              color: #c62828;
              border: 2px solid #ffcdd2;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">🤑 Tham lam</button>

            <button class="emotion-btn" data-emotion="gian" style="
              background: #fff3e0;
              color: #ef6c00;
              border: 2px solid #ffcc02;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">😡 Giận dữ</button>

            <button class="emotion-btn" data-emotion="lo_au" style="
              background: #f3e5f5;
              color: #7b1fa2;
              border: 2px solid #e1bee7;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">😰 Lo lắng</button>

            <button class="emotion-btn" data-emotion="so_hai" style="
              background: #e8f5e8;
              color: #388e3c;
              border: 2px solid #c8e6c9;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">😨 Sợ hãi</button>

            <button class="emotion-btn" data-emotion="si_me" style="
              background: #e3f2fd;
              color: #1976d2;
              border: 2px solid #bbdefb;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">😵‍💫 Si mê</button>

            <button class="emotion-btn" data-emotion="u_me" style="
              background: #fce4ec;
              color: #ad1457;
              border: 2px solid #f8bbd9;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">😴 U mê</button>

            <button class="emotion-btn" data-emotion="phan_khich" style="
              background: #fff8e1;
              color: #f57f17;
              border: 2px solid #fff176;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">🤩 Phấn khích</button>

            <button class="emotion-btn" data-emotion="hung_phan" style="
              background: #e0f2f1;
              color: #00695c;
              border: 2px solid #b2dfdb;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">😤 Hưng phấn</button>

            <button class="emotion-btn" data-emotion="binh_tinh" style="
              background: #e8f5e8;
              color: #2e7d32;
              border: 2px solid #c8e6c9;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">😌 Bình tĩnh</button>

            <button class="emotion-btn" data-emotion="tinh_thuc" style="
              background: #e3f2fd;
              color: #1565c0;
              border: 2px solid #90caf9;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">🧘‍♂️ Tỉnh thức</button>
          </div>

          <textarea
            id="psychology-notes"
            placeholder="Mô tả thêm về cảm xúc và suy nghĩ hiện tại của bạn..."
            style="
              width: 100%;
              height: 80px;
              padding: 12px;
              border: 2px solid #e0e0e0;
              border-radius: 8px;
              font-size: 14px;
              resize: vertical;
              font-family: inherit;
              margin-top: 8px;
            "
          ></textarea>
        </div>

        <div style="display: flex; gap: 12px;">
          <button id="skip-psychology" style="
            flex: 1;
            background: #666;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">Bỏ qua</button>
          <button id="analyze-psychology" style="
            flex: 2;
            background: #1976d2;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
          ">🧠 Phân tích tâm lý</button>
        </div>

        <div id="psychology-result" style="
          margin-top: 16px;
          padding: 16px;
          background: #f5f5f5;
          border-radius: 8px;
          display: none;
        "></div>
      </div>
    `,document.body.appendChild(e);let t="";e.querySelectorAll(".emotion-btn").forEach(s=>{s.addEventListener("click",i=>{const r=i.target;t=r.getAttribute("data-emotion")||"",e.querySelectorAll(".emotion-btn").forEach(l=>{l.style.borderColor=l.style.backgroundColor}),r.style.borderColor="#1976d2",r.style.borderWidth="3px"})}),(o=document.getElementById("skip-psychology"))==null||o.addEventListener("click",()=>{e.remove()}),(n=document.getElementById("analyze-psychology"))==null||n.addEventListener("click",()=>{this.analyzePsychologyState(t)})}createAIPsychologyModal(){var t,o;const e=document.createElement("div");e.id="ai-psychology-modal",e.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,e.innerHTML=`
      <div style="
        background: white;
        padding: 32px;
        border-radius: 16px;
        max-width: 500px;
        width: 90%;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="text-align: center; margin-bottom: 24px;">
          <div style="font-size: 48px; margin-bottom: 16px;">🧠</div>
          <h3 style="color: #1976d2; margin: 0 0 8px 0;">Đánh giá tâm lý AI</h3>
          <p style="color: #666; margin: 0; font-size: 14px;">
            Hãy chia sẻ cảm xúc hiện tại để AI đánh giá tâm lý giao dịch
          </p>
        </div>

        <div style="margin-bottom: 24px;">
          <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">
            Hiện tại bạn đang cảm thấy như thế nào?
          </label>
          <textarea 
            id="psychology-diary" 
            placeholder="Ví dụ: Tôi cảm thấy hơi lo lắng vì thua lỗ hôm qua, nhưng cũng muốn gỡ lại. Tôi đã ngủ đủ giấc và cảm thấy tỉnh táo..."
            style="
              width: 100%;
              height: 120px;
              padding: 12px;
              border: 2px solid #e0e0e0;
              border-radius: 8px;
              font-size: 14px;
              resize: vertical;
              font-family: inherit;
            "
          ></textarea>
          <div style="margin-top: 8px; font-size: 12px; color: #666;">
            💡 Hãy thành thật về cảm xúc: tham lam, giận dữ, lo lắng, sợ hãi, phấn khích...
          </div>
        </div>

        <div style="display: flex; gap: 12px;">
          <button id="skip-assessment" style="
            flex: 1;
            background: #666;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">Bỏ qua</button>
          <button id="ai-analyze" style="
            flex: 2;
            background: #1976d2;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
          ">🤖 Phân tích AI</button>
        </div>

        <div id="ai-result" style="
          margin-top: 16px;
          padding: 16px;
          background: #f5f5f5;
          border-radius: 8px;
          display: none;
        "></div>
      </div>
    `,document.body.appendChild(e),(t=document.getElementById("skip-assessment"))==null||t.addEventListener("click",()=>{e.remove()}),(o=document.getElementById("ai-analyze"))==null||o.addEventListener("click",()=>{this.performAIAnalysis()})}async analyzePsychologyState(e){var s;const t=((s=document.getElementById("psychology-notes"))==null?void 0:s.value)||"",o=document.getElementById("psychology-result"),n=document.getElementById("analyze-psychology");if(!e){alert("Vui lòng chọn trạng thái cảm xúc hiện tại");return}n.textContent="🧠 Đang phân tích...",n.disabled=!0;try{const r={tham:20,gian:15,lo_au:30,so_hai:25,si_me:35,u_me:40,phan_khich:45,hung_phan:50,binh_tinh:85,tinh_thuc:95}[e]||50,l=this.calculateBlockDurationFromScore(r),a=r>=70,h={tham:"Tâm tham lam có thể dẫn đến quyết định liều lĩnh và mất kiểm soát",gian:"Tâm giận dữ làm mờ khả năng phán đoán và dễ dẫn đến sai lầm",lo_au:"Tâm lo lắng tạo ra căng thẳng và ảnh hưởng đến quyết định",so_hai:"Tâm sợ hãi có thể khiến bạn bỏ lỡ cơ hội hoặc thoát quá sớm",si_me:"Tâm si mê làm mất tỉnh táo và khả năng nhìn nhận thực tế",u_me:"Tâm u mê thiếu sự tỉnh thức cần thiết cho giao dịch",phan_khich:"Tâm phấn khích có thể dẫn đến quyết định vội vàng",hung_phan:"Tâm hưng phấn cần được kiểm soát để tránh rủi ro",binh_tinh:"Tâm bình tĩnh là nền tảng tốt cho giao dịch có ý thức",tinh_thuc:"Tâm tỉnh thức là trạng thái lý tưởng cho giao dịch"}[e]||"Cần đánh giá thêm";o&&(o.style.display="block",o.innerHTML=`
          <div style="text-align: center; margin-bottom: 16px;">
            <div style="font-size: 32px; margin-bottom: 8px;">
              ${r>=80?"😊":r>=60?"😐":"😰"}
            </div>
            <div style="font-size: 24px; font-weight: bold; color: ${r>=80?"#4caf50":r>=60?"#ff9800":"#f44336"};">
              ${r}/100 điểm
            </div>
          </div>

          <div style="margin-bottom: 16px;">
            <strong>Phân tích tâm lý:</strong>
            <p style="margin: 8px 0; font-size: 14px; line-height: 1.4;">${h}</p>
            ${t?`<p style="margin: 8px 0; font-size: 13px; color: #666; font-style: italic;">"${t}"</p>`:""}
          </div>

          ${a?`
            <div style="background: #d4edda; padding: 12px; border-radius: 8px; border-left: 4px solid #28a745; margin-bottom: 16px;">
              <strong>✅ Có thể giao dịch:</strong> Tâm lý ổn định
              <br><small>Hãy duy trì sự tỉnh thức và tuân thủ nguyên tắc giao dịch.</small>
            </div>
          `:`
            <div style="background: #fff3cd; padding: 12px; border-radius: 8px; border-left: 4px solid #ffc107; margin-bottom: 16px;">
              <strong>⚠️ Khuyến nghị nghỉ ngơi:</strong> ${this.formatDuration(l)}
              <br><small>Hãy dành thời gian thiền định và rèn luyện tâm để chuẩn bị tốt hơn.</small>
            </div>
          `}

          <div style="display: flex; gap: 8px;">
            ${a?`
              <button onclick="document.getElementById('psychology-state-modal').remove()" style="
                flex: 1; background: #4caf50; color: white; border: none; padding: 10px; border-radius: 6px; cursor: pointer;
              ">✅ Tiếp tục giao dịch</button>
            `:`
              <button onclick="window.open('${chrome.runtime.getURL("options.html#meditation")}', '_blank')" style="
                flex: 1; background: #9c27b0; color: white; border: none; padding: 10px; border-radius: 6px; cursor: pointer;
              ">🧘‍♂️ Đi thiền</button>
            `}
            <button onclick="document.getElementById('psychology-state-modal').remove()" style="
              background: #666; color: white; border: none; padding: 10px 16px; border-radius: 6px; cursor: pointer;
            ">Đóng</button>
          </div>
        `),await chrome.storage.local.set({lastPsychologyState:{timestamp:Date.now(),emotion:e,notes:t,score:r,shouldTrade:a,blockDuration:l}}),!a&&l>0&&await this.blockTradingWithDuration(l)}catch(i){console.error("Psychology analysis error:",i),o&&(o.style.display="block",o.innerHTML=`
          <div style="background: #f8d7da; padding: 12px; border-radius: 8px; color: #721c24;">
            <strong>❌ Lỗi phân tích:</strong> Không thể phân tích tâm lý. Vui lòng thử lại.
          </div>
        `)}n.textContent="🧠 Phân tích tâm lý",n.disabled=!1}calculateBlockDurationFromScore(e){return e>=80?0:e>=60?15:e>=45?60:e>=30?240:e>=15?720:1440}async performAIAnalysis(){var n;const e=(n=document.getElementById("psychology-diary"))==null?void 0:n.value,t=document.getElementById("ai-result"),o=document.getElementById("ai-analyze");if(!e.trim()){alert("Vui lòng chia sẻ cảm xúc hiện tại của bạn");return}if(!this.psychologyAI){alert("AI chưa được cấu hình. Vui lòng thêm OpenAI API key trong Settings.");return}o.textContent="🤖 Đang phân tích...",o.disabled=!0;try{const s={emotionalState:e,financialSituation:"Không rõ",recentPerformance:"Không rõ",sleepQuality:"Không rõ",stressLevel:"Không rõ",motivation:e,additionalNotes:"Đánh giá từ nhật ký tâm trạng"},i=await this.psychologyAI.assessPsychology(s);t&&(t.style.display="block",t.innerHTML=`
          <div style="text-align: center; margin-bottom: 16px;">
            <div style="font-size: 32px; margin-bottom: 8px;">
              ${i.score>=80?"😊":i.score>=60?"😐":"😰"}
            </div>
            <div style="font-size: 24px; font-weight: bold; color: ${i.score>=80?"#4caf50":i.score>=60?"#ff9800":"#f44336"};">
              ${i.score}/100 điểm
            </div>
          </div>
          
          <div style="margin-bottom: 16px;">
            <strong>Phân tích AI:</strong>
            <p style="margin: 8px 0; font-size: 14px; line-height: 1.4;">${i.aiAnalysis}</p>
          </div>
          
          <div style="margin-bottom: 16px;">
            <strong>Khuyến nghị:</strong>
            <p style="margin: 8px 0; font-size: 14px; line-height: 1.4;">${i.recommendation}</p>
          </div>
          
          ${i.blockDuration>0?`
            <div style="background: #fff3cd; padding: 12px; border-radius: 8px; border-left: 4px solid #ffc107;">
              <strong>⚠️ Khuyến nghị nghỉ ngơi:</strong> ${this.formatDuration(i.blockDuration)}
            </div>
          `:`
            <div style="background: #d4edda; padding: 12px; border-radius: 8px; border-left: 4px solid #28a745;">
              <strong>✅ Có thể giao dịch:</strong> Tâm lý ổn định
            </div>
          `}
          
          <div style="margin-top: 16px; display: flex; gap: 8px;">
            ${i.shouldTrade?`
              <button onclick="document.getElementById('ai-psychology-modal').remove()" style="
                flex: 1; background: #4caf50; color: white; border: none; padding: 10px; border-radius: 6px; cursor: pointer;
              ">✅ Tiếp tục giao dịch</button>
            `:`
              <button onclick="window.open('${chrome.runtime.getURL("options.html#meditation")}', '_blank')" style="
                flex: 1; background: #9c27b0; color: white; border: none; padding: 10px; border-radius: 6px; cursor: pointer;
              ">🧘‍♂️ Đi thiền</button>
            `}
            <button onclick="document.getElementById('ai-psychology-modal').remove()" style="
              background: #666; color: white; border: none; padding: 10px 16px; border-radius: 6px; cursor: pointer;
            ">Đóng</button>
          </div>
        `),await chrome.storage.local.set({lastAIAssessment:{timestamp:Date.now(),score:i.score,shouldTrade:i.shouldTrade,blockDuration:i.blockDuration}}),!i.shouldTrade&&i.blockDuration>0&&await this.blockTradingWithDuration(i.blockDuration)}catch(s){console.error("AI analysis error:",s),t&&(t.style.display="block",t.innerHTML=`
          <div style="background: #f8d7da; padding: 12px; border-radius: 8px; color: #721c24;">
            <strong>❌ Lỗi phân tích AI:</strong> ${s.message||"Không thể kết nối với AI"}
          </div>
        `)}o.textContent="🤖 Phân tích AI",o.disabled=!1}formatDuration(e){return e<60?`${e} phút`:e<1440?`${Math.round(e/60)} tiếng`:`${Math.round(e/1440)} ngày`}async blockTradingWithDuration(e){try{const t=new Date,o=new Date(t.getTime()+e*60*1e3);await chrome.storage.local.set({tradingBlocked:!0,blockDate:t.toISOString(),blockUntil:o.toISOString(),blockDurationMinutes:e,needsPsychologyConfirmation:!0}),setTimeout(()=>window.location.reload(),2e3)}catch(t){console.error("Error blocking trading:",t)}}startBehaviorTracking(){setTimeout(()=>{this.observeTradeActions(),this.observeTradeResults()},3e3)}observeTradeActions(){console.log("🎯 Starting trade action observation...");const e=['button[class*="deal"]','button[class*="trade"]','button[class*="up"]','button[class*="down"]','button[class*="higher"]','button[class*="lower"]','button[class*="call"]','button[class*="put"]',".deal-button",".trade-button",".option-button",'[data-direction="up"]','[data-direction="down"]',".trading-panel button",".deal-panel button"],t=['input[data-test-id*="amount"]','input[class*="amount"]','input[type="number"]',".amount-input",'[data-field="amount"]'],o=()=>{t.forEach(s=>{document.querySelectorAll(s).forEach(r=>{r.hasAttribute("data-monitored")||(r.setAttribute("data-monitored","true"),r.addEventListener("change",l=>{const a=l.target;this.lastTradeAmount=parseFloat(a.value)||0,console.log("💰 Amount changed:",this.lastTradeAmount)}))})})},n=()=>{e.forEach(s=>{document.querySelectorAll(s).forEach(r=>{r.hasAttribute("data-monitored")||(r.setAttribute("data-monitored","true"),r.addEventListener("click",l=>{console.log("🎯 Deal button clicked:",r),this.isValidTradeButton(r)&&console.log("✅ Valid trade button clicked, waiting for trade execution...")}))})})};o(),n(),this.tradeObserver=new MutationObserver(s=>{let i=!1;s.forEach(r=>{r.addedNodes.forEach(l=>{if(l.nodeType===Node.ELEMENT_NODE){const a=l;e.forEach(c=>{a.matches&&a.matches(c)&&(i=!0),a.querySelectorAll&&a.querySelectorAll(c).length>0&&(i=!0)})}})}),i&&setTimeout(()=>{o(),n()},500)}),this.tradeObserver.observe(document.body,{childList:!0,subtree:!0})}isValidTradeButton(e){var l;const t=((l=e.textContent)==null?void 0:l.toLowerCase())||"",o=e.className.toLowerCase(),n=e.id.toLowerCase(),s=["up","down","higher","lower","call","put","deal","trade","buy","sell"],i=["close","cancel","menu","settings","help","login","register","deposit","withdraw"];for(const a of i)if(t.includes(a)||o.includes(a)||n.includes(a))return!1;for(const a of s)if(t.includes(a)||o.includes(a)||n.includes(a))return!0;const r=e.parentElement;if(r){const a=r.className.toLowerCase();if(a.includes("deal")||a.includes("trade")||a.includes("trading"))return!0}return!1}showTradeReasonModal(){const e=Date.now(),t=1e4;if(console.log("🎯 Attempting to show trade reason modal"),console.log(`⏰ Time since last trade modal: ${e-this.lastTradeReasonModalTime}ms (cooldown: ${t}ms)`),e-this.lastTradeReasonModalTime<t){console.log("⏰ Trade reason modal on cooldown");return}const o=document.getElementById("trade-reason-modal");o&&(console.log("📝 Removing existing trade reason modal"),o.remove()),this.lastTradeReasonModalTime=e,console.log("🤔 Creating trade reason modal");const n=document.createElement("div");n.id="trade-reason-modal",n.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,n.innerHTML=`
      <div style="
        background: white;
        padding: 24px;
        border-radius: 16px;
        max-width: 400px;
        width: 90%;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        text-align: center;
      ">
        <div style="font-size: 32px; margin-bottom: 16px;">🤔</div>
        <h3 style="color: #1976d2; margin: 0 0 16px 0;">Lý do vào lệnh</h3>
        <p style="color: #666; margin: 0 0 20px 0; font-size: 14px;">
          Lệnh này bạn vào vì điều gì?
        </p>

        <div style="display: flex; flex-direction: column; gap: 8px; margin-bottom: 20px;">
          <button class="reason-btn" data-reason="method" style="
            background: #4caf50;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">✅ Theo nguyên tắc phương pháp</button>
          
          <button class="reason-btn" data-reason="greed" style="
            background: #ff9800;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🤑 Vì tâm tham lam</button>
          
          <button class="reason-btn" data-reason="anger" style="
            background: #f44336;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">😡 Vì tâm giận dữ (gỡ lại)</button>
          
          <button class="reason-btn" data-reason="guess" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🎲 Vì suy đoán tầm bậy</button>
        </div>

        <button onclick="document.getElementById('trade-reason-modal').remove()" style="
          background: #666;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 12px;
        ">Bỏ qua</button>
      </div>
    `,document.body.appendChild(n),n.querySelectorAll(".reason-btn").forEach(s=>{s.addEventListener("click",i=>{const r=i.target.getAttribute("data-reason");this.handleTradeReason(r),n.remove()})})}async handleTradeReason(e){if(e==="method")console.log("✅ Trade based on method principles");else{const t=document.createElement("div");t.style.cssText=`
        position: fixed;
        top: 20px;
        right: 20px;
        background: #fff3cd;
        border: 1px solid #ffc107;
        padding: 16px;
        border-radius: 8px;
        z-index: 999999;
        max-width: 300px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      `,t.innerHTML=`
        <div style="color: #856404;">
          <strong>⚠️ Cảnh báo tâm lý</strong>
          <p style="margin: 8px 0; font-size: 14px;">
            Giao dịch không theo nguyên tắc có thể gây thua lỗ. Hãy nghỉ ngơi và rèn tâm.
          </p>
          <button onclick="window.open('${chrome.runtime.getURL("options.html#meditation")}', '_blank')" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 8px;
          ">🧘‍♂️ Thiền định</button>
          <button onclick="this.parentElement.parentElement.remove()" style="
            background: #666;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
          ">Đóng</button>
        </div>
      `,document.body.appendChild(t),setTimeout(()=>{t.parentElement&&t.remove()},1e4)}await chrome.storage.local.set({lastTradeReason:{timestamp:Date.now(),reason:e,amount:this.lastTradeAmount}})}observeTradeResults(){console.log("📊 Starting trade result observation...");const e=new Set;setTimeout(()=>{const n=["progress-bar-item","progress-bar-timeline",".option.win",".option:not(.win)","lottie-player.lose","option-animation"];let s=0;n.forEach(i=>{const r=document.querySelectorAll(i);r.forEach(l=>{e.add(l),s++}),console.log(`📊 Found ${r.length} existing elements for selector: ${i}`)}),console.log(`📊 Initial scan: marked ${s} existing elements total`)},1e3),new MutationObserver(n=>{n.forEach(s=>{var i;if(s.addedNodes.length>0&&console.log("🔄 DOM mutation detected, added nodes:",s.addedNodes.length),s.addedNodes.forEach(r=>{var l;if(r.nodeType===Node.ELEMENT_NODE){const a=r,c=((l=a.tagName)==null?void 0:l.toLowerCase())||"",h=a.className||"";console.log("➕ New element added:",c,h),e.has(a)?console.log("⏭️ Skipping existing element:",c):(console.log("🆕 Processing truly new element:",c),this.processNewElement(a));const m=a.querySelectorAll("*");m.length>0&&(console.log("👶 Checking",m.length,"child elements"),m.forEach(u=>{var p;if(!e.has(u)){const f=((p=u.tagName)==null?void 0:p.toLowerCase())||"",y=u.className||"";console.log("👶 Processing new child:",f,y),this.processNewElement(u)}}))}}),s.type==="attributes"){const r=s.target,l=Date.now(),a=((i=r.tagName)==null?void 0:i.toLowerCase())||"",c=r.className||"",h=s.attributeName;console.log("🎨 Attribute change detected:",h,a,c),h==="class"&&(c.includes("ng-trigger-progressBarItemInOut")||c.includes("ng-star-inserted")||c.includes("ng-trigger"))&&(a==="progress-bar-item"||a==="main")&&this.isElementVisible(r)&&(console.log("🎯 Angular animation detected for trade element:",a),this.checkForTradeExecution(r)),h==="class"&&a==="lottie-player"&&c.includes("lose")&&(l-this.lastMindfulnessModalTime>3e3?(console.log("😔 Loss result detected via class change"),this.lastMindfulnessModalTime=l,setTimeout(()=>this.showMindfulnessModal("loss",!0),2e3)):console.log("⏰ Loss modal on cooldown (class change)")),h==="style"&&(a==="progress-bar-item"||a==="main"&&c.includes("container"))&&this.isElementVisible(r)&&(console.log("👁️ Style change detected for trade element:",a),this.checkForTradeExecution(r))}})}).observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["class","style","data-state"]}),this.startVisibilityObserver()}startVisibilityObserver(){console.log("👁️ Starting visibility observer for trade execution detection");const e=()=>{document.querySelectorAll("main.container").forEach(n=>{this.isElementVisible(n)&&(console.log("👁️ Found visible container:",n.className),this.checkForTradeExecution(n))}),document.querySelectorAll("progress-bar-item").forEach(n=>{this.isElementVisible(n)&&(console.log("👁️ Found visible progress-bar-item:",n.className),this.checkForTradeExecution(n))})};setInterval(e,2e3),setTimeout(e,1e3)}isElementVisible(e){const t=window.getComputedStyle(e),o=e.getBoundingClientRect();return t.display!=="none"&&t.visibility!=="hidden"&&t.opacity!=="0"&&o.width>0&&o.height>0}checkForTradeExecution(e){var l;const t=Date.now(),o=((l=e.tagName)==null?void 0:l.toLowerCase())||"",n=e.className||"",s=`${o}-${n.replace(/\s+/g,"-")}`,i=this.isValidTradeExecution(e),r=this.lastTradeElementState.get(s)||!1;console.log("🔍 Checking trade execution:",o,`valid=${i}, wasBefore=${r}`),i&&!r?(console.log("🆕 NEW trade execution state detected (became valid)"),console.log("🎯 Triggering trade reason modal for new trade"),this.lastTradeReasonModalTime=t,this.currentTradeId=s,setTimeout(()=>this.showTradeReasonModal(),1e3)):i&&r?console.log("⏭️ Trade execution still valid (no state change)"):!i&&r&&console.log("🔚 Trade execution ended (became invalid)"),this.lastTradeElementState.set(s,i)}processNewElement(e){var i;const t=Date.now(),o=((i=e.tagName)==null?void 0:i.toLowerCase())||"",n=e.className||"";console.log("🔍 Processing new element:",o,n),(o==="progress-bar-item"||o==="progress-bar-timeline"||o==="main"&&n.includes("container"))&&(console.log("🎯 Found potential trade execution element:",o,n),this.isValidTradeExecution(e)?(console.log("✅ Valid trade execution confirmed"),console.log("🎯 Forcing trade reason modal (ignoring cooldown for real trade)"),this.lastTradeReasonModalTime=t,setTimeout(()=>this.showTradeReasonModal(),1e3)):console.log("❌ Trade execution validation failed")),e.matches&&e.matches(".option.win")&&this.isValidWinResult(e)&&(console.log("🎉 NEW win result detected:",n),console.log("🎯 Forcing win modal (ignoring cooldown for real trade result)"),this.lastMindfulnessModalTime=t,setTimeout(()=>this.showMindfulnessModal("win",!0),2e3));let s=!1;o==="lottie-player"&&n.includes("lose")&&this.isValidLossResult(e)&&(console.log("😔 NEW loss result detected (animation):",o,n),console.log("🎯 Forcing loss modal (ignoring cooldown for real trade result)"),this.lastMindfulnessModalTime=t,setTimeout(()=>this.showMindfulnessModal("loss",!0),2e3),s=!0),!s&&o==="div"&&n.includes("option")&&!n.includes("win")&&!n.includes("analytics")&&this.isValidLossResult(e)&&(console.log("😔 NEW loss result detected (option):",o,n),console.log("🎯 Forcing loss modal (ignoring cooldown for real trade result)"),this.lastMindfulnessModalTime=t,setTimeout(()=>this.showMindfulnessModal("loss",!0),2e3))}isValidTradeExecution(e){var n;const t=((n=e.tagName)==null?void 0:n.toLowerCase())||"",o=e.className||"";if(console.log("🔍 Validating trade execution:",t,o),t==="main"&&o.includes("container")){const s=e.querySelector("progress-bar-item"),i=e.querySelector("progress-bar-timeline"),r=e.querySelector(".profit");console.log("📊 Container validation:",{hasProgressBar:!!s,hasProgressTimeline:!!i,hasProfit:!!r});const l=!!(s||i);return console.log("📊 Container valid:",l),l}if(t==="progress-bar-item"){const s=e.querySelector(".profit"),i=e.querySelector(".arrow"),r=e.querySelector("progress-bar-timeline"),l=e.querySelector(".asset-icon");console.log("📈 Progress bar validation:",{hasProfit:!!s,hasArrow:!!i,hasTimeline:!!r,hasAssetIcon:!!l}),s&&console.log("💰 Profit text:",s.textContent),i&&console.log("➡️ Arrow text:",i.textContent);const a=!!(s||i||l);return console.log("📈 Trade execution valid:",a),a}if(t==="progress-bar-timeline"){const s=e.querySelector(".progress"),i=e.querySelector(".text");console.log("⏱️ Timeline validation:",{hasProgress:!!s,hasText:!!i}),i&&console.log("⏰ Timeline text:",i.textContent);const r=!!(s&&i);return console.log("⏱️ Timeline valid:",r),r}return console.log("❌ Not a recognized trade execution element"),!1}isValidWinResult(e){var n;const t=e.className||"",o=((n=e.tagName)==null?void 0:n.toLowerCase())||"";if(console.log("🏆 Validating win result:",o,t),t.includes("option")&&t.includes("win")){const s=e.querySelector(".currency");if(s){const i=s.textContent||"";console.log("💰 Win currency text:",i);const r=!i.includes("0,00")&&(i.includes("$")||i.includes("₫"));return console.log("💰 Has profit:",r),r}return console.log("✅ Win class detected without currency check"),!0}return!1}isValidLossResult(e){var n;const t=e.className||"",o=((n=e.tagName)==null?void 0:n.toLowerCase())||"";if(console.log("😔 Validating loss result:",o,t),o==="lottie-player"&&t.includes("lose"))return console.log("❌ Loss animation detected (priority)"),!0;if(o==="div"&&t.includes("option")&&!t.includes("win")&&!t.includes("analytics")&&!t.includes("time")&&!t.includes("button")&&!t.includes("close")){const s=e.querySelector(".currency");if(s){const i=s.textContent||"";console.log("💸 Loss currency text:",i);const r=i.includes("0,00");if(console.log("💸 Is loss:",r),r){const l=e.querySelector(".badge"),a=e.querySelector(".assets");return console.log("🔍 Trade result structure:",{hasBadge:!!l,hasAssets:!!a}),!!(l&&a)}}}return!1}showMindfulnessModal(e,t=!1){const o=Date.now(),n=3e3;if(console.log(`🎯 Attempting to show mindfulness modal for ${e} (force: ${t})`),console.log(`⏰ Time since last modal: ${o-this.lastMindfulnessModalTime}ms (cooldown: ${n}ms)`),!t&&o-this.lastMindfulnessModalTime<n){console.log("⏰ Mindfulness modal on cooldown");return}const s=document.querySelector('[id*="mindfulness-modal"]');s&&(console.log("🧘‍♂️ Removing existing modal:",s.id),s.remove()),console.log(`🙏 Creating mindfulness modal for ${e}`);const i=e==="win",r=i?["Tôi biết ơn vì kết quả tốt này và sẽ không để nó làm tôi kiêu ngạo","Thắng lợi này là nhờ sự chuẩn bị kỹ lưỡng và tâm tỉnh thức","Tôi sẽ giữ tâm bình thản và tiếp tục theo nguyên tắc","Mọi thắng lợi đều vô thường, tôi không bám víu vào nó"]:["Thua lỗ là bài học quý báu để tôi trưởng thành hơn","Tôi chấp nhận kết quả này với tâm bình thản và không giận dữ","Mọi thua lỗ đều vô thường, tôi sẽ học hỏi và tiến bước","Tôi buông bỏ sự thất vọng và tập trung vào cải thiện bản thân"],l=document.createElement("div");l.id=`mindfulness-modal-${e}-${Date.now()}`,l.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,l.innerHTML=`
      <div style="
        background: white;
        padding: 24px;
        border-radius: 16px;
        max-width: 400px;
        width: 90%;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        text-align: center;
      ">
        <div style="font-size: 32px; margin-bottom: 16px;">${i?"🙏":"🧘‍♂️"}</div>
        <h3 style="color: ${i?"#4caf50":"#ff9800"}; margin: 0 0 16px 0;">
          ${i?"Lời buông xả cho thắng lợi":"Lời buông xả cho thua lỗ"}
        </h3>
        
        <div style="margin-bottom: 20px;">
          <p style="color: #666; margin: 0 0 16px 0; font-size: 14px;">
            Hãy chọn một câu để thực hành buông xả:
          </p>
          
          <div style="display: flex; flex-direction: column; gap: 8px;">
            ${r.map((a,c)=>`
              <button class="mindfulness-btn" data-text="${a}" style="
                background: ${i?"#e8f5e8":"#fff3e0"};
                color: ${i?"#2e7d32":"#f57c00"};
                border: 1px solid ${i?"#c8e6c9":"#ffcc02"};
                padding: 12px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 13px;
                text-align: left;
                line-height: 1.4;
              ">${a}</button>
            `).join("")}
          </div>
        </div>

        <button onclick="this.parentElement.parentElement.remove()" style="
          background: #666;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 12px;
        ">Đóng</button>
      </div>
    `,document.body.appendChild(l),l.querySelectorAll(".mindfulness-btn").forEach(a=>{a.addEventListener("click",c=>{const h=c.target.getAttribute("data-text");this.showMindfulnessConfirmation(h||""),l.remove()})}),setTimeout(()=>{l.parentElement&&l.remove()},3e4)}showMindfulnessConfirmation(e){const t=document.createElement("div");t.style.cssText=`
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4caf50;
      color: white;
      padding: 16px;
      border-radius: 8px;
      z-index: 999999;
      max-width: 300px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      line-height: 1.4;
    `,t.innerHTML=`
      <div>
        <strong>🙏 Thực hành buông xả</strong>
        <p style="margin: 8px 0 0 0;">"${e}"</p>
      </div>
    `,document.body.appendChild(t),setTimeout(()=>{t.parentElement&&t.remove()},5e3)}async showBlockedMessage(){var s,i;const e=await chrome.storage.local.get(["blockUntil","blockDurationMinutes","blockDate"]),t=document.createElement("div");t.id="trading-block-overlay",t.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.9);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;const o=this.calculateTimeRemaining(e),n=this.formatDuration(e.blockDurationMinutes||1440);t.innerHTML=`
      <div style="
        background: white;
        padding: 40px;
        border-radius: 16px;
        text-align: center;
        max-width: 500px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="font-size: 64px; margin-bottom: 20px;">🧘‍♂️</div>
        <h2 style="color: #d32f2f; margin: 0 0 16px 0;">Giao dịch bị khóa</h2>
        <p style="color: #666; margin: 0 0 16px 0; line-height: 1.5;">
          Hệ thống đã khóa giao dịch trong <strong>${n}</strong> để bảo vệ tâm lý của bạn.<br>
          Hãy sử dụng thời gian này để nghỉ ngơi và rèn luyện tâm.
        </p>
        <div id="countdown" style="
          background: #f5f5f5;
          padding: 16px;
          border-radius: 8px;
          margin: 16px 0;
          font-size: 18px;
          font-weight: bold;
          color: #d32f2f;
        ">
          Còn lại: ${o}
        </div>
        <div style="display: flex; gap: 12px; justify-content: center;">
          <button id="meditation-btn" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🧘‍♂️ Thiền định</button>
          <button id="close-tab-btn" style="
            background: #666;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">❌ Đóng tab</button>
        </div>
        <p style="color: #999; font-size: 12px; margin: 16px 0 0 0;">
          Thời gian khóa dựa trên đánh giá tâm lý của bạn
        </p>
      </div>
    `,document.body.appendChild(t),this.startCountdown(e),(s=document.getElementById("meditation-btn"))==null||s.addEventListener("click",()=>{this.openMeditationOptions()}),(i=document.getElementById("close-tab-btn"))==null||i.addEventListener("click",()=>{window.close()})}calculateTimeRemaining(e){const t=new Date;if(e.blockUntil){const n=new Date(e.blockUntil).getTime()-t.getTime();if(n<=0)return"Đã hết hạn";const s=Math.floor(n/(1e3*60*60)),i=Math.floor(n%(1e3*60*60)/(1e3*60));return s>0?`${s} tiếng ${i} phút`:`${i} phút`}return"Không xác định"}startCountdown(e){const t=document.getElementById("countdown");if(!t)return;const n=setInterval(()=>{const s=this.calculateTimeRemaining(e);t.textContent=`Còn lại: ${s}`,s==="Đã hết hạn"&&window.location.reload()},6e4);window.addEventListener("beforeunload",()=>{clearInterval(n)})}async checkPsychologyConfirmation(){try{(await chrome.storage.local.get(["needsPsychologyConfirmation"])).needsPsychologyConfirmation&&this.showPsychologyConfirmation()}catch(e){console.error("Error checking psychology confirmation:",e)}}showPsychologyConfirmation(){var o,n;const e=document.createElement("div");e.id="psychology-confirmation-modal",e.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,e.innerHTML=`
      <div style="
        background: white;
        padding: 32px;
        border-radius: 16px;
        text-align: center;
        max-width: 450px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="font-size: 48px; margin-bottom: 16px;">🧠</div>
        <h3 style="color: #1976d2; margin: 0 0 16px 0;">Xác nhận tâm lý</h3>
        <p style="color: #666; margin: 0 0 24px 0; line-height: 1.5;">
          Bạn đã hoàn thành thiền định hoặc nghỉ ngơi?<br>
          Tâm lý hiện tại có ổn định và sẵn sàng giao dịch không?
        </p>

        <div style="margin-bottom: 24px;">
          <p style="color: #333; font-weight: bold; margin: 0 0 12px 0;">
            Trạng thái tâm lý hiện tại:
          </p>
          <div style="display: flex; flex-direction: column; gap: 8px;">
            <button class="psychology-option" data-state="balanced" style="
              background: #e8f5e8;
              color: #2e7d32;
              border: 2px solid #e8f5e8;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😌 Cân bằng và tỉnh táo</button>
            <button class="psychology-option" data-state="confident" style="
              background: #e3f2fd;
              color: #1976d2;
              border: 2px solid #e3f2fd;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😊 Tự tin và kiên nhẫn</button>
            <button class="psychology-option" data-state="not_ready" style="
              background: #fff3e0;
              color: #f57c00;
              border: 2px solid #fff3e0;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😐 Chưa sẵn sàng, cần thêm thời gian</button>
          </div>
        </div>

        <div style="display: flex; gap: 12px; justify-content: center;">
          <button id="continue-trading-btn" style="
            background: #4caf50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            opacity: 0.5;
          " disabled>✅ Tiếp tục giao dịch</button>
          <button id="more-meditation-btn" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🧘‍♂️ Thiền thêm</button>
        </div>
      </div>
    `,document.body.appendChild(e);let t="";e.querySelectorAll(".psychology-option").forEach(s=>{s.addEventListener("click",i=>{const r=i.target;t=r.getAttribute("data-state")||"",e.querySelectorAll(".psychology-option").forEach(a=>{a.style.borderColor=a.style.backgroundColor}),r.style.borderColor="#1976d2";const l=document.getElementById("continue-trading-btn");t==="balanced"||t==="confident"?(l.disabled=!1,l.style.opacity="1"):(l.disabled=!0,l.style.opacity="0.5")})}),(o=document.getElementById("continue-trading-btn"))==null||o.addEventListener("click",()=>{(t==="balanced"||t==="confident")&&this.confirmPsychologyAndContinue(t)}),(n=document.getElementById("more-meditation-btn"))==null||n.addEventListener("click",()=>{this.openMeditationOptions()})}async confirmPsychologyAndContinue(e){try{await chrome.storage.local.set({needsPsychologyConfirmation:!1,lastConfirmationTime:Date.now(),confirmedPsychologyState:e});const t=document.getElementById("psychology-confirmation-modal");t&&t.remove(),this.showSuccessMessage("✅ Tâm lý đã được xác nhận! Bạn có thể giao dịch an toàn.")}catch(t){console.error("Error confirming psychology:",t)}}openMeditationOptions(){try{chrome.runtime.sendMessage({action:"openOptions",tab:"meditation"})}catch(e){console.error("Error opening meditation options:",e),window.open(chrome.runtime.getURL("options.html#meditation"),"_blank")}}showSuccessMessage(e){const t=document.createElement("div");t.style.cssText=`
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4caf50;
      color: white;
      padding: 16px 24px;
      border-radius: 8px;
      z-index: 999999;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      animation: slideIn 0.3s ease-out;
    `,t.textContent=e;const o=document.createElement("style");o.textContent=`
      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    `,document.head.appendChild(o),document.body.appendChild(t),setTimeout(()=>{t.remove(),o.remove()},3e3)}}document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>new b):new b;
