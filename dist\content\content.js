var $=Object.defineProperty;var z=(v,e,t)=>e in v?$(v,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):v[e]=t;var k=(v,e,t)=>z(v,typeof e!="symbol"?e+"":e,t);console.log("🎯 Binomo Trading Assistant - Simple Content Script Loaded");class S{constructor(){k(this,"currentFlow",null);k(this,"apiBase","http://127.0.0.1:3001");k(this,"methodSettings",{});k(this,"isBlocked",!1);this.init()}async fetchWithTimeout(e,t={},o=5e3){const n=new AbortController,i=setTimeout(()=>n.abort(),o);try{const r=await fetch(e,{...t,signal:n.signal,headers:{"Content-Type":"application/json",...t.headers}});return clearTimeout(i),r}catch(r){throw clearTimeout(i),r.name==="AbortError"?new Error("Request timeout - JSON Server may not be running"):r}}async init(){if(console.log("✅ Page ready, initializing Binomo Trading Assistant..."),!window.location.href.includes("binomo1.com/trading")){console.log("Not on Binomo trading page, exiting...");return}if(await this.checkTradingBlock(),this.isBlocked){this.showBlockedMessage();return}await this.loadMethodSettings(),await this.waitForPageReady(),setTimeout(()=>{this.startTradingFlow()},2e3),this.setupMessageListener()}async checkTradingBlock(){try{const e=await chrome.storage.local.get(["tradingBlocked","blockDate"]);if(e.tradingBlocked&&e.blockDate){const t=new Date(e.blockDate);new Date().toDateString()!==t.toDateString()?(await chrome.storage.local.remove(["tradingBlocked","blockDate"]),this.isBlocked=!1,console.log("✅ New day - trading unblocked")):(this.isBlocked=!0,console.log("🚫 Trading still blocked for today"))}else this.isBlocked=!1}catch(e){console.error("Error checking trading block:",e),this.isBlocked=!1}}showBlockedMessage(){var t,o;const e=document.createElement("div");e.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,e.innerHTML=`
      <div style="
        background: white;
        padding: 40px;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        text-align: center;
        max-width: 500px;
        margin: 20px;
      ">
        <div style="font-size: 64px; margin-bottom: 20px;">🧘‍♂️</div>
        <h1 style="color: #333; margin-bottom: 20px; font-size: 28px;">
          Thời gian nghỉ ngơi
        </h1>
        <p style="color: #666; margin-bottom: 30px; font-size: 16px; line-height: 1.6;">
          Tâm lý hiện tại không phù hợp để giao dịch.<br>
          Hãy dành thời gian thiền để tái tạo năng lượng tích cực.<br>
          <strong>Giao dịch sẽ được mở lại vào ngày mai.</strong>
        </p>
        <button id="meditation-btn" style="
          background: linear-gradient(45deg, #667eea, #764ba2);
          color: white;
          border: none;
          padding: 15px 30px;
          border-radius: 25px;
          font-size: 16px;
          cursor: pointer;
          margin-right: 10px;
          transition: transform 0.2s;
        ">
          🧘‍♂️ Đi thiền
        </button>
        <button id="close-tab-btn" style="
          background: #f44336;
          color: white;
          border: none;
          padding: 15px 30px;
          border-radius: 25px;
          font-size: 16px;
          cursor: pointer;
          transition: transform 0.2s;
        ">
          🚪 Đóng tab
        </button>
      </div>
    `,document.body.appendChild(e),(t=document.getElementById("meditation-btn"))==null||t.addEventListener("click",()=>{this.sendMessageSafely({action:"openOptions",tab:"meditation"}),window.close()}),(o=document.getElementById("close-tab-btn"))==null||o.addEventListener("click",()=>{window.close()}),document.body.style.overflow="hidden"}async sendMessageSafely(e){var t,o,n;try{if(!((t=chrome==null?void 0:chrome.runtime)!=null&&t.id))throw new Error("Chrome runtime not available");const i=await chrome.runtime.sendMessage(e);return console.log("Message sent successfully:",i),i}catch(i){if(console.log("Could not send message to background script:",i),e.action==="openOptions")try{if((o=chrome==null?void 0:chrome.runtime)!=null&&o.getURL){const r=chrome.runtime.getURL("options.html");return e.tab&&((n=chrome==null?void 0:chrome.storage)!=null&&n.local)&&await chrome.storage.local.set({requestedTab:e.tab}),window.open(r,"_blank"),console.log("Opened options page via fallback method"),{success:!0}}else console.log("Chrome runtime getURL not available")}catch(r){console.log("Could not open options page via fallback:",r)}return{success:!1,error:i.message||"Unknown error"}}}startManualTradingMode(){console.log("🎯 Starting manual trading mode..."),this.currentFlow="trading",this.setupTradingMonitoring(),this.showFloatingAssistant()}setupTradingMonitoring(){new MutationObserver(t=>{t.forEach(o=>{o.type==="childList"&&this.checkForTradeResults()})}).observe(document.body,{childList:!0,subtree:!0}),console.log("✅ Trading monitoring started")}checkForTradeResults(){const e=document.querySelectorAll('[class*="win"], [class*="profit"], [class*="success"]'),t=document.querySelectorAll('[class*="loss"], [class*="lose"], [class*="fail"]');e.forEach(o=>{var n;if(!o.hasAttribute("data-processed")){o.setAttribute("data-processed","true");const i=(n=o.textContent)==null?void 0:n.match(/\$?(\d+\.?\d*)/),r=i?parseFloat(i[1]):void 0;this.handleTradeResult("win",r)}}),t.forEach(o=>{o.hasAttribute("data-processed")||(o.setAttribute("data-processed","true"),this.handleTradeResult("loss"))})}showFloatingAssistant(){var o,n,i;const e=document.getElementById("trading-assistant-float");e&&e.remove();const t=document.createElement("div");t.id="trading-assistant-float",t.style.cssText=`
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 300px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.15);
      z-index: 999999;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      border: 2px solid #1976d2;
    `,t.innerHTML=`
      <div style="padding: 16px; border-bottom: 1px solid #e0e0e0;">
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <div style="display: flex; align-items: center;">
            <span style="font-size: 20px; margin-right: 8px;">🤖</span>
            <strong style="color: #1976d2;">Trading Assistant</strong>
          </div>
          <button id="close-assistant" style="
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #666;
          ">×</button>
        </div>
      </div>
      <div style="padding: 16px;">
        <div style="background: #e8f5e8; padding: 12px; border-radius: 8px; margin-bottom: 12px;">
          <div style="display: flex; align-items: center; margin-bottom: 8px;">
            <span style="font-size: 16px; margin-right: 8px;">✅</span>
            <strong style="color: #2e7d32;">Chế độ giao dịch thủ công</strong>
          </div>
          <p style="margin: 0; font-size: 14px; color: #2e7d32;">
            Extension đang theo dõi và sẽ hiển thị thông điệp chánh niệm khi có kết quả giao dịch.
          </p>
        </div>
        <div style="display: grid; gap: 8px;">
          <button id="restart-flow" style="
            background: #1976d2;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
          ">🔄 Khởi động lại flow</button>
          <button id="meditation-break" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
          ">🧘‍♂️ Nghỉ thiền</button>
        </div>
      </div>
    `,document.body.appendChild(t),(o=document.getElementById("close-assistant"))==null||o.addEventListener("click",()=>{t.remove()}),(n=document.getElementById("restart-flow"))==null||n.addEventListener("click",()=>{t.remove(),this.startTradingFlow()}),(i=document.getElementById("meditation-break"))==null||i.addEventListener("click",()=>{t.remove(),this.sendMessageSafely({action:"openOptions",tab:"meditation"})})}async blockTradingUntilTomorrow(){try{const e=new Date;await chrome.storage.local.set({tradingBlocked:!0,blockDate:e.toISOString()}),console.log("🚫 Trading blocked until tomorrow")}catch(e){console.error("Error blocking trading:",e)}}handleTradeResult(e,t){e==="win"?this.showMindfulWinMessage(t):this.showMindfulLossMessage()}showMindfulWinMessage(e){const t=["🙏 Tôi cảm ơn thị trường đã tạo ra nơi để tôi rèn luyện tâm và kiếm lợi nhuận đủ sống.","🌟 Tôi mong tôi khiêm nhường và biết ơn với thành công này.","💪 Tôi biết sẽ còn nhiều khó khăn trong luyện tâm nhưng tôi vẫn sẽ học hỏi và rèn luyện tiếp.","🧘‍♂️ Thành công này là kết quả của sự kiên nhẫn và kỷ luật."],o=`
      <div style="text-align: center; padding: 20px;">
        <div style="font-size: 48px; margin-bottom: 20px;">🎉</div>
        <h3 style="color: #4caf50; margin-bottom: 20px;">Lệnh Thắng${e?` (+$${e})`:""}</h3>
        <div style="background: #e8f5e9; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
          <p style="font-style: italic; color: #2e7d32; line-height: 1.6; margin: 0;">
            "${t[Math.floor(Math.random()*t.length)]}"
          </p>
        </div>
        <p style="color: #666; font-size: 14px;">
          Hãy duy trì tâm thái khiêm nhường và tiếp tục theo đúng kế hoạch giao dịch.
        </p>
      </div>
    `;this.createModal("trade-result","🧠 Tâm Niệm Sau Giao Dịch",o,[{text:"Tiếp tục giao dịch",primary:!0,onClick:()=>this.removeModal("trade-result")}])}showMindfulLossMessage(){const e=["🙏 Tôi cũng cảm ơn thị trường đã tạo ra nơi để tôi rèn luyện tâm.","⏳ Dù thế nào đi nữa tôi vẫn sẽ chờ đợi, phân tích và giao dịch theo đúng nguyên tắc của phương pháp.","🎯 Tôi chờ đợi tín hiệu tốt, hiểu chúng và chờ và quyết định vào lệnh.","🧘‍♂️ Mỗi lần thua là một bài học quý giá cho hành trình rèn luyện tâm."],t=`
      <div style="text-align: center; padding: 20px;">
        <div style="font-size: 48px; margin-bottom: 20px;">🤲</div>
        <h3 style="color: #ff9800; margin-bottom: 20px;">Lệnh Thua</h3>
        <div style="background: #fff3e0; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
          <p style="font-style: italic; color: #f57c00; line-height: 1.6; margin: 0;">
            "${e[Math.floor(Math.random()*e.length)]}"
          </p>
        </div>
        <p style="color: #666; font-size: 14px;">
          Hãy giữ bình tĩnh, tuân thủ kế hoạch và chờ đợi cơ hội tốt hơn.
        </p>
      </div>
    `;this.createModal("trade-result","🧠 Tâm Niệm Sau Giao Dịch",t,[{text:"Tiếp tục quan sát",primary:!0,onClick:()=>this.removeModal("trade-result")}])}async loadMethodSettings(){try{const e=await chrome.storage.local.get(["methodSettings"]);e.methodSettings?(this.methodSettings=e.methodSettings,console.log("✅ Method settings loaded:",this.methodSettings)):this.methodSettings={bollinger_bands:!0,rsi_divergence:!0,support_resistance:!0,moving_average:!0,price_action:!0}}catch(e){console.error("Error loading method settings:",e),this.methodSettings={bollinger_bands:!0,rsi_divergence:!0,support_resistance:!0,moving_average:!0,price_action:!0}}}waitForPageReady(){return new Promise(e=>{document.readyState==="complete"?e():window.addEventListener("load",()=>e())})}createModal(e,t,o,n=[]){this.removeModal(e);const i=document.createElement("div");i.id=e,i.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      animation: fadeIn 0.3s ease-out;
    `;const r=document.createElement("div");if(r.style.cssText=`
      background: white;
      border-radius: 12px;
      padding: 0;
      max-width: 600px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      animation: slideIn 0.3s ease-out;
    `,!document.getElementById("binomo-animations")){const u=document.createElement("style");u.id="binomo-animations",u.textContent=`
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        @keyframes slideIn {
          from { opacity: 0; transform: scale(0.9) translateY(-20px); }
          to { opacity: 1; transform: scale(1) translateY(0); }
        }
      `,document.head.appendChild(u)}const s=document.createElement("div");s.style.cssText=`
      padding: 24px 24px 16px;
      border-bottom: 1px solid #e0e0e0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    `,s.innerHTML=`
      <h2 style="margin: 0; color: #1976d2; font-size: 20px; font-weight: 600;">${t}</h2>
      <button onclick="document.getElementById('${e}').remove()" style="
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #666;
        padding: 4px;
        border-radius: 4px;
        transition: background-color 0.2s;
      " onmouseover="this.style.backgroundColor='#f5f5f5'" onmouseout="this.style.backgroundColor='transparent'">×</button>
    `;const d=document.createElement("div");if(d.style.cssText=`
      padding: 24px;
    `,d.innerHTML=o,n.length>0){const u=document.createElement("div");u.style.cssText=`
        padding: 16px 24px 24px;
        display: flex;
        gap: 12px;
        justify-content: flex-end;
        border-top: 1px solid #e0e0e0;
      `,n.forEach(h=>{const l=document.createElement("button");l.textContent=h.text,l.style.cssText=`
          padding: 10px 20px;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          font-weight: 500;
          transition: all 0.2s;
          ${h.primary?"background: #1976d2; color: white;":"background: #f5f5f5; color: #666;"}
        `,h.primary?(l.onmouseover=()=>l.style.backgroundColor="#1565c0",l.onmouseout=()=>l.style.backgroundColor="#1976d2"):(l.onmouseover=()=>l.style.backgroundColor="#e0e0e0",l.onmouseout=()=>l.style.backgroundColor="#f5f5f5"),l.onclick=h.onClick,u.appendChild(l)}),r.appendChild(s),r.appendChild(d),r.appendChild(u)}else r.appendChild(s),r.appendChild(d);return i.appendChild(r),document.body.appendChild(i),i}removeModal(e){const t=document.getElementById(e);t&&t.parentNode&&t.parentNode.removeChild(t)}showSuccessMessage(e){const t=document.createElement("div");if(t.style.cssText=`
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4caf50;
      color: white;
      padding: 16px 24px;
      border-radius: 8px;
      z-index: 1000000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      animation: slideInRight 0.3s ease-out;
    `,t.textContent=e,!document.getElementById("toast-animations")){const o=document.createElement("style");o.id="toast-animations",o.textContent=`
        @keyframes slideInRight {
          from { opacity: 0; transform: translateX(100%); }
          to { opacity: 1; transform: translateX(0); }
        }
      `,document.head.appendChild(o)}document.body.appendChild(t),setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t)},3e3)}async showDailyGoals(){console.log("🎯 Showing daily goals modal..."),this.currentFlow="goals";const e=await this.getTodayGoals(),t=`
      <div style="margin-bottom: 20px;">
        <p style="color: #666; margin-bottom: 16px;">Thiết lập mục tiêu giao dịch hàng ngày để duy trì kỷ luật và quản lý rủi ro hiệu quả.</p>
        ${e?`
          <div style="background: #e8f5e8; padding: 12px; border-radius: 6px; margin-bottom: 16px;">
            <small style="color: #2e7d32;">✅ <strong>Đã có mục tiêu hôm nay.</strong> Bạn có thể cập nhật lại nếu cần.</small>
          </div>
        `:""}
      </div>
      
      <div style="display: grid; gap: 16px;">
        <div>
          <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #1976d2; font-size: 16px;">🧘‍♂️ Mục đích tâm linh khi giao dịch</label>
          <textarea id="spiritual-purpose" placeholder="Ví dụ: Tôi giao dịch để rèn luyện tâm, giảm bớt tham lam, rèn luyện chánh niệm, giảm tham sân si sợ, biết đủ - đủ ăn đủ uống đủ mặc, biết vô thường - có được thì có mất, rèn luyện kỹ năng giao dịch..." style="
            width: 100%;
            padding: 16px;
            border: 2px solid #e3f2fd;
            border-radius: 8px;
            font-size: 15px;
            min-height: 120px;
            resize: vertical;
            transition: all 0.2s;
            box-sizing: border-box;
            background: #fafafa;
            line-height: 1.5;
          " onfocus="this.style.borderColor='#1976d2'; this.style.background='white'; this.style.boxShadow='0 0 0 3px rgba(25,118,210,0.1)'" onblur="this.style.borderColor='#e3f2fd'; this.style.background='#fafafa'; this.style.boxShadow='none'">${(e==null?void 0:e.spiritualPurpose)||""}</textarea>
          <small style="color: #666; font-style: italic;">Hãy viết về mục đích tâm linh và sự rèn luyện nội tâm qua giao dịch</small>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 12px;">
          <div>
            <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #333; font-size: 14px;">💰 Mục tiêu lợi nhuận ($)</label>
            <input type="number" id="profit-target" placeholder="50" value="${(e==null?void 0:e.profitTarget)||""}" style="
              width: 100%;
              padding: 10px;
              border: 2px solid #e0e0e0;
              border-radius: 6px;
              font-size: 14px;
              transition: border-color 0.2s;
              box-sizing: border-box;
            " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'">
          </div>

          <div>
            <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #333; font-size: 14px;">🛑 Giới hạn thua lỗ ($)</label>
            <input type="number" id="loss-limit" placeholder="20" value="${(e==null?void 0:e.lossLimit)||""}" style="
              width: 100%;
              padding: 10px;
              border: 2px solid #e0e0e0;
              border-radius: 6px;
              font-size: 14px;
              transition: border-color 0.2s;
              box-sizing: border-box;
            " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'">
          </div>

          <div>
            <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #333; font-size: 14px;">📊 Số lệnh tối đa</label>
            <input type="number" id="max-trades" placeholder="20" value="${(e==null?void 0:e.maxTrades)||""}" style="
              width: 100%;
              padding: 10px;
              border: 2px solid #e0e0e0;
              border-radius: 6px;
              font-size: 14px;
              transition: border-color 0.2s;
              box-sizing: border-box;
            " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'">
          </div>
        </div>

        <div>
          <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #333;">📝 Mục tiêu học tập hôm nay</label>
          <textarea id="learning-goal" placeholder="Ví dụ: Thực hành phương pháp Bollinger Bands với kỷ luật..." style="
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            min-height: 80px;
            resize: vertical;
            transition: border-color 0.2s;
            box-sizing: border-box;
          " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'">${(e==null?void 0:e.tradingGoal)||""}</textarea>
        </div>
      </div>
    `,o=[{text:"Hủy",onClick:()=>this.removeModal("daily-goals-modal")},{text:e?"Cập nhật mục tiêu":"Lưu mục tiêu",primary:!0,onClick:()=>this.saveDailyGoals()}];this.createModal("daily-goals-modal","🎯 Thiết lập Mục tiêu Hàng ngày",t,o)}async saveDailyGoals(){var d,u,h,l,g,b,m,p;const e=(d=document.getElementById("spiritual-purpose"))==null?void 0:d.value,t=(u=document.getElementById("profit-target"))==null?void 0:u.value,o=(h=document.getElementById("loss-limit"))==null?void 0:h.value,n=(l=document.getElementById("max-trades"))==null?void 0:l.value,i=(g=document.getElementById("learning-goal"))==null?void 0:g.value;if(!e||e.length<20){alert("Vui lòng viết mục đích tâm linh ít nhất 20 ký tự!");return}if(!t||!o||!n){alert("Vui lòng điền đầy đủ thông tin số liệu!");return}const r=new Date().toISOString().split("T")[0],s={id:`${r}-${Date.now()}`,date:r,spiritualPurpose:e,profitTarget:parseFloat(t),lossLimit:parseFloat(o),maxTrades:parseInt(n),tradingGoal:i||"Giao dịch có kỷ luật",lessons:[],createdAt:new Date().toISOString()};try{console.log("🔍 Testing API connection...");const c=await this.fetchWithTimeout(`${this.apiBase}/dailyGoals`,{method:"GET"});if(!c.ok)throw new Error(`API connection failed: ${c.status} ${c.statusText}`);console.log("✅ API connection successful");const a=await this.getTodayGoals();let y;if(a){const x={...a,...s,id:a.id};y=await this.fetchWithTimeout(`${this.apiBase}/dailyGoals/${a.id}`,{method:"PUT",body:JSON.stringify(x)}),console.log("✅ Daily goals updated:",x)}else y=await this.fetchWithTimeout(`${this.apiBase}/dailyGoals`,{method:"POST",body:JSON.stringify(s)}),console.log("✅ Daily goals created:",s);if(y.ok)this.removeModal("daily-goals-modal"),this.showSuccessMessage(a?"Mục tiêu đã được cập nhật!":"Mục tiêu hàng ngày đã được lưu!"),setTimeout(()=>this.showPsychologyAssessment(),1500);else{let x;try{x=await y.text()}catch{x=`HTTP ${y.status} ${y.statusText}`}throw console.error("❌ API Error Response:",{status:y.status,statusText:y.statusText,errorText:x}),new Error(`Failed to save goals: ${x}`)}}catch(c){console.error("❌ Error saving goals:",c);let a=`Không thể lưu mục tiêu!

`;(b=c.message)!=null&&b.includes("Failed to fetch")||(m=c.message)!=null&&m.includes("API connection failed")?(a+=`🔌 Lỗi kết nối:
`,a+=`• JSON Server không chạy hoặc không thể truy cập
`,a+=`• Hãy chạy: npm run server
`,a+=`• Kiểm tra port 3001 có bị chiếm không

`):(p=c.message)!=null&&p.includes("duplicate")?(a+=`🔄 Lỗi trùng lặp:
`,a+=`• Mục tiêu hôm nay đã tồn tại
`,a+=`• Hãy refresh trang và thử lại

`):a+=`📋 Chi tiết lỗi: ${c.message||"Unknown error"}

`,a+=`💡 Hướng dẫn khắc phục:
`,a+=`1. Kiểm tra JSON Server: npm run server
`,a+=`2. Refresh trang binomo1.com/trading
`,a+="3. Reload extension nếu cần",alert(a)}}async startTradingFlow(){try{console.log("🚀 Starting trading flow..."),await this.getTodayGoals()?this.showPsychologyAssessment():this.showDailyGoals()}catch(e){console.error("Error starting trading flow:",e),alert("Không thể kết nối với JSON Server. Hãy chạy: npm run server")}}async getTodayGoals(){try{const e=new Date().toISOString().split("T")[0],t=await this.fetchWithTimeout(`${this.apiBase}/dailyGoals`);if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);return(await t.json()).find(n=>n.date===e)}catch(e){return console.error("Error fetching today goals:",e),e.message.includes("timeout")&&console.error("💡 Hint: Make sure JSON Server is running with: npm run server"),null}}showPsychologyAssessment(){console.log("🧠 Showing psychology assessment..."),this.currentFlow="psychology";const e=`
      <div style="margin-bottom: 20px;">
        <p style="color: #666; margin-bottom: 16px;">Đánh giá trạng thái tâm lý hiện tại để đưa ra quyết định giao dịch phù hợp và an toàn.</p>
        <div style="background: #e3f2fd; padding: 12px; border-radius: 6px; margin-bottom: 16px;">
          <small style="color: #1976d2;">💡 <strong>Lưu ý:</strong> Hãy trung thực với cảm xúc hiện tại để nhận được khuyến nghị chính xác nhất.</small>
        </div>
      </div>

      <div style="display: grid; gap: 16px;">
        <div>
          <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">🎭 Trạng thái tâm lý hiện tại:</label>
          <select id="psychology-state" style="
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            box-sizing: border-box;
            transition: border-color 0.2s;
          " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'">
            <option value="">Chọn trạng thái tâm lý...</option>
            <option value="balanced">😌 Cân bằng - Tâm trạng ổn định, tự tin nhưng không chủ quan</option>
            <option value="greedy">🤑 Tham lam - Muốn kiếm tiền nhanh, không kiên nhẫn</option>
            <option value="fearful">😰 Sợ hãi - Lo lắng mất tiền, ngần ngại vào lệnh</option>
            <option value="impatient">⚡ Vội vàng - Không kiên nhẫn chờ setup, muốn vào lệnh ngay</option>
            <option value="overconfident">😎 Tự hào - Quá tự tin sau chuỗi thắng, cảm thấy "bất bại"</option>
            <option value="angry">😡 Tức giận - Muốn "revenge" sau khi thua, cảm xúc tiêu cực</option>
            <option value="stressed">😵 Căng thẳng - Áp lực tài chính, lo lắng về kết quả</option>
            <option value="euphoric">🤩 Hưng phấn - Quá phấn khích sau thắng lớn, mất tỉnh táo</option>
          </select>
        </div>

        <div>
          <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">📝 Mô tả chi tiết cảm xúc và suy nghĩ:</label>
          <textarea id="emotion-description" placeholder="Ví dụ: Hôm nay tôi cảm thấy khá tự tin sau khi đọc phân tích thị trường, nhưng cũng hơi lo lắng vì tuần trước thua một ít..." style="
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            min-height: 100px;
            resize: vertical;
            transition: border-color 0.2s;
            box-sizing: border-box;
          " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'"></textarea>
          <small style="color: #666;">Tối thiểu 20 ký tự để có đánh giá chính xác</small>
        </div>

        <div>
          <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">💰 Tình hình tài chính gần đây:</label>
          <select id="financial-situation" style="
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            box-sizing: border-box;
            transition: border-color 0.2s;
          " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'">
            <option value="">Chọn tình hình tài chính...</option>
            <option value="stable">✅ Ổn định - Không áp lực tài chính, giao dịch với tiền dư</option>
            <option value="slight_pressure">⚠️ Áp lực nhẹ - Có một chút áp lực nhưng vẫn kiểm soát được</option>
            <option value="high_pressure">🚨 Áp lực cao - Cần tiền gấp, giao dịch với tiền quan trọng</option>
            <option value="desperate">💸 Tuyệt vọng - Giao dịch để "cứu vãn" tình hình tài chính</option>
          </select>
        </div>

        <div>
          <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">📊 Kết quả giao dịch gần đây:</label>
          <select id="recent-performance" style="
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            box-sizing: border-box;
            transition: border-color 0.2s;
          " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'">
            <option value="">Chọn kết quả gần đây...</option>
            <option value="winning_streak">🔥 Chuỗi thắng - Thắng liên tiếp trong vài phiên gần đây</option>
            <option value="mixed_results">⚖️ Lẫn lộn - Có thắng có thua, kết quả không ổn định</option>
            <option value="losing_streak">📉 Chuỗi thua - Thua liên tiếp, đang trong giai đoạn khó khăn</option>
            <option value="break_even">➡️ Hòa vốn - Không lãi không lỗ trong thời gian gần đây</option>
            <option value="new_trader">🆕 Trader mới - Chưa có nhiều kinh nghiệm giao dịch</option>
          </select>
        </div>
      </div>
    `,t=[{text:"Quay lại",onClick:()=>{this.removeModal("psychology-assessment"),this.showDailyGoals()}},{text:"Đánh giá",primary:!0,onClick:()=>this.processPsychologyAssessment()}];this.createModal("psychology-assessment","🧠 Đánh giá Tâm lý",e,t)}async processPsychologyAssessment(){var r,s,d,u;const e=(r=document.getElementById("psychology-state"))==null?void 0:r.value,t=(s=document.getElementById("emotion-description"))==null?void 0:s.value,o=(d=document.getElementById("financial-situation"))==null?void 0:d.value,n=(u=document.getElementById("recent-performance"))==null?void 0:u.value;if(!e){alert("Vui lòng chọn trạng thái tâm lý hiện tại!");return}if(!t||t.length<20){alert("Vui lòng mô tả cảm xúc chi tiết ít nhất 20 ký tự!");return}if(!o){alert("Vui lòng chọn tình hình tài chính!");return}if(!n){alert("Vui lòng chọn kết quả giao dịch gần đây!");return}const i={id:Date.now().toString(),timestamp:new Date().toISOString(),state:e,description:t,financialSituation:o,recentPerformance:n,recommendation:this.generatePsychologyRecommendation(e,o,n)};try{if((await fetch(`${this.apiBase}/psychologyStates`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)})).ok)console.log("✅ Psychology assessment saved:",i),this.removeModal("psychology-assessment"),this.showPsychologyRecommendation(i);else throw new Error("Failed to save assessment")}catch(h){console.error("❌ Error saving assessment:",h),alert("Không thể lưu đánh giá. Hãy kiểm tra JSON Server đang chạy!")}}generatePsychologyRecommendation(e,t,o){const n={balanced:{safe:!0,riskLevel:"low",message:"Tuyệt vời! Bạn đang ở trạng thái lý tưởng để giao dịch.",advice:"Hãy duy trì sự cân bằng này và tuân thủ kế hoạch giao dịch."},greedy:{safe:!1,riskLevel:"high",message:"Cảnh báo: Tham lam có thể dẫn đến quyết định sai lầm.",advice:"Hãy bình tĩnh, giảm size lệnh và tập trung vào chất lượng thay vì số lượng."},fearful:{safe:!1,riskLevel:"medium",message:"Sợ hãi có thể khiến bạn bỏ lỡ cơ hội hoặc cắt lỗ quá sớm.",advice:"Hãy xem xét nghỉ ngơi hoặc giao dịch với size nhỏ để lấy lại tự tin."},impatient:{safe:!1,riskLevel:"high",message:"Vội vàng là kẻ thù lớn nhất của trader.",advice:"Không nên giao dịch khi thiếu kiên nhẫn. Hãy nghỉ ngơi và quay lại sau."},overconfident:{safe:!1,riskLevel:"high",message:"Quá tự tin có thể dẫn đến chủ quan và rủi ro cao.",advice:"Hãy cẩn thận hơn, giảm size lệnh và tuân thủ nghiêm ngặt risk management."},angry:{safe:!1,riskLevel:"extreme",message:"Tuyệt đối không giao dịch khi tức giận.",advice:"Hãy nghỉ ngơi hoàn toàn và chỉ quay lại khi tâm trạng đã ổn định."},stressed:{safe:!1,riskLevel:"high",message:"Căng thẳng làm giảm khả năng ra quyết định đúng đắn.",advice:"Không nên giao dịch khi có áp lực. Hãy giải quyết vấn đề gốc rễ trước."},euphoric:{safe:!1,riskLevel:"extreme",message:"Hưng phấn quá mức có thể dẫn đến mất kiểm soát.",advice:"Hãy bình tĩnh lại, nghỉ ngơi và không tăng size lệnh."}},i={stable:{multiplier:1,warning:""},slight_pressure:{multiplier:1.2,warning:"Áp lực tài chính nhẹ có thể ảnh hưởng đến quyết định."},high_pressure:{multiplier:1.5,warning:"Áp lực tài chính cao - rất nguy hiểm cho giao dịch!"},desperate:{multiplier:2,warning:"Tuyệt đối không nên giao dịch khi tuyệt vọng!"}},r={winning_streak:{multiplier:1.3,warning:"Chuỗi thắng có thể gây ra quá tự tin."},mixed_results:{multiplier:1,warning:""},losing_streak:{multiplier:1.4,warning:"Chuỗi thua có thể gây ra tâm lý revenge."},break_even:{multiplier:.9,warning:""},new_trader:{multiplier:1.1,warning:"Trader mới cần đặc biệt cẩn thận."}},s=n[e]||n.balanced,d=i[t]||i.stable,u=r[o]||r.mixed_results,h=d.multiplier*u.multiplier;let l=s.safe,g=s.riskLevel;h>1.5?(l=!1,g="extreme"):h>1.2&&(l=!1,g==="low"?g="medium":g==="medium"&&(g="high"));const b=[d.warning,u.warning].filter(m=>m);return{safe:l,riskLevel:g,message:s.message,advice:s.advice,warnings:b,riskScore:Math.round(h*100)/100,shouldTrade:l&&h<1.3,maxRiskPerTrade:l?h<1.1?"2%":"1%":"0%"}}showPsychologyRecommendation(e){const t=e.recommendation,o={low:{color:"#4caf50",bgColor:"#e8f5e8",icon:"✅",title:"An toàn để giao dịch"},medium:{color:"#ff9800",bgColor:"#fff3e0",icon:"⚠️",title:"Cần cẩn thận"},high:{color:"#f44336",bgColor:"#ffebee",icon:"🚨",title:"Rủi ro cao"},extreme:{color:"#d32f2f",bgColor:"#ffcdd2",icon:"🛑",title:"Không nên giao dịch"}},n=o[t.riskLevel]||o.medium,i=`
      <div style="text-align: center; margin-bottom: 24px;">
        <div style="
          display: inline-block;
          padding: 20px;
          border-radius: 50%;
          background: ${n.bgColor};
          margin-bottom: 16px;
        ">
          <span style="font-size: 48px;">${n.icon}</span>
        </div>
        <h3 style="margin: 0; color: ${n.color}; font-size: 20px;">
          ${n.title}
        </h3>
        <div style="margin-top: 8px;">
          <span style="
            background: ${n.color};
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
          ">
            Risk Score: ${t.riskScore}
          </span>
        </div>
      </div>

      <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h4 style="margin: 0 0 12px 0; color: #333; font-size: 16px;">📋 Đánh giá chi tiết:</h4>
        <div style="margin-bottom: 12px; color: #333;">
          <strong style="color: #333;">Trạng thái:</strong> <span style="color: #333;">${this.getStateLabel(e.state)}</span>
        </div>
        <div style="margin-bottom: 12px; color: #333;">
          <strong style="color: #333;">Tình hình tài chính:</strong> <span style="color: #333;">${this.getFinancialLabel(e.financialSituation)}</span>
        </div>
        <div style="margin-bottom: 12px; color: #333;">
          <strong style="color: #333;">Kết quả gần đây:</strong> <span style="color: #333;">${this.getPerformanceLabel(e.recentPerformance)}</span>
        </div>
        <div style="color: #333;">
          <strong style="color: #333;">Mô tả:</strong> <em style="color: #666;">"${e.description}"</em>
        </div>
      </div>

      <div style="background: ${n.bgColor}; padding: 16px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid ${n.color};">
        <h4 style="margin: 0 0 8px 0; color: ${n.color};">💡 Khuyến nghị:</h4>
        <p style="margin: 0 0 8px 0; color: #333; line-height: 1.5;">${t.message}</p>
        <p style="margin: 0; color: #666; line-height: 1.5;"><strong>Lời khuyên:</strong> ${t.advice}</p>
      </div>

      ${t.warnings.length>0?`
        <div style="background: #fff3cd; padding: 16px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #ffc107;">
          <h4 style="margin: 0 0 8px 0; color: #856404;">⚠️ Cảnh báo:</h4>
          ${t.warnings.map(s=>`<p style="margin: 0 0 4px 0; color: #856404;">• ${s}</p>`).join("")}
        </div>
      `:""}

      <div style="background: #e3f2fd; padding: 16px; border-radius: 8px; margin-bottom: 20px;">
        <h4 style="margin: 0 0 12px 0; color: #1976d2;">📊 Thông số giao dịch khuyến nghị:</h4>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; color: #333;">
          <div>
            <strong style="color: #333;">Nên giao dịch:</strong> <span style="color: #333;">${t.shouldTrade?"✅ Có":"❌ Không"}</span>
          </div>
          <div>
            <strong style="color: #333;">Risk tối đa/lệnh:</strong> <span style="color: #333;">${t.maxRiskPerTrade}</span>
          </div>
        </div>
      </div>

      <div style="font-size: 14px; color: #666; text-align: center;">
        <p style="margin: 0;">Đánh giá này dựa trên thông tin bạn cung cấp và chỉ mang tính tham khảo.</p>
      </div>
    `,r=[{text:"Đánh giá lại",onClick:()=>{this.removeModal("psychology-recommendation"),this.showPsychologyAssessment()}}];t.shouldTrade?r.push({text:"Tiếp tục giao dịch",primary:!0,onClick:()=>{this.removeModal("psychology-recommendation"),this.startManualTradingMode(),this.showSuccessMessage("✅ Bắt đầu giao dịch thủ công! Extension sẽ theo dõi và hỗ trợ bạn.")}}):r.push({text:"Nghỉ ngơi",primary:!0,onClick:async()=>{this.removeModal("psychology-recommendation"),await this.blockTradingUntilTomorrow(),this.showSuccessMessage("Quyết định khôn ngoan! Đang chuyển đến trang thiền..."),setTimeout(()=>{this.sendMessageSafely({action:"openOptions",tab:"meditation"})},2e3)}}),this.createModal("psychology-recommendation","🧠 Kết quả Đánh giá Tâm lý",i,r)}getStateLabel(e){return{balanced:"😌 Cân bằng",greedy:"🤑 Tham lam",fearful:"😰 Sợ hãi",impatient:"⚡ Vội vàng",overconfident:"😎 Tự hào",angry:"😡 Tức giận",stressed:"😵 Căng thẳng",euphoric:"🤩 Hưng phấn"}[e]||e}getFinancialLabel(e){return{stable:"✅ Ổn định",slight_pressure:"⚠️ Áp lực nhẹ",high_pressure:"🚨 Áp lực cao",desperate:"💸 Tuyệt vọng"}[e]||e}getPerformanceLabel(e){return{winning_streak:"🔥 Chuỗi thắng",mixed_results:"⚖️ Lẫn lộn",losing_streak:"📉 Chuỗi thua",break_even:"➡️ Hòa vốn",new_trader:"🆕 Trader mới"}[e]||e}async showTradingMethodSelector(){console.log("📊 Showing trading method selector..."),this.currentFlow="method";const t=(await this.loadCustomMethods()).filter(i=>this.methodSettings[i.id]!==!1),o=`
      <div style="margin-bottom: 24px;">
        <p style="color: #666; margin-bottom: 16px;">Chọn phương pháp phân tích kỹ thuật phù hợp với điều kiện thị trường hiện tại.</p>
        <div style="background: #e3f2fd; padding: 12px; border-radius: 6px; margin-bottom: 16px;">
          <small style="color: #1976d2;">💡 <strong>Lưu ý:</strong> Mỗi phương pháp có bộ câu hỏi phân tích riêng để đảm bảo setup chất lượng.</small>
        </div>
      </div>

      <div style="display: grid; gap: 16px;">
        ${this.methodSettings.bollinger_bands!==!1?`
        <div style="border: 2px solid #e0e0e0; border-radius: 8px; padding: 16px; cursor: pointer; transition: all 0.2s; background: white;"
             onclick="this.style.borderColor='#1976d2'; this.style.backgroundColor='#f3f8ff'; document.querySelectorAll('[data-method]').forEach(el => {if(el !== this) {el.style.borderColor='#e0e0e0'; el.style.backgroundColor='white';}}) ; document.getElementById('selected-method').value='bollinger_bands';"
             data-method="bollinger_bands">
          <div style="display: flex; align-items: center; margin-bottom: 8px;">
            <span style="font-size: 24px; margin-right: 12px;">📈</span>
            <h4 style="margin: 0; color: #333;">Bollinger Bands Breakout</h4>
          </div>
          <p style="margin: 0 0 8px 0; color: #666; font-size: 14px;">Giao dịch dựa trên việc giá breakout khỏi dải Bollinger Bands với volume xác nhận.</p>
          <div style="font-size: 12px; color: #888;">
            <strong>Phù hợp:</strong> Thị trường trending, volatility cao<br>
            <strong>Timeframe:</strong> 5m, 15m, 30m
          </div>
        </div>
        `:""}

        ${this.methodSettings.rsi_divergence!==!1?`
        <div style="border: 2px solid #e0e0e0; border-radius: 8px; padding: 16px; cursor: pointer; transition: all 0.2s; background: white;"
             onclick="this.style.borderColor='#1976d2'; this.style.backgroundColor='#f3f8ff'; document.querySelectorAll('[data-method]').forEach(el => {if(el !== this) {el.style.borderColor='#e0e0e0'; el.style.backgroundColor='white';}}) ; document.getElementById('selected-method').value='rsi_divergence';"
             data-method="rsi_divergence">
          <div style="display: flex; align-items: center; margin-bottom: 8px;">
            <span style="font-size: 24px; margin-right: 12px;">📊</span>
            <h4 style="margin: 0; color: #333;">RSI Divergence</h4>
          </div>
          <p style="margin: 0 0 8px 0; color: #666; font-size: 14px;">Tìm kiếm sự phân kỳ giữa giá và RSI để dự đoán sự đảo chiều xu hướng.</p>
          <div style="font-size: 12px; color: #888;">
            <strong>Phù hợp:</strong> Đỉnh/đáy thị trường, overbought/oversold<br>
            <strong>Timeframe:</strong> 15m, 30m, 1h
          </div>
        </div>
        `:""}

        ${this.methodSettings.support_resistance!==!1?`
        <div style="border: 2px solid #e0e0e0; border-radius: 8px; padding: 16px; cursor: pointer; transition: all 0.2s; background: white;"
             onclick="this.style.borderColor='#1976d2'; this.style.backgroundColor='#f3f8ff'; document.querySelectorAll('[data-method]').forEach(el => {if(el !== this) {el.style.borderColor='#e0e0e0'; el.style.backgroundColor='white';}}) ; document.getElementById('selected-method').value='support_resistance';"
             data-method="support_resistance">
          <div style="display: flex; align-items: center; margin-bottom: 8px;">
            <span style="font-size: 24px; margin-right: 12px;">🔄</span>
            <h4 style="margin: 0; color: #333;">Support & Resistance</h4>
          </div>
          <p style="margin: 0 0 8px 0; color: #666; font-size: 14px;">Giao dịch tại các vùng support/resistance quan trọng với xác nhận price action.</p>
          <div style="font-size: 12px; color: #888;">
            <strong>Phù hợp:</strong> Thị trường sideway, range-bound<br>
            <strong>Timeframe:</strong> 5m, 15m, 30m, 1h
          </div>
        </div>
        `:""}

        ${this.methodSettings.moving_average!==!1?`
        <div style="border: 2px solid #e0e0e0; border-radius: 8px; padding: 16px; cursor: pointer; transition: all 0.2s; background: white;"
             onclick="this.style.borderColor='#1976d2'; this.style.backgroundColor='#f3f8ff'; document.querySelectorAll('[data-method]').forEach(el => {if(el !== this) {el.style.borderColor='#e0e0e0'; el.style.backgroundColor='white';}}) ; document.getElementById('selected-method').value='moving_average';"
             data-method="moving_average">
          <div style="display: flex; align-items: center; margin-bottom: 8px;">
            <span style="font-size: 24px; margin-right: 12px;">📉</span>
            <h4 style="margin: 0; color: #333;">Moving Average Crossover</h4>
          </div>
          <p style="margin: 0 0 8px 0; color: #666; font-size: 14px;">Sử dụng sự cắt nhau của các đường MA để xác định xu hướng và điểm vào lệnh.</p>
          <div style="font-size: 12px; color: #888;">
            <strong>Phù hợp:</strong> Thị trường trending mạnh<br>
            <strong>Timeframe:</strong> 15m, 30m, 1h
          </div>
        </div>
        `:""}

        ${this.methodSettings.price_action!==!1?`
        <div style="border: 2px solid #e0e0e0; border-radius: 8px; padding: 16px; cursor: pointer; transition: all 0.2s; background: white;"
             onclick="this.style.borderColor='#1976d2'; this.style.backgroundColor='#f3f8ff'; document.querySelectorAll('[data-method]').forEach(el => {if(el !== this) {el.style.borderColor='#e0e0e0'; el.style.backgroundColor='white';}}) ; document.getElementById('selected-method').value='price_action';"
             data-method="price_action">
          <div style="display: flex; align-items: center; margin-bottom: 8px;">
            <span style="font-size: 24px; margin-right: 12px;">🕯️</span>
            <h4 style="margin: 0; color: #333;">Price Action Patterns</h4>
          </div>
          <p style="margin: 0 0 8px 0; color: #666; font-size: 14px;">Phân tích các mô hình nến Nhật và price action để dự đoán hướng di chuyển.</p>
          <div style="font-size: 12px; color: #888;">
            <strong>Phù hợp:</strong> Mọi điều kiện thị trường<br>
            <strong>Timeframe:</strong> 1m, 5m, 15m, 30m
          </div>
        </div>
        `:""}

        ${t.length>0?`
          <div style="margin: 20px 0; padding: 16px; background: #e8f5e8; border-radius: 8px;">
            <h4 style="margin: 0 0 12px 0; color: #2e7d32;">🎯 Phương pháp tùy chỉnh của bạn:</h4>
            <div style="display: grid; gap: 12px;">
              ${t.map(i=>`
                <div style="border: 2px solid #4caf50; border-radius: 8px; padding: 12px; cursor: pointer; transition: all 0.2s; background: white;"
                     onclick="this.style.borderColor='#1976d2'; this.style.backgroundColor='#f3f8ff'; document.querySelectorAll('[data-method]').forEach(el => {el.style.borderColor='#e0e0e0'; el.style.backgroundColor='white';}); document.getElementById('selected-method').value='${i.id}';"
                     data-method="${i.id}">
                  <div style="display: flex; align-items: center; margin-bottom: 6px;">
                    <span style="font-size: 20px; margin-right: 8px;">${i.icon}</span>
                    <h5 style="margin: 0; color: #333;">${i.name}</h5>
                    <span style="margin-left: auto; background: #4caf50; color: white; padding: 2px 6px; border-radius: 10px; font-size: 10px;">CUSTOM</span>
                  </div>
                  <p style="margin: 0; color: #666; font-size: 13px;">${i.description}</p>
                  <div style="font-size: 11px; color: #888; margin-top: 4px;">
                    <strong>Câu hỏi:</strong> ${i.questions.length} | <strong>Điểm tối đa:</strong> ${i.totalMaxScore}
                  </div>
                </div>
              `).join("")}
            </div>
          </div>
        `:""}
      </div>

      <input type="hidden" id="selected-method" value="">

      <div style="margin-top: 20px; padding: 16px; background: #fff3cd; border-radius: 6px; border-left: 4px solid #ffc107;">
        <small style="color: #856404;">
          <strong>⚠️ Quan trọng:</strong> Hãy chọn phương pháp mà bạn đã học và hiểu rõ.
          Mỗi phương pháp sẽ có bộ câu hỏi phân tích để đảm bảo setup chất lượng trước khi vào lệnh.
        </small>
      </div>
    `,n=[{text:"Quay lại",onClick:()=>{this.removeModal("trading-method-selector"),this.showPsychologyAssessment()}},{text:"Tiếp tục phân tích",primary:!0,onClick:()=>this.processTradingMethodSelection()}];this.createModal("trading-method-selector","📊 Chọn Phương pháp Giao dịch",o,n)}async processTradingMethodSelection(){var t;const e=(t=document.getElementById("selected-method"))==null?void 0:t.value;if(!e){alert("Vui lòng chọn một phương pháp giao dịch!");return}console.log("✅ Trading method selected:",e),this.removeModal("trading-method-selector"),e==="custom"?(this.removeModal("trading-method-selector"),this.showSuccessMessage("Vui lòng vào Options để tạo phương pháp tùy chỉnh!")):await this.showTradingAnalysis(e)}async showTradingAnalysis(e,t){console.log("🔍 Showing trading analysis for method:",e),this.currentFlow="analysis";const o=t||await this.getTradingMethodData(e),n=`
      <div style="margin-bottom: 24px;">
        <div style="display: flex; align-items: center; margin-bottom: 16px;">
          <span style="font-size: 32px; margin-right: 12px;">${o.icon}</span>
          <div>
            <h3 style="margin: 0; color: #333;">${o.name}</h3>
            <p style="margin: 4px 0 0 0; color: #666; font-size: 14px;">${o.description}</p>
          </div>
        </div>

        <div style="background: #e8f5e8; padding: 12px; border-radius: 6px; margin-bottom: 16px;">
          <small style="color: #2e7d32;">
            <strong>📋 Hướng dẫn:</strong> Trả lời tất cả câu hỏi dựa trên chart hiện tại.
            Hệ thống sẽ tính điểm và đưa ra khuyến nghị có nên vào lệnh hay không.
          </small>
        </div>
      </div>

      <div style="display: grid; gap: 16px;">
        ${o.questions.map((r,s)=>`
          <div style="border: 1px solid #e0e0e0; border-radius: 6px; padding: 16px; background: white;">
            <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #333;">
              ${s+1}. ${r.text}
            </label>

            ${r.type==="select"?`
              <select id="question-${s}" style="
                width: 100%;
                padding: 10px;
                border: 2px solid #e0e0e0;
                border-radius: 4px;
                font-size: 14px;
                background: white;
                color: #333;
                box-sizing: border-box;
              ">
                <option value="" style="color: #999;">Chọn đáp án...</option>
                ${r.options.map(d=>`
                  <option value="${d.value}" style="color: #333;">${d.label}</option>
                `).join("")}
              </select>
            `:r.type==="radio"?`
              <div style="display: grid; gap: 8px;">
                ${r.options.map(d=>`
                  <label style="display: flex; align-items: center; cursor: pointer; padding: 8px; border-radius: 4px; transition: background-color 0.2s;"
                         onmouseover="this.style.backgroundColor='#f5f5f5'"
                         onmouseout="this.style.backgroundColor='transparent'">
                    <input type="radio" name="question-${s}" value="${d.value}" style="margin-right: 8px;">
                    <span style="font-size: 14px; color: #333;">${d.label}</span>
                  </label>
                `).join("")}
              </div>
            `:`
              <textarea id="question-${s}" placeholder="${r.placeholder||"Nhập mô tả chi tiết..."}" style="
                width: 100%;
                padding: 10px;
                border: 2px solid #e0e0e0;
                border-radius: 4px;
                font-size: 14px;
                color: #333;
                background: white;
                min-height: 80px;
                resize: vertical;
                box-sizing: border-box;
              "></textarea>
            `}

            ${r.hint?`
              <small style="color: #666; margin-top: 4px; display: block;">
                💡 ${r.hint}
              </small>
            `:""}
          </div>
        `).join("")}
      </div>

      <div style="margin-top: 20px; padding: 16px; background: #e3f2fd; border-radius: 6px;">
        <h4 style="margin: 0 0 8px 0; color: #1976d2;">📊 Hệ thống chấm điểm:</h4>
        <div style="font-size: 14px; color: #1976d2;">
          • <strong>80-100%:</strong> Setup xuất sắc - Nên vào lệnh<br>
          • <strong>60-79%:</strong> Setup tốt - Có thể cân nhắc<br>
          • <strong>40-59%:</strong> Setup trung bình - Cần cẩn thận<br>
          • <strong>0-39%:</strong> Setup yếu - Không nên vào lệnh
        </div>
      </div>
    `,i=[{text:"Chọn lại phương pháp",onClick:()=>{this.removeModal("trading-analysis"),this.showTradingMethodSelector()}},{text:"Phân tích kết quả",primary:!0,onClick:()=>this.processTradingAnalysis(e,o)}];this.createModal("trading-analysis","🔍 Phân tích Thị trường",n,i)}async getTradingMethodData(e){if(e.startsWith("custom_")){const o=await this.getCustomMethod(e);if(o)return o}const t={bollinger_bands:{name:"Bollinger Bands Breakout",icon:"📈",description:"Giao dịch dựa trên việc giá breakout khỏi dải Bollinger Bands với volume xác nhận",questions:[{text:"Giá có breakout rõ ràng khỏi Upper hoặc Lower Bollinger Band không?",type:"radio",options:[{value:"strong_breakout",label:"✅ Có - Breakout mạnh với nến đóng ngoài band",weight:25},{value:"weak_breakout",label:"⚠️ Có - Nhưng breakout yếu, chỉ chạm band",weight:10},{value:"no_breakout",label:"❌ Không - Giá vẫn trong dải bands",weight:0}]},{text:"Volume tại thời điểm breakout như thế nào?",type:"radio",options:[{value:"high_volume",label:"🔥 Volume cao hơn trung bình rõ rệt",weight:20},{value:"normal_volume",label:"📊 Volume bình thường",weight:10},{value:"low_volume",label:"📉 Volume thấp hơn trung bình",weight:0}]},{text:"Bollinger Bands đang trong trạng thái nào?",type:"radio",options:[{value:"expanding",label:"📈 Đang mở rộng (volatility tăng)",weight:20},{value:"stable",label:"➡️ Ổn định",weight:10},{value:"contracting",label:"📉 Đang thu hẹp (volatility giảm)",weight:5}]},{text:"RSI hiện tại đang ở vùng nào?",type:"select",options:[{value:"overbought",label:"Overbought (>70) - Phù hợp cho short",weight:15},{value:"oversold",label:"Oversold (<30) - Phù hợp cho long",weight:15},{value:"neutral",label:"Neutral (30-70) - Trung tính",weight:10},{value:"extreme",label:"Extreme (>80 hoặc <20) - Cần cẩn thận",weight:5}]},{text:"Mô tả setup hiện tại và lý do vào lệnh:",type:"textarea",placeholder:"Ví dụ: Giá breakout mạnh khỏi upper band với volume cao, RSI chưa overbought, xu hướng tăng rõ ràng...",hint:"Mô tả chi tiết giúp đánh giá chất lượng setup"}]},rsi_divergence:{name:"RSI Divergence",icon:"📊",description:"Tìm kiếm sự phân kỳ giữa giá và RSI để dự đoán sự đảo chiều xu hướng",questions:[{text:"Có xuất hiện divergence rõ ràng giữa giá và RSI không?",type:"radio",options:[{value:"strong_divergence",label:"✅ Có - Divergence rõ ràng qua nhiều đỉnh/đáy",weight:30},{value:"weak_divergence",label:"⚠️ Có - Nhưng divergence yếu",weight:15},{value:"no_divergence",label:"❌ Không có divergence",weight:0}]},{text:"Loại divergence nào đang xuất hiện?",type:"radio",options:[{value:"bearish",label:"🔴 Bearish - Giá cao hơn, RSI thấp hơn",weight:20},{value:"bullish",label:"🟢 Bullish - Giá thấp hơn, RSI cao hơn",weight:20},{value:"hidden_bearish",label:"🟠 Hidden Bearish - Trong downtrend",weight:15},{value:"hidden_bullish",label:"🟡 Hidden Bullish - Trong uptrend",weight:15}]},{text:"RSI hiện tại ở vùng nào?",type:"select",options:[{value:"extreme_overbought",label:"Extreme Overbought (>80)",weight:20},{value:"overbought",label:"Overbought (70-80)",weight:15},{value:"oversold",label:"Oversold (20-30)",weight:15},{value:"extreme_oversold",label:"Extreme Oversold (<20)",weight:20},{value:"neutral",label:"Neutral (30-70)",weight:5}]},{text:"Có tín hiệu xác nhận nào khác không?",type:"radio",options:[{value:"multiple_confirmations",label:"✅ Có nhiều tín hiệu xác nhận (MA, support/resistance, etc.)",weight:15},{value:"some_confirmations",label:"⚠️ Có một vài tín hiệu xác nhận",weight:10},{value:"no_confirmations",label:"❌ Không có tín hiệu xác nhận khác",weight:0}]},{text:"Mô tả chi tiết về divergence và setup:",type:"textarea",placeholder:"Ví dụ: Bearish divergence rõ ràng qua 3 đỉnh, RSI ở vùng overbought, có resistance mạnh...",hint:"Mô tả cụ thể về các đỉnh/đáy tạo divergence"}]},support_resistance:{name:"Support & Resistance",icon:"🔄",description:"Giao dịch tại các vùng support/resistance quan trọng với xác nhận price action",questions:[{text:"Vùng support/resistance hiện tại có mạnh không?",type:"radio",options:[{value:"very_strong",label:"🔥 Rất mạnh - Đã test nhiều lần, có volume cao",weight:25},{value:"strong",label:"✅ Mạnh - Đã test vài lần, rõ ràng",weight:20},{value:"moderate",label:"⚠️ Trung bình - Chưa test nhiều",weight:10},{value:"weak",label:"❌ Yếu - Mới hình thành hoặc không rõ ràng",weight:0}]},{text:"Giá đang ở vị trí nào so với vùng S/R?",type:"radio",options:[{value:"at_support",label:"🟢 Tại support - Chuẩn bị bounce",weight:20},{value:"at_resistance",label:"🔴 Tại resistance - Chuẩn bị reject",weight:20},{value:"near_sr",label:"⚠️ Gần vùng S/R - Chưa chạm",weight:10},{value:"far_from_sr",label:"❌ Xa vùng S/R - Setup không hợp lệ",weight:0}]},{text:"Có tín hiệu price action xác nhận không?",type:"radio",options:[{value:"strong_rejection",label:"✅ Có - Rejection mạnh với nến đảo chiều rõ ràng",weight:20},{value:"weak_rejection",label:"⚠️ Có - Rejection yếu",weight:10},{value:"breakout",label:"🚀 Breakout - Giá vượt qua S/R",weight:15},{value:"no_signal",label:"❌ Không có tín hiệu rõ ràng",weight:0}]},{text:"Volume tại vùng S/R như thế nào?",type:"select",options:[{value:"high_volume",label:"Volume cao - Xác nhận mạnh",weight:15},{value:"normal_volume",label:"Volume bình thường",weight:10},{value:"low_volume",label:"Volume thấp - Thiếu conviction",weight:5}]},{text:"Mô tả vùng S/R và setup hiện tại:",type:"textarea",placeholder:"Ví dụ: Resistance mạnh tại 1.2500 đã test 3 lần, xuất hiện doji rejection với volume cao...",hint:"Mô tả cụ thể về vùng S/R và các tín hiệu price action"}]},moving_average:{name:"Moving Average Crossover",icon:"📉",description:"Sử dụng sự cắt nhau của các đường MA để xác định xu hướng và điểm vào lệnh",questions:[{text:"Có xuất hiện crossover giữa các đường MA không?",type:"radio",options:[{value:"golden_cross",label:"🟡 Golden Cross - MA ngắn cắt lên MA dài",weight:25},{value:"death_cross",label:"💀 Death Cross - MA ngắn cắt xuống MA dài",weight:25},{value:"approaching",label:"⚠️ Đang tiến gần crossover",weight:10},{value:"no_cross",label:"❌ Không có crossover",weight:0}]},{text:"Xu hướng tổng thể như thế nào?",type:"radio",options:[{value:"strong_uptrend",label:"📈 Uptrend mạnh - Tất cả MA hướng lên",weight:20},{value:"strong_downtrend",label:"📉 Downtrend mạnh - Tất cả MA hướng xuống",weight:20},{value:"weak_trend",label:"⚠️ Xu hướng yếu - MA không rõ ràng",weight:10},{value:"sideways",label:"➡️ Sideway - MA nằm ngang",weight:5}]},{text:"Giá hiện tại so với các đường MA?",type:"radio",options:[{value:"above_all",label:"⬆️ Trên tất cả MA - Bullish",weight:15},{value:"below_all",label:"⬇️ Dưới tất cả MA - Bearish",weight:15},{value:"between_ma",label:"🔄 Giữa các MA - Không rõ ràng",weight:5},{value:"at_ma",label:"📍 Tại đường MA - Test support/resistance",weight:10}]},{text:"Slope (độ dốc) của các đường MA?",type:"select",options:[{value:"steep_up",label:"Dốc lên mạnh - Momentum tốt",weight:15},{value:"gentle_up",label:"Dốc lên nhẹ - Momentum yếu",weight:10},{value:"flat",label:"Nằm ngang - Không có momentum",weight:5},{value:"gentle_down",label:"Dốc xuống nhẹ - Momentum yếu",weight:10},{value:"steep_down",label:"Dốc xuống mạnh - Momentum tốt",weight:15}]},{text:"Mô tả setup MA và lý do vào lệnh:",type:"textarea",placeholder:"Ví dụ: Golden cross vừa xảy ra, giá trên tất cả MA, slope dốc lên mạnh, xu hướng tăng rõ ràng...",hint:"Mô tả cụ thể về crossover và xu hướng"}]},price_action:{name:"Price Action Patterns",icon:"🕯️",description:"Phân tích các mô hình nến Nhật và price action để dự đoán hướng di chuyển",questions:[{text:"Có xuất hiện pattern nến Nhật nào không?",type:"radio",options:[{value:"strong_reversal",label:"🔄 Reversal pattern mạnh (Hammer, Doji, Engulfing)",weight:25},{value:"continuation",label:"➡️ Continuation pattern (Flag, Pennant)",weight:20},{value:"weak_pattern",label:"⚠️ Pattern yếu hoặc không rõ ràng",weight:10},{value:"no_pattern",label:"❌ Không có pattern đặc biệt",weight:0}]},{text:"Cấu trúc thị trường hiện tại?",type:"radio",options:[{value:"higher_highs_lows",label:"📈 Higher Highs & Higher Lows - Uptrend",weight:20},{value:"lower_highs_lows",label:"📉 Lower Highs & Lower Lows - Downtrend",weight:20},{value:"range_bound",label:"🔄 Range-bound - Sideway",weight:10},{value:"unclear",label:"❓ Cấu trúc không rõ ràng",weight:0}]},{text:"Có breakout khỏi cấu trúc quan trọng không?",type:"radio",options:[{value:"strong_breakout",label:"🚀 Breakout mạnh với volume cao",weight:20},{value:"weak_breakout",label:"⚠️ Breakout yếu hoặc false breakout",weight:5},{value:"no_breakout",label:"❌ Không có breakout",weight:10}]},{text:"Momentum hiện tại như thế nào?",type:"select",options:[{value:"strong_momentum",label:"Momentum mạnh - Nến liên tiếp cùng hướng",weight:15},{value:"weak_momentum",label:"Momentum yếu - Nến nhỏ, indecision",weight:5},{value:"no_momentum",label:"Không có momentum rõ ràng",weight:0}]},{text:"Mô tả pattern và setup price action:",type:"textarea",placeholder:"Ví dụ: Hammer xuất hiện tại support, breakout khỏi range với momentum mạnh, cấu trúc uptrend...",hint:"Mô tả cụ thể về pattern nến và cấu trúc thị trường"}]}};return t[e]||t.bollinger_bands}async processTradingAnalysis(e,t){var l;const o=[];let n=0,i=0,r="";for(let g=0;g<t.questions.length;g++){const b=t.questions[g];let m="",p=0;if(b.type==="radio"){const c=document.querySelector(`input[name="question-${g}"]:checked`);if(c){m=c.value;const a=b.options.find(y=>y.value===m);p=a?a.weight:0}}else if(b.type==="select"){const c=document.getElementById(`question-${g}`);if(c&&c.value){m=c.value;const a=b.options.find(y=>y.value===m);p=a?a.weight:0}}else if(b.type==="textarea"){const c=document.getElementById(`question-${g}`);c&&(m=c.value,r=m,p=m.length>20?10:0)}o.push({question:b.text,answer:m,score:p}),n+=p,i+=Math.max(...((l=b.options)==null?void 0:l.map(c=>c.weight))||[10])}const s=o.filter(g=>!g.answer).length;if(s>0){alert(`Vui lòng trả lời tất cả ${s} câu hỏi còn lại!`);return}const d=Math.round(n/i*100),u=this.generateTradingRecommendation(d,e),h={id:Date.now().toString(),timestamp:new Date().toISOString(),method:e,methodName:t.name,answers:o,totalScore:n,maxScore:i,percentage:d,recommendation:u,description:r};try{if((await fetch(`${this.apiBase}/tradingSessions`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(h)})).ok)console.log("✅ Trading analysis saved:",h),this.removeModal("trading-analysis"),this.showAnalysisResults(h);else throw new Error("Failed to save analysis")}catch(g){console.error("❌ Error saving analysis:",g),this.removeModal("trading-analysis"),this.showAnalysisResults(h)}}generateTradingRecommendation(e,t){let o={shouldTrade:!1,riskLevel:"high",confidence:"low",message:"",advice:"",maxRisk:"0%"};return e>=80?o={shouldTrade:!0,riskLevel:"low",confidence:"high",message:"Setup xuất sắc! Tất cả điều kiện đều thuận lợi.",advice:"Đây là cơ hội tốt để vào lệnh. Hãy tuân thủ risk management và theo dõi chặt chẽ.",maxRisk:"2%"}:e>=60?o={shouldTrade:!0,riskLevel:"medium",confidence:"medium",message:"Setup tốt với một số điều kiện thuận lợi.",advice:"Có thể cân nhắc vào lệnh nhưng cần giảm size và cẩn thận hơn.",maxRisk:"1%"}:e>=40?o={shouldTrade:!1,riskLevel:"high",confidence:"low",message:"Setup trung bình, nhiều yếu tố chưa thuận lợi.",advice:"Nên chờ setup tốt hơn. Nếu vẫn muốn vào lệnh, hãy giảm size xuống mức tối thiểu.",maxRisk:"0.5%"}:o={shouldTrade:!1,riskLevel:"extreme",confidence:"very_low",message:"Setup yếu, không đủ điều kiện để giao dịch.",advice:"Tuyệt đối không nên vào lệnh. Hãy chờ cơ hội tốt hơn hoặc học thêm về phương pháp này.",maxRisk:"0%"},o}showAnalysisResults(e){const t=e.recommendation,n=(s=>s>=80?{color:"#4caf50",bgColor:"#e8f5e8",icon:"🎯"}:s>=60?{color:"#ff9800",bgColor:"#fff3e0",icon:"⚠️"}:s>=40?{color:"#f44336",bgColor:"#ffebee",icon:"🚨"}:{color:"#d32f2f",bgColor:"#ffcdd2",icon:"🛑"})(e.percentage),i=`
      <div style="text-align: center; margin-bottom: 24px;">
        <div style="
          display: inline-block;
          padding: 20px;
          border-radius: 50%;
          background: ${n.bgColor};
          margin-bottom: 16px;
        ">
          <span style="font-size: 48px;">${n.icon}</span>
        </div>
        <h3 style="margin: 0; color: ${n.color}; font-size: 24px;">
          ${e.percentage}%
        </h3>
        <p style="margin: 8px 0 0 0; color: #666;">
          ${e.totalScore}/${e.maxScore} điểm
        </p>
      </div>

      <div style="background: ${n.bgColor}; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid ${n.color};">
        <h4 style="margin: 0 0 8px 0; color: ${n.color};">📋 Đánh giá Setup:</h4>
        <p style="margin: 0 0 8px 0; color: #333; line-height: 1.5;">${t.message}</p>
        <p style="margin: 0; color: #666; line-height: 1.5;"><strong>Khuyến nghị:</strong> ${t.advice}</p>
      </div>

      <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h4 style="margin: 0 0 12px 0; color: #333;">📊 Chi tiết phân tích:</h4>
        <div style="margin-bottom: 12px; color: #333;">
          <strong style="color: #333;">Phương pháp:</strong> <span style="color: #333;">${e.methodName}</span>
        </div>
        <div style="margin-bottom: 12px; color: #333;">
          <strong style="color: #333;">Thời gian:</strong> <span style="color: #333;">${new Date(e.timestamp).toLocaleString("vi-VN")}</span>
        </div>
        ${e.description?`
          <div style="margin-bottom: 12px; color: #333;">
            <strong style="color: #333;">Mô tả setup:</strong><br>
            <em style="color: #666;">"${e.description}"</em>
          </div>
        `:""}
      </div>

      <div style="background: #e3f2fd; padding: 16px; border-radius: 8px; margin-bottom: 20px;">
        <h4 style="margin: 0 0 12px 0; color: #1976d2;">🎯 Thông số giao dịch:</h4>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; font-size: 14px; color: #333;">
          <div>
            <strong style="color: #333;">Nên giao dịch:</strong> <span style="color: #333;">${t.shouldTrade?"✅ Có":"❌ Không"}</span>
          </div>
          <div>
            <strong style="color: #333;">Risk tối đa:</strong> <span style="color: #333;">${t.maxRisk}</span>
          </div>
          <div>
            <strong style="color: #333;">Confidence:</strong> <span style="color: #333;">${t.confidence}</span>
          </div>
          <div>
            <strong style="color: #333;">Risk Level:</strong> <span style="color: #333;">${t.riskLevel}</span>
          </div>
        </div>
      </div>

      <div style="background: #fff3cd; padding: 16px; border-radius: 8px; margin-bottom: 20px;">
        <h4 style="margin: 0 0 8px 0; color: #856404;">💡 Lưu ý quan trọng:</h4>
        <ul style="margin: 0; padding-left: 20px; color: #856404; font-size: 14px;">
          <li>Kết quả này chỉ mang tính tham khảo</li>
          <li>Luôn tuân thủ risk management</li>
          <li>Theo dõi thị trường liên tục</li>
          <li>Có kế hoạch exit rõ ràng</li>
        </ul>
      </div>
    `,r=[{text:"Phân tích lại",onClick:()=>{this.removeModal("analysis-results"),this.showTradingMethodSelector()}}];t.shouldTrade?r.push({text:"Tiếp tục giao dịch",primary:!0,onClick:()=>{this.removeModal("analysis-results"),this.showTradingInterface(e)}}):r.push({text:"Chờ cơ hội khác",primary:!0,onClick:()=>{this.removeModal("analysis-results"),this.showSuccessMessage("Quyết định khôn ngoan! Hãy chờ setup tốt hơn.")}}),this.createModal("analysis-results","📊 Kết quả Phân tích",i,r)}showTradingInterface(e){console.log("💹 Showing trading interface..."),this.currentFlow="trading";const t=`
      <div style="text-align: center; padding: 40px 20px;">
        <div style="font-size: 48px; margin-bottom: 16px;">💹</div>
        <h3 style="margin: 0 0 16px 0; color: #333;">Giao diện Giao dịch</h3>
        <p style="color: #666; margin-bottom: 20px;">Tính năng này đang được phát triển...</p>
        <p style="color: #666; font-size: 14px;">Sẽ có giao diện đặt lệnh, quản lý risk và theo dõi thống kê real-time.</p>
        <div style="background: #e8f5e8; padding: 12px; border-radius: 6px; margin-top: 16px;">
          <small style="color: #2e7d32;">✅ <strong>Setup đã được phê duyệt:</strong> ${e.percentage}% - ${e.recommendation.message}</small>
        </div>
      </div>
    `,o=[{text:"Quay lại",onClick:()=>{this.removeModal("trading-interface"),this.showAnalysisResults(e)}},{text:"Hoàn thành",primary:!0,onClick:()=>{this.removeModal("trading-interface"),this.showSuccessMessage("Cảm ơn bạn đã test Trading Method Selector! Tính năng Trading Interface sẽ sớm được phát triển.")}}];this.createModal("trading-interface","💹 Giao diện Giao dịch",t,o)}showCustomMethodCreator(){console.log("⚙️ Showing custom method creator...");const e=`
      <div style="margin-bottom: 24px;">
        <p style="color: #666; margin-bottom: 16px;">Tạo phương pháp giao dịch tùy chỉnh với bộ câu hỏi phân tích riêng phù hợp với strategy của bạn.</p>
        <div style="background: #e8f5e8; padding: 12px; border-radius: 6px; margin-bottom: 16px;">
          <small style="color: #2e7d32;">💡 <strong>Hướng dẫn:</strong> Tạo 3-7 câu hỏi để đánh giá setup giao dịch. Mỗi câu hỏi sẽ có trọng số điểm để tính toán khuyến nghị.</small>
        </div>
      </div>

      <div style="display: grid; gap: 16px;">
        <div>
          <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #333;">📊 Tên phương pháp</label>
          <input type="text" id="method-name" placeholder="Ví dụ: Fibonacci Retracement Strategy" style="
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            color: #333;
            background: white;
            box-sizing: border-box;
            transition: border-color 0.2s;
          " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'">
        </div>

        <div>
          <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #333;">📝 Mô tả phương pháp</label>
          <textarea id="method-description" placeholder="Mô tả ngắn gọn về phương pháp giao dịch này..." style="
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            color: #333;
            background: white;
            min-height: 80px;
            resize: vertical;
            box-sizing: border-box;
            transition: border-color 0.2s;
          " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'"></textarea>
        </div>

        <div>
          <label style="display: block; margin-bottom: 6px; font-weight: 500; color: #333;">🎯 Icon (emoji)</label>
          <input type="text" id="method-icon" placeholder="📈" maxlength="2" style="
            width: 100px;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 20px;
            color: #333;
            background: white;
            text-align: center;
            box-sizing: border-box;
            transition: border-color 0.2s;
          " onfocus="this.style.borderColor='#1976d2'" onblur="this.style.borderColor='#e0e0e0'">
          <small style="color: #666; margin-left: 8px;">Chọn 1 emoji đại diện</small>
        </div>
      </div>

      <div style="margin: 24px 0;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
          <h4 style="margin: 0; color: #333;">❓ Câu hỏi phân tích (tối thiểu 3 câu)</h4>
          <button onclick="window.addCustomQuestion()" style="
            background: #1976d2;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
          " onmouseover="this.style.backgroundColor='#1565c0'" onmouseout="this.style.backgroundColor='#1976d2'">
            + Thêm câu hỏi
          </button>
        </div>

        <div id="custom-questions-container">
          <!-- Questions will be added here dynamically -->
        </div>
      </div>

      <div style="background: #fff3cd; padding: 16px; border-radius: 6px; border-left: 4px solid #ffc107;">
        <h4 style="margin: 0 0 8px 0; color: #856404;">💡 Lưu ý quan trọng:</h4>
        <ul style="margin: 0; padding-left: 20px; color: #856404; font-size: 14px;">
          <li>Mỗi câu hỏi nên tập trung vào 1 khía cạnh cụ thể của setup</li>
          <li>Trọng số cao (20-30) cho yếu tố quan trọng nhất</li>
          <li>Trọng số thấp (5-10) cho yếu tố phụ trợ</li>
          <li>Tổng trọng số nên từ 80-120 điểm</li>
        </ul>
      </div>
    `,t=[{text:"Quay lại",onClick:()=>{this.removeModal("custom-method-creator"),this.showTradingMethodSelector()}},{text:"Tạo phương pháp",primary:!0,onClick:()=>this.saveCustomMethod()}];this.createModal("custom-method-creator","⚙️ Tạo Phương pháp Tùy chỉnh",e,t),setTimeout(()=>{this.initializeCustomQuestions()},100)}initializeCustomQuestions(){window.addCustomQuestion=()=>this.addCustomQuestion(),window.removeCustomQuestion=e=>this.removeCustomQuestion(e);for(let e=0;e<3;e++)this.addCustomQuestion()}addCustomQuestion(){const e=document.getElementById("custom-questions-container");if(!e)return;const t=e.children.length,o=document.createElement("div");o.style.cssText=`
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      padding: 16px;
      margin-bottom: 16px;
      background: white;
    `,o.innerHTML=`
      <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 12px;">
        <h5 style="margin: 0; color: #333;">Câu hỏi ${t+1}</h5>
        ${t>=3?`
          <button onclick="window.removeCustomQuestion(${t})" style="
            background: #f44336;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-left: auto;
          ">Xóa</button>
        `:""}
      </div>

      <div style="display: grid; gap: 12px;">
        <div>
          <label style="display: block; margin-bottom: 4px; font-weight: 500; color: #333; font-size: 14px;">Nội dung câu hỏi:</label>
          <input type="text" id="question-text-${t}" placeholder="Ví dụ: Có tín hiệu divergence rõ ràng không?" style="
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            color: #333;
            background: white;
            box-sizing: border-box;
          ">
        </div>

        <div style="display: grid; grid-template-columns: 1fr auto; gap: 12px; align-items: end;">
          <div>
            <label style="display: block; margin-bottom: 4px; font-weight: 500; color: #333; font-size: 14px;">Loại câu hỏi:</label>
            <select id="question-type-${t}" onchange="window.updateQuestionOptions(${t})" style="
              width: 100%;
              padding: 10px;
              border: 1px solid #ddd;
              border-radius: 4px;
              font-size: 14px;
              color: #333;
              background: white;
            ">
              <option value="radio">Multiple Choice (Radio)</option>
              <option value="select">Dropdown (Select)</option>
              <option value="textarea">Mô tả chi tiết (Textarea)</option>
            </select>
          </div>

          <button onclick="window.addQuestionOption(${t})" id="add-option-btn-${t}" style="
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            white-space: nowrap;
          ">+ Thêm đáp án</button>
        </div>

        <div id="question-options-${t}">
          <!-- Options will be added here -->
        </div>
      </div>
    `,e.appendChild(o),window.updateQuestionOptions=n=>this.updateQuestionOptions(n),window.addQuestionOption=n=>this.addQuestionOption(n),window.removeQuestionOption=(n,i)=>this.removeQuestionOption(n,i),this.addQuestionOption(t),this.addQuestionOption(t)}removeCustomQuestion(e){const t=document.getElementById("custom-questions-container");!t||t.children.length<=3||t.children[e]&&(t.removeChild(t.children[e]),Array.from(t.children).forEach((o,n)=>{const i=o.querySelector("h5");i&&(i.textContent=`Câu hỏi ${n+1}`)}))}updateQuestionOptions(e){const t=document.getElementById(`question-type-${e}`),o=document.getElementById(`question-options-${e}`),n=document.getElementById(`add-option-btn-${e}`);!t||!o||!n||(t.value==="textarea"?(o.innerHTML="",n.style.display="none"):(n.style.display="block",o.children.length===0&&(this.addQuestionOption(e),this.addQuestionOption(e))))}addQuestionOption(e){const t=document.getElementById(`question-options-${e}`);if(!t)return;const o=t.children.length,n=document.createElement("div");n.style.cssText=`
      display: grid;
      grid-template-columns: 1fr auto auto;
      gap: 8px;
      align-items: center;
      margin-bottom: 8px;
      padding: 8px;
      background: #f8f9fa;
      border-radius: 4px;
    `,n.innerHTML=`
      <input type="text" id="option-text-${e}-${o}" placeholder="Nội dung đáp án..." style="
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        color: #333;
        background: white;
      ">
      <input type="number" id="option-weight-${e}-${o}" placeholder="Điểm" min="0" max="50" value="10" style="
        width: 60px;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        color: #333;
        background: white;
        text-align: center;
      ">
      ${o>=2?`
        <button onclick="window.removeQuestionOption(${e}, ${o})" style="
          background: #f44336;
          color: white;
          border: none;
          padding: 6px 8px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
        ">×</button>
      `:"<div></div>"}
    `,t.appendChild(n)}removeQuestionOption(e,t){const o=document.getElementById(`question-options-${e}`);!o||o.children.length<=2||o.children[t]&&o.removeChild(o.children[t])}async saveCustomMethod(){var d,u,h,l,g,b,m;const e=(d=document.getElementById("method-name"))==null?void 0:d.value,t=(u=document.getElementById("method-description"))==null?void 0:u.value,o=(h=document.getElementById("method-icon"))==null?void 0:h.value;if(!e||e.length<3){alert("Vui lòng nhập tên phương pháp (ít nhất 3 ký tự)!");return}if(!t||t.length<10){alert("Vui lòng nhập mô tả phương pháp (ít nhất 10 ký tự)!");return}if(!o||o.length===0){alert("Vui lòng chọn icon cho phương pháp!");return}const n=document.getElementById("custom-questions-container");if(!n||n.children.length<3){alert("Cần ít nhất 3 câu hỏi để tạo phương pháp!");return}const i=[];let r=0;for(let p=0;p<n.children.length;p++){const c=(l=document.getElementById(`question-text-${p}`))==null?void 0:l.value,a=(g=document.getElementById(`question-type-${p}`))==null?void 0:g.value;if(!c||c.length<5){alert(`Câu hỏi ${p+1}: Vui lòng nhập nội dung câu hỏi (ít nhất 5 ký tự)!`);return}const y={text:c,type:a};if(a==="textarea")y.placeholder="Mô tả chi tiết setup...",r+=10;else{const x=document.getElementById(`question-options-${p}`);if(!x||x.children.length<2){alert(`Câu hỏi ${p+1}: Cần ít nhất 2 đáp án!`);return}const C=[];let T=0;for(let f=0;f<x.children.length;f++){const M=(b=document.getElementById(`option-text-${p}-${f}`))==null?void 0:b.value,w=parseInt(((m=document.getElementById(`option-weight-${p}-${f}`))==null?void 0:m.value)||"0");if(!M||M.length<2){alert(`Câu hỏi ${p+1}, Đáp án ${f+1}: Vui lòng nhập nội dung đáp án!`);return}if(isNaN(w)||w<0||w>50){alert(`Câu hỏi ${p+1}, Đáp án ${f+1}: Điểm số phải từ 0-50!`);return}C.push({value:`option_${f}`,label:M,weight:w}),T=Math.max(T,w)}y.options=C,r+=T}i.push(y)}if(r<50){alert("Tổng điểm tối đa quá thấp! Hãy tăng trọng số các đáp án để đạt ít nhất 50 điểm.");return}if(r>200){alert("Tổng điểm tối đa quá cao! Hãy giảm trọng số các đáp án để không vượt quá 200 điểm.");return}const s={id:`custom_${Date.now()}`,name:e,icon:o,description:t,questions:i,totalMaxScore:r,createdAt:new Date().toISOString(),isCustom:!0};try{if((await fetch(`${this.apiBase}/customMethods`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)})).ok)console.log("✅ Custom method saved:",s),this.removeModal("custom-method-creator"),this.showCustomMethodSuccess(s);else throw new Error("Failed to save custom method")}catch(p){console.error("❌ Error saving custom method:",p),alert("Không thể lưu phương pháp tùy chỉnh. Hãy kiểm tra JSON Server đang chạy!")}}showCustomMethodSuccess(e){const t=`
      <div style="text-align: center; margin-bottom: 24px;">
        <div style="
          display: inline-block;
          padding: 20px;
          border-radius: 50%;
          background: #e8f5e8;
          margin-bottom: 16px;
        ">
          <span style="font-size: 48px;">${e.icon}</span>
        </div>
        <h3 style="margin: 0; color: #4caf50; font-size: 20px;">
          Phương pháp đã được tạo thành công!
        </h3>
      </div>

      <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h4 style="margin: 0 0 12px 0; color: #333;">📊 Thông tin phương pháp:</h4>
        <div style="margin-bottom: 12px; color: #333;">
          <strong style="color: #333;">Tên:</strong> <span style="color: #333;">${e.name}</span>
        </div>
        <div style="margin-bottom: 12px; color: #333;">
          <strong style="color: #333;">Mô tả:</strong> <span style="color: #333;">${e.description}</span>
        </div>
        <div style="margin-bottom: 12px; color: #333;">
          <strong style="color: #333;">Số câu hỏi:</strong> <span style="color: #333;">${e.questions.length}</span>
        </div>
        <div style="color: #333;">
          <strong style="color: #333;">Điểm tối đa:</strong> <span style="color: #333;">${e.totalMaxScore}</span>
        </div>
      </div>

      <div style="background: #e8f5e8; padding: 16px; border-radius: 8px; margin-bottom: 20px;">
        <h4 style="margin: 0 0 8px 0; color: #2e7d32;">✅ Phương pháp đã sẵn sàng sử dụng!</h4>
        <p style="margin: 0; color: #2e7d32; font-size: 14px;">
          Bạn có thể test ngay phương pháp này hoặc quay lại để chọn phương pháp khác.
        </p>
      </div>
    `,o=[{text:"Quay lại danh sách",onClick:()=>{this.removeModal("custom-method-success"),this.showTradingMethodSelector()}},{text:"Test phương pháp này",primary:!0,onClick:()=>{this.removeModal("custom-method-success"),this.showTradingAnalysis(e.id,e)}}];this.createModal("custom-method-success","✅ Tạo thành công",t,o)}async getCustomMethod(e){try{return(await(await fetch(`${this.apiBase}/customMethods`)).json()).find(n=>n.id===e)}catch(t){return console.error("Error fetching custom method:",t),null}}async loadCustomMethods(){try{const e=await fetch(`${this.apiBase}/customMethods`);if(e.ok){const t=await e.json();return console.log("✅ Loaded custom methods:",t.length),t}}catch{console.log("No custom methods found or server not available")}return[]}restartFlow(){["daily-goals-modal","psychology-assessment","trading-method-selector","trading-analysis","trading-interface","stats-dashboard"].forEach(t=>this.removeModal(t)),this.currentFlow=null,this.startTradingFlow()}setupMessageListener(){chrome.runtime.onMessage.addListener((e,t,o)=>{switch(console.log("Content script received message:",e),e.action){case"showStats":this.showSuccessMessage("Stats dashboard đang được phát triển...");break;case"restartFlow":this.restartFlow();break;case"startFlow":this.startTradingFlow();break;case"updateMethodSettings":this.methodSettings=e.methodSettings,console.log("✅ Method settings updated:",this.methodSettings);break;case"tradeResult":this.handleTradeResult(e.result,e.profit);break;default:console.log("Unknown action:",e.action)}o({success:!0})})}}document.addEventListener("DOMContentLoaded",()=>{const v=new S;window.BinomoTradingAssistant=v});if(document.readyState==="loading")document.addEventListener("DOMContentLoaded",()=>{const v=new S;window.BinomoTradingAssistant=v});else{const v=new S;window.BinomoTradingAssistant=v}
