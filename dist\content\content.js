var x=Object.defineProperty;var v=(h,t,e)=>t in h?x(h,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):h[t]=e;var d=(h,t,e)=>v(h,typeof t!="symbol"?t+"":t,e);console.log("🎯 Binomo Trading Assistant - Enhanced Content Script Loaded");class k{constructor(t){d(this,"apiKey");d(this,"baseURL","https://api.openai.com/v1/chat/completions");this.apiKey=t}async assessPsychology(t){try{const e=this.createAssessmentPrompt(t),o=await this.callOpenAI(e);return this.parseAIResponse(o,t)}catch(e){return console.error("Error in AI psychology assessment:",e),this.fallbackAssessment(t)}}createAssessmentPrompt(t){return`
Bạn là một chuyên gia tâm lý giao dịch với 20 năm kinh nghiệm. H<PERSON>y đánh giá tâm lý giao dịch của người này:

THÔNG TIN ĐÁNH GIÁ:
- Trạng thái cảm xúc: ${t.emotionalState}
- Tình hình tài chính: ${t.financialSituation}
- Kết quả giao dịch gần đây: ${t.recentPerformance}
- Chất lượng giấc ngủ: ${t.sleepQuality}
- Mức độ căng thẳng: ${t.stressLevel}
- Động lực giao dịch: ${t.motivation}
${t.additionalNotes?`- Ghi chú thêm: ${t.additionalNotes}`:""}

Hãy trả về đánh giá theo format JSON chính xác sau:
{
  "score": [số điểm từ 0-100],
  "emotional_factor": [điểm cảm xúc 0-100],
  "financial_factor": [điểm tài chính 0-100],
  "physical_factor": [điểm thể chất 0-100],
  "mental_factor": [điểm tinh thần 0-100],
  "should_trade": [true/false],
  "recommendation": "[khuyến nghị ngắn gọn]",
  "analysis": "[phân tích chi tiết 2-3 câu]",
  "risk_factors": ["[yếu tố rủi ro 1]", "[yếu tố rủi ro 2]"],
  "positive_factors": ["[yếu tố tích cực 1]", "[yếu tố tích cực 2]"]
}

TIÊU CHÍ ĐÁNH GIÁ:
- 90-100: Tâm lý xuất sắc, sẵn sàng giao dịch
- 80-89: Tâm lý tốt, có thể giao dịch cẩn thận
- 60-79: Tâm lý trung bình, nên nghỉ 15-30 phút
- 30-59: Tâm lý kém, nên nghỉ 4-8 tiếng
- 0-29: Tâm lý rất kém, nên nghỉ 12-24 tiếng

Chỉ trả về JSON, không có text khác.
`}async callOpenAI(t){const e=await fetch(this.baseURL,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.apiKey}`},body:JSON.stringify({model:"gpt-4o-mini",messages:[{role:"system",content:"Bạn là chuyên gia tâm lý giao dịch. Luôn trả về JSON hợp lệ."},{role:"user",content:t}],temperature:.3,max_tokens:1e3})});if(!e.ok)throw new Error(`OpenAI API error: ${e.status}`);return(await e.json()).choices[0].message.content}parseAIResponse(t,e){try{const o=t.replace(/```json\n?|\n?```/g,"").trim(),i=JSON.parse(o),s=this.getScoreLevel(i.score),n=this.calculateBlockDuration(i.score,i);return{score:i.score,level:s,shouldTrade:i.should_trade&&i.score>=60,blockDuration:n,recommendation:i.recommendation,aiAnalysis:i.analysis,factors:{emotional:i.emotional_factor||50,financial:i.financial_factor||50,physical:i.physical_factor||50,mental:i.mental_factor||50}}}catch(o){return console.error("Error parsing AI response:",o),this.fallbackAssessment(e)}}getScoreLevel(t){return t>=90?"excellent":t>=80?"good":t>=60?"fair":t>=30?"poor":"critical"}calculateBlockDuration(t,e){let o=0;return t>=80?o=0:t>=60?o=15:t>=45?o=60:t>=30?o=240:t>=15?o=720:o=1440,e.risk_factors&&e.risk_factors.length>2&&(o=Math.min(o*1.5,1440)),e.positive_factors&&e.positive_factors.length>2&&(o=Math.max(o*.7,0)),Math.round(o)}fallbackAssessment(t){let e=50;t.emotionalState.includes("cân bằng")||t.emotionalState.includes("tích cực")?e+=20:(t.emotionalState.includes("căng thẳng")||t.emotionalState.includes("lo âu"))&&(e-=20),t.financialSituation.includes("ổn định")?e+=15:t.financialSituation.includes("khó khăn")&&(e-=15),t.recentPerformance.includes("tốt")||t.recentPerformance.includes("lãi")?e+=10:(t.recentPerformance.includes("thua")||t.recentPerformance.includes("lỗ"))&&(e-=15),e=Math.max(0,Math.min(100,e));const o=this.getScoreLevel(e),i={excellent:0,good:0,fair:15,poor:240,critical:1440};return{score:e,level:o,shouldTrade:e>=60,blockDuration:i[o],recommendation:e>=60?"Có thể giao dịch cẩn thận":"Nên nghỉ ngơi và thiền định",aiAnalysis:"Đánh giá dựa trên quy tắc cơ bản (AI không khả dụng)",factors:{emotional:e,financial:e,physical:e,mental:e}}}}class b{constructor(){d(this,"isBlocked",!1);d(this,"psychologyAI",null);d(this,"lastTradeAmount",0);d(this,"tradeObserver",null);d(this,"lastTradeReasonModalTime",0);d(this,"lastMindfulnessModalTime",0);d(this,"hasShownAIPsychologyModal",!1);this.init(),window.testMindfulnessModal=t=>{console.log(`🧪 Manual test trigger for ${t} modal`),this.lastMindfulnessModalTime=0,this.showMindfulnessModal(t)},console.log('🧪 Test function available: testMindfulnessModal("win") or testMindfulnessModal("loss")')}async init(){await this.initializeAI(),await this.checkTradingBlock(),this.isBlocked?this.showBlockedMessage():(this.checkPsychologyConfirmation(),this.showPsychologyStateModal(),this.startBehaviorTracking())}async initializeAI(){try{const t=await chrome.storage.local.get(["openaiApiKey"]);t.openaiApiKey&&(this.psychologyAI=new k(t.openaiApiKey))}catch(t){console.error("Error initializing AI:",t)}}async checkTradingBlock(){try{const t=await chrome.storage.local.get(["tradingBlocked","blockDate","blockUntil","blockDurationMinutes"]);if(t.tradingBlocked){const e=new Date;if(t.blockUntil){const o=new Date(t.blockUntil);if(e>=o){await chrome.storage.local.remove(["tradingBlocked","blockDate","blockUntil","blockDurationMinutes"]),this.isBlocked=!1;return}}this.isBlocked=!0}}catch(t){console.error("Error checking trading block:",t),this.isBlocked=!1}}showPsychologyStateModal(){setTimeout(()=>{!this.isBlocked&&!document.getElementById("psychology-state-modal")&&this.createPsychologyStateModal()},2e3)}createPsychologyStateModal(){var o,i;const t=document.createElement("div");t.id="psychology-state-modal",t.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,t.innerHTML=`
      <div style="
        background: white;
        padding: 32px;
        border-radius: 16px;
        max-width: 500px;
        width: 90%;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="text-align: center; margin-bottom: 24px;">
          <div style="font-size: 48px; margin-bottom: 16px;">🧠</div>
          <h3 style="color: #1976d2; margin: 0 0 8px 0;">Trạng thái tâm lý hiện tại</h3>
          <p style="color: #666; margin: 0; font-size: 14px;">
            Hãy chia sẻ cảm xúc và trạng thái tâm lý của bạn lúc này
          </p>
        </div>

        <div style="margin-bottom: 24px;">
          <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">
            Hiện tại bạn đang cảm thấy như thế nào?
          </label>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 16px;">
            <button class="emotion-btn" data-emotion="tham" style="
              background: #ffebee;
              color: #c62828;
              border: 2px solid #ffcdd2;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">🤑 Tham lam</button>

            <button class="emotion-btn" data-emotion="gian" style="
              background: #fff3e0;
              color: #ef6c00;
              border: 2px solid #ffcc02;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">😡 Giận dữ</button>

            <button class="emotion-btn" data-emotion="lo_au" style="
              background: #f3e5f5;
              color: #7b1fa2;
              border: 2px solid #e1bee7;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">😰 Lo lắng</button>

            <button class="emotion-btn" data-emotion="so_hai" style="
              background: #e8f5e8;
              color: #388e3c;
              border: 2px solid #c8e6c9;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">😨 Sợ hãi</button>

            <button class="emotion-btn" data-emotion="si_me" style="
              background: #e3f2fd;
              color: #1976d2;
              border: 2px solid #bbdefb;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">😵‍💫 Si mê</button>

            <button class="emotion-btn" data-emotion="u_me" style="
              background: #fce4ec;
              color: #ad1457;
              border: 2px solid #f8bbd9;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">😴 U mê</button>

            <button class="emotion-btn" data-emotion="phan_khich" style="
              background: #fff8e1;
              color: #f57f17;
              border: 2px solid #fff176;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">🤩 Phấn khích</button>

            <button class="emotion-btn" data-emotion="hung_phan" style="
              background: #e0f2f1;
              color: #00695c;
              border: 2px solid #b2dfdb;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">😤 Hưng phấn</button>

            <button class="emotion-btn" data-emotion="binh_tinh" style="
              background: #e8f5e8;
              color: #2e7d32;
              border: 2px solid #c8e6c9;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">😌 Bình tĩnh</button>

            <button class="emotion-btn" data-emotion="tinh_thuc" style="
              background: #e3f2fd;
              color: #1565c0;
              border: 2px solid #90caf9;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
              text-align: center;
            ">🧘‍♂️ Tỉnh thức</button>
          </div>

          <textarea
            id="psychology-notes"
            placeholder="Mô tả thêm về cảm xúc và suy nghĩ hiện tại của bạn..."
            style="
              width: 100%;
              height: 80px;
              padding: 12px;
              border: 2px solid #e0e0e0;
              border-radius: 8px;
              font-size: 14px;
              resize: vertical;
              font-family: inherit;
              margin-top: 8px;
            "
          ></textarea>
        </div>

        <div style="display: flex; gap: 12px;">
          <button id="skip-psychology" style="
            flex: 1;
            background: #666;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">Bỏ qua</button>
          <button id="analyze-psychology" style="
            flex: 2;
            background: #1976d2;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
          ">🧠 Phân tích tâm lý</button>
        </div>

        <div id="psychology-result" style="
          margin-top: 16px;
          padding: 16px;
          background: #f5f5f5;
          border-radius: 8px;
          display: none;
        "></div>
      </div>
    `,document.body.appendChild(t);let e="";t.querySelectorAll(".emotion-btn").forEach(s=>{s.addEventListener("click",n=>{const r=n.target;e=r.getAttribute("data-emotion")||"",t.querySelectorAll(".emotion-btn").forEach(l=>{l.style.borderColor=l.style.backgroundColor}),r.style.borderColor="#1976d2",r.style.borderWidth="3px"})}),(o=document.getElementById("skip-psychology"))==null||o.addEventListener("click",()=>{t.remove()}),(i=document.getElementById("analyze-psychology"))==null||i.addEventListener("click",()=>{this.analyzePsychologyState(e)})}createAIPsychologyModal(){var e,o;const t=document.createElement("div");t.id="ai-psychology-modal",t.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,t.innerHTML=`
      <div style="
        background: white;
        padding: 32px;
        border-radius: 16px;
        max-width: 500px;
        width: 90%;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="text-align: center; margin-bottom: 24px;">
          <div style="font-size: 48px; margin-bottom: 16px;">🧠</div>
          <h3 style="color: #1976d2; margin: 0 0 8px 0;">Đánh giá tâm lý AI</h3>
          <p style="color: #666; margin: 0; font-size: 14px;">
            Hãy chia sẻ cảm xúc hiện tại để AI đánh giá tâm lý giao dịch
          </p>
        </div>

        <div style="margin-bottom: 24px;">
          <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">
            Hiện tại bạn đang cảm thấy như thế nào?
          </label>
          <textarea 
            id="psychology-diary" 
            placeholder="Ví dụ: Tôi cảm thấy hơi lo lắng vì thua lỗ hôm qua, nhưng cũng muốn gỡ lại. Tôi đã ngủ đủ giấc và cảm thấy tỉnh táo..."
            style="
              width: 100%;
              height: 120px;
              padding: 12px;
              border: 2px solid #e0e0e0;
              border-radius: 8px;
              font-size: 14px;
              resize: vertical;
              font-family: inherit;
            "
          ></textarea>
          <div style="margin-top: 8px; font-size: 12px; color: #666;">
            💡 Hãy thành thật về cảm xúc: tham lam, giận dữ, lo lắng, sợ hãi, phấn khích...
          </div>
        </div>

        <div style="display: flex; gap: 12px;">
          <button id="skip-assessment" style="
            flex: 1;
            background: #666;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">Bỏ qua</button>
          <button id="ai-analyze" style="
            flex: 2;
            background: #1976d2;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
          ">🤖 Phân tích AI</button>
        </div>

        <div id="ai-result" style="
          margin-top: 16px;
          padding: 16px;
          background: #f5f5f5;
          border-radius: 8px;
          display: none;
        "></div>
      </div>
    `,document.body.appendChild(t),(e=document.getElementById("skip-assessment"))==null||e.addEventListener("click",()=>{t.remove()}),(o=document.getElementById("ai-analyze"))==null||o.addEventListener("click",()=>{this.performAIAnalysis()})}async analyzePsychologyState(t){var s;const e=((s=document.getElementById("psychology-notes"))==null?void 0:s.value)||"",o=document.getElementById("psychology-result"),i=document.getElementById("analyze-psychology");if(!t){alert("Vui lòng chọn trạng thái cảm xúc hiện tại");return}i.textContent="🧠 Đang phân tích...",i.disabled=!0;try{const r={tham:20,gian:15,lo_au:30,so_hai:25,si_me:35,u_me:40,phan_khich:45,hung_phan:50,binh_tinh:85,tinh_thuc:95}[t]||50,l=this.calculateBlockDurationFromScore(r),a=r>=70,g={tham:"Tâm tham lam có thể dẫn đến quyết định liều lĩnh và mất kiểm soát",gian:"Tâm giận dữ làm mờ khả năng phán đoán và dễ dẫn đến sai lầm",lo_au:"Tâm lo lắng tạo ra căng thẳng và ảnh hưởng đến quyết định",so_hai:"Tâm sợ hãi có thể khiến bạn bỏ lỡ cơ hội hoặc thoát quá sớm",si_me:"Tâm si mê làm mất tỉnh táo và khả năng nhìn nhận thực tế",u_me:"Tâm u mê thiếu sự tỉnh thức cần thiết cho giao dịch",phan_khich:"Tâm phấn khích có thể dẫn đến quyết định vội vàng",hung_phan:"Tâm hưng phấn cần được kiểm soát để tránh rủi ro",binh_tinh:"Tâm bình tĩnh là nền tảng tốt cho giao dịch có ý thức",tinh_thuc:"Tâm tỉnh thức là trạng thái lý tưởng cho giao dịch"}[t]||"Cần đánh giá thêm";o&&(o.style.display="block",o.innerHTML=`
          <div style="text-align: center; margin-bottom: 16px;">
            <div style="font-size: 32px; margin-bottom: 8px;">
              ${r>=80?"😊":r>=60?"😐":"😰"}
            </div>
            <div style="font-size: 24px; font-weight: bold; color: ${r>=80?"#4caf50":r>=60?"#ff9800":"#f44336"};">
              ${r}/100 điểm
            </div>
          </div>

          <div style="margin-bottom: 16px;">
            <strong>Phân tích tâm lý:</strong>
            <p style="margin: 8px 0; font-size: 14px; line-height: 1.4;">${g}</p>
            ${e?`<p style="margin: 8px 0; font-size: 13px; color: #666; font-style: italic;">"${e}"</p>`:""}
          </div>

          ${a?`
            <div style="background: #d4edda; padding: 12px; border-radius: 8px; border-left: 4px solid #28a745; margin-bottom: 16px;">
              <strong>✅ Có thể giao dịch:</strong> Tâm lý ổn định
              <br><small>Hãy duy trì sự tỉnh thức và tuân thủ nguyên tắc giao dịch.</small>
            </div>
          `:`
            <div style="background: #fff3cd; padding: 12px; border-radius: 8px; border-left: 4px solid #ffc107; margin-bottom: 16px;">
              <strong>⚠️ Khuyến nghị nghỉ ngơi:</strong> ${this.formatDuration(l)}
              <br><small>Hãy dành thời gian thiền định và rèn luyện tâm để chuẩn bị tốt hơn.</small>
            </div>
          `}

          <div style="display: flex; gap: 8px;">
            ${a?`
              <button onclick="document.getElementById('psychology-state-modal').remove()" style="
                flex: 1; background: #4caf50; color: white; border: none; padding: 10px; border-radius: 6px; cursor: pointer;
              ">✅ Tiếp tục giao dịch</button>
            `:`
              <button onclick="window.open('${chrome.runtime.getURL("options.html#meditation")}', '_blank')" style="
                flex: 1; background: #9c27b0; color: white; border: none; padding: 10px; border-radius: 6px; cursor: pointer;
              ">🧘‍♂️ Đi thiền</button>
            `}
            <button onclick="document.getElementById('psychology-state-modal').remove()" style="
              background: #666; color: white; border: none; padding: 10px 16px; border-radius: 6px; cursor: pointer;
            ">Đóng</button>
          </div>
        `),await chrome.storage.local.set({lastPsychologyState:{timestamp:Date.now(),emotion:t,notes:e,score:r,shouldTrade:a,blockDuration:l}}),!a&&l>0&&await this.blockTradingWithDuration(l)}catch(n){console.error("Psychology analysis error:",n),o&&(o.style.display="block",o.innerHTML=`
          <div style="background: #f8d7da; padding: 12px; border-radius: 8px; color: #721c24;">
            <strong>❌ Lỗi phân tích:</strong> Không thể phân tích tâm lý. Vui lòng thử lại.
          </div>
        `)}i.textContent="🧠 Phân tích tâm lý",i.disabled=!1}calculateBlockDurationFromScore(t){return t>=80?0:t>=60?15:t>=45?60:t>=30?240:t>=15?720:1440}async performAIAnalysis(){var i;const t=(i=document.getElementById("psychology-diary"))==null?void 0:i.value,e=document.getElementById("ai-result"),o=document.getElementById("ai-analyze");if(!t.trim()){alert("Vui lòng chia sẻ cảm xúc hiện tại của bạn");return}if(!this.psychologyAI){alert("AI chưa được cấu hình. Vui lòng thêm OpenAI API key trong Settings.");return}o.textContent="🤖 Đang phân tích...",o.disabled=!0;try{const s={emotionalState:t,financialSituation:"Không rõ",recentPerformance:"Không rõ",sleepQuality:"Không rõ",stressLevel:"Không rõ",motivation:t,additionalNotes:"Đánh giá từ nhật ký tâm trạng"},n=await this.psychologyAI.assessPsychology(s);e&&(e.style.display="block",e.innerHTML=`
          <div style="text-align: center; margin-bottom: 16px;">
            <div style="font-size: 32px; margin-bottom: 8px;">
              ${n.score>=80?"😊":n.score>=60?"😐":"😰"}
            </div>
            <div style="font-size: 24px; font-weight: bold; color: ${n.score>=80?"#4caf50":n.score>=60?"#ff9800":"#f44336"};">
              ${n.score}/100 điểm
            </div>
          </div>
          
          <div style="margin-bottom: 16px;">
            <strong>Phân tích AI:</strong>
            <p style="margin: 8px 0; font-size: 14px; line-height: 1.4;">${n.aiAnalysis}</p>
          </div>
          
          <div style="margin-bottom: 16px;">
            <strong>Khuyến nghị:</strong>
            <p style="margin: 8px 0; font-size: 14px; line-height: 1.4;">${n.recommendation}</p>
          </div>
          
          ${n.blockDuration>0?`
            <div style="background: #fff3cd; padding: 12px; border-radius: 8px; border-left: 4px solid #ffc107;">
              <strong>⚠️ Khuyến nghị nghỉ ngơi:</strong> ${this.formatDuration(n.blockDuration)}
            </div>
          `:`
            <div style="background: #d4edda; padding: 12px; border-radius: 8px; border-left: 4px solid #28a745;">
              <strong>✅ Có thể giao dịch:</strong> Tâm lý ổn định
            </div>
          `}
          
          <div style="margin-top: 16px; display: flex; gap: 8px;">
            ${n.shouldTrade?`
              <button onclick="document.getElementById('ai-psychology-modal').remove()" style="
                flex: 1; background: #4caf50; color: white; border: none; padding: 10px; border-radius: 6px; cursor: pointer;
              ">✅ Tiếp tục giao dịch</button>
            `:`
              <button onclick="window.open('${chrome.runtime.getURL("options.html#meditation")}', '_blank')" style="
                flex: 1; background: #9c27b0; color: white; border: none; padding: 10px; border-radius: 6px; cursor: pointer;
              ">🧘‍♂️ Đi thiền</button>
            `}
            <button onclick="document.getElementById('ai-psychology-modal').remove()" style="
              background: #666; color: white; border: none; padding: 10px 16px; border-radius: 6px; cursor: pointer;
            ">Đóng</button>
          </div>
        `),await chrome.storage.local.set({lastAIAssessment:{timestamp:Date.now(),score:n.score,shouldTrade:n.shouldTrade,blockDuration:n.blockDuration}}),!n.shouldTrade&&n.blockDuration>0&&await this.blockTradingWithDuration(n.blockDuration)}catch(s){console.error("AI analysis error:",s),e&&(e.style.display="block",e.innerHTML=`
          <div style="background: #f8d7da; padding: 12px; border-radius: 8px; color: #721c24;">
            <strong>❌ Lỗi phân tích AI:</strong> ${s.message||"Không thể kết nối với AI"}
          </div>
        `)}o.textContent="🤖 Phân tích AI",o.disabled=!1}formatDuration(t){return t<60?`${t} phút`:t<1440?`${Math.round(t/60)} tiếng`:`${Math.round(t/1440)} ngày`}async blockTradingWithDuration(t){try{const e=new Date,o=new Date(e.getTime()+t*60*1e3);await chrome.storage.local.set({tradingBlocked:!0,blockDate:e.toISOString(),blockUntil:o.toISOString(),blockDurationMinutes:t,needsPsychologyConfirmation:!0}),setTimeout(()=>window.location.reload(),2e3)}catch(e){console.error("Error blocking trading:",e)}}startBehaviorTracking(){setTimeout(()=>{this.observeTradeActions(),this.observeTradeResults()},3e3)}observeTradeActions(){console.log("🎯 Starting trade action observation...");const t=['button[class*="deal"]','button[class*="trade"]','button[class*="up"]','button[class*="down"]','button[class*="higher"]','button[class*="lower"]','button[class*="call"]','button[class*="put"]',".deal-button",".trade-button",".option-button",'[data-direction="up"]','[data-direction="down"]',".trading-panel button",".deal-panel button"],e=['input[data-test-id*="amount"]','input[class*="amount"]','input[type="number"]',".amount-input",'[data-field="amount"]'],o=()=>{e.forEach(s=>{document.querySelectorAll(s).forEach(r=>{r.hasAttribute("data-monitored")||(r.setAttribute("data-monitored","true"),r.addEventListener("change",l=>{const a=l.target;this.lastTradeAmount=parseFloat(a.value)||0,console.log("💰 Amount changed:",this.lastTradeAmount)}))})})},i=()=>{t.forEach(s=>{document.querySelectorAll(s).forEach(r=>{r.hasAttribute("data-monitored")||(r.setAttribute("data-monitored","true"),r.addEventListener("click",l=>{console.log("🎯 Deal button clicked:",r),this.isValidTradeButton(r)&&console.log("✅ Valid trade button clicked, waiting for trade execution...")}))})})};o(),i(),this.tradeObserver=new MutationObserver(s=>{let n=!1;s.forEach(r=>{r.addedNodes.forEach(l=>{if(l.nodeType===Node.ELEMENT_NODE){const a=l;t.forEach(c=>{a.matches&&a.matches(c)&&(n=!0),a.querySelectorAll&&a.querySelectorAll(c).length>0&&(n=!0)})}})}),n&&setTimeout(()=>{o(),i()},500)}),this.tradeObserver.observe(document.body,{childList:!0,subtree:!0})}isValidTradeButton(t){var l;const e=((l=t.textContent)==null?void 0:l.toLowerCase())||"",o=t.className.toLowerCase(),i=t.id.toLowerCase(),s=["up","down","higher","lower","call","put","deal","trade","buy","sell"],n=["close","cancel","menu","settings","help","login","register","deposit","withdraw"];for(const a of n)if(e.includes(a)||o.includes(a)||i.includes(a))return!1;for(const a of s)if(e.includes(a)||o.includes(a)||i.includes(a))return!0;const r=t.parentElement;if(r){const a=r.className.toLowerCase();if(a.includes("deal")||a.includes("trade")||a.includes("trading"))return!0}return!1}showTradeReasonModal(){const t=Date.now();if(t-this.lastTradeReasonModalTime<1e4){console.log("⏰ Trade reason modal on cooldown");return}if(document.getElementById("trade-reason-modal")){console.log("📝 Trade reason modal already exists");return}this.lastTradeReasonModalTime=t,console.log("🤔 Showing trade reason modal");const o=document.createElement("div");o.id="trade-reason-modal",o.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,o.innerHTML=`
      <div style="
        background: white;
        padding: 24px;
        border-radius: 16px;
        max-width: 400px;
        width: 90%;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        text-align: center;
      ">
        <div style="font-size: 32px; margin-bottom: 16px;">🤔</div>
        <h3 style="color: #1976d2; margin: 0 0 16px 0;">Lý do vào lệnh</h3>
        <p style="color: #666; margin: 0 0 20px 0; font-size: 14px;">
          Lệnh này bạn vào vì điều gì?
        </p>

        <div style="display: flex; flex-direction: column; gap: 8px; margin-bottom: 20px;">
          <button class="reason-btn" data-reason="method" style="
            background: #4caf50;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">✅ Theo nguyên tắc phương pháp</button>
          
          <button class="reason-btn" data-reason="greed" style="
            background: #ff9800;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🤑 Vì tâm tham lam</button>
          
          <button class="reason-btn" data-reason="anger" style="
            background: #f44336;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">😡 Vì tâm giận dữ (gỡ lại)</button>
          
          <button class="reason-btn" data-reason="guess" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🎲 Vì suy đoán tầm bậy</button>
        </div>

        <button onclick="document.getElementById('trade-reason-modal').remove()" style="
          background: #666;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 12px;
        ">Bỏ qua</button>
      </div>
    `,document.body.appendChild(o),o.querySelectorAll(".reason-btn").forEach(i=>{i.addEventListener("click",s=>{const n=s.target.getAttribute("data-reason");this.handleTradeReason(n),o.remove()})})}async handleTradeReason(t){if(t==="method")console.log("✅ Trade based on method principles");else{const e=document.createElement("div");e.style.cssText=`
        position: fixed;
        top: 20px;
        right: 20px;
        background: #fff3cd;
        border: 1px solid #ffc107;
        padding: 16px;
        border-radius: 8px;
        z-index: 999999;
        max-width: 300px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      `,e.innerHTML=`
        <div style="color: #856404;">
          <strong>⚠️ Cảnh báo tâm lý</strong>
          <p style="margin: 8px 0; font-size: 14px;">
            Giao dịch không theo nguyên tắc có thể gây thua lỗ. Hãy nghỉ ngơi và rèn tâm.
          </p>
          <button onclick="window.open('${chrome.runtime.getURL("options.html#meditation")}', '_blank')" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 8px;
          ">🧘‍♂️ Thiền định</button>
          <button onclick="this.parentElement.parentElement.remove()" style="
            background: #666;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
          ">Đóng</button>
        </div>
      `,document.body.appendChild(e),setTimeout(()=>{e.parentElement&&e.remove()},1e4)}await chrome.storage.local.set({lastTradeReason:{timestamp:Date.now(),reason:t,amount:this.lastTradeAmount}})}observeTradeResults(){console.log("📊 Starting trade result observation...");const t=new Set;setTimeout(()=>{const i=["progress-bar-item","progress-bar-timeline",".option.win",".option:not(.win)","lottie-player.lose","option-animation"];let s=0;i.forEach(n=>{const r=document.querySelectorAll(n);r.forEach(l=>{t.add(l),s++}),console.log(`📊 Found ${r.length} existing elements for selector: ${n}`)}),console.log(`📊 Initial scan: marked ${s} existing elements total`)},1e3),new MutationObserver(i=>{i.forEach(s=>{var n;if(s.addedNodes.length>0&&console.log("🔄 DOM mutation detected, added nodes:",s.addedNodes.length),s.addedNodes.forEach(r=>{var l;if(r.nodeType===Node.ELEMENT_NODE){const a=r,c=((l=a.tagName)==null?void 0:l.toLowerCase())||"",g=a.className||"";console.log("➕ New element added:",c,g),t.has(a)?console.log("⏭️ Skipping existing element:",c):(console.log("🆕 Processing truly new element:",c),this.processNewElement(a));const p=a.querySelectorAll("*");p.length>0&&(console.log("👶 Checking",p.length,"child elements"),p.forEach(u=>{var m;if(!t.has(u)){const f=((m=u.tagName)==null?void 0:m.toLowerCase())||"",y=u.className||"";console.log("👶 Processing new child:",f,y),this.processNewElement(u)}}))}}),s.type==="attributes"&&s.attributeName==="class"){const r=s.target,l=Date.now(),a=((n=r.tagName)==null?void 0:n.toLowerCase())||"",c=r.className||"";console.log("🎨 Class change detected:",a,c),a==="lottie-player"&&c.includes("lose")&&(l-this.lastMindfulnessModalTime>3e3?(console.log("😔 Loss result detected via class change"),this.lastMindfulnessModalTime=l,setTimeout(()=>this.showMindfulnessModal("loss",!0),2e3)):console.log("⏰ Loss modal on cooldown (class change)"))}})}).observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["class"]})}processNewElement(t){var n;const e=Date.now(),o=((n=t.tagName)==null?void 0:n.toLowerCase())||"",i=t.className||"";console.log("🔍 Processing new element:",o,i),(o==="progress-bar-item"||o==="progress-bar-timeline")&&this.isValidTradeExecution(t)&&(e-this.lastTradeReasonModalTime>1e4?(console.log("📈 NEW trade execution detected:",o,i),this.lastTradeReasonModalTime=e,setTimeout(()=>this.showTradeReasonModal(),1e3)):console.log("⏰ Trade execution on cooldown")),t.matches&&t.matches(".option.win")&&this.isValidWinResult(t)&&(console.log("🎉 NEW win result detected:",i),console.log("🎯 Forcing win modal (ignoring cooldown for real trade result)"),this.lastMindfulnessModalTime=e,setTimeout(()=>this.showMindfulnessModal("win",!0),2e3));let s=!1;o==="lottie-player"&&i.includes("lose")&&this.isValidLossResult(t)&&(console.log("😔 NEW loss result detected (animation):",o,i),console.log("🎯 Forcing loss modal (ignoring cooldown for real trade result)"),this.lastMindfulnessModalTime=e,setTimeout(()=>this.showMindfulnessModal("loss",!0),2e3),s=!0),!s&&o==="div"&&i.includes("option")&&!i.includes("win")&&!i.includes("analytics")&&this.isValidLossResult(t)&&(console.log("😔 NEW loss result detected (option):",o,i),console.log("🎯 Forcing loss modal (ignoring cooldown for real trade result)"),this.lastMindfulnessModalTime=e,setTimeout(()=>this.showMindfulnessModal("loss",!0),2e3))}isValidTradeExecution(t){var i;const e=((i=t.tagName)==null?void 0:i.toLowerCase())||"",o=t.className||"";if(console.log("🔍 Validating trade execution:",e,o),e==="progress-bar-item"){const s=t.querySelector(".profit"),n=t.querySelector(".arrow"),r=t.querySelector("progress-bar-timeline");return console.log("📈 Progress bar validation:",{hasProfit:!!s,hasArrow:!!n,hasTimeline:!!r}),!!(s&&n&&r)}if(e==="progress-bar-timeline"){const s=t.querySelector(".progress"),n=t.querySelector(".text");return console.log("⏱️ Timeline validation:",{hasProgress:!!s,hasText:!!n}),!!(s&&n)}return!1}isValidWinResult(t){var i;const e=t.className||"",o=((i=t.tagName)==null?void 0:i.toLowerCase())||"";if(console.log("🏆 Validating win result:",o,e),e.includes("option")&&e.includes("win")){const s=t.querySelector(".currency");if(s){const n=s.textContent||"";console.log("💰 Win currency text:",n);const r=!n.includes("0,00")&&(n.includes("$")||n.includes("₫"));return console.log("💰 Has profit:",r),r}return console.log("✅ Win class detected without currency check"),!0}return!1}isValidLossResult(t){var i;const e=t.className||"",o=((i=t.tagName)==null?void 0:i.toLowerCase())||"";if(console.log("😔 Validating loss result:",o,e),o==="lottie-player"&&e.includes("lose"))return console.log("❌ Loss animation detected (priority)"),!0;if(o==="div"&&e.includes("option")&&!e.includes("win")&&!e.includes("analytics")&&!e.includes("time")&&!e.includes("button")&&!e.includes("close")){const s=t.querySelector(".currency");if(s){const n=s.textContent||"";console.log("💸 Loss currency text:",n);const r=n.includes("0,00");if(console.log("💸 Is loss:",r),r){const l=t.querySelector(".badge"),a=t.querySelector(".assets");return console.log("🔍 Trade result structure:",{hasBadge:!!l,hasAssets:!!a}),!!(l&&a)}}}return!1}showMindfulnessModal(t,e=!1){const o=Date.now(),i=3e3;if(console.log(`🎯 Attempting to show mindfulness modal for ${t} (force: ${e})`),console.log(`⏰ Time since last modal: ${o-this.lastMindfulnessModalTime}ms (cooldown: ${i}ms)`),!e&&o-this.lastMindfulnessModalTime<i){console.log("⏰ Mindfulness modal on cooldown");return}const s=document.querySelector('[id*="mindfulness-modal"]');s&&(console.log("🧘‍♂️ Removing existing modal:",s.id),s.remove()),console.log(`🙏 Creating mindfulness modal for ${t}`);const n=t==="win",r=n?["Tôi biết ơn vì kết quả tốt này và sẽ không để nó làm tôi kiêu ngạo","Thắng lợi này là nhờ sự chuẩn bị kỹ lưỡng và tâm tỉnh thức","Tôi sẽ giữ tâm bình thản và tiếp tục theo nguyên tắc","Mọi thắng lợi đều vô thường, tôi không bám víu vào nó"]:["Thua lỗ là bài học quý báu để tôi trưởng thành hơn","Tôi chấp nhận kết quả này với tâm bình thản và không giận dữ","Mọi thua lỗ đều vô thường, tôi sẽ học hỏi và tiến bước","Tôi buông bỏ sự thất vọng và tập trung vào cải thiện bản thân"],l=document.createElement("div");l.id=`mindfulness-modal-${t}-${Date.now()}`,l.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,l.innerHTML=`
      <div style="
        background: white;
        padding: 24px;
        border-radius: 16px;
        max-width: 400px;
        width: 90%;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        text-align: center;
      ">
        <div style="font-size: 32px; margin-bottom: 16px;">${n?"🙏":"🧘‍♂️"}</div>
        <h3 style="color: ${n?"#4caf50":"#ff9800"}; margin: 0 0 16px 0;">
          ${n?"Lời buông xả cho thắng lợi":"Lời buông xả cho thua lỗ"}
        </h3>
        
        <div style="margin-bottom: 20px;">
          <p style="color: #666; margin: 0 0 16px 0; font-size: 14px;">
            Hãy chọn một câu để thực hành buông xả:
          </p>
          
          <div style="display: flex; flex-direction: column; gap: 8px;">
            ${r.map((a,c)=>`
              <button class="mindfulness-btn" data-text="${a}" style="
                background: ${n?"#e8f5e8":"#fff3e0"};
                color: ${n?"#2e7d32":"#f57c00"};
                border: 1px solid ${n?"#c8e6c9":"#ffcc02"};
                padding: 12px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 13px;
                text-align: left;
                line-height: 1.4;
              ">${a}</button>
            `).join("")}
          </div>
        </div>

        <button onclick="this.parentElement.parentElement.remove()" style="
          background: #666;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 12px;
        ">Đóng</button>
      </div>
    `,document.body.appendChild(l),l.querySelectorAll(".mindfulness-btn").forEach(a=>{a.addEventListener("click",c=>{const g=c.target.getAttribute("data-text");this.showMindfulnessConfirmation(g||""),l.remove()})}),setTimeout(()=>{l.parentElement&&l.remove()},3e4)}showMindfulnessConfirmation(t){const e=document.createElement("div");e.style.cssText=`
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4caf50;
      color: white;
      padding: 16px;
      border-radius: 8px;
      z-index: 999999;
      max-width: 300px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      line-height: 1.4;
    `,e.innerHTML=`
      <div>
        <strong>🙏 Thực hành buông xả</strong>
        <p style="margin: 8px 0 0 0;">"${t}"</p>
      </div>
    `,document.body.appendChild(e),setTimeout(()=>{e.parentElement&&e.remove()},5e3)}async showBlockedMessage(){var s,n;const t=await chrome.storage.local.get(["blockUntil","blockDurationMinutes","blockDate"]),e=document.createElement("div");e.id="trading-block-overlay",e.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.9);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;const o=this.calculateTimeRemaining(t),i=this.formatDuration(t.blockDurationMinutes||1440);e.innerHTML=`
      <div style="
        background: white;
        padding: 40px;
        border-radius: 16px;
        text-align: center;
        max-width: 500px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="font-size: 64px; margin-bottom: 20px;">🧘‍♂️</div>
        <h2 style="color: #d32f2f; margin: 0 0 16px 0;">Giao dịch bị khóa</h2>
        <p style="color: #666; margin: 0 0 16px 0; line-height: 1.5;">
          Hệ thống đã khóa giao dịch trong <strong>${i}</strong> để bảo vệ tâm lý của bạn.<br>
          Hãy sử dụng thời gian này để nghỉ ngơi và rèn luyện tâm.
        </p>
        <div id="countdown" style="
          background: #f5f5f5;
          padding: 16px;
          border-radius: 8px;
          margin: 16px 0;
          font-size: 18px;
          font-weight: bold;
          color: #d32f2f;
        ">
          Còn lại: ${o}
        </div>
        <div style="display: flex; gap: 12px; justify-content: center;">
          <button id="meditation-btn" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🧘‍♂️ Thiền định</button>
          <button id="close-tab-btn" style="
            background: #666;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">❌ Đóng tab</button>
        </div>
        <p style="color: #999; font-size: 12px; margin: 16px 0 0 0;">
          Thời gian khóa dựa trên đánh giá tâm lý của bạn
        </p>
      </div>
    `,document.body.appendChild(e),this.startCountdown(t),(s=document.getElementById("meditation-btn"))==null||s.addEventListener("click",()=>{this.openMeditationOptions()}),(n=document.getElementById("close-tab-btn"))==null||n.addEventListener("click",()=>{window.close()})}calculateTimeRemaining(t){const e=new Date;if(t.blockUntil){const i=new Date(t.blockUntil).getTime()-e.getTime();if(i<=0)return"Đã hết hạn";const s=Math.floor(i/(1e3*60*60)),n=Math.floor(i%(1e3*60*60)/(1e3*60));return s>0?`${s} tiếng ${n} phút`:`${n} phút`}return"Không xác định"}startCountdown(t){const e=document.getElementById("countdown");if(!e)return;const i=setInterval(()=>{const s=this.calculateTimeRemaining(t);e.textContent=`Còn lại: ${s}`,s==="Đã hết hạn"&&window.location.reload()},6e4);window.addEventListener("beforeunload",()=>{clearInterval(i)})}async checkPsychologyConfirmation(){try{(await chrome.storage.local.get(["needsPsychologyConfirmation"])).needsPsychologyConfirmation&&this.showPsychologyConfirmation()}catch(t){console.error("Error checking psychology confirmation:",t)}}showPsychologyConfirmation(){var o,i;const t=document.createElement("div");t.id="psychology-confirmation-modal",t.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,t.innerHTML=`
      <div style="
        background: white;
        padding: 32px;
        border-radius: 16px;
        text-align: center;
        max-width: 450px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="font-size: 48px; margin-bottom: 16px;">🧠</div>
        <h3 style="color: #1976d2; margin: 0 0 16px 0;">Xác nhận tâm lý</h3>
        <p style="color: #666; margin: 0 0 24px 0; line-height: 1.5;">
          Bạn đã hoàn thành thiền định hoặc nghỉ ngơi?<br>
          Tâm lý hiện tại có ổn định và sẵn sàng giao dịch không?
        </p>

        <div style="margin-bottom: 24px;">
          <p style="color: #333; font-weight: bold; margin: 0 0 12px 0;">
            Trạng thái tâm lý hiện tại:
          </p>
          <div style="display: flex; flex-direction: column; gap: 8px;">
            <button class="psychology-option" data-state="balanced" style="
              background: #e8f5e8;
              color: #2e7d32;
              border: 2px solid #e8f5e8;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😌 Cân bằng và tỉnh táo</button>
            <button class="psychology-option" data-state="confident" style="
              background: #e3f2fd;
              color: #1976d2;
              border: 2px solid #e3f2fd;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😊 Tự tin và kiên nhẫn</button>
            <button class="psychology-option" data-state="not_ready" style="
              background: #fff3e0;
              color: #f57c00;
              border: 2px solid #fff3e0;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😐 Chưa sẵn sàng, cần thêm thời gian</button>
          </div>
        </div>

        <div style="display: flex; gap: 12px; justify-content: center;">
          <button id="continue-trading-btn" style="
            background: #4caf50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            opacity: 0.5;
          " disabled>✅ Tiếp tục giao dịch</button>
          <button id="more-meditation-btn" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🧘‍♂️ Thiền thêm</button>
        </div>
      </div>
    `,document.body.appendChild(t);let e="";t.querySelectorAll(".psychology-option").forEach(s=>{s.addEventListener("click",n=>{const r=n.target;e=r.getAttribute("data-state")||"",t.querySelectorAll(".psychology-option").forEach(a=>{a.style.borderColor=a.style.backgroundColor}),r.style.borderColor="#1976d2";const l=document.getElementById("continue-trading-btn");e==="balanced"||e==="confident"?(l.disabled=!1,l.style.opacity="1"):(l.disabled=!0,l.style.opacity="0.5")})}),(o=document.getElementById("continue-trading-btn"))==null||o.addEventListener("click",()=>{(e==="balanced"||e==="confident")&&this.confirmPsychologyAndContinue(e)}),(i=document.getElementById("more-meditation-btn"))==null||i.addEventListener("click",()=>{this.openMeditationOptions()})}async confirmPsychologyAndContinue(t){try{await chrome.storage.local.set({needsPsychologyConfirmation:!1,lastConfirmationTime:Date.now(),confirmedPsychologyState:t});const e=document.getElementById("psychology-confirmation-modal");e&&e.remove(),this.showSuccessMessage("✅ Tâm lý đã được xác nhận! Bạn có thể giao dịch an toàn.")}catch(e){console.error("Error confirming psychology:",e)}}openMeditationOptions(){try{chrome.runtime.sendMessage({action:"openOptions",tab:"meditation"})}catch(t){console.error("Error opening meditation options:",t),window.open(chrome.runtime.getURL("options.html#meditation"),"_blank")}}showSuccessMessage(t){const e=document.createElement("div");e.style.cssText=`
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4caf50;
      color: white;
      padding: 16px 24px;
      border-radius: 8px;
      z-index: 999999;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      animation: slideIn 0.3s ease-out;
    `,e.textContent=t;const o=document.createElement("style");o.textContent=`
      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    `,document.head.appendChild(o),document.body.appendChild(e),setTimeout(()=>{e.remove(),o.remove()},3e3)}}document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>new b):new b;
