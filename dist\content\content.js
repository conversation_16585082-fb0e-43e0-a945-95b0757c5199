var g=Object.defineProperty;var h=(n,t,o)=>t in n?g(n,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):n[t]=o;var d=(n,t,o)=>h(n,typeof t!="symbol"?t+"":t,o);console.log("🎯 Binomo Trading Assistant - Minimal Content Script Loaded");class c{constructor(){d(this,"isBlocked",!1);this.init()}async init(){await this.checkTradingBlock(),this.isBlocked?this.showBlockedMessage():this.checkPsychologyConfirmation()}async checkTradingBlock(){try{const t=await chrome.storage.local.get(["tradingBlocked","blockDate"]);if(t.tradingBlocked&&t.blockDate){const o=new Date(t.blockDate);(new Date().getTime()-o.getTime())/(1e3*60*60)<24?this.isBlocked=!0:(await chrome.storage.local.remove(["tradingBlocked","blockDate"]),this.isBlocked=!1)}}catch(t){console.error("Error checking trading block:",t),this.isBlocked=!1}}showBlockedMessage(){var o,e;const t=document.createElement("div");t.id="trading-block-overlay",t.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.9);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,t.innerHTML=`
      <div style="
        background: white;
        padding: 40px;
        border-radius: 16px;
        text-align: center;
        max-width: 500px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="font-size: 64px; margin-bottom: 20px;">🧘‍♂️</div>
        <h2 style="color: #d32f2f; margin: 0 0 16px 0;">Giao dịch bị khóa</h2>
        <p style="color: #666; margin: 0 0 24px 0; line-height: 1.5;">
          Hệ thống đã khóa giao dịch đến ngày mai để bảo vệ tâm lý của bạn.<br>
          Hãy sử dụng thời gian này để nghỉ ngơi và rèn luyện tâm.
        </p>
        <div style="display: flex; gap: 12px; justify-content: center;">
          <button id="meditation-btn" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🧘‍♂️ Thiền định</button>
          <button id="close-tab-btn" style="
            background: #666;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">❌ Đóng tab</button>
        </div>
        <p style="color: #999; font-size: 12px; margin: 16px 0 0 0;">
          Khóa sẽ tự động mở sau 24 giờ
        </p>
      </div>
    `,document.body.appendChild(t),(o=document.getElementById("meditation-btn"))==null||o.addEventListener("click",()=>{this.openMeditationOptions()}),(e=document.getElementById("close-tab-btn"))==null||e.addEventListener("click",()=>{window.close()})}async checkPsychologyConfirmation(){try{(await chrome.storage.local.get(["needsPsychologyConfirmation","lastConfirmationTime"])).needsPsychologyConfirmation&&this.showPsychologyConfirmation()}catch(t){console.error("Error checking psychology confirmation:",t)}}showPsychologyConfirmation(){var e,s;const t=document.createElement("div");t.id="psychology-confirmation-modal",t.style.cssText=`
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `,t.innerHTML=`
      <div style="
        background: white;
        padding: 32px;
        border-radius: 16px;
        text-align: center;
        max-width: 450px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
      ">
        <div style="font-size: 48px; margin-bottom: 16px;">🧠</div>
        <h3 style="color: #1976d2; margin: 0 0 16px 0;">Xác nhận tâm lý</h3>
        <p style="color: #666; margin: 0 0 24px 0; line-height: 1.5;">
          Bạn đã hoàn thành thiền định hoặc nghỉ ngơi?<br>
          Tâm lý hiện tại có ổn định và sẵn sàng giao dịch không?
        </p>
        
        <div style="margin-bottom: 24px;">
          <p style="color: #333; font-weight: bold; margin: 0 0 12px 0;">
            Trạng thái tâm lý hiện tại:
          </p>
          <div style="display: flex; flex-direction: column; gap: 8px;">
            <button class="psychology-option" data-state="balanced" style="
              background: #e8f5e8;
              color: #2e7d32;
              border: 2px solid #e8f5e8;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😌 Cân bằng và tỉnh táo</button>
            <button class="psychology-option" data-state="confident" style="
              background: #e3f2fd;
              color: #1976d2;
              border: 2px solid #e3f2fd;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😊 Tự tin và kiên nhẫn</button>
            <button class="psychology-option" data-state="not_ready" style="
              background: #fff3e0;
              color: #f57c00;
              border: 2px solid #fff3e0;
              padding: 12px;
              border-radius: 8px;
              cursor: pointer;
              font-size: 14px;
            ">😐 Chưa sẵn sàng, cần thêm thời gian</button>
          </div>
        </div>

        <div style="display: flex; gap: 12px; justify-content: center;">
          <button id="continue-trading-btn" style="
            background: #4caf50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            opacity: 0.5;
          " disabled>✅ Tiếp tục giao dịch</button>
          <button id="more-meditation-btn" style="
            background: #9c27b0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
          ">🧘‍♂️ Thiền thêm</button>
        </div>
      </div>
    `,document.body.appendChild(t);let o="";t.querySelectorAll(".psychology-option").forEach(l=>{l.addEventListener("click",p=>{const r=p.target;o=r.getAttribute("data-state")||"",t.querySelectorAll(".psychology-option").forEach(a=>{a.style.borderColor=a.style.backgroundColor}),r.style.borderColor="#1976d2";const i=document.getElementById("continue-trading-btn");o==="balanced"||o==="confident"?(i.disabled=!1,i.style.opacity="1"):(i.disabled=!0,i.style.opacity="0.5")})}),(e=document.getElementById("continue-trading-btn"))==null||e.addEventListener("click",()=>{(o==="balanced"||o==="confident")&&this.confirmPsychologyAndContinue(o)}),(s=document.getElementById("more-meditation-btn"))==null||s.addEventListener("click",()=>{this.openMeditationOptions()})}async confirmPsychologyAndContinue(t){try{await chrome.storage.local.set({needsPsychologyConfirmation:!1,lastConfirmationTime:Date.now(),confirmedPsychologyState:t});const o=document.getElementById("psychology-confirmation-modal");o&&o.remove(),this.showSuccessMessage("✅ Tâm lý đã được xác nhận! Bạn có thể giao dịch an toàn.")}catch(o){console.error("Error confirming psychology:",o)}}openMeditationOptions(){try{chrome.runtime.sendMessage({action:"openOptions",tab:"meditation"})}catch(t){console.error("Error opening meditation options:",t),window.open(chrome.runtime.getURL("options.html#meditation"),"_blank")}}showSuccessMessage(t){const o=document.createElement("div");o.style.cssText=`
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4caf50;
      color: white;
      padding: 16px 24px;
      border-radius: 8px;
      z-index: 999999;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      animation: slideIn 0.3s ease-out;
    `,o.textContent=t;const e=document.createElement("style");e.textContent=`
      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    `,document.head.appendChild(e),document.body.appendChild(o),setTimeout(()=>{o.remove(),e.remove()},3e3)}}document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>new c):new c;
