# 🔧 JSON Server Troubleshooting Guide

## ❌ **Vấn đề gặp phải:**
```
npm run server
# Lỗi: json-server command not found
# Hoặc: npm ERR! notarget No matching version found
```

## ✅ **Root Cause:**
- **json-server** chưa được cài đặt
- **Version conflict** với npm registry
- **Package.json** có script nhưng dependency bị thiếu

## 🛠️ **Solutions Applied:**

### 1. **Kiểm tra json-server có được cài đặt không:**
```bash
npm list json-server
# Output: └── (empty)  ❌ Chưa cài đặt
```

### 2. **Cài đặt json-server version cụ thể:**
```bash
# Cài đặt version stable
npm install json-server@0.17.4 --save-dev

# Kết quả:
# ✅ added 118 packages, and audited 357 packages in 1m
# ✅ found 0 vulnerabilities
```

### 3. **Verify installation:**
```bash
npm list json-server
# Output: └── json-server@0.17.4  ✅ Đã cài đặt
```

### 4. **Khởi động JSON Server:**
```bash
npm run server

# Output:
# \{^_^}/ hi!
# Loading db.json
# Done
# Resources
# http://0.0.0.0:3001/dailyGoals
# http://0.0.0.0:3001/psychologyStates
# ...
# Watching...
```

## 📊 **JSON Server Status:**

### ✅ **Working Endpoints:**
```
http://localhost:3001/dailyGoals        ✅ Daily goals data
http://localhost:3001/psychologyStates  ✅ Psychology assessments
http://localhost:3001/tradingMethods    ✅ Trading methods
http://localhost:3001/tradingSessions   ✅ Trading sessions
http://localhost:3001/trades            ✅ Individual trades
http://localhost:3001/statistics        ✅ Statistics data
http://localhost:3001/settings          ✅ User settings
```

### 🧪 **Test API:**
```bash
# Test với curl
curl http://localhost:3001/dailyGoals

# Response:
# StatusCode: 200 OK ✅
# Content: JSON data với sample goals
```

## 🔧 **Package.json Configuration:**

### **Scripts:**
```json
{
  "scripts": {
    "server": "json-server --watch db.json --port 3001 --host 0.0.0.0",
    "dev": "concurrently \"npm run server\" \"vite\"",
    "build": "vite build && npm run copy-assets"
  }
}
```

### **Dependencies:**
```json
{
  "devDependencies": {
    "json-server": "^0.17.4",  ✅ Installed
    "concurrently": "^9.1.0"   ✅ For running multiple commands
  }
}
```

## 🚀 **Extension Integration:**

### **API Client (src/services/api.ts):**
```typescript
const API_BASE_URL = 'http://localhost:3001';

export const api = {
  // Daily Goals
  getDailyGoals: () => fetch(`${API_BASE_URL}/dailyGoals`),
  createDailyGoal: (goal) => fetch(`${API_BASE_URL}/dailyGoals`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(goal)
  }),
  
  // Psychology States
  getPsychologyStates: () => fetch(`${API_BASE_URL}/psychologyStates`),
  // ... other endpoints
};
```

### **Storage Wrapper (src/utils/storage.ts):**
```typescript
export const storage = {
  async getTodayGoals() {
    const response = await api.getDailyGoals();
    const goals = await response.json();
    return goals.find(g => g.date === new Date().toISOString().split('T')[0]);
  },
  
  async setTodayGoals(goals) {
    return await api.createDailyGoal({
      ...goals,
      date: new Date().toISOString().split('T')[0]
    });
  }
  // ... other methods
};
```

## 🔍 **Troubleshooting Steps:**

### **1. JSON Server không khởi động:**
```bash
# Kiểm tra port 3001 có bị chiếm không
netstat -an | findstr 3001

# Kill process nếu cần (Windows)
taskkill /F /PID <process_id>

# Khởi động lại
npm run server
```

### **2. CORS Issues:**
JSON Server tự động enable CORS, nhưng nếu gặp vấn đề:
```bash
# Thêm CORS headers
json-server --watch db.json --port 3001 --host 0.0.0.0 --middlewares ./cors.js
```

### **3. Extension không connect được:**
- ✅ **Kiểm tra JSON Server đang chạy:** `http://localhost:3001`
- ✅ **Kiểm tra API endpoints** trong DevTools Network tab
- ✅ **Verify manifest.json** có permissions đúng
- ✅ **Check content script** có load được không

### **4. Data không persist:**
- ✅ **db.json file** phải có write permissions
- ✅ **JSON Server** phải có quyền ghi file
- ✅ **Backup db.json** thường xuyên

## 📝 **Development Workflow:**

### **1. Start Development:**
```bash
# Terminal 1: JSON Server
npm run server

# Terminal 2: Vite Dev Server  
npm run dev

# Terminal 3: Build Extension
npm run build
```

### **2. Extension Testing:**
1. **Load extension** từ `dist/` folder
2. **Visit:** https://binomo1.com/trading
3. **Check popup:** API status should be "online"
4. **Test content script:** Should connect to JSON Server

### **3. API Testing:**
```bash
# Test all endpoints
curl http://localhost:3001/dailyGoals
curl http://localhost:3001/psychologyStates
curl http://localhost:3001/tradingMethods
curl http://localhost:3001/tradingSessions
curl http://localhost:3001/trades
curl http://localhost:3001/statistics
curl http://localhost:3001/settings
```

## ⚠️ **Common Issues:**

### **Issue 1: Port 3001 already in use**
```bash
# Solution: Change port in package.json
"server": "json-server --watch db.json --port 3002 --host 0.0.0.0"

# Update API_BASE_URL in src/services/api.ts
const API_BASE_URL = 'http://localhost:3002';
```

### **Issue 2: db.json not found**
```bash
# Solution: Ensure db.json exists in root directory
# Copy from backup or recreate with sample data
```

### **Issue 3: Permission denied**
```bash
# Solution: Run as administrator or check file permissions
# Ensure db.json is writable
```

## 🎯 **Success Indicators:**

### ✅ **JSON Server Running:**
- Console shows: `\{^_^}/ hi!`
- All endpoints listed
- `Watching...` message displayed

### ✅ **Extension Connected:**
- Popup shows "JSON Server đang hoạt động"
- Content script loads without errors
- API calls successful in Network tab

### ✅ **Data Flow Working:**
- Extension → JSON Server → db.json
- CRUD operations successful
- Data persists between sessions

---

**🎉 JSON Server bây giờ hoạt động hoàn hảo và extension có thể kết nối thành công!**
