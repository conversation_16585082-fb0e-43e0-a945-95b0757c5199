import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Step,
  StepL<PERSON>l,
  StepContent,
  Chip,
  Alert,
  CircularProgress,
  Divider,
  Card,
  CardContent,
  LinearProgress,
  TextField
} from '@mui/material';
import {
  TrendingUp,
  Psychology,
  Analytics,
  CheckCircle,
  Warning,
  Block,
  Refresh
} from '@mui/icons-material';
// Note: For sidebar, we'll create simplified inline components instead of importing complex modals
import { ApiService } from '../services/api';
import { PsychologyAI, type PsychologyAssessment, type AssessmentInput } from '../services/psychology-ai';

interface TradingFlow {
  step: number;
  completed: boolean;
  data?: any;
}

const Sidebar: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [flowData, setFlowData] = useState<TradingFlow>({
    step: 0,
    completed: false
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dailyGoals, setDailyGoals] = useState<any>(null);
  const [psychologyResult, setPsychologyResult] = useState<any>(null);
  const [tradingMethod, setTradingMethod] = useState<any>(null);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [isBlocked, setIsBlocked] = useState(false);

  // Trading flow steps
  const steps = [
    {
      label: 'Mục tiêu hàng ngày',
      description: 'Thiết lập mục đích tâm linh và mục tiêu giao dịch',
      icon: <TrendingUp />,
      component: 'goals'
    },
    {
      label: 'Đánh giá tâm lý',
      description: 'Kiểm tra trạng thái tinh thần trước khi giao dịch',
      icon: <Psychology />,
      component: 'psychology'
    },
    {
      label: 'Sẵn sàng giao dịch',
      description: 'Hoàn tất chuẩn bị và mở Binomo',
      icon: <CheckCircle />,
      component: 'ready'
    }
  ];

  useEffect(() => {
    initializeSidebar();
  }, []);

  const initializeSidebar = async () => {
    setLoading(true);
    try {
      // Check if trading is blocked
      const blockStatus = await checkTradingBlock();
      if (blockStatus) {
        setIsBlocked(true);
        setLoading(false);
        return;
      }

      // Check existing daily goals
      const goals = await ApiService.getTodayGoals();
      if (goals) {
        setDailyGoals(goals);
        setCurrentStep(1);
      }
    } catch (error) {
      setError('Không thể kết nối với server. Hãy chạy: npm run server');
    }
    setLoading(false);
  };

  const checkTradingBlock = async (): Promise<boolean> => {
    try {
      const result = await chrome.storage.local.get(['tradingBlocked', 'blockDate']);
      if (result.tradingBlocked && result.blockDate) {
        const blockDate = new Date(result.blockDate);
        const now = new Date();
        const diffHours = (now.getTime() - blockDate.getTime()) / (1000 * 60 * 60);
        
        if (diffHours < 24) {
          return true;
        } else {
          // Unblock after 24 hours
          await chrome.storage.local.remove(['tradingBlocked', 'blockDate']);
          return false;
        }
      }
      return false;
    } catch (error) {
      console.error('Error checking trading block:', error);
      return false;
    }
  };

  const handleStepComplete = (stepIndex: number, data: any) => {
    switch (stepIndex) {
      case 0: // Daily Goals
        setDailyGoals(data);
        setCurrentStep(1);
        break;
      case 1: // Psychology Assessment
        setPsychologyResult(data);
        if (data.shouldTrade) {
          setCurrentStep(2); // Go directly to ready step
        } else {
          // Block trading with calculated duration
          const blockDuration = data.blockDuration || 1440; // Default 24 hours
          blockTradingWithDuration(blockDuration);
        }
        break;
      case 2: // Ready to trade
        // All steps completed, ready to trade
        break;
    }
  };

  const blockTradingWithDuration = async (durationMinutes: number) => {
    try {
      const now = new Date();
      const blockUntil = new Date(now.getTime() + durationMinutes * 60 * 1000);

      await chrome.storage.local.set({
        tradingBlocked: true,
        blockDate: now.toISOString(),
        blockUntil: blockUntil.toISOString(),
        blockDurationMinutes: durationMinutes,
        needsPsychologyConfirmation: true
      });
      setIsBlocked(true);
    } catch (error) {
      console.error('Error blocking trading:', error);
    }
  };

  const resetFlow = () => {
    setCurrentStep(0);
    setDailyGoals(null);
    setPsychologyResult(null);
    setTradingMethod(null);
    setAnalysisResult(null);
    setError(null);
  };

  const openMeditationTab = () => {
    chrome.tabs.create({ url: chrome.runtime.getURL('options.html#meditation') });
  };

  const openBinomoForTrading = async () => {
    try {
      // Clear psychology confirmation flag since user is ready to trade
      await chrome.storage.local.set({
        needsPsychologyConfirmation: false,
        lastConfirmationTime: Date.now(),
        confirmedPsychologyState: 'ready_from_sidebar'
      });

      // Open Binomo in new tab
      chrome.tabs.create({ url: 'https://binomo1.com/trading' });

      // Show success message
      console.log('✅ Opened Binomo for trading with confirmed psychology state');
    } catch (error) {
      console.error('Error opening Binomo:', error);
      // Fallback: open directly
      window.open('https://binomo1.com/trading', '_blank');
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="100vh">
        <CircularProgress />
      </Box>
    );
  }

  if (isBlocked) {
    return (
      <Paper sx={{ p: 3, m: 2, textAlign: 'center' }}>
        <Block sx={{ fontSize: 48, color: 'error.main', mb: 2 }} />
        <Typography variant="h6" color="error" gutterBottom>
          Giao dịch bị khóa
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Hệ thống đã khóa giao dịch đến ngày mai để bảo vệ tâm lý của bạn.
        </Typography>
        <Button
          variant="contained"
          color="secondary"
          onClick={openMeditationTab}
          sx={{ mb: 2 }}
        >
          🧘‍♂️ Thiền định
        </Button>
        <Typography variant="caption" display="block" color="text.secondary">
          Hãy sử dụng thời gian này để nghỉ ngơi và rèn luyện tâm.
        </Typography>
      </Paper>
    );
  }

  if (error) {
    return (
      <Paper sx={{ p: 3, m: 2 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button variant="outlined" onClick={initializeSidebar} startIcon={<Refresh />}>
          Thử lại
        </Button>
      </Paper>
    );
  }

  return (
    <Box sx={{ width: '100%', maxWidth: 400, p: 2 }}>
      {/* Header */}
      <Paper sx={{ p: 2, mb: 2, textAlign: 'center' }}>
        <Typography variant="h6" color="primary" gutterBottom>
          🤖 Trading Assistant
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Hướng dẫn giao dịch có ý thức
        </Typography>
      </Paper>

      {/* Progress Overview */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="subtitle2" gutterBottom>
            Tiến độ: {currentStep + 1}/{steps.length} bước
          </Typography>
          <LinearProgress
            variant="determinate"
            value={((currentStep + 1) / steps.length) * 100}
            sx={{ mb: 1 }}
          />
          <Typography variant="caption" color="text.secondary">
            {currentStep >= steps.length ? 'Sẵn sàng giao dịch!' : `Bước ${currentStep + 1}: ${steps[currentStep]?.label}`}
          </Typography>
        </CardContent>
      </Card>

      {/* Trading Flow Stepper */}
      <Paper sx={{ p: 2 }}>
        <Stepper activeStep={currentStep} orientation="vertical">
          {steps.map((step, index) => (
            <Step key={step.label}>
              <StepLabel
                optional={
                  index === currentStep ? (
                    <Typography variant="caption">Bước hiện tại</Typography>
                  ) : null
                }
                icon={step.icon}
              >
                {step.label}
              </StepLabel>
              <StepContent>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {step.description}
                </Typography>
                
                {/* Render appropriate component */}
                {index === currentStep && (
                  <Box>
                    {step.component === 'goals' && (
                      <SidebarGoalsForm onComplete={(data) => handleStepComplete(0, data)} />
                    )}

                    {step.component === 'psychology' && (
                      <SidebarPsychologyForm onComplete={(data) => handleStepComplete(1, data)} />
                    )}

                    {step.component === 'ready' && (
                      <Box sx={{ p: 2, bgcolor: 'success.light', borderRadius: 1 }}>
                        <Typography variant="h6" color="success.dark" gutterBottom>
                          ✅ Sẵn sàng giao dịch!
                        </Typography>
                        <Typography variant="body2" sx={{ mb: 2 }}>
                          Tâm lý ổn định, có thể bắt đầu giao dịch an toàn.
                        </Typography>
                        <Box display="flex" gap={1}>
                          <Button
                            variant="contained"
                            color="success"
                            size="small"
                            onClick={openBinomoForTrading}
                          >
                            🚀 Mở Binomo
                          </Button>
                          <Button
                            variant="outlined"
                            size="small"
                            onClick={resetFlow}
                            startIcon={<Refresh />}
                          >
                            Làm lại
                          </Button>
                        </Box>
                      </Box>
                    )}
                  </Box>
                )}

                {/* Show completed step summary */}
                {index < currentStep && (
                  <Box>
                    <Chip 
                      label="Hoàn thành" 
                      color="success" 
                      size="small" 
                      icon={<CheckCircle />}
                    />
                  </Box>
                )}
              </StepContent>
            </Step>
          ))}
        </Stepper>



        {/* Reset Button */}
        {currentStep > 0 && (
          <Box sx={{ mt: 2, textAlign: 'center' }}>
            <Button
              variant="text"
              size="small"
              onClick={resetFlow}
              startIcon={<Refresh />}
            >
              Khởi động lại flow
            </Button>
          </Box>
        )}
      </Paper>
    </Box>
  );
};

// Simplified inline components for sidebar

const SidebarGoalsForm: React.FC<{ onComplete: (data: any) => void }> = ({ onComplete }) => {
  const [formData, setFormData] = useState({
    spiritualPurpose: '',
    profitTarget: '',
    lossLimit: '',
    maxTrades: ''
  });

  const handleSubmit = async () => {
    try {
      const goals = {
        id: new Date().toISOString().split('T')[0],
        date: new Date().toISOString().split('T')[0],
        tradingGoal: formData.spiritualPurpose,
        profitTarget: parseFloat(formData.profitTarget) || 0,
        lossLimit: parseFloat(formData.lossLimit) || 0,
        maxTrades: parseInt(formData.maxTrades) || 0,
        completed: false
      };

      await ApiService.setTodayGoals(goals);
      onComplete(goals);
    } catch (error) {
      console.error('Error saving goals:', error);
    }
  };

  return (
    <Box sx={{ p: 2 }}>
      {/* Spiritual Purpose - Highlighted */}
      <Card variant="outlined" sx={{ mb: 2, border: '2px solid', borderColor: 'primary.main' }}>
        <CardContent sx={{ p: 2 }}>
          <Typography variant="subtitle2" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold' }}>
            🎯 Mục đích tâm linh hôm nay:
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={4}
            value={formData.spiritualPurpose}
            onChange={(e) => setFormData({ ...formData, spiritualPurpose: e.target.value })}
            placeholder="Hôm nay tôi giao dịch để rèn luyện tâm tính kiên nhẫn, kỷ luật và tỉnh thức..."
            variant="outlined"
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'background.paper',
                '&:hover': {
                  backgroundColor: 'action.hover',
                },
                '&.Mui-focused': {
                  backgroundColor: 'background.paper',
                }
              }
            }}
          />
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            💡 Tập trung vào mục đích tâm linh thay vì chỉ lợi nhuận
          </Typography>
        </CardContent>
      </Card>

      {/* Trading Targets - Compact */}
      <Card variant="outlined" sx={{ mb: 2 }}>
        <CardContent sx={{ p: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            📊 Mục tiêu giao dịch:
          </Typography>
          <Box display="flex" gap={1}>
            <TextField
              size="small"
              label="Lãi mục tiêu"
              type="number"
              value={formData.profitTarget}
              onChange={(e) => setFormData({ ...formData, profitTarget: e.target.value })}
              InputProps={{
                startAdornment: <Typography variant="caption" sx={{ mr: 0.5 }}>$</Typography>
              }}
            />
            <TextField
              size="small"
              label="Lỗ tối đa"
              type="number"
              value={formData.lossLimit}
              onChange={(e) => setFormData({ ...formData, lossLimit: e.target.value })}
              InputProps={{
                startAdornment: <Typography variant="caption" sx={{ mr: 0.5 }}>$</Typography>
              }}
            />
            <TextField
              size="small"
              label="Số lệnh"
              type="number"
              value={formData.maxTrades}
              onChange={(e) => setFormData({ ...formData, maxTrades: e.target.value })}
              InputProps={{
                endAdornment: <Typography variant="caption" sx={{ ml: 0.5 }}>lệnh</Typography>
              }}
            />
          </Box>
        </CardContent>
      </Card>

      <Button
        variant="contained"
        size="medium"
        onClick={handleSubmit}
        disabled={!formData.spiritualPurpose.trim()}
        fullWidth
        sx={{
          py: 1.5,
          fontSize: '1rem',
          fontWeight: 'bold'
        }}
      >
        ✅ Lưu mục tiêu hàng ngày
      </Button>
    </Box>
  );
};

const SidebarPsychologyForm: React.FC<{ onComplete: (data: any) => void }> = ({ onComplete }) => {
  const [formData, setFormData] = useState({
    emotionalState: '',
    financialSituation: '',
    recentPerformance: '',
    sleepQuality: '',
    stressLevel: '',
    motivation: '',
    additionalNotes: ''
  });
  const [loading, setLoading] = useState(false);
  const [useAI, setUseAI] = useState(false);

  const quickStates = [
    { value: 'balanced', label: '😌 Cân bằng và tỉnh táo', score: 85 },
    { value: 'confident', label: '😊 Tự tin nhưng cẩn thận', score: 80 },
    { value: 'neutral', label: '😐 Bình thường', score: 60 },
    { value: 'tired', label: '😴 Mệt mỏi', score: 40 },
    { value: 'stressed', label: '😰 Căng thẳng', score: 30 },
    { value: 'emotional', label: '😡 Cảm xúc', score: 20 }
  ];

  const handleQuickAssessment = async (state: any) => {
    setLoading(true);
    try {
      const blockDuration = calculateBlockDuration(state.score);

      const result = {
        score: state.score,
        shouldTrade: state.score >= 60,
        blockDuration,
        recommendation: state.score >= 60
          ? 'Có thể giao dịch cẩn thận'
          : `Nên nghỉ ngơi ${formatDuration(blockDuration)}`,
        timestamp: Date.now(),
        method: 'quick'
      };

      await ApiService.createPsychologyState({
        timestamp: Date.now(),
        state: state.value as any,
        description: state.label,
        canTrade: state.score >= 60
      });

      onComplete(result);
    } catch (error) {
      console.error('Error in quick assessment:', error);
    }
    setLoading(false);
  };

  const handleAIAssessment = async () => {
    setLoading(true);
    try {
      // Get OpenAI API key from storage
      const result = await chrome.storage.local.get(['openaiApiKey']);
      if (!result.openaiApiKey) {
        alert('Vui lòng cấu hình OpenAI API key trong Settings');
        setLoading(false);
        return;
      }

      const psychologyAI = new PsychologyAI(result.openaiApiKey);
      const assessment = await psychologyAI.assessPsychology(formData as AssessmentInput);

      await ApiService.createPsychologyState({
        timestamp: Date.now(),
        state: 'ai_assessed' as any,
        description: `AI Score: ${assessment.score}`,
        canTrade: assessment.shouldTrade
      });

      onComplete({
        ...assessment,
        timestamp: Date.now(),
        method: 'ai'
      });
    } catch (error) {
      console.error('Error in AI assessment:', error);
      alert('Lỗi đánh giá AI. Vui lòng thử lại hoặc sử dụng đánh giá nhanh.');
    }
    setLoading(false);
  };

  const calculateBlockDuration = (score: number): number => {
    if (score >= 80) return 0;
    if (score >= 60) return 15;
    if (score >= 45) return 60;
    if (score >= 30) return 240;
    if (score >= 15) return 720;
    return 1440;
  };

  const formatDuration = (minutes: number): string => {
    if (minutes === 0) return 'không cần nghỉ';
    if (minutes < 60) return `${minutes} phút`;
    if (minutes < 1440) return `${Math.round(minutes / 60)} tiếng`;
    return `${Math.round(minutes / 1440)} ngày`;
  };

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="subtitle2" gutterBottom>
        Đánh giá tâm lý giao dịch:
      </Typography>

      {/* Quick Assessment */}
      <Box mb={2}>
        <Typography variant="caption" color="text.secondary" gutterBottom>
          Đánh giá nhanh:
        </Typography>
        <Box display="flex" flexDirection="column" gap={0.5}>
          {quickStates.map((state) => (
            <Button
              key={state.value}
              variant="outlined"
              size="small"
              onClick={() => handleQuickAssessment(state)}
              disabled={loading}
              sx={{ justifyContent: 'flex-start', fontSize: '0.7rem', py: 0.5 }}
            >
              {state.label}
            </Button>
          ))}
        </Box>
      </Box>

      {/* AI Assessment Toggle */}
      <Box display="flex" alignItems="center" mb={1}>
        <Typography variant="caption" sx={{ flex: 1 }}>
          Đánh giá AI chi tiết:
        </Typography>
        <Button
          size="small"
          variant={useAI ? 'contained' : 'outlined'}
          onClick={() => setUseAI(!useAI)}
          sx={{ fontSize: '0.6rem', py: 0.5, px: 1 }}
        >
          {useAI ? 'Đang bật' : 'Bật AI'}
        </Button>
      </Box>

      {/* AI Assessment Form */}
      {useAI && (
        <Box mb={2}>
          <TextField
            fullWidth
            size="small"
            label="Trạng thái cảm xúc"
            value={formData.emotionalState}
            onChange={(e) => setFormData({ ...formData, emotionalState: e.target.value })}
            placeholder="Cân bằng, căng thẳng, lo âu..."
            sx={{ mb: 1 }}
          />
          <TextField
            fullWidth
            size="small"
            label="Tình hình tài chính"
            value={formData.financialSituation}
            onChange={(e) => setFormData({ ...formData, financialSituation: e.target.value })}
            placeholder="Ổn định, khó khăn, dư dả..."
            sx={{ mb: 1 }}
          />
          <TextField
            fullWidth
            size="small"
            label="Kết quả gần đây"
            value={formData.recentPerformance}
            onChange={(e) => setFormData({ ...formData, recentPerformance: e.target.value })}
            placeholder="Lãi, lỗ, hòa vốn..."
            sx={{ mb: 1 }}
          />
          <Button
            variant="contained"
            size="small"
            onClick={handleAIAssessment}
            disabled={loading || !formData.emotionalState}
            fullWidth
          >
            {loading ? 'Đang đánh giá...' : '🤖 Đánh giá AI'}
          </Button>
        </Box>
      )}

      {loading && (
        <Box display="flex" justifyContent="center" mt={1}>
          <CircularProgress size={20} />
        </Box>
      )}
    </Box>
  );
};

const SidebarMethodForm: React.FC<{ onComplete: (data: any) => void }> = ({ onComplete }) => {
  const [selectedMethod, setSelectedMethod] = useState('');

  const tradingMethods = [
    { id: 'bollinger_bands', name: '📊 Bollinger Bands', description: 'Giao dịch theo dải Bollinger' },
    { id: 'support_resistance', name: '📈 Support/Resistance', description: 'Giao dịch tại vùng hỗ trợ/kháng cự' },
    { id: 'trend_following', name: '📉 Trend Following', description: 'Theo xu hướng thị trường' },
    { id: 'reversal', name: '🔄 Reversal', description: 'Giao dịch đảo chiều' }
  ];

  const handleSubmit = () => {
    const method = tradingMethods.find(m => m.id === selectedMethod);
    if (method) {
      onComplete(method);
    }
  };

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="subtitle2" gutterBottom>
        Chọn phương pháp giao dịch:
      </Typography>

      <Box display="flex" flexDirection="column" gap={1} mb={2}>
        {tradingMethods.map((method) => (
          <Card
            key={method.id}
            sx={{
              cursor: 'pointer',
              border: selectedMethod === method.id ? '2px solid #1976d2' : '1px solid #e0e0e0'
            }}
            onClick={() => setSelectedMethod(method.id)}
          >
            <CardContent sx={{ p: 1.5 }}>
              <Typography variant="body2" fontWeight="bold">
                {method.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {method.description}
              </Typography>
            </CardContent>
          </Card>
        ))}
      </Box>

      <Button
        variant="contained"
        size="small"
        onClick={handleSubmit}
        disabled={!selectedMethod}
        fullWidth
      >
        Chọn phương pháp
      </Button>
    </Box>
  );
};

const SidebarAnalysisForm: React.FC<{ method: any; onComplete: (data: any) => void }> = ({ method, onComplete }) => {
  const [answers, setAnswers] = useState<Record<string, boolean>>({});

  const analysisQuestions = [
    { id: 'trend_clear', text: 'Xu hướng thị trường rõ ràng?' },
    { id: 'volume_good', text: 'Khối lượng giao dịch tốt?' },
    { id: 'setup_valid', text: 'Setup hợp lệ theo phương pháp?' },
    { id: 'risk_acceptable', text: 'Rủi ro có thể chấp nhận?' },
    { id: 'timing_right', text: 'Thời điểm vào lệnh phù hợp?' }
  ];

  const handleSubmit = () => {
    const totalQuestions = analysisQuestions.length;
    const positiveAnswers = Object.values(answers).filter(Boolean).length;
    const percentage = Math.round((positiveAnswers / totalQuestions) * 100);

    const shouldTrade = percentage >= 80;
    const recommendation = {
      shouldTrade,
      percentage,
      message: shouldTrade
        ? `Setup chất lượng cao (${percentage}%) - Có thể giao dịch`
        : `Setup chưa tối ưu (${percentage}%) - Nên chờ setup tốt hơn`
    };

    onComplete({ recommendation, answers, percentage });
  };

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="subtitle2" gutterBottom>
        Phân tích setup - {method.name}:
      </Typography>

      <Box display="flex" flexDirection="column" gap={1} mb={2}>
        {analysisQuestions.map((question) => (
          <Box key={question.id} display="flex" alignItems="center" justifyContent="space-between">
            <Typography variant="caption" sx={{ flex: 1 }}>
              {question.text}
            </Typography>
            <Box display="flex" gap={0.5}>
              <Button
                size="small"
                variant={answers[question.id] === true ? 'contained' : 'outlined'}
                color="success"
                onClick={() => setAnswers({ ...answers, [question.id]: true })}
                sx={{ minWidth: 40, fontSize: '0.7rem' }}
              >
                Có
              </Button>
              <Button
                size="small"
                variant={answers[question.id] === false ? 'contained' : 'outlined'}
                color="error"
                onClick={() => setAnswers({ ...answers, [question.id]: false })}
                sx={{ minWidth: 40, fontSize: '0.7rem' }}
              >
                Không
              </Button>
            </Box>
          </Box>
        ))}
      </Box>

      <Button
        variant="contained"
        size="small"
        onClick={handleSubmit}
        disabled={Object.keys(answers).length < analysisQuestions.length}
        fullWidth
      >
        Phân tích setup
      </Button>
    </Box>
  );
};

export default Sidebar;
